<?php

namespace App\Http\Controllers\Api\V1;

use Akaunting\Api\Data\ItemData;
use Akaunting\Api\Http\Requests\BaseListElementsDTO;
use Akaunting\Api\Http\Services\ItemElementService;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\WorkerRequests\StoreWorkOrderItemsRequest;
use App\Models\WorkOrders;
use App\Models\WorkOrderItemRequest;
use App\Models\WorkOrderRequestedItem;
use App\Models\ContractUsableItem;
use App\Models\Notification;
use App\Http\Helpers\NotificationHelper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Http\Helpers\WorkorderHelper;

class InventoryController extends Controller
{
    public function __construct(protected ItemElementService $itemElementService,){}

    /**
     * Start the examination of a work order.
     *
     * @param int $workOrderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function startExamine($workOrderId)
    {
        
        try {
                // Retrieve the work order
                $workOrder = WorkOrders::find($workOrderId);

                // Update the examine_button_clicked_at column with the current timestamp
                $workOrder->update(['examine_button_clicked_at' => now()]);

                if(Auth::user()->user_type == 'team_leader')
                {
                    // Notification messages
                    $message = __('work_order.notifications.team_leader_started_work_order_examination', ['team_leader_name' => Auth::user()->name], 'en');
                    $message_ar = __('work_order.notifications.team_leader_started_work_order_examination', ['team_leader_name' => Auth::user()->name], 'ar');
                }
                else
                {
                    // Notification messages
                    $message = __('work_order.notifications.worker_started_work_order_examination', ['worker_name' => Auth::user()->name], 'en');
                    $message_ar = __('work_order.notifications.worker_started_work_order_examination', ['worker_name' => Auth::user()->name], 'ar');
                }
            

            // Send the notification and timeline message
            NotificationHelper::sendWorkOrderNotification(Auth::user(), $workOrder, $message, $message_ar, 'started_examine');

            return response()->json([
                'success' => true,
                'message' => 'Work order examination started successfully.',
            ]);

        } catch (\Exception $e) {
            // Handle any exceptions that might occur
            return response()->json([
                'success' => false,
                'message' => 'Failed to start work order examination.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    /**
     * Fetch inventory items.
     *
     * @param  int $workOrderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetchInventoryItems($workOrderId)
    {
        try {
            // Retrieve the work order
            $workOrder = WorkOrders::find($workOrderId);

            // Fetch usable items related to the contract
            $selectableItems = $workOrder->contract->usableItems->map(function (ContractUsableItem $item) {
                return $item->getItem();
            });

            // Filter out items where all fields are null and reset the array keys
            $filteredItems = $selectableItems->filter(function ($item) {
                // Check if any values in the item are not null
                return collect($item)->filter()->isNotEmpty();
            })->values(); // Reset array keys

            // Check if there are usable items
            if ($filteredItems->count() === 0) {
                // Return a JSON response for empty items
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch inventory items.',
                ], 500);
            }

            // Return a JSON response with the fetched items
            return response()->json([
                'success' => true,
                'message' => 'Inventory items fetched successfully.',
                'data' => $filteredItems,
            ], 200);
        } catch (\Exception $e) {
            // Handle any exceptions that might occur
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch inventory items.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    /**
     * Store work order items.
     *
     * @param  StoreWorkOrderItemsRequest  $request
     * @param  int  $workOrderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeWorkOrderItems(StoreWorkOrderItemsRequest $request, $workOrderId)
    {
        //Begin : Check Worker is on leave , if yes then retrun error message
        $check_approved_leavedata = WorkorderHelper::checkActiveApprovedOfflineRequest();

        if($check_approved_leavedata)
        {
            return response()->json(['status' => false, 'message' => __('api.onLeave')], 401);
        }

        //End : Check Worker is on leave , if yes then retrun error message

        try {
            // Start a database transaction
            DB::beginTransaction();

            // Determine the status based on the presence of items
            $status = empty($request->input('items')) ? 'not_requested' : 'requested';
            // Create a work order item request
            $workOrderItemRequest = WorkOrderItemRequest::create([
                'work_order_id' => $workOrderId,
                'worker_id' => Auth::id(),
                'status' => $status,
                'request_note' => $request->input('note'),
            ]);
            
            // Store work order requested items only if items are present
            if ($status === 'requested') {
                foreach ($request->input('items') as $item) {
                    WorkOrderRequestedItem::create([
                        'request_id' => $workOrderItemRequest->id,
                        'item_id' => $item['item_id'],
                        'quantity' => $item['quantity'],
                    ]);
                }
            }

            // Retrieve the work order
            $workOrder = WorkOrders::find($workOrderId);

            if($status == 'not_requested') {
                // Notification messages
                if(Auth::user()->user_type == 'team_leader')
                {
                        $message = __('work_order.notifications.team_leader_finished_examination_and_he_doesnt_need_parts_to_continue_work', ['team_leader_name' => Auth::user()->name], 'en');
                        $message_ar = __('work_order.notifications.team_leader_finished_examination_and_he_doesnt_need_parts_to_continue_work', ['team_leader_name' => Auth::user()->name], 'ar');
                }
                else
                {
                    $message = __('work_order.notifications.worker_finished_examination_and_he_doesnt_need_parts_to_continue_work', ['worker_name' => Auth::user()->name], 'en');
                    $message_ar = __('work_order.notifications.worker_finished_examination_and_he_doesnt_need_parts_to_continue_work', ['worker_name' => Auth::user()->name], 'ar');
                }

                $notification_sub_type = 'dont_need_spare_parts';

                // Update the requested_missing_spare_parts column with the 1
                $workOrder->update(['requested_missing_spare_parts' => 1]);

            } else {
                // Notification messages
                if(Auth::user()->user_type == 'team_leader')
                {
                    $message = __('work_order.notifications.team_leader_finished_examination_and_he_requested_the_needed_parts_to_continue_work', ['team_leader_name' => Auth::user()->name], 'en');
                    $message_ar = __('work_order.notifications.team_leader_finished_examination_and_he_requested_the_needed_parts_to_continue_work', ['team_leader_name' => Auth::user()->name], 'ar');
                }
                else
                {
                    $message = __('work_order.notifications.worker_finished_examination_and_he_requested_the_needed_parts_to_continue_work', ['worker_name' => Auth::user()->name], 'en');
                    $message_ar = __('work_order.notifications.worker_finished_examination_and_he_requested_the_needed_parts_to_continue_work', ['worker_name' => Auth::user()->name], 'ar');
                }
                

                $notification_sub_type = 'need_spare_parts';
            }

            // Send th notification and timeline message
            NotificationHelper::sendWorkOrderNotification(Auth::user(), $workOrder, $message, $message_ar, $notification_sub_type);
            
            // For manage pause notification
            if($status == 'requested') {
                //Pause workorder untill request approve & requested_missing_spare_parts column with the 1
                $workOrder->update(['requested_missing_spare_parts' => 1, 'status' => 3,'pause_start_time' => now(),'pause_time_spent' => "",'pause_time_spent_minutes' => "",'pause_end_time' => NULL]);
                //Send Notification
                // Send th notification and timeline message
                $pause_message = __('work_order.notifications.pause_due_to_approval_of_spare_part_request', ['workorder_id' => $workOrder->work_order_id], 'en');
                $pause_message_ar = __('work_order.notifications.pause_due_to_approval_of_spare_part_request', ['workorder_id' => $workOrder->work_order_id], 'ar');
                NotificationHelper::sendPauseWorkOrderNotification(Auth::user(), $workOrder, $pause_message, $pause_message_ar, 'pause_workorder');
            }

            // Commit the transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Work order items stored successfully.',
            ], 200);
        } catch (\Exception $e) {
            // Rollback the transaction on exception
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to store work order items.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

}
