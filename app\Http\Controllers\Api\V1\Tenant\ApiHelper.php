<?php

namespace App\Http\Controllers\Api\V1\Tenant;

use App\CentralLogics\CategoryLogic;
use App\CentralLogics\Helpers;
use App\Http\Helpers\WorkorderHelper;
use App\Http\Controllers\Controller;
use App\Models\{User, WorkOrders, PropertyBuildings, MaintenanceRequest, Chatroom, RoomsTypeFloors, AssetCategory, NoChecklistAction, ChecklistSubtask, NoChecklistSubtask, SubtaskActionsList, Notification, Property};
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Auth;
use DB;
use Helper;
use File;
use Image;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use Log;

use ImagesUploadHelper;
use App\Services\FirebaseService ;

use App\Jobs\SendFirebaseNotificationJob;
class ApiHelper
{




    public static function get_time_line($post_date)
    {

        $current = strtotime(date("Y-m-d"));
        $date    = strtotime($post_date);

        $datediff = $date - $current;
        $difference = floor($datediff / (60 * 60 * 24));

        $time_line = '';
        if($difference == 0) {
            $time_line = 'today';
        } elseif($difference < -1) {
            $time_line = date('Y-m-d H:i:s', strtotime($post_date));
        } else {
            $time_line = 'yesterday';
        }

        return $time_line;
    }


    public static function get_time_line_minutes($post_date)
    {

        $ptime = strtotime($post_date);
        $etime = time() - $ptime;

        // 1646911813 1649590249
        //dd(date('d-m-Y',strtotime('-1 month')));
        $one_mnth_time = strtotime('-1 month');
        if($ptime > strtotime('-1 month')) {
            if ($etime < 1) {
                return '0 ثواني';
            }

            $a = array( 365 * 24 * 60 * 60  =>  'year',
                        30 * 24 * 60 * 60  =>  'month',
                            24 * 60 * 60  =>  'يوم',
                                60 * 60  =>  'ساعة',
                                        60  =>  'دقيقه',
                                        1  =>  'ثانية'
                        );
            $a_plural = array( 'year'   => 'years',
                            'month'  => 'months',
                            'يوم'    => 'يوم',
                            'ساعة'   => 'ساعة',
                            'دقيقه' => 'دقائق',
                            'ثانية' => 'ثواني'
                        );

            foreach ($a as $secs => $str) {
                $d = $etime / $secs;
                if ($d >= 1) {
                    $r = round($d);
                    //return   ($r > 1 ? $a_plural[$str] : $str). ' ' .$r.' ' . ' قبل ';
                    return   ' قبل ' .$r.' ' . ($r > 1 ? $a_plural[$str] : $str);
                }
            }
        } else {
            return date('d-m-Y', strtotime($post_date));
        }

    }


    public static function getCommentCount($rating, $comment_id)
    {

        $comment_rating = DB::table('tenant_post_comment_rating')->where('comment_id', $comment_id)->where('rating', $rating)->count();
        return $comment_rating;
    }

    public static function getTenantDeviceTokensRaisedMaintenanceRequest($maintenance_id, $building_id)
    {
        $devices_tokens = DB::table('maintanance_request')->select('users.device_token', 'users.user_type', 'users.status', 'users.is_deleted')
        ->leftJoin('users', 'maintanance_request.phone', '=', 'users.phone')
        //->where('users.building_ids',$building_id)
        //->whereRaw("find_in_set($building_id, users.building_ids)")
        ->where('maintanance_request.id', $maintenance_id)
        ->where('users.device_token', '!=', '')
        ->where('users.user_type', 'tenant')
        ->where('users.status', 1)
        ->where('users.is_deleted', 'no')
        ->pluck('users.device_token')->toArray();
        if($devices_tokens) {
            return $devices_tokens;
        } else {
            return [];
        }
    }

    public static function getTenantDeviceTokensRaisedMaintenanceRequestByuserid($maintenance_id,$building_id,$user_type) {
        $devices_tokens = DB::table('maintanance_request')->select('users.device_token','users.user_type','users.status','users.is_deleted');
        if($user_type == 'sp_worker')
        {
            $devices_tokens = $devices_tokens->leftJoin('users','maintanance_request.user_id','=','users.id');
        }
        else
        {
            $devices_tokens = $devices_tokens->leftJoin('users','maintanance_request.phone','=','users.phone');
        }

        //->where('users.building_ids',$building_id)
        //->whereRaw("find_in_set($building_id, users.building_ids)")
        $devices_tokens = $devices_tokens->where('maintanance_request.id',$maintenance_id)
        ->where('users.device_token','!=','')
        ->where('users.user_type',$user_type)
        ->where('users.status', 1)
        ->where('users.is_deleted', 'no')
        ->pluck('users.device_token')->toArray();
        if($devices_tokens) {
            return $devices_tokens;
        } else {
            return [];
        }
    }

    public static function getUserroletypeRaisedMaintenanceRequest($maintenance_id, $building_id)
    {
        $user_role_type = DB::table('maintanance_request')->select('users.id', 'users.user_type', 'selected_app_langugage')
        ->leftJoin('users', 'maintanance_request.phone', '=', 'users.phone')
        ->where('maintanance_request.id', $maintenance_id)
        ->where('users.user_type', '!=', '')
        ->first();

        return $user_role_type;
    }

    public static function getUserroletypeRaisedMaintenanceRequestByuserid($maintenance_id, $building_id)
    {
        $user_role_type = DB::table('maintanance_request')->select('users.id', 'users.user_type', 'selected_app_langugage')
        ->leftJoin('users', 'maintanance_request.user_id', '=', 'users.id')
        ->where('maintanance_request.id', $maintenance_id)
        ->whereIn('users.user_type', ['tenant','sp_worker'])
        ->first();

        if(empty($user_role_type)) {
            $user_role_type = DB::table('maintanance_request')->select('users.id', 'users.user_type', 'selected_app_langugage')
            ->leftJoin('users', 'maintanance_request.phone', '=', 'users.phone')
            ->where('maintanance_request.id', $maintenance_id)
            ->where('users.user_type', 'tenant')
            ->first();
        }

        return $user_role_type;
    }


    public static function getDeviceTokens($user_type, $user_id, $building_id)
    {

        $devices_tokens = User::select('device_token')
        ->where('id', '!=', $user_id)
        ->where('user_type', $user_type)
        ->where('building_ids', $building_id)
        ->where('status', 1)
        ->where('is_deleted', 'no')
        ->where('device_token', '!=', '')
        ->pluck('device_token')->toArray();
        if($devices_tokens) {
            return $devices_tokens;
        } else {
            return [];
        }
    }


    public static function getDeviceTokenstenant($user_type,$user_id,$building_id,$phone) {

        $devices_tokens = User::select('device_token')
        ->where('id','!=',$user_id)
        ->where('user_type',$user_type)
        ->where('building_ids',$building_id)
        ->where('status', 1)
        ->where('is_deleted', 'no')
        ->where('device_token','!=','')
        ->where('phone','!=',$phone)
        ->pluck('device_token')->toArray();
        if($devices_tokens)
        return $devices_tokens;
        else
        return [];
    }


    public static function getTenantids($user_type,$user_id,$building_id,$phone) {

        $user_data = User::select('id')
        ->where('id','!=',$user_id)
        ->where('user_type',$user_type)
        ->where('building_ids',$building_id)
        ->where('status', 1)
        ->where('is_deleted', 'no')
        ->where('phone','!=',$phone)
        //->where('device_token','!=','')
        ->pluck('id')->toArray();
        if($user_data)
        return $user_data;
        else
        return [];
    }


    public static function getAllTenantsDeviceTokensByBuilding($building_id, $maintanance_request_id = NULL) {
        if($maintanance_request_id)
        {
            $user_ids = DB::table('maintanance_request')->select('users.device_token', 'users.id')
            ->leftJoin('users', 'maintanance_request.phone', '=', 'users.phone')
            //->where('users.building_ids',$building_id)
            //->whereRaw("find_in_set($building_id, users.building_ids)")
            ->where('maintanance_request.id', $maintanance_request_id)
            ->where('users.device_token', '!=', '')
            ->pluck('users.id')->toArray();
        }
        //dd($user_ids);
        $devices_tokens = DB::table('users')->select('device_token')
        ->where('user_type', 'tenant')
        ->where('building_ids', $building_id);
        if($maintanance_request_id && !empty($user_ids)) {
            $devices_tokens = $devices_tokens->whereNotIn('id', $user_ids);
        }
        $devices_tokens = $devices_tokens->where('device_token', '!=', '')
        ->pluck('device_token')->toArray();
        if($devices_tokens) {
            return $devices_tokens;
        } else {
            return [];
        }
    }

    public static function getDeviceBuildingManagersTokens($user_type,$building_id,$phone) {
        $user_id = auth()->user()->id;
        $devices_tokens = DB::table('users')->select('device_token')
        ->where('id','!=',$user_id)
        ->whereIn('user_type',$user_type)
        ->where('device_token','!=','')
        ->where('phone','!=',$phone)
        //->whereIn('building_ids',explode(',',$building_id))
        ->whereRaw("find_in_set($building_id, building_ids)")
        ->groupBy('device_token')
        ->pluck('device_token')->toArray();
        if($devices_tokens) {
            return $devices_tokens;
        } else {
            return [];
        }
    }


    public static function getBuildingManagersIds($user_type,$building_id,$phone) {
        $user_id = auth()->user()->id;
        $user_data = DB::table('users')->select('id')
        ->where('id','!=',$user_id)
        ->whereIn('user_type',$user_type)
        ->where('phone','!=',$phone)
        //->where('device_token','!=','')
        //->whereIn('building_ids',explode(',',$building_id))
        ->whereRaw("find_in_set($building_id, building_ids)")
        ->groupBy('id')
        ->pluck('id')->toArray();
        if($user_data)
        return $user_data;
        else
        return [];
    }


    public static function getPostChatCreatedDeviceTokens($chat_id,$building_id) {
        $user_id = auth()->user()->id;
        $phone = auth()->user()->phone;
        $devices_tokens = DB::table('users')->select('device_token')

        ->leftJoin('tenant_chatroom','tenant_chatroom.tenant_id','=','users.id')
        ->where('users.id','!=',$user_id)
        ->where('phone','!=',$phone)
        ->where('users.device_token','!=','')
        ->where('tenant_chatroom.id',$chat_id)
        ->where('tenant_chatroom.building_id',$building_id)
        ->pluck('users.device_token')->toArray();
        if($devices_tokens) {
            return $devices_tokens;
        } else {
            return [];
        }
    }


    public static function getPostChatCreatedIds($chat_id,$building_id) {
        $user_id = auth()->user()->id;
        $phone = auth()->user()->phone;
        $user_data = DB::table('users')->select('users.id')
        ->leftJoin('tenant_chatroom','tenant_chatroom.tenant_id','=','users.id')
        ->where('users.id','!=',$user_id)
        ->where('phone','!=',$phone)
        //->where('users.device_token','!=','')
        ->where('tenant_chatroom.id',$chat_id)
        ->where('tenant_chatroom.building_id',$building_id)
        ->pluck('users.id')->toArray();
        if($user_data)
        return $user_data;
        else
        return [];
    }



/* Tenant App */
    public static function send_notification_FCM($device_tokens, $message, $building_id, $notification_type, $section_id, $created_by)
    {
        Log::info("Function API HELPER Start:send_notification_FCM ");
        $firebaseservice =new FirebaseService();
        $user_ids = ApiHelper::getUserIdsByDeviceTokens($device_tokens);
        if(!empty($user_ids)) {
            $notification_sub_type = '';
            if($notification_type == 'maintenance_request_accepted') {
                $notification_sub_type = 'maintenance_request_accepted';
            }
            $user_id_list = implode(',', $user_ids);
            $insert_data = [
                'user_id' => $user_id_list,
                'building_ids' => $building_id,
                'message' => $message['title'],
                'message_ar' => $message['title'],
                'message_desc' => $message['body'],
                'message_desc_ar' => $message['body'],
                'section_type' => 'tenant_app',
                'section_id' => $section_id,
                'notification_sub_type' => $notification_sub_type,
                'notification_type' => $notification_type,
                'created_by' => $created_by,
                'created_at' => date('Y-m-d H:i:s')
            ];
            $notification_insert_data[]  =  $insert_data;
            Notification::insert($notification_insert_data);
        }
        if($notification_type == 'new_work_order' || $notification_type == 'work_order_closed') {
            $user_ids_data = ApiHelper::getUserIdswithtokenByDeviceTokens($device_tokens);
            if(!empty($user_ids_data)) {
                /* PARAM JOB */
                    $user_ids_device=$user_ids_data;
                    $building_id=$building_id;
                    $deviceTokens=null;
                    $title=$message['title'];
                    $body= $message['body'];
                    $badge ='';
                    $sound='';
                    $contentAvailable=true;
                    SendFirebaseNotificationJob::dispatch($building_id,$user_ids_device,$deviceTokens, $title, $body, $badge, $sound, $contentAvailable);

            } else {

                /* Service paramtres */
                $user_ids_device=null;
                $building_id=null;
                $deviceTokens=$device_tokens;
                $title=$message['title'];
                $body= $message['body'];
                $badge ='';
                $sound='';
                $contentAvailable=null;
                $firebaseservice->sendNotificationToMultipleDevices($deviceTokens, $title, $body, $badge, $sound, $contentAvailable);
            }
        } else {
            /* Service paramtres */
            $user_ids_device=null;
            $building_id=null;
            $deviceTokens=$device_tokens;
            $title=$message['title'];
            $body= $message['body'];
            $badge ='';
            $sound='';
            $contentAvailable=null;

           $firebaseservice->sendNotificationToMultipleDevices($deviceTokens, $title, $body, $badge, $sound, $contentAvailable);


        }

        Log::info("Function API HELPER End:send_notification_FCM ");

    }


/* Tenant App (Use Job) */
    public static function send_notification_FCM_tenant($device_tokens, $message, $building_id, $notification_type, $section_id, $created_by, $tenant_id)
    {
        Log::info("Function API HELPER Start:send_notification_FCM_tenant");
        $Log_Verif = [
            'registration_ids' => $device_tokens,
            'messgae' => $message,
            'maintenanceRequest_building_id' =>  $building_id,
            'notification_type' => $notification_type,
            'maintananceRequestId' => $section_id,
            'AuthuserId' =>  $created_by,
            'tenant_id' => $tenant_id
        ];
        Log::info("After CAll Function From ApiHelper:",$Log_Verif);
        $user_ids = ApiHelper::getUserIdsByDeviceTokens($device_tokens);

        $notification_sub_type = '';
        if($notification_type == 'maintenance_request_accepted') {
            $notification_sub_type = 'maintenance_request_accepted';
        }
        $insert_data = [
            'user_id' => $tenant_id,
            'building_ids' => $building_id,
            'message' => $message['title'],
            'message_ar' => $message['title'],
            'message_desc' => $message['body'],
            'message_desc_ar' => $message['body'],
            'section_type' => 'tenant_app',
            'section_id' => $section_id,
            'notification_sub_type' => $notification_sub_type,
            'notification_type' => $notification_type,
            'created_by' => $created_by,
            'created_at' => date('Y-m-d H:i:s')
        ];
        $notification_insert_data[]  =  $insert_data;
        Notification::insert($notification_insert_data);
        if(!empty($user_ids))
        {
                $user_ids_data = ApiHelper::getUserIdswithtokenByDeviceTokenswithId($device_tokens,explode(',',$tenant_id));
                if(!empty($user_ids_data)) {
                    /* PARAM JOB */
                    $user_ids_device=$user_ids_data;
                    $building_id=$building_id;
                    $deviceTokens=null;
                    $title=$message['title'];
                    $body= $message['body'];
                    $badge ='';
                    $sound='';
                    $contentAvailable=true;
                    Log::info('user_ids_data',  ['user_ids_data' => $user_ids_data]);
                    SendFirebaseNotificationJob::dispatch($building_id,$user_ids_device,$deviceTokens, $title, $body, $badge, $sound, $contentAvailable);

                }

        }

        Log::info("Function API HELPER End:send_notification_FCM_tenant");

    }




/* Worker App */
    public static function send_maintenance_request_notification_worker_FCM($worker_id, $device_tokens, $message, $building_id, $notification_type, $section_id, $created_by)
    {
        // Bluck insert
        Log::info("Function API HELPER:send_maintenance_request_notification_worker_FCM ");
        $user_ids = ApiHelper::getUserIdsByDeviceTokens($device_tokens);
        if(count($device_tokens) > 0) {
            $user_ids_device=null;
            $building_id=null;
            $deviceTokens=$device_tokens;
            $title=$message['title'];
            $body= $message['body'];
            $badge =self::fetchUnreadNotificationCount($worker_id)+1;
            $sound='default';
            $contentAvailable=true;
            $firebase= new FirebaseService();
                $firebase->sendNotificationToMultipleDevices($deviceTokens, $title, $body, $badge, $sound, $contentAvailable);
        }
        $notification_sub_type = '';
        if($notification_type == 'maintenance_request_accepted') {
            $notification_sub_type = 'maintenance_request_accepted';
        }

        $insert_data = [
            'user_id' => $worker_id,
            'building_ids' => $building_id,
            'message' => $message['title'],
            'message_ar' => $message['title'],
            'message_desc' => $message['body'],
            'message_desc_ar' => $message['body'],
            'section_type' => 'worker_app',
            'section_id' => $section_id,
            'notification_sub_type' => $notification_sub_type,
            'notification_type' => $notification_type,
            'created_by' => $created_by,
            'created_at' => date('Y-m-d H:i:s')
        ];

        DB::table('notifications')->insert($insert_data);
        // }
        if(isset($response)) {
            return $response;
        }

    }

    /* Worker App */
    public static function send_created_rm_notification_SP($sp_id, $device_tokens, $message, $building_id=null, $notification_type, $section_id, $created_by)
    {
        // Bluck insert
        Log::info("Function API HELPER:send_created_rm_notification_SP ");
        if (!empty($device_tokens) && (is_array($device_tokens) || $device_tokens instanceof Countable) && count($device_tokens) > 0) {
            $title=$message['title'];
            $body= $message['body'];
            $badge =self::fetchUnreadNotificationCount($sp_id)+1;
            $sound='default';
            $contentAvailable=true;
            $deviceTokensArray = is_array($device_tokens) ? $device_tokens : [$device_tokens];
            $firebase= new FirebaseService();
            Log::info('Invalid Device Tokens List', [
                'error' => 'Device tokens should be an array.',
                'deviceTokens' => $deviceTokensArray
            ]);
               $firebase->sendNotificationToMultipleDevices($deviceTokensArray, $title, $body, $badge, $sound, $contentAvailable);
        }

        $insert_data = [
            'user_id' => $sp_id,
            'building_ids' => $building_id,
            'message' => $message['title'],
            'message_ar' => $message['title_ar'],
            'message_desc' => $message['body'],
            'message_desc_ar' => $message['body'],
            'section_type' => 'worker_app',
            'section_id' => $section_id,
            'section_type' => 'user',
            'notification_sub_type' => $notification_type,
            'is_timeline'=> 'no',
            'notification_type' => $notification_type,
            'created_by' => $created_by,
            'created_at' => date('Y-m-d H:i:s')
        ];

        DB::table('notifications')->insert($insert_data);
        // }
        if(isset($response)) {
            return $response;
        }

    }
/* Worker App */
    public static function send_notification_worker_FCM($worker_id, $device_tokens, $message, $building_id=null, $notification_type, $section_id)
    {
        Log::info("Function API HELPER:send_notification_worker_FCM ");
        try
        {
            if(count($device_tokens) > 0) {
                $device_tokens = array_unique($device_tokens);
                 $user_ids_device=null;
                 $deviceTokens=$device_tokens;
                 $title=$message['title'];
                 $body= $message['body'];
                 $badge =self::fetchUnreadNotificationCount($worker_id)+1;
                 $sound='default';
                 $contentAvailable=true;
                 $firebase= new FirebaseService();
                    $firebase->sendNotificationToMultipleDevices($deviceTokens, $title, $body, $badge, $sound, $contentAvailable);
            }
        } catch (\Exception $exception) {
            Log::error("send_notification_worker_FCM|" . $exception->getMessage());
        }


            if($notification_type == 'new_availability_change_request' || $notification_type == 'leave_request_expire' || $notification_type == 'leave_request_end' || $notification_type == 'leave_request_start')
            {
                $created_by = $worker_id;
            }
            else
            {
                $created_by = Auth::user()->id;
            }

            $insert_data = [
                //'user_id' => $user_id_list,
                'user_id' => $worker_id,
                'building_ids' => $building_id,
                'message' => $message['title'],
                'message_ar' => $message['title'],
                'section_type' => 'worker_app',
                'additional_param' => $section_id,
                'section_id' => $section_id,
                'notification_type' => $notification_type,
                'notification_sub_type' => $notification_type,
                'created_by' => $created_by,
                'created_at' => date('Y-m-d H:i:s')
            ];

        $notification_insert_data[]  =  $insert_data;
        Notification::insert($notification_insert_data);
        if(isset($response)) {
           // log::info($notification_insert_data);
            return $response;
        }

    }
/* Worker App */
    public static function send_custom_notification_worker_FCM($worker_id, $device_tokens, $message, $building_id, $notification_type, $section_id, $createdBy=null, $storeDbNotification = false, $dbNotificationData=null)
    {
        Log::info("Function API HELPER:send_custom_notification_worker_FCM ");
        // payload data, it will vary according to requirement
        if (count($device_tokens) > 0) {
            $device_tokens = array_unique($device_tokens);
          /* Service paramtres */
          $user_ids_device=null;
          $building_id=null;
          $deviceTokens=$device_tokens;
          $title=$message['title'];
          $body= $message['body'];
          $badge =self::fetchUnreadNotificationCount($worker_id)+1;
          $sound='default';
          $contentAvailable=true;
          $firebase= new FirebaseService();
                $firebase->sendNotificationToMultipleDevices($deviceTokens, $title, $body, $badge, $sound, $contentAvailable);

        }

        if ($storeDbNotification) {

            Notification::insert($dbNotificationData);
        }
        if (isset($response)) {
            return $response;
        }

    }


    //Get Unread Notification Count
    public static function fetchUnreadNotificationCount($worker_id)
    {
        // Build notifications query
        $countUnreadnotification = Notification::where('section_type', 'worker_app')
            ->whereRaw("find_in_set($worker_id, user_id)")
            ->whereNull('is_read')
            ->count();

        return $countUnreadnotification;
    }

    public static function getUserIdsByDeviceTokens($device_tokens)
    {
        $all_users_ids = DB::table('users')
        ->select('users.id')
        ->whereIn('users.device_token', $device_tokens)
        ->pluck('users.id')->toArray();
        if(count($all_users_ids) > 0) {
            return $all_users_ids;
        } else {
            return [];
        }
    }


    public static function getUserIdswithtokenByDeviceTokens($device_tokens)
    {
        $all_users_ids = DB::table('users')
        ->select('users.id', 'users.device_token')
        ->whereIn('users.device_token', $device_tokens)
        ->get();
        if(count($all_users_ids) > 0) {
            return $all_users_ids;
        } else {
            return [];
        }
    }


    public static function getUserIdswithtokenByDeviceTokenswithId($device_tokens,$userids)
    {
        $all_users_ids = DB::table('users')
        ->select('users.id', 'users.device_token')
        ->whereIn('users.device_token', $device_tokens);
        if(count($userids) > 0)
        {
            $all_users_ids = $all_users_ids->whereIn('users.id', $userids);
        }
        $all_users_ids = $all_users_ids->groupBy('users.device_token')->get();
        if(count($all_users_ids) > 0) {
            return $all_users_ids;
        } else {
            return [];
        }
    }

    public static function getWorkOrderProgressSteps($id)
    {

        $user = auth()->user();
        $inpending_status = false;
        $inprogress_status = false;
        $complete_status = false;
        $scheduled_status = false;
        $wo = WorkOrders::where('id', $id)->first();
        $status = ApiHelper::getWorkOrderStatus($wo->workorder_journey, $id);

        $wtfs = DB::table('work_time_frame')
        ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
        ->where('user_id', $user->project_user_id)
        ->first();
        if(!isset($wtfs)) {
            $time = "00:00:00";
        } else {
            $time = $wtfs->start_time;
        }

        $job_started_at = '';
        $worker_started_at = '';
        $scheduled_date = '';

        if($status == 'Pending') {
            $inpending_status = true;
            $job_started_at = $wo->created_at;
            if($wo->work_order_type == "preventive") {
                $job_started_at = $wo->start_date.' '.$time;
            }
        } elseif($status == 'In Progress') {
            $inprogress_status = true;
            $inpending_status = true;
            $job_started_at = $wo->created_at;
            if($wo->work_order_type == "preventive") {
                $job_started_at = $wo->start_date.' '.$time;
            }
            $worker_started_at = $wo->job_started_at;
        } elseif($status == 'Completed') {
            $inpending_status = true;
            $inprogress_status = true;
            $complete_status = true;
            $job_started_at = $wo->created_at;
            if($wo->work_order_type == "preventive") {
                $job_started_at = $wo->start_date.' '.$time;
            }
            $worker_started_at = $wo->worker_started_at;
        }

        if(!empty($wo->contract_type == 'warranty' && $wo->workorder_journey != 'finished') && ($status == 'Pending' or $status == 'In Progress')) {
            $inpending_status = true;
            $job_started_at = $wo->created_at;
            if($wo->work_order_type == "preventive") {
                $job_started_at = $wo->start_date.' '.$time;
            }
            $inprogress_status = true;
            $worker_started_at = $wo->created_at;
        }

        if($wo->start_date > now()) {
            $scheduled_status = true;
            $inpending_status = true;
            $job_started_at = $wo->created_at;
            $scheduled_date = $wo->start_date.' '.$wo->schedule_start_time;
        }

        $arr = [
            'pending' => [
                'status' => $inpending_status,
                'date' => !empty($job_started_at) ? date('d M Y | h:i A', strtotime($job_started_at)) : ''
            ],
            'in_progress' => [
                'status' => $inprogress_status,
                'date' => !empty($worker_started_at) ? date('d M Y | h:i A', strtotime($worker_started_at)) : '',
            ],
            'completed' => [
                'status' => $complete_status,
                'date' => !empty($wo->job_completion_date) ? date('d M Y | h:i A', strtotime($wo->job_completion_date)) : ''
            ],
            'scheduled' => [
                'status' => $scheduled_status,
                'date' => !empty($scheduled_date) ? date('d M Y | h:i A', strtotime($scheduled_date)) : ''
            ],
        ];
        return $arr;
    }

    public static function getBuildingData($user_id, $user_type)
    {
        $building_data;
        if($user_type == 'tenant') {
            $user_details = DB::table('users')->where('id', $user_id)->first();
            $building_data = PropertyBuildings::select('property_buildings.building_name', 'property_buildings.id')
            ->where('property_buildings.id', $user_details->building_ids)->first();
        } elseif($user_type == 'building_manager' || $user_type == 'building_manager_employee') {
            $user_details = DB::table('users')->where('id', $user_id)->first();
            if(isset($user_details) && count(explode(',', $user_details->building_ids)) == 1) {
                $building_data = PropertyBuildings::select('property_buildings.building_name', 'property_buildings.id')
                ->where('property_buildings.id', $user_details->building_ids)->first();
            } else {
                $building_data = [
                    'building_name' => '',
                    'id' => ''
                ];
            }
        } else {
            $building_data = [
                'building_name' => '',
                'id' => ''
            ];
        }
        return $building_data;
    }



    public static function getApartmentData($user_id, $user_phone, $user_type)
    {
        // $userdata = User::with(['building' => function ($query) {
        //     $query->select('id','property_id','building_name','deleted_at')->whereNull('deleted_at');
        // },'projectDetails' => function ($query) {
        //     $query->select('*')->whereNull('deleted_at');
        // }])
        $userdata = User::with(['building' => function ($query) {
            $query->select('id', 'property_id', 'building_name', 'deleted_at')->whereNull('deleted_at');
        }])
        ->select('users.id as user_id', 'users.apartment', 'building_ids', 'project_id')
        ->where('users.phone', $user_phone)
        ->where('users.is_deleted', 'no')
        ->whereNull('users.deleted_at')
        ->get()
        ->toArray();
        if(isset($userdata) && !empty($userdata)) {
            foreach($userdata as $k => $v) {

                $userdata[$k]['user_data'] = User::where('id', '=', $v['user_id'])->first();
                if(isset($userdata[$k]['user_data']) && !empty($userdata[$k]['user_data']))
                {
                        $show_community = false;
                        $show_contract = false;
                        $show_share_post = false;
                        $project_data = ApiHelper::getProjectData($userdata[$k]['user_data']['project_id']);

                        // Check project settings for showing community, contract, and share post features
                        $show_community = $project_data->use_tenant_module == 1 && $project_data->community_status == 1;
                        $show_contract = $project_data->use_tenant_module == 1 && $project_data->contract_status == 1;
                        $show_share_post = $project_data->use_tenant_module == 1 && $project_data->share_post == 1;
                        $userdata[$k]['user_data']['show_community'] = $show_community;
                        $userdata[$k]['user_data']['show_contract'] = $show_contract;
                        $userdata[$k]['user_data']['show_share_post'] = $show_share_post;
                }


                $userdata[$k]['building'] = PropertyBuildings::select('id', 'property_id', 'building_name', 'deleted_at')->whereNull('deleted_at')->whereIn('id', explode(',', $v['building_ids']))->get()->toArray();
                foreach($userdata[$k]['building'] as $key => $val) {
                    $property_data = Property::where('id', $val['property_id'])->whereNull('properties.deleted_at')->first()->toarray();

                    if(isset($property_data) && !empty($property_data)) {
                        $userdata[$k]['building'][$key]['building_name'] = $property_data['property_type'] == 'complex' ? $property_data['complex_name'].' - '.$val['building_name'] : $val['building_name'];
                    }
                    $userdata[$k]['building'][$key]['total_tenant'] = Helper::getNoOfResidents($val['id']);
                }
            }

        }
        return $userdata;
    }


    public static function getMultipleProjectData($user_id, $user_phone, $user_type) {
        $userData = User::with(['projectDetails' => function ($query) {
                $query->whereNull('deleted_at')->with('projectSettings');
            }])
            ->select('users.project_id', 'users.created_at', 'users.unit_receival_later_clicked_at')
            ->where('users.phone', $user_phone)
            ->where('users.is_deleted', 'no')
            ->whereNull('users.deleted_at')
            ->groupBy('users.project_id')
            ->get()
            ->toArray();

        // Filter out elements where both 'project_id' and 'project_details' are null
        $filteredUserData = array_filter($userData, function ($data) {
            return !is_null($data['project_id']) || !is_null($data['project_details']);
        });

        if (!empty($filteredUserData)) {
            foreach ($filteredUserData as $k => $v) {
                if (is_array($v['project_details']) && $v['project_details'] !== null) {
                    $projectDetails = $v['project_details'];

                    $unitReceivalFormReceived = false;
                    // Check if 'project_settings' exists and is an array
                    if (isset($projectDetails['project_settings']) && is_array($projectDetails['project_settings'])) {
                        $projectSettings = $projectDetails['project_settings'];

                        if (isset($projectSettings['unit_receival_form_received']) && $projectSettings['unit_receival_form_received'] == 1) {
                            if ($projectSettings['tenant_form_option'] == 'new_tenants_only') {
                                if ($v['created_at'] >= $projectSettings['unit_receival_form_received_date']) {
                                    $unitReceivalFormReceived = true;
                                }
                            } else {
                                $unitReceivalFormReceived = true;
                            }

                            if (!is_null($v['unit_receival_later_clicked_at'])) {
                                $expirationPeriodDays = $projectSettings['form_expiration_period_days'];
                                $expirationDate = Carbon::parse($v['unit_receival_later_clicked_at'])->addDays($expirationPeriodDays);
                                if (now()->greaterThan($expirationDate)) {
                                    $unitReceivalFormReceived = false;
                                }
                            }
                        }
                    }

                    $groupedMaintenanceRequestsCount = MaintenanceRequest::where([
                        ['generated_from', '=', 'tenant'],
                        ['user_id', '=', $user_id],
                    ])
                    ->count();
                    if($groupedMaintenanceRequestsCount > 0) {
                        $unitReceivalFormReceived = false;
                    }

                    $filteredUserData[$k]['unitReceivalFormReceived'] = $unitReceivalFormReceived;

                    // Existing logic for project details
                    $filteredUserData[$k]['project_details']['project_image'] = ImagesUploadHelper::displayImage($v['project_details']['project_image'], 'uploads/project_images');
                    $filteredUserData[$k]['project_details']['show_community'] = $v['project_details']['use_tenant_module'] == 1 && $v['project_details']['community_status'] == 1;
                    $filteredUserData[$k]['project_details']['show_contract'] = $v['project_details']['use_tenant_module'] == 1 && $v['project_details']['contract_status'] == 1;
                    $filteredUserData[$k]['project_details']['show_share_post'] = $v['project_details']['use_tenant_module'] == 1 && $v['project_details']['share_post'] == 1;
                }
            }
        }

        return $filteredUserData;
    }

    public static function getProjectData($project_id)
    {
        $data = DB::table('projects_details')->where('id', $project_id)->first();
        return $data;
    }

    public static function getPostChatBuildingId($chat_id)
    {
        $data = DB::table('tenant_chatroom')->where('id', $chat_id)->first();
        if($data) {
            return $data->building_id;
        } else {
            return '';
        }
    }

    public static function getProjectSettings($project_id)
    {
        $data = DB::table('project_settings')->where('project_id', $project_id)->first();
        return $data;
    }

    public static function getProjectImageByPropertyID($property_id)
    {
        $data = DB::table('users')
                ->select('projects_details.*')

                ->leftJoin('properties', 'properties.user_id', 'users.id')
                ->leftJoin('projects_details', 'users.project_id', 'projects_details.id')
                ->where('properties.id', $property_id);
        /*
        $query = str_replace(array('?'), array('\'%s\''), $data->toSql());
        $query = vsprintf($query, $data->getBindings());
        dump($query);
        die;
        */

        $data = $data->first();
        return !empty($data->project_image) ? ImagesUploadHelper::displayImageMobileApp($data->project_image, 'uploads/project_images') : "";
    }


    public static function projectHasBuilding($project_id)
    {
        $project_id = $project_id;
        $proejct_data = Helper::getProjectDetails($project_id);
        if(!empty($proejct_data)) {
            $project_user_id = $proejct_data->project_user_id;
            //dd($project_user_id); //288
            $building_data = PropertyBuildings::select('building_name', 'id');

            $service_provider_id = auth()->user()->service_provider;
            //dd( $service_provider_id );
            $contracts = DB::table('contracts')
            ->select('contract_property_buildings.*', 'properties.user_id')
            ->join('contract_property_buildings', 'contract_property_buildings.contract_id', '=', 'contracts.id')
            ->leftJoin('service_providers_project_mapping', 'service_providers_project_mapping.service_provider_id', '=', 'contracts.service_provider_id')
            ->leftJoin('property_buildings as p1', 'p1.id', 'contract_property_buildings.property_building_id')
            ->leftJoin('properties', 'properties.id', '=', 'p1.property_id')
            ->where('service_providers_project_mapping.project_id', $project_id)
            ->where('contracts.service_provider_id', $service_provider_id)
            ->where('properties.user_id', $project_user_id)
            ;
            /*
            $query = str_replace(array('?'), array('\'%s\''), $contracts->toSql());
            $query = vsprintf($query, $contracts->getBindings());
            dump($query);
            die;
            */
            $contracts = $contracts->get()->pluck('property_building_id')->toArray();
            //dd($contracts);

            if(!empty($contracts)) {
                //$building_data = $building_data->leftJoin('properties','properties.id','=','property_buildings.property_id');
                //$building_data = $building_data->where('properties.user_id',auth()->user()->project_user_id);
                $building_data = $building_data->whereIn('property_buildings.id', $contracts);

                $data = $building_data->get();
                if($data) {
                    return true;
                } else {
                    return false;
                }

            } else {
                return false;
            }
        }
    }

    public static function sendSms($message, $to)
    {
        $url          = env('SMS_API_URL', null);
        $user_name    = env('SMS_USER_NAME', null);
        $password     = env('SMS_USER_PASSWORD', null);
        $senderID     = env("SMS_SENDER_ID", null);
        $message_type = env("MESSAGE_TYPE", null);
        $sbn_val      = "UserName=" . urlencode($user_name) . "&Password=" . urlencode($password) . "&MessageType=" . urlencode($message_type) . "&Recipients=" . urlencode($to) . "&SenderName=" . urlencode($senderID) . "&MessageText=" . urlencode($message);
        $ch           = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $sbn_val);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $server_output = curl_exec($ch);
        //dd($server_output);
        curl_close($ch);
    }

    public static function createMaintenanceWorkorder($data)
    {
        $user           = Auth::user();
        $asset_name     = 0;
        $asset_number   = 0;
        $last_inc       = null;
        $work_orderType = null;
        $wrk_typ        = null;
        $r_hs           = $data;
        $unique_id      = rand(1111111, 9999999);
        $r_hs['unit_id'] = RoomsTypeFloors::where([['building_id', $r_hs['building_id']],['floor',  $r_hs['floor']],['room',$r_hs['space_no'] ] ])->value('id');
        $asset_category = AssetCategory::find($r_hs['asset_category_id']);
        $r_hs['service_type'] = $asset_category->service_type;
        $r_hs['asset_category'] = $asset_category->id;
        if ($r_hs['service_type'] == "hard") { //If service_type is hard_service
            $work_orderType = "HS";
            $wrk_typ        = "hard";
        }
        if ($r_hs['service_type'] == "soft") { //If service_type is soft_service
            $work_orderType = "SS";
            $wrk_typ        = "soft";
        }
        /**Asset name not define */
        if (!empty($r_hs['asset_name_define'])) {
            $asset_name = $r_hs['asset_name_define'];
        }
        if (!empty($r_hs['asset_number_define'])) {
            $asset_number = $r_hs['asset_number_define'];
        }

        if ($r_hs['service_type'] == 'soft') { //If service_type is soft_service
            $asset_category_id = $r_hs['asset_category'];
        } else {
            $asset_category_id = $r_hs['asset_category'];
        }
        //$contact_arr = explode('-',$r_hs['contract']);
        $contact_id = $r_hs['contract'];
        $contract_types = $r_hs['contract_types'];
        $r_hs['contract'] = $contact_id;
        /**get the work day information */
        $wtfs = DB::table('work_time_frame')
            ->select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'official_vacation_days')
            ->where('user_id', $user->project_user_id)
            ->first();
        if(!isset($wtfs)) { //If there work time frame added
            $wtfs = new stdClass();
            $wtfs->start_time = "00:00:00";
            $wtfs->end_time = "00:00:00";
            $wtfs->monday = 0;
            $wtfs->tuesday = 0;
            $wtfs->wednesday = 0;
            $wtfs->thursday = 0;
            $wtfs->friday = 0;
            $wtfs->saturday = 0;
            $wtfs->sunday = 0;
            $wtfs->official_vacation_days = '';
        }
        $get_contract_data = DB::table('contracts')
            ->select('id', 'contract_number', 'contract_types', 'service_provider_id')
            ->where('status', 1)
            ->where('is_deleted', 'no')
            ->where('id', $r_hs['contract'])
            ->first();
        $service_provider_id = $get_contract_data->service_provider_id;
        $get_sp_admin = DB::table('users')
            ->select('id', 'name')
            ->where('status', 1)
            ->where('is_deleted', 'no')
            ->where('service_provider', $service_provider_id)
            ->where('user_type', 'sp_admin')
            ->first();
        $contract_id = $r_hs['contract'];
        $property_id = $r_hs['property_id'];
        $get_supervisors = DB::table('users')
            ->select('users.id', 'users.name')
            ->join('user_assets_mapping', 'user_assets_mapping.user_id', '=', 'users.id')
            ->where('users.status', 1)
            ->where('users.is_deleted', 'no')
            ->whereRaw("find_in_set($property_id, users.building_ids)")
            ->whereIn('user_assets_mapping.contract_id', explode(',', $contract_id))
            ->whereIn('user_assets_mapping.asset_id', explode(',', $asset_category_id))
            ->where('user_assets_mapping.user_type', 'supervisor')
            //->whereRaw("find_in_set($asset_category_id, asset_categories)")
            ->where('users.user_type', 'supervisor')
            ->groupBy('users.id')
            ->get();
        $get_supervisors = json_decode(json_encode($get_supervisors), true);
        $sup_ids         = [];
        if (!empty($get_supervisors)) { //If has supervisors
            foreach ($get_supervisors as $sup) {
                $sup_ids[] = $sup['id'];
            }
        }
        $contract_asset_categories = DB::table('contract_asset_categories')
            ->select('id', 'priority_id')
            ->where('asset_category_id', $asset_category_id)
            ->where('contract_number', $get_contract_data->contract_number)
            ->first();
        $contract_priorities = DB::table('contract_priorities')
            ->select('id', 'service_window', 'service_window_type')
            ->where('priority_id', $contract_asset_categories->priority_id)
            ->where('contract_number', $get_contract_data->contract_number)
            ->orderBy('id', 'desc')
            ->first();
        $start_date = date('Y-m-d');
        $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
        /**end work day information */
        if (strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) {
            $start_date = date('Y-m-d', strtotime('+1 day', strtotime($start_date))) . ' ' . $wtfs->start_time;
        }
        $time           = strtotime($start_date);
        $date           = date('Y-m-d H:i:s', $time);
        // Work Time Frame
        $dayofweek = date('w', strtotime($date));

        $check_flag_wo = 0;
        while($check_flag_wo == 0) {
            switch ($dayofweek) {
                case 1: //mon
                    if ($wtfs->monday == 0) {
                        $date        = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                        $notify_user = true;
                        $dayofweek = date('w', strtotime($date));
                        $check_flag_wo = 0;
                    } else {
                        if(trim($wtfs->official_vacation_days) != "") {
                            $official_vacation_days = array_map('trim', explode(',', $wtfs->official_vacation_days));
                            $search_vacation_date =  date('d-m-Y', strtotime($date));
                            $dayofweek = date('w', strtotime($date));

                            if(in_array($search_vacation_date, $official_vacation_days)) {
                                $date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                                $search_vacation_date =  date('d-m-Y', strtotime($date));
                                $check_flag_wo = 0;
                                $dayofweek = date('w', strtotime($date));
                            } else {
                                $check_flag_wo = 1;
                            }
                        } else {
                            $check_flag_wo = 1;
                        }
                    }
                    break;
                case 2: //tue
                    if ($wtfs->tuesday == 0) {
                        $date        = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                        $notify_user = true;
                        $check_flag_wo = 0;
                        $dayofweek = date('w', strtotime($date));
                    } else {
                        if(trim($wtfs->official_vacation_days) != "") {
                            $official_vacation_days = array_map('trim', explode(',', $wtfs->official_vacation_days));
                            $search_vacation_date =  date('d-m-Y', strtotime($date));
                            $dayofweek = date('w', strtotime($date));

                            if(in_array($search_vacation_date, $official_vacation_days)) {
                                $date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                                $search_vacation_date =  date('d-m-Y', strtotime($date));
                                $check_flag_wo = 0;
                                $dayofweek = date('w', strtotime($date));
                            } else {
                                $check_flag_wo = 1;
                            }
                        } else {
                            $check_flag_wo = 1;
                        }
                    }
                    break;
                case 3: //wed
                    if ($wtfs->wednesday == 0) {
                        $date        = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                        $notify_user = true;
                        $check_flag_wo = 0;
                        $dayofweek = date('w', strtotime($date));
                    } else {
                        if(trim($wtfs->official_vacation_days) != "") {
                            $official_vacation_days = array_map('trim', explode(',', $wtfs->official_vacation_days));
                            $search_vacation_date =  date('d-m-Y', strtotime($date));
                            $dayofweek = date('w', strtotime($date));

                            if(in_array($search_vacation_date, $official_vacation_days)) {
                                $date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                                $search_vacation_date =  date('d-m-Y', strtotime($date));
                                $check_flag_wo = 0;
                                $dayofweek = date('w', strtotime($date));
                            } else {
                                $check_flag_wo = 1;
                            }
                        } else {
                            $check_flag_wo = 1;
                        }
                    }
                    break;
                case 4: //thu
                    if ($wtfs->thursday == 0) {

                        $date        = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                        $notify_user = true;
                        $check_flag_wo = 0;
                        $dayofweek = date('w', strtotime($date));
                    } else {
                        if(trim($wtfs->official_vacation_days) != "") {
                            $official_vacation_days = array_map('trim', explode(',', $wtfs->official_vacation_days));
                            $search_vacation_date =  date('d-m-Y', strtotime($date));
                            $dayofweek = date('w', strtotime($date));

                            if(in_array($search_vacation_date, $official_vacation_days)) {
                                $date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                                $search_vacation_date =  date('d-m-Y', strtotime($date));
                                $check_flag_wo = 0;
                                $dayofweek = date('w', strtotime($date));
                            } else {
                                $check_flag_wo = 1;
                            }
                        } else {
                            $check_flag_wo = 1;
                        }
                    }
                    break;
                case 5: //fri
                    if ($wtfs->friday == 0) {
                        $date        = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                        $notify_user = true;
                        $check_flag_wo = 0;
                        $dayofweek = date('w', strtotime($date));
                    } else {
                        if(trim($wtfs->official_vacation_days) != "") {
                            $official_vacation_days = array_map('trim', explode(',', $wtfs->official_vacation_days));
                            $search_vacation_date =  date('d-m-Y', strtotime($date));
                            $dayofweek = date('w', strtotime($date));

                            if(in_array($search_vacation_date, $official_vacation_days)) {
                                $date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                                $search_vacation_date =  date('d-m-Y', strtotime($date));
                                $dayofweek = date('w', strtotime($date));
                                $check_flag_wo = 0;
                            } else {
                                $check_flag_wo = 1;
                            }
                        } else {
                            $check_flag_wo = 1;
                        }
                    }
                    break;
                case 6: //sat
                    if ($wtfs->saturday == 0) {
                        $date        = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                        $notify_user = true;
                        $check_flag_wo = 0;
                        $dayofweek = date('w', strtotime($date));
                    } else {
                        if(trim($wtfs->official_vacation_days) != "") {
                            $official_vacation_days = array_map('trim', explode(',', $wtfs->official_vacation_days));
                            $search_vacation_date =  date('d-m-Y', strtotime($date));
                            $dayofweek = date('w', strtotime($date));

                            if(in_array($search_vacation_date, $official_vacation_days)) {
                                $date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                                $search_vacation_date =  date('d-m-Y', strtotime($date));
                                $dayofweek = date('w', strtotime($date));
                                $check_flag_wo = 0;
                            } else {
                                $check_flag_wo = 1;
                            }
                        } else {
                            $check_flag_wo = 1;
                        }
                    }
                    break;
                case 0: //sun
                    if ($wtfs->sunday == 0) {
                        $date        = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                        $notify_user = true;
                        $check_flag_wo = 0;
                        $dayofweek = date('w', strtotime($date));
                    } else {
                        if(trim($wtfs->official_vacation_days) != "") {
                            $official_vacation_days = array_map('trim', explode(',', $wtfs->official_vacation_days));
                            $search_vacation_date =  date('d-m-Y', strtotime($date));
                            $dayofweek = date('w', strtotime($date));

                            if(in_array($search_vacation_date, $official_vacation_days)) {
                                $date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($date)));
                                $search_vacation_date =  date('d-m-Y', strtotime($date));
                                $dayofweek = date('w', strtotime($date));
                                $check_flag_wo = 0;
                            } else {
                                $check_flag_wo = 1;
                            }
                        } else {
                            $check_flag_wo = 1;
                        }
                    }
                    break;
            }
        }

        $target_date = date('Y-m-d H:i:s', strtotime('+' . $contract_priorities->service_window . ' '.$contract_priorities->service_window_type, strtotime($date)));
        $dayofweek   = date('w', strtotime($target_date));
        $dayname     = lcfirst(date('l', strtotime($target_date)));
        for ($i = 0; $i <= 6; $i++) {
            if ($i == $dayofweek && $wtfs->$dayname == 0) {
                $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                $dayofweek   = date('w', strtotime($target_date));
                $dayname     = lcfirst(date('l', strtotime($target_date)));

                switch ($dayofweek) {
                    case 1: //mon
                        if ($wtfs->monday == 0) {
                            $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                            $dayofweek   = date('w', strtotime($target_date));
                            $dayname     = lcfirst(date('l', strtotime($target_date)));
                        }
                        break;
                    case 2: //tue
                        if ($wtfs->tuesday == 0) {
                            $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                            $dayofweek   = date('w', strtotime($target_date));
                            $dayname     = lcfirst(date('l', strtotime($target_date)));
                        }
                        break;
                    case 3: //wed
                        if ($wtfs->wednesday == 0) {
                            $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                            $dayofweek   = date('w', strtotime($target_date));
                            $dayname     = lcfirst(date('l', strtotime($target_date)));
                        }
                        break;
                    case 4: //thu
                        if ($wtfs->thursday == 0) {
                            $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                            $dayofweek   = date('w', strtotime($target_date));
                            $dayname     = lcfirst(date('l', strtotime($target_date)));
                        }
                        break;
                    case 5: //fri
                        if ($wtfs->friday == 0) {
                            $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                            $dayofweek   = date('w', strtotime($target_date));
                            $dayname     = lcfirst(date('l', strtotime($target_date)));
                        }
                        break;
                    case 6: //sat
                        if ($wtfs->saturday == 0) {
                            $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                            $dayofweek   = date('w', strtotime($target_date));
                            $dayname     = lcfirst(date('l', strtotime($target_date)));
                        }
                        break;
                    case 0: //sun
                        if ($wtfs->sunday == 0) {
                            $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                            $dayofweek   = date('w', strtotime($target_date));
                            $dayname     = lcfirst(date('l', strtotime($target_date)));
                        }
                        break;
                }
            }
        }

        $rms = new WorkOrders();
        $rms->frequency_id = '';
        if (isset($r_hs['choose_asset_checklist'])) {
            $get_frequency_data = DB::table('checklists')
                ->select('list_id', 'frequency_id')
                ->where('id', $r_hs['choose_asset_checklist'])
                ->first();
            $rms->frequency_id = $get_frequency_data->frequency_id;
        }

        $get_workorder_last_id = DB::table('work_orders')->select('id')->orderBy('id', 'desc')->first();
        if ($get_workorder_last_id) {
            $last_inc = $get_workorder_last_id->id + 1;
        } else {
            $last_inc = 1;
        }
        $property_buildings = DB::table('property_buildings')
            ->select('building_name')
            ->where('id', $r_hs['property_id'])
            ->first();
        $work_order_number = str_pad($last_inc, 6, '0', STR_PAD_LEFT);
        $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number);
        $work_order_number = str_pad($work_order_number, 6, '0', STR_PAD_LEFT);
        if($contract_types == "Regular") {
            $contract_type = 'regular';
        } else {
            $contract_type = 'warranty';
            $rms->workorder_journey = 'job_execution';
        }
        $rms->project_user_id = $user->project_user_id;
        $rms->created_by = $user->id;
        $rms->service_provider_id = isset($get_sp_admin->id) ? $get_sp_admin->id : 0;
        $rms->supervisor_id = implode(',', $sup_ids);
        $rms->work_order_id = 'RM-'.$work_order_number;
        $rms->unique_id = $unique_id;
        $rms->work_order_type = "reactive";
        $rms->service_type = $wrk_typ;
        $rms->property_id = $r_hs['property_id'];
        $rms->unit_id = isset($r_hs['unit_id']) ? $r_hs['unit_id'] : 0;
        $rms->floor = $r_hs['floor'];
        $rms->room = $r_hs['space_no'];
        $rms->asset_category_id = !empty($asset_category_id) ? $asset_category_id : 0;

        $rms->asset_name_id = !empty($asset_name) ? $asset_name : 0;
        $rms->asset_number_id = !empty($asset_number) ? $asset_number : 0;

        $rms->contract_id = $r_hs['contract'];

        $rms->contract_type = $contract_type;

        $rms->description = $r_hs['description'];
        $rms->checklist_id = !empty($r_hs['choose_asset_checklist']) ? $r_hs['choose_asset_checklist'] : 0;
        $rms->service_category_id = !empty($r_hs['service_category']) ? $r_hs['service_category'] : 0;
        $rms->start_date          = !empty($date) ? $date : '0000-00-00';
        $rms->end_date            = !empty($date) ? $date : '0000-00-00';
        $rms->target_date         = !empty($target_date) ? $target_date : '0000-00-00';
        $rms->status              = 1;
        $rms->is_deleted          = "no";
        $rms->created_at          = date('Y-m-d H:i:s');
        $rms->wtf_start_time      =   isset($wtfs->start_time) ? $wtfs->start_time : "00:00:00";
        $rms->wtf_end_time         =   isset($wtfs->end_time) ? $wtfs->end_time : "00:00:00";
        $rms->wo_images = $r_hs['image1'] == "1" ? "" : $r_hs['image1'];
        $rms->maintanance_request_id = $r_hs['maintanance_request_id'];
        if ($rms->save()) {
            if(!empty($rms->maintanance_request_id)) {
                $maintanance_data = [
                    'status' => 'In Progress'
                ];
                \Helper::updateMaintenanceRequest($maintanance_data, $rms->maintanance_request_id);

                $maintanance_id = $rms->maintanance_request_id;

                $details = MaintenanceRequest::where('id', $rms->maintanance_request_id)->first();
                $total_emails[]               = $details->email;
            }

            $message = 'Work Order Submitted by <strong>'.Auth::user()->name.'</strong>';
            $message_ar = 'تم تسليم أمر العمل من قبل <strong>'.Auth::user()->name.'</strong>';
            $start_date = $date;

            $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;

            /**end work day information */
            if (strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) {
                $created_at = date('Y-m-d', strtotime('+1 day', strtotime($start_date))) . ' ' . $wtfs->start_time;
            } elseif (strtotime(date('Y-m-d', strtotime($start_date))) == strtotime(date('Y-m-d'))) {
                $created_at = date('Y-m-d H:i:s');
            } else {
                //$created_at = date('Y-m-d H:i:s');
                $created_at = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->start_time;
            }
            DB::table('notifications')->insert(array('user_id' => $user->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $rms->property_id,'notification_sub_type' => 'new_work_order_created','section_id' => $rms->id, 'created_at' => $created_at));
            // Save the work order in email cron

            //echo '<pre>'; print_r($priority); die;

            $data = array(
                'wo_id' => $rms->id,
                'work_order_type' => 'reactive',
                'status' => 'WO-Created',
                'created_at' => date('Y-m-d H:i:s'),
            );
            DB::table('work_orders_email_jobs')->insert($data);
            if(!empty($rms->maintanance_request_id)) {
                //dd('Y');
                $chat_created_token = ApiHelper::getTenantDeviceTokensRaisedMaintenanceRequest($rms->maintanance_request_id,$rms->property_id);
                $chat_created_token_user_id = ApiHelper::getTenantIdRaisedMaintenanceRequestByuserid($rms->maintanance_request_id,$rms->property_id,'tenant');

                $all_tenants = ApiHelper::getAllTenantsDeviceTokensByBuilding($rms->property_id);
                $all_users_token = array_merge($all_tenants, $chat_created_token);
                $registration_ids = array_unique($all_users_token);//get tenants device token

                $all_tenants_id = ApiHelper::getAllTenantsIdByBuilding($rms->property_id);
                $all_tenants_id = array_unique(array_merge($all_tenants_id,$chat_created_token_user_id));

                $messgae = array(
                    "title" => 'تمت الموافقة على طلب الصيانة !',
                    "body" => $rms->description
                );
                //if(count($registration_ids)>0) {
                    $m_registration_ids = $registration_ids;
                    $notification_type = 'maintenance_request_accepted';
                    $res = ApiHelper::send_maintenance_notification_FCM($all_tenants_id, $registration_ids, $messgae, $rms->property_id,$notification_type,$rms->id,$user->id);
                //}
                $chat_created_token = ApiHelper::getAllTenantsDeviceTokensByBuilding($r_hs['property_id'], $rms->maintanance_request_id);
                $registration_ids = array_unique($chat_created_token);// // get tenants device token

                $all_tenants_id = ApiHelper::getAllTenantsIdByBuilding($r_hs['property_id'], $rms->maintanance_request_id);
                $all_tenants_id = array_unique($all_tenants_id);

                $messgae = array(
                    "title" => 'تم إنشاء طلب صيانة جديد في مجتمعك !',
                    "body" => $rms->description
                );
                //if(count($registration_ids)>0) {
                    $notification_type = 'new_work_order';
                    $res = ApiHelper::send_maintenance_notification_FCM($all_tenants_id, $registration_ids, $messgae, $rms->property_id,$notification_type,$rms->id,$user->id);
                //}
            } else {
                $chat_created_token = ApiHelper::getAllTenantsDeviceTokensByBuilding($r_hs['property_id']);
                $registration_ids = array_unique($chat_created_token);//get tenants device token

                $all_tenants_id = ApiHelper::getAllTenantsIdByBuilding($r_hs['property_id']);
                $all_tenants_id = array_unique($all_tenants_id);

                $messgae = array(
                    "title" => 'تم إنشاء طلب صيانة جديد في مجتمعك !',
                    "body" => $rms->description
                );
                //if(count($registration_ids)>0) {
                    $notification_type = 'new_work_order';
                    $res = ApiHelper::send_maintenance_notification_FCM($all_tenants_id, $registration_ids, $messgae, $rms->property_id,$notification_type,$rms->id,$user->id);
                //}
            }
            // Send email notification
            $user = Auth::user();
            if ($user->user_type == 'building_manager_employee') {
                $user_ids[]               = $user->id;
                $user_ids[]               = isset($get_sp_admin->id) ? $get_sp_admin->id : '';
                $user_ids[]               = \Helper::getTheBuildingManager($user->id);
                $total_email_recivers_ids = array_merge($user_ids, $sup_ids);

            } else {
                $user_ids[]               = $user->id;
                $user_ids[]               = isset($get_sp_admin->id) ? $get_sp_admin->id : '';
                $total_email_recivers_ids = array_merge($user_ids, $sup_ids);
            }
            return true;
        } else {
            return false;
        }
    }



    public static function count_tenant_unread_notifications($user_id, $building_id)
    {
        $unread_noti_count = 0;
        $result = DB::table('notifications')
                    ->where('section_type', '=', 'tenant_app')
                    ->whereRaw("find_in_set($user_id, user_id)")
                    ->where('building_ids', '=', $building_id)
                    ->orderBy('id', 'desc')
                    ->get();
        if(isset($result) && count($result) > 0) {
            $unread_noti_count = 0;
            foreach($result as $key => $noti) {
                if(!empty($noti->is_read_by_users)) {
                    $is_read_noti_users = explode(',', $noti->is_read_by_users);
                } else {
                    $is_read_noti_users = [];
                }

                if(in_array($user_id, $is_read_noti_users)) {
                } else {
                    $unread_noti_count++;
                }
            }
        }
        if(isset($result) && count($result) > 0) {
            return $unread_noti_count;
        } else {
            $unread_noti_count = 0;
            return $unread_noti_count;
        }
    }

    /**
     * Get the status of a work order based on its journey and other parameters.
     *
     * @param string $workorder_journey The journey status of the work order.
     * @param int $id The ID of the work order.
     * @return string The calculated status ('In Progress', 'Completed', or empty).
     */
    public static function getWorkOrderStatus($workorder_journey, $id)
    {
        // Retrieve work order information
        $workOrder = WorkOrders::select('contract_type', 'worker_started_at', 'maintanance_request_id', 'workorder_journey', 'start_date','schedule_start_time')
            ->where('id', $id)
            ->first();

        $status = '';

        // Check if work order information is available
        if ($workOrder) {
            $workorder_journey = $workOrder->workorder_journey;
            $contract_type = $workOrder->contract_type;

            if (!empty($workOrder->maintanance_request_id)) { // MaintenanceWO
                if ($contract_type == 'regular' || $contract_type == 'warranty') {
                    if (empty($workOrder->worker_started_at) && $workorder_journey != 'finished' &&
                        Carbon::parse($workOrder->start_date . ' ' . $workOrder->schedule_start_time)->gt(Carbon::now()->addHour())
                    ) {
                        $status = 'Scheduled';
                    } elseif (empty($workOrder->worker_started_at) && $workorder_journey != 'finished') {
                        $status = 'In Progress';
                    } elseif (!empty($workOrder->worker_started_at) && $workorder_journey != 'finished') {
                        $status = 'In Progress';
                    } elseif ($workorder_journey == 'finished') {
                        $status = 'Completed';
                    }
                }
            } else { // Normal WO
                if ($contract_type == 'regular' || $contract_type == 'warranty') {
                    if (empty($workOrder->worker_started_at) && $workorder_journey != 'finished') {
                        $status = 'In Progress';
                    } elseif (!empty($workOrder->worker_started_at) && $workorder_journey != 'finished') {
                        $status = 'In Progress';
                    } elseif ($workorder_journey == 'finished') {
                        $status = 'Completed';
                    }
                }
            }
        }

        return $status;
    }

    /**
     * Get the name of the user who created a work order.
     *
     * @param int|null $wo_id The ID of the work order.
     * @param int|null $maintenance_id The ID of the maintenance request.
     * @return string The name of the user who created the work order.
     */
    public static function workorder_created_by($wo_id, $maintenance_id)
    {
        if (!empty($wo_id)) {
            $workOrder = WorkOrders::find($wo_id);

            if (!empty($workOrder->maintanance_request_id) && $workOrder->maintanance_request_id != 0) {
                // Maintenance Request
                $maintenanceRequest = MaintenanceRequest::find($workOrder->maintanance_request_id);

                if ($maintenanceRequest) {
                    if ($maintenanceRequest->generated_from == 'web') {
                        return $maintenanceRequest->name;
                    } elseif ($maintenanceRequest->generated_from == 'app') {
                        return $maintenanceRequest->name;
                    } else {
                        return $workOrder->createdByUser->name;
                    }
                }
            } else {
                return $workOrder->createdByUser->name;
            }
        } else {
            $maintenanceRequest = MaintenanceRequest::find($maintenance_id);

            if ($maintenanceRequest) {
                if ($maintenanceRequest->generated_from == 'web') {
                    return $maintenanceRequest->name;
                } else {
                    return $maintenanceRequest->name;
                }
            }
        }
    }

    /**
     * Get the latest chat details for the authenticated user.
     *
     * @param int $user_id The ID of the authenticated user.
     * @param int|null $property_id The ID of the property (optional).
     * @return array The array containing the latest chat details.
     */
    public static function getLatestChat($authenticatedUserId, $property_id, $profile_img)
    {
        $response = [];

        // Query the Chatroom model with related data
        $chatroomQuery = Chatroom::with([
            'tenant', // Assuming 'tenant' is the relationship name for the user associated with the chatroom
            'chatroomAttachments', // Assuming 'chatroomAttachments' is the relationship for chat attachments
            // ... other relationships
        ]);

        // Filter by property ID if provided
        if (isset($property_id)) {
            $chatroomQuery = $chatroomQuery->where('tenant_chatroom.building_id', $property_id);
        }

        // Get the latest chatroom data
        $chatroom = $chatroomQuery->orderBy('tenant_chatroom.id', 'desc')->first();

        if ($chatroom) {
            // Retrieve related chat attachments
            $chat_images = $chatroom->chatroomAttachments;

            // Retrieve chat ratings (likes and dislikes)
            $chat_rating = $chatroom->chatroomRatings()->where('rating', '1')->count();
            $chat_dislike = $chatroom->chatroomRatings()->where('rating', '0')->count();

            // Count chat comments
            $chat_comments_count = $chatroom->chatComments->count();

            // Check if tenant exists
            $tenant = $chatroom->tenant;

            if($tenant->user_type != 'tenant')
            {
                $profile_img = $tenant ? $tenant->getProfileImageUrl() : null;
            }
            // Construct the response array
            $response[] = [
                'id' => $chatroom->id,
                'building_id' => $chatroom->building_id,
                'tenant_id' => $chatroom->tenant_id,
                'first_name' => $tenant ? $tenant->first_name : null,
                'last_name' => $tenant ? $tenant->last_name : null,
                'name' => $tenant ? $tenant->name : null,
                'user_type' => $tenant ? $tenant->user_type : null,
                'profile_img' => $tenant ? $profile_img : null, // Assuming 'getProfileImageUrl()' is a method in User model
                'post_date' => $chatroom->created_at->format('Y-m-d H:i:s'),
                'post_type' => $chatroom->post_type,
                'message' => $chatroom->message,
                'destination_tenant_id' => $chatroom->destination_tenant_id,
                'chat_attachment' => $chat_images,
                'chat_like' => $chat_rating,
                'chat_dislike' => $chat_dislike,
                'chat_comments_count' => $chat_comments_count,
            ];
        }

        return $response;
    }


    /**
     * Get the work order result for a given work order ID.
     *
     * @param int $id The ID of the work order
     * @return array|null The work order result data, or null if not found
     */
    public static function getWorkOrderResult($workOrder)
    {
        $workOrder = clone $workOrder;
        //Retrieve the work order details along with relevant user names
        $workOrder = $workOrder->where('work_orders.status', 4)->first();

        if (!$workOrder) {
            return null; // Return null if the work order is not found
        }

        // Convert the work order to an associative array
        $workOrderArray = $workOrder->toArray();

        $workOrderArray['raised_by'] = $workOrder->createdByUser->name;
        $workOrderArray['worker_name'] = $workOrder->worker->name ?? '';

        // Check if the checklist_id is set in the work order
        if (isset($workOrderArray['checklist_id']) && $workOrderArray['checklist_id'] > 0) {
            // Get checklist task data using ApiHelper

            $checklistTaskData = WorkorderHelper::getCheckListTask($workOrderArray['checklist_id'], $workOrderArray['id']);

            $workOrderArray['checklist_task'] = $checklistTaskData;
        }

        // Get checklist images and convert their paths to URLs
        $workOrderArray['images'] = Workorders::get_checklist_images($workOrderArray['id']);
        if (count($workOrderArray['images']) > 0) {
            foreach ($workOrderArray['images'] as $key => $row) {
                $workOrderArray['images'][$key] = !empty($row) ? ImagesUploadHelper::displayImage($row, 'actions') : '';
            }
        }

        return $workOrderArray;
    }


    public static function getSupervisorsOrSPName($workOrderDetail)
    {
        if(isset($workOrderDetail['supervisor_id']) && $workOrderDetail['supervisor_id'] != null && $workOrderDetail['assigned_to'] == 'supervisor') {
            $supervisors = WorkOrders::get_supervisors($workOrderDetail['supervisor_id']);
        } else {
            $supervisors = WorkOrders::get_supervisors($workOrderDetail['service_provider_id']);
        }
        if($supervisors != '' && ($workOrderDetail['worker_id'] != 0 || $workOrderDetail['assigned_to'] != "sp_worker")) {
            return $supervisors;
        } else {
            return __('work_order.table.no_action_taken_from_sps');
        }
    }



    public static function getChecklistCompletedTaskDetails($work_order_id, $checklist_task_id)
    {
        $work_order_checklists = NoChecklistAction::select('*')
                            ->where('work_order_id', $work_order_id)
                            ->where('worker_id', Auth::user()->id)
                            ->where('is_reopen_wo', 0)
                            ->where('checklist_task_id', $checklist_task_id)
                            ->first();
        if(isset($work_order_checklists)) {
            $work_order_checklists->comment = $work_order_checklists->comment == null || $work_order_checklists->comment == "null" || $work_order_checklists->comment == "NULL" ? 'no' : $work_order_checklists->comment;
            $new_array = [];
            $photos = json_decode($work_order_checklists->photos);
            if(!empty($photos))
            {
                foreach($photos as $key => $value)
                {
                    if(trim(array_reverse(explode('.',url('storage/'.$value)))[0]) != 'pdf')
                    {
                        $new_array[$key]['img'] = ImagesUploadHelper::displayImage($value, 'actions');
                    }
                    else {
                        $new_array[$key]['img'] = url('storage/'.$value);
                    }
                }
            }
            $work_order_checklists->photos = $new_array;
        }
        return $work_order_checklists;
    }


    public static function getChecklistSubmittedSubtask($work_order_id, $checklist_task_id, $is_submitted)
    {
        $worker_id = Auth::user()->id;
        $subtask_checklists = ChecklistSubtask::select('*')
                                ->selectSub(function ($query) {
                                    $query->from('subtask_actions_list')
                                        ->selectRaw('COUNT(*)')
                                        ->whereColumn('checklist_subtask_id', '=', "checklist_subtasks.id")
                                        ->limit(1);
                                }, 'check_count_checklist_action')
                                // ->selectSub(function ($query) use($work_order_id,$worker_id) {
                                //     $query->from('no_checklist_subtask')
                                //         ->selectRaw('created_at')
                                //         ->where('work_order_id', '=', $work_order_id)
                                //         ->where('worker_id', '=', $worker_id)
                                //         ->whereColumn('checklist_subtask_id', '=', "checklist_subtasks.id")
                                //         ->limit(1);
                                // }, 'checked_at')
                            ->where('checklist_task_id', $checklist_task_id)
                            ->orderby('id', 'asc')
                            ->get()->toArray();

        if(isset($subtask_checklists) && !empty($subtask_checklists)) {
            foreach($subtask_checklists as $k => $v) {
                if($is_submitted == 'yes' || $is_submitted == "yes") {
                    $check_submitted_checklist = NoChecklistSubtask::select('*')
                    ->where('work_order_id', $work_order_id)
                    ->where('worker_id', $worker_id)
                    ->where('checklist_subtask_id', $v['id'])
                    ->first();
                    if(isset($check_submitted_checklist) && !empty($check_submitted_checklist)) {
                        $subtask_checklists[$k]['checked_at'] = $check_submitted_checklist->created_at;
                        $subtask_checklists[$k]['check_user_by'] = $worker_id;
                    }
                }

                //$check_count_checklist_action = SubtaskActionsList::where('checklist_subtask_id',$v['id'])->count();
                $subtask_checklists[$k]['is_actions_available'] = $v['check_count_checklist_action'] > 0 || $v['check_count_checklist_action'] > "0" ? true : false;
            }
        }
        return $subtask_checklists;
    }



    public static function getWorkorderChecklists($work_order_id)
    {
        $workerId = Auth::user()->id;
        $work_order_checklists = WorkOrders::select('checklist_tasks.*')
        ->selectRaw("(select count(id) from no_checklist_actions where work_order_id = '$work_order_id' and worker_id = '$workerId' and checklist_tasks.id = no_checklist_actions.checklist_task_id and is_reopen_wo = 0) as checklist_count")
        ->join('checklists', 'checklists.id', '=', 'work_orders.checklist_id')
        ->join('checklist_tasks', 'checklist_tasks.checklist_id', '=', 'checklists.list_id')
        ->where('work_orders.id', $work_order_id)
        ->where('checklist_tasks.is_deleted', "no")
        ->get()->toArray();
        if(!empty($work_order_checklists)) {
            foreach($work_order_checklists as $key => $woc) {
                $work_order_checklists[$key]['comment'] = $woc['comment'] == null || $woc['comment'] == "null" || $woc['comment'] == "NULL" ? 'no' : $woc['comment'];
                $multiple_options = explode(',', $woc['multiple_options']);
                $new_array = [];
                if(!empty($multiple_options) && ($woc['multiple_options'] != null || $woc['multiple_options'] != '')) {
                    foreach($multiple_options as $key1 => $value) {
                        $new_array[$key1]['option'] = $value;
                    }
                }
                $work_order_checklists[$key]['multiple_options'] = $new_array;
                $work_order_checklists[$key]['subtask_list'] = ChecklistSubtask::where('checklist_subtasks.checklist_task_id', $woc['id'])->orderby('id', 'asc')->get();
            }
        }
        return $work_order_checklists;
    }


/* Tenant App (Use Job) */
  public static function send_maintenance_notification_FCM($final_all_tenants_id, $device_tokens, $message, $building_id,$notification_type,$section_id,$created_by) {

    Log::info("Function API HELPER Start:send_maintenance_notification_FCM ");
    $notification_sub_type = '';
    if($notification_type == 'maintenance_request_accepted') {
        $notification_sub_type = 'maintenance_request_accepted';
    }
    if($notification_type == 'maintenance_request_scheduled') {
        $notification_sub_type = 'maintenance_request_scheduled';
    }
    $user_id_list = implode(',',$final_all_tenants_id);
    $insert_data = [
        'user_id' => $user_id_list,
        'building_ids' => $building_id,
        'message' => $message['title'],
        'message_ar' => array_key_exists('title_ar', $message) ? $message['title_ar'] : $message['title'],
        'message_desc' => $message['body'],
        'message_desc_ar' => array_key_exists('body_ar', $message) ? $message['body_ar'] : $message['body'],
        'section_type' => 'tenant_app',
        'section_id' => $section_id,
        'notification_sub_type' => $notification_sub_type,
        'notification_type' => $notification_type,
        'created_by' => $created_by,
        'created_at' => date('Y-m-d H:i:s')
    ];
    $notification_insert_data[]  =  $insert_data;
    Notification::insert($notification_insert_data);
    if(count($device_tokens)>0)
    {
        $user_ids = ApiHelper::getUserIdsByDeviceTokens($device_tokens);
            $user_ids_data = ApiHelper::getUserIdswithtokenByDeviceTokenswithId($device_tokens,$final_all_tenants_id);
            if(!empty($user_ids_data)) {
                $user_ids_device=$user_ids_data;
                $building_id=$building_id;
                $deviceTokens=null;
                $title=$message['title'];
                $body= $message['body'];
                $badge ='';
                $sound='';
                $contentAvailable=true;
                SendFirebaseNotificationJob::dispatch($building_id,$user_ids_device,$deviceTokens, $title, $body, $badge, $sound, $contentAvailable);
            }

    }
    Log::info("Function API HELPER End:send_maintenance_notification_FCM ");
    }



    public static function getAllTenantsIdByBuilding($building_id, $maintanance_request_id = NULL) {
        if($maintanance_request_id)
        {
            $user_ids = DB::table('maintanance_request')->select('users.device_token', 'users.id')
            ->leftJoin('users','maintanance_request.phone','=','users.phone')
            ->where('maintanance_request.id',$maintanance_request_id)
            //->where('users.device_token','!=','')
            ->pluck('users.id')->toArray();
        }

        $users_data = DB::table('users')->select('id')
        ->where('user_type','tenant')
        ->where('building_ids',$building_id);
        if($maintanance_request_id && !empty($user_ids))
        {
            $users_data = $users_data->whereNotIn('id', $user_ids);
        }
        $users_data = $users_data->pluck('id')->toArray();
        if($users_data)
        return $users_data;
        else
        return [];
    }



    public static function getTenantIdRaisedMaintenanceRequestByuserid($maintenance_id,$building_id,$user_type) {
        $user_data = DB::table('maintanance_request')->select('users.id','users.user_type','users.status','users.is_deleted');
        if($user_type == 'sp_worker')
        {
            $user_data = $user_data->leftJoin('users','maintanance_request.user_id','=','users.id');
        }
        else
        {
            $user_data = $user_data->leftJoin('users','maintanance_request.phone','=','users.phone');
        }

        //->where('users.building_ids',$building_id)
        //->whereRaw("find_in_set($building_id, users.building_ids)")
        $user_data = $user_data->where('maintanance_request.id',$maintenance_id)
        ->where('users.user_type',$user_type)
        ->where('users.status', 1)
        ->where('users.is_deleted', 'no')
        ->pluck('users.id')->toArray();
        if($user_data)
        return $user_data;
        else
        return [];
    }



}
