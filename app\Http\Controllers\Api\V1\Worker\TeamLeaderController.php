<?php

namespace App\Http\Controllers\Api\V1\Worker;

use App\Http\Controllers\Api\V1\Tenant\ApiHelper;
 use App\Http\Requests\API\TeamLeaderRequests\AssignWorkorderRequest;
 use App\Models\WorkOrders;
 use App\Services\WorkOrderService;
 use Exception;
use Throwable;
use Illuminate\Http\Request;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use App\Http\Traits\FunctionsTrait;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\ListWorkerRequest;
use App\Http\Resources\WorkerCollection;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Validator;
 use App\Http\Traits\TeamLeaderTrait;
 use App\Http\Traits\AssetNameTrait;
 use App\Http\Requests\API\TeamLeaderRequests\WorkorderListByStatusRequest;

class TeamLeaderController extends Controller
{
    use FunctionsTrait, TeamLeaderTrait,AssetNameTrait;

    public function __construct(private UserService $userService) {}

    public function getDashboard(Request $request, WorkOrderService $workOrderService)
        { 
            
            try {
                // ✅ Fetch Dashboard Data

                $result = [
                    'workorders_count_by_status' => $workOrderService->getWorkOrdersCountForAssignedWorkers(),
                    'workorders_count_all' => [
                        'active_task' => $workOrderService->getTeamLeaderActiveTaskCount(),
                        'examine'     => $workOrderService->getTeamLeaderUpcomingTaskCount(['examine' => true]),
                        'upcoming'    => $workOrderService->getTeamLeaderUpcomingTaskCount(['examine' => null]),
                    ],
                    'workorders_count_by_type' => $workOrderService->getTeamLeaderWorkOrderCount(),
                    'worker_location_data' => $this->getWorkerLocationData(),
                ];
                
                if (!empty($result)) //If there are pending work orders
                {
                    return response()->json(['status' => true, 'message' => __('api.oreder_fetched_successfully'), 'result' => $result], 200);
                } else {
                    return response()->json(['status' => false, 'message' => __('api.no_order_found')], 200);
                }
            } 
            
            catch (DecryptException $th) {
                Log::error("getDashboard error: ".$th);

                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to fetch dashboard data.'
                ], 500);
            }
        }





        public function getCountWorkorders(Request $request, WorkOrderService $workOrderService)
        {             
            try {

                $filters = [
                'search'            => $request->search,
                'sort_by'           => $request->sort_by,
                'property_id'       => $request->selected_property_id,
                'worker_id'         => $request->selected_worker_id,
                'zone_id'           => $request->selected_zone,
                'unit_id'           => $request->selected_unit,
                'asset_name_id'     => $request->selected_asset_name,
                'service_type'      => $request->selected_service_type,
                'work_order_type'   => $request->selected_workorder_type,
                'from_date'         => $request->from_date,
                'to_date'           => $request->to_date,
            ];

                // ✅ Fetch Data
                $result = $workOrderService->getWorkOrdersCountForAssignedWorkers($filters);

                // Append new items with same format
                $result[] = ['type' => 'active_task', 'value' => $workOrderService->getTeamLeaderActiveTaskCount($filters)];
                $result[] = ['type' => 'examine', 'value' => $workOrderService->getTeamLeaderUpcomingTaskCount(array_merge($filters,['examine'=> true]))];
                $result[] = ['type' => 'upcoming', 'value' => $workOrderService->getTeamLeaderUpcomingTaskCount(array_merge($filters,['examine'=> null]))];
                
                if (!empty($result)) //If there are pending work orders
                {
                    return response()->json(['status' => true, 'message' => __('api.oreder_fetched_successfully'), 'result' => $result], 200);
                } else {
                    return response()->json(['status' => false, 'message' => __('api.no_order_found')], 200);
                }
            } 
            
            catch (DecryptException $th) {
                Log::error("getCountWorkorders error: ".$th);

                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to fetch workorder count.'
                ], 500);
            }
        }



        public function getWorkorderlistbyStatus(WorkorderListByStatusRequest $request,WorkOrderService $workOrderService)
        { 
            
            try {
                    $filters = [
                        'search'            => $request->search,
                        'sort_by'           => $request->sort_by,
                        'property_id'       => $request->selected_property_id,
                        'worker_id'         => $request->selected_worker_id,
                        'zone_id'           => $request->selected_zone,
                        'unit_id'           => $request->selected_unit,
                        'asset_name_id'     => $request->selected_asset_name,
                        'service_type'      => $request->selected_service_type,
                        'work_order_type'   => $request->selected_workorder_type,
                        'from_date'         => $request->from_date,
                        'to_date'           => $request->to_date,
                    ];

                    // ✅ Fetch Data
                    $result = $workOrderService->getWorkordersListforAssignedWorkers($request->filter_type,$request->page,$filters);
                    
                    if (!$result->isEmpty()) //If there are pending work orders
                    {
                        return response()->json(['status' => true, 'message' => __('api.oreder_fetched_successfully'), 'result' => $result], 200);
                    } else {
                        return response()->json(['status' => false, 'message' => __('api.no_order_found')], 200);
                    }
                } 
            
            catch (DecryptException $th) {
                Log::error("getWorkorderlistbyStatus error: ".$th);

                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to fetch workorder list.'
                ], 500);
            }
        }


   /**
     * List the workers assigned to a specific team lead based on filters.
     *
     * @param  ListWorkerRequest  $request
     * @return JsonResponse
     */
    public function listWorkers(ListWorkerRequest $request): JsonResponse
    {
        try {
        
            // Get team lead's ID and role from the validated request
            $id = $request->id;
            $role = $request->role;
            $workOrderId = $request->work_order_id ?? 0;

            // Fetch the team lead user record by ID and role or fail
            $teamLead = $this->userService->getUserByRoleOrFail($id, $role);

            // Get the base query for assigned workers of the team lead
            $query = $this->userService->getAssignedWorkersQuery($teamLead,$workOrderId);

            // Apply case-insensitive search filter on worker name if provided
            if ($request->filled('name')) {
                $query->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($request->name) . '%']);
            }

            // Apply case-insensitive search on profession names (EN, AR, UR) if provided
            if ($request->filled('profession')) {
                $query->whereHas('profession', function ($q) use ($request) {
                    $search = strtolower($request->profession);
                    $q->whereRaw('LOWER(profession_en) LIKE ?', ["%$search%"])
                    ->orWhereRaw('LOWER(profession_ar) LIKE ?', ["%$search%"])
                    ->orWhereRaw('LOWER(profession_ur) LIKE ?', ["%$search%"]);
                });
            }

            // Apply pagination with default of 10 items per page
            $perPage = is_numeric($request->input('per_page')) ? (int) $request->input('per_page') : 10;
            $paginated = $query->paginate($perPage);

            // If no records found, return a 404 with proper message
            if ($paginated->isEmpty()) {
                return response()->json([
                    'status' => false,
                    'message' => __('api.no_records_found'),
                ], 404);
            }

            // Return the paginated list using a resource collection
          return (new WorkerCollection($paginated, 'list'))->response()->setStatusCode(200);

        }
        catch (Exception $e) {
            // Log unexpected errors and return generic message
            Log::error('listWorkers error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => __('api.unexcepted_error'),
            ], 500);
        }
    }




    public function geteWorkorderFilterdata(Request $request)
    {       
        try
        {
            // Step 1: Get assigned worker IDs for the logged-in user, fallback to [0] if empty
        $workerIds = Auth::user()->assigned_workers ? explode(',', Auth::user()->assigned_workers) : [0];
        
        // Step 2: Fetch properties related to assigned workers via PropertyService
        $propertyService = new \App\Services\PropertyService();
        $properties = $propertyService->getPropertiesByWorkerIds($workerIds);

        // Step 3: Fetch asset names related to assigned workers
        $assetname = $this->getAssetNamesForUsers($workerIds);

        // Step 4: Fetch zone (floors) and unit data for assigned workers
        $unitdata = $this->getPropertyFilterValues($workerIds);

        // Step 5: Fetch list of assigned workers visible to the logged-in user
        $workerList = $this->userService->getAssignedWorkersListQuery(Auth::user())->get();

        // Step 6: Prepare structured response data
            $response = [
                'properties' => $properties,
                'service_type' => ['soft','hard'],
                'asset_name' => $assetname,
                'zone' => array_values($unitdata['zone']),
                'unit' => $unitdata['unit'],
                'workorder_type' => ['preventive', 'reactive'],
                'worker_list' => $workerList
            ];
            return response()->json(['status' => true, 'message' => 'Data fetched Successfully!', 'result' => $response], 201);
        }
        catch (Exception $e) {
            // Log unexpected errors and return generic message for response
            Log::error('geteWorkorderFilterdata error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'An unexpected error occurred. Please try again later.',
            ], 500);
        }
    }




    public function assignWorkorder(AssignWorkorderRequest $request,WorkOrderService $workOrderService)
    {       
                try {
                $work_order_id = $request->work_order_id;
                $assign_type = $request->assign_type;
                $user_id = $request->user_id;

                // Step 1: Fetch the work order
                $workOrder = WorkOrders::find($work_order_id);

                if (!$workOrder) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Work order not found.'
                    ], 404);
                }


                return match ($assign_type) {
                    'team_leader' => $workOrderService->assignToTeamLeader($workOrder, Auth::id()),
                    'worker' => $workOrderService->assignToWorker($workOrder, $user_id),
                    default => ['status' => false, 'code' => 400, 'message' => 'Invalid assignment type.'],
                };

            } catch (Exception $e) {
                Log::error('assignWorkorder error: ' . $e->getMessage());
                return response()->json([
                    'status' => false,
                    'message' => 'An unexpected error occurred. Please try again later.'
                ], 500);
            }
    }
}

?>