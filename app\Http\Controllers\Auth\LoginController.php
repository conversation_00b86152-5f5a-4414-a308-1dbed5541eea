<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Auth;
use Session;
use App\Models\User;
use App;
use App\Jobs\ProcessCrmLogin;
use Helper;
use App\Services\CRM\CRMAuthService;
use App\Services\CRM\CRMWorkspace;
use Illuminate\Support\Facades\Log;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    protected $crmAuthService;
    protected $crmWorkspaceService;


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(CRMAuthService $crmAuthService, CRMWorkspace $crmWorkspaceService)
    {
        $this->middleware('guest')->except('logout');
        $this->crmAuthService = $crmAuthService;
        $this->crmWorkspaceService = $crmWorkspaceService;

    }

    /**
     * Show the application login form.
     *
     * @return \Illuminate\View\View
     */
    public function loginForm ()
    {
        $pageTitle = 'Login';
        $pageDescription = 'Login Page';
        return view('auth.login', compact('pageTitle', 'pageDescription'));
    }

    protected function getCredentials(Request $request)
    {
        return [
            'email' => $request->input('email'),
            'password' => $request->input('password'),
        ];
    }

    public function login(Request $request) {
        $this->validate($request,['email' => 'required|email','password' => 'required']);
        if (Auth::guard()->attempt($this->getCredentials($request))){
            //authentication passed
        }
        if( Auth::check() )  {
            //dd(Auth::user()->status);
            if(Auth::user()->user_type == 'sp_worker' || Auth::user()->user_type == 'team_leader') {

                Auth::logout();
                return redirect()->back()->withErrors(['msg' => trans('login.login_page.These_credentials_do_not_match_our_records')]);
            }else{
                if(Auth::user()->status == 0) {

                    if (Auth::user()->user_type == 'sp_admin') {
                        return redirect()->to(route('psp.profile-view',['type'=>'profile']));
                    }

                    Auth::logout();
                    //  return redirect()->back()->withErrors(['msg' => trans('login.login_page.in_active_user')]);
                    return view('auth.login')->withErrors(['msg' => trans('login.login_page.in_active_user')]);
                } else {
                    if (Auth::user()->user_type == 'osool_admin' && Auth::user()->first_login) {
                        session()->put('FirstLogin', true);
                        Auth::user()->update(['first_login' => false]);
                    }

                    // $this->crmAuthService->login('<EMAIL>', 'Tarqeem21');
                    // $this->crmAuthService->login('<EMAIL>', '123456');

                    if (!empty(Auth::user()?->crmUser?->crm_user_id) || Auth::user()->user_type == 'super_admin'  || Auth::user()->user_type == 'osool_admin') {
                        // $this->crmAuthService->login('<EMAIL>', 'Tarqeem21');
                        // $this->crmAuthService->login($request->email, $request->password);
                        // $this->crmWorkspaceService->setActiveWorkspaces();
                        ProcessCrmLogin::dispatch($request->email, $request->password);
                        session()->put('plain_user_password', $request->password);
                    }
                    //Needed For tast Tangible Task
                   /*  if (Auth::user()->user_type=='building_manager_employee'|| Auth::user()->user_type == 'building_manager' ) {
                        $this->crmAuthService->login('<EMAIL>', '123456');
                        $this->crmWorkspaceService->setActiveWorkspaces();
                      
                    } */


                    // $setActiveWorkspaces = auth()->user()->workspace;
                    // $token = Session::get('crm_api_token');
                    // dd($setActiveWorkspaces);
                    return redirect()->intended(route('admin.dashboard'));

                }
            }
        } else {
            return view('auth.login')->withErrors(['msg' => trans('login.login_page.These_credentials_do_not_match_our_records')]);
        }
    }

    public function logout(Request $request) {
        $current_language = App::currentLocale();
        $user_id = Auth::user()->id;
        if(Auth::user()->user_type == 'osool_admin' || Auth::user()->user_type == 'super_admin')
        {
            //dd($user_id);
            User::where('id', $user_id)->update(['project_id'=>0,'project_user_id' => 0]);
        }

        // clear crm tokens
        auth()->user()->crm_api_token = null;
        auth()->user()->workspace_slug = null;
        auth()->user()->save();

        Auth::logout();
        Session::forget('entered_project_id');
        Session::forget(App\Services\AkauntingService::AKAUNTING_COMPANY_SESSION_HANDLE);
        Session::flush();

        //Set Language
        Session::put('locale', $current_language);

        return redirect('/login');
    }
}
