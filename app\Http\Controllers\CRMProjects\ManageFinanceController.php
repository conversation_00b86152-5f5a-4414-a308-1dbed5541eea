<?php

namespace App\Http\Controllers\CRMProjects;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;
use App\Services\CRM\Projects\FinanceServices;
use App\Http\Traits\FunctionsTrait;
use PDF2 as MPDF;

class ManageFinanceController extends Controller{
    use FunctionsTrait;

    public function index( $id = null )
 {

        if ( $id ) {
            try {
                $projectID = Crypt::decrypt( $id );
                $documentType = $this->getDocumentTypeFromRoute();
                return view( 'CRMProjects.manage-fianance.index', [
                    'projectID' => $projectID,
                    'documentType' => $documentType
                ] );
            } catch ( \Illuminate\Contracts\Encryption\DecryptException $e ) {
                return redirect()->route( 'admin.dashboard' );
            }
        } else {
            return redirect()->route( 'admin.dashboard' );
        }
    }

    private function getDocumentTypeFromRoute()
 {

        $routeName = Route::currentRouteName();
        if ( strpos( $routeName, 'proposol' ) !== false ) {
            return 'proposal';
        } elseif ( strpos( $routeName, 'invoice' ) !== false ) {
            return 'invoice';
        } elseif ( strpos( $routeName, 'bill' ) !== false ) {
            return 'bill';
        }

        return null;

    }

    public function Details( $projectId, $id ) {
        $routeName = Route::currentRouteName();
        try {
            if (auth()->user()->user_type!='admin') {
                
              return redirect()->route( 'admin.dashboard' );
            }
            if ( empty( $projectId ) || empty( $id ) ) {
                return redirect()->route( 'admin.dashboard' );
            }
            $projectID = Crypt::decrypt( $projectId );
            if ( strpos( $routeName, 'proposol' ) !== false ) {

                return view( 'CRMProjects.manage-fianance.details', [
                    'projectID' => $projectID,
                    'documentId' => $id,
                    'documentType' => 'proposal'
                ] );

            } elseif ( strpos( $routeName, 'invoice' ) !== false ) {
                return view( 'CRMProjects.manage-fianance.details', [
                    'projectID' => $projectID,
                    'documentId' => $id,
                    'documentType' => 'invoice'
                ] );

            } else {

                return view( 'CRMProjects.manage-fianance.details', [
                    'projectID' => $projectID,
                    'documentId' => $id,
                    'documentType' => 'bill'
                ] );
            }

        } catch ( \Illuminate\Contracts\Encryption\DecryptException $e ) {
            return redirect()->route( 'admin.dashboard' );
        }

    }

    public function edit( $projectId, $id ) {
        $routeName = Route::currentRouteName();
        try {
            if ( empty( $projectId ) || empty( $id ) ) {
                return redirect()->route( 'admin.dashboard' );
            }
            $projectID = Crypt::decrypt( $projectId );
            if ( strpos( $routeName, 'proposol' ) !== false ) {

                return view( 'CRMProjects.manage-fianance.edit', [
                    'projectID' => $projectID,
                    'documentId' => $id,
                    'documentType' => 'proposal'
                ] );

            } elseif ( strpos( $routeName, 'invoice' ) !== false ) {
                return view( 'CRMProjects.manage-fianance.edit', [
                    'projectID' => $projectID,
                    'documentId' => $id,
                    'documentType' => 'invoice'
                ] );

            } else {

                return view( 'CRMProjects.manage-fianance.edit', [
                    'projectID' => $projectID,
                    'documentId' => $id,
                    'documentType' => 'bill'
                ] );
            }

        } catch ( \Illuminate\Contracts\Encryption\DecryptException $e ) {
            return redirect()->route( 'admin.dashboard' );
        }

    }
    public function Create(  $id ) {
        $routeName = Route::currentRouteName();
        try {
            if ( empty( $id ) ) {
                return redirect()->route( 'admin.dashboard' );
            }
            $projectID = Crypt::decrypt( $id );
            if ( strpos( $routeName, 'proposol' ) !== false ) {

                return view('CRMProjects.manage-fianance.create', [
                    'projectID' => $projectID,
                    'documentType' => 'proposal'
                ] );

            } elseif ( strpos( $routeName, 'invoice' ) !== false ) {
                return view('CRMProjects.manage-fianance.create', [
                    'projectID' => $projectID,
                    
                    'documentType' => 'invoice'
                ] );

            } else {

                return view('CRMProjects.manage-fianance.create', [
                    'projectID' => $projectID,
                    'documentType' => 'bill'
                ] );
            }

        } catch ( \Illuminate\Contracts\Encryption\DecryptException $e ) {
            return redirect()->route( 'admin.dashboard' );
        }

    }

    public function uploadAttachments(Request $request, $link) {
        try {
            if(isset($link)){
                $decryptedLink = $this->decryptToken($link);
                
                if(isset($decryptedLink)){
                    $filename = $request->file('file')->getClientOriginalName();
                    $filePath = $request->file('file')->storeAs('uploads/attachments', $filename, 'local');
                    $absolutePath = storage_path('app/' . $filePath);
                    $financeService = app(FinanceServices::class);
                    $result = $financeService->uploadAttachment($decryptedLink['projectId'], $decryptedLink['documentType'], $decryptedLink['documentId'], $absolutePath, $filename);

                    if(isset($result) && $result['status'] == "success"){
                        $this->createDataSession('success', __('finace_manage.common.file_uploaded_successfully'));
                        return back();
                    }
        
                    else{
                        $this->createDataSession('error', __('finace_manage.common.file_uploaded_error'));
                        return back();
                    }
                }

                else{
                    $this->createDataSession('error', __('finace_manage.common.file_uploaded_error'));
                    return back();
                } 
            }

            else{
                $this->createDataSession('error', __('finace_manage.common.file_uploaded_error'));
                return back();
            } 
        } 
        
        catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            return redirect()->route( 'admin.dashboard' );
        }
    }
}
