<?php
namespace App\Http\Helpers;

use Akaunting\Api\Akaunting;
use Akaunting\Api\Data\ItemData;
use Akaunting\Api\Base\TableConfigInterface;
use Akaunting\Api\Data\Providers\BaseDataProvider;
use Akaunting\Api\Data\Providers\FormDataProvider;
use Akaunting\Api\View\Configs\ContractItemTableConfig;
use Akaunting\Api\View\Configs\WorkOrderItemFormTableConfig;
use App\Enums\AssignType;
use App\Models\ContractPropertyBuildings;
use App\Models\ProjectsDetails;
use App\Models\ProjectSettings;
use App\Models\PropertyBuildings;
use App\Models\ServiceProviderProjectMapping;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Yajra\DataTables\DataTables;
use Carbon\Carbon;
use App\Models\Property;
use App\Models\AssetCategory;
use App\Models\User;
use App\Models\WorkOrders;
use App\Models\Notification;
use App\Models\MaintenanceRequest;
use App\Models\Checklists;
use App\Models\ChecklistTasks;
use App\Models\NoChecklistAction;
use App\Models\NoChecklistSubtask;
use App\Models\NoChecklistSubtaskAction;
use App\Models\WorkTimeFrame;
use App\Models\ContractAssetCategories;
use App\Models\ContractPriority;
use App\Models\FrequencyMaster;
use App\Models\ManageWorkerAvailabilityStatus;
use App\Models\WorkerAvailabilityRequestReasonType;
use App\Models\WorkOrderItemRequest;
use App\Models\ServiceProviderMissingItemRequest;
use App\Models\ServiceProvieRequestedMissingItem;
use App\Models\WorkerLocation;
use App\Models\WorkerLocationLog;
use Illuminate\Support\Facades\DB;
use App\Models\Asset;
use App\Http\Helpers\ImagesUploadHelper;
use Illuminate\Support\Str;

use Illuminate\Support\Facades\Artisan;
use Log;
use App\Models\WorkerAttendances;
use App\Models\WorkerWorkOrderTimeTracking;
use App\Models\RoomsTypeFloors;
use App\Http\Helpers\MaintenancePortalHelper;
use Illuminate\Support\Facades\Lang;
use App\Models\PpmRequestWorkordersMapping;
use App\Models\ManagePpmRequest;
use App\Enums\WorkOrder\OrderType;

class WorkorderHelper
{
    /**
     * Filter and retrieve properties based on user and service provider.
     *
     * @param  int  $userId
     * @param  int  $serviceProviderId
     * @return \Illuminate\Support\Collection
     */
    public static function getFilteredProperties($loggedInUser, $user_id, $service_provider_id = null)
    {
        // Initialize the query with property and building data
        $properties = Property::with([
            'propertyBuildings' => function ($query) use ($loggedInUser) {
                $query->select('id', 'building_name', 'property_id');
            }
        ]);

        // Apply filtering based on user type and conditions
        $properties = Helper::filterProperties($loggedInUser, $properties, $service_provider_id);

        // Retrieve properties and associated building data
        $properties = $properties
            ->orderBy('properties.id', 'asc')
            ->get()
            ->toArray();

        $properties = array_map(function ($property) {
            if ($property['property_type'] === 'building') {
                $property['building_id'] = isset($property['property_buildings'][0]['id']) ? $property['property_buildings'][0]['id'] : 0;
                $property['building_name'] = isset($property['property_buildings'][0]['building_name']) ? $property['property_buildings'][0]['building_name'] : '';
            }
            return $property;
        }, $properties);

        // Return the retrieved properties
        return $properties;
    }

    /**
     * Filter and retrieve asset categories based on the user's context.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Support\Collection
     */
    public static function getFilteredAssetCategories($loggedInUser)
    {
        $data = AssetCategory::whereHas('priority')
            ->where('is_deleted', 'no')
            ->where('status', '=', 1);

        if ($loggedInUser->isBuildingManager() || $loggedInUser->isBuildingManagerEmployee() || $loggedInUser->isSupervisor()) {
            $data = $data->whereIn('asset_categories.id', explode(',', $loggedInUser->asset_categories));
        } elseif ($loggedInUser->isServiceProviderAdmin()) {
            $asset_category_ids = $loggedInUser->assetCategoryIds()->pluck('asset_category_id')->unique()->toArray();
            $data = $data->whereIn('asset_categories.id', $asset_category_ids);
        } else {
            $data = $data->where('asset_categories.user_id', $loggedInUser->project_user_id);
        }
        $data = $data->get()->toArray();
        return $data;
    }

    public static function getAllAssetsFromAssetCategories($asset_categories_data)
    {
        $assetCatIds = [];
        foreach ($asset_categories_data as $acId) {
            array_push($assetCatIds, $acId['id']);
        }
        $data = Asset::where('is_deleted', 'no')->whereIn('asset_category_id', $assetCatIds)->get()->toArray();
        return $data;
    }

    /**
     * Filter and retrieve supervisor data based on user and service provider.
     *
     * @param  \App\Models\User  $user
     * @param  int  $serviceProviderId
     * @return \Illuminate\Support\Collection
     */
    public static function getFilteredSupervisors($loggedInUser, $service_provider_id)
    {
        if (!is_array($service_provider_id)) {
            $service_provider_id = explode(',', $service_provider_id);
        }
        $supervisors = [];
        if ($loggedInUser->isBuildingManager() || $loggedInUser->isBuildingManagerEmployee()) {
            // Split the building IDs into an array
            $buildingIds = explode(',', $loggedInUser->building_ids);

            // Query to retrieve supervisors' data along with their associated admin data
            $supervisors_data = User::select('users.id', 'users.name', 'users.sp_admin_id', 'spu.id as sp_id', 'spu.name as sp_name')
                ->join('users as spu', 'spu.id', '=', 'users.sp_admin_id')
                ->where('users.user_type', 'supervisor')
                ->where(function ($query) use ($buildingIds) {
                    if (!empty($buildingIds)) {
                        $buildingIdsString = implode(',', $buildingIds);
                        $query->whereRaw("FIND_IN_SET(?, users.building_ids)", [$buildingIdsString]);
                    }
                })
                ->whereNull('users.deleted_at')
                ->get();

            $supervisors = [];
            $sp_admins = [];

            // Populate arrays with supervisors' and admin data
            foreach ($supervisors_data as $supervisor) {
                $supervisors[] = [
                    'id' => $supervisor['id'],
                    'name' => $supervisor['name']
                ];
                $sp_admins[] = [
                    'id' => $supervisor['sp_id'],
                    'name' => $supervisor['sp_name']
                ];
            }

            // Merge and deduplicate supervisors' and admin data
            $uniqueData = array_merge($supervisors, $sp_admins);
            $uniqueData = collect($uniqueData)->unique('id')->values()->all();

            // Return the final merged and deduplicated data
            return $uniqueData;
        } else if ($loggedInUser->isServiceProviderAdmin()) {
            return $loggedInUser->supervisors()
                ->where('user_type', 'supervisor')
                ->select('id', 'name')
                ->get()
                ->toArray();
        } else {
            return User::select('id', 'name')->whereIn('service_provider', $service_provider_id)
                ->whereIn('user_type', ['supervisor', 'sp_admin'])
                ->get()->toArray();
        }
    }



    public static function getFilteredWorkers(array $filters = [])
    {
        $user = Auth::user();
        $query = User::query()->select('id', 'name')
            ->where('user_type', 'sp_worker')
            ->where('is_deleted', 'no')
            ->whereNull('deleted_at')
            ->where('status', 1);

        switch ($user->user_type) {
            case 'sp_admin':
                $query->where('service_provider', $user->service_provider);
                break;

            case 'supervisor':
                $query->whereRaw("find_in_set(?, supervisor_id)", [$user->id]);
                break;

            case 'sp_worker':
                $query->where('id', $user->id);
                break;

            case 'building_manager_employee':
            case 'building_manager':
                $contractIds = ContractPropertyBuildings::whereIn('property_building_id', explode(',', $user->building_ids))
                    ->pluck('contract_id')
                    ->toArray();
                $query->whereIn('contract_ids', $contractIds);
            break;
            default:
                $query->where('project_user_id', $user->project_user_id);
                break;
        }

        if (!empty($filters)) {
            foreach ($filters as $key => $value) {
                $query->where($key, $value);
            }
        }

        return $query->get()->toArray();
    }

    /**
     * Filter and retrieve work orders data based on user and service provider.
     *
     * @param  \App\Models\User  $user
     * @param  int  $serviceProviderId
     * @return \Illuminate\Support\Collection
     */
    public static function getFilteredWorkOrders($loggedInUser, $service_provider_id = NULL, $search = NULL, $page = NULL, $asset_id = NULL)
    {
        $no_worker_assigned = \App::getLocale() == 'en' ? 'No worker assigned' : 'لم يتم تعيين عامل';
        $started_on_behalf = \App::getLocale() == 'en' ? 'Started on behalf' : 'تم البدء بالنيابة عن العامل';

        $statusOpen = \App::getLocale() == 'en' ? 'Open' : 'مفتوح';
        $statusInProgress = \App::getLocale() == 'en' ? 'In Progress' : 'قيد التنفيذ';
        $statusOnHold = \App::getLocale() == 'en' ? 'On Hold' : 'توقف مؤقتا';
        $statusClosed = \App::getLocale() == 'en' ? 'Closed' : 'مغلق';
        $statusDeleted = \App::getLocale() == 'en' ? 'Deleted' : 'محذوف';
        $statusReopen = \App::getLocale() == 'en' ? 'Re Open' : 'تمت إعادة فتحه';
        $statusScheduled = \App::getLocale() == 'en' ? 'Scheduled' : 'مجدول';
        $statusWarranty = \App::getLocale() == 'en' ? 'Warranty' : 'ضمان';
        // Get the current date and time
        $currentDateTime = Carbon::now();

        $columnIndex = $search['columnIndex']; // Column index from DataTables
        $sortDirection = $search['sortDirection'];  // 'asc' or 'desc'

        // Retrieve work orders data along with related information using Eloquent relationships
        $work_orders_query = WorkOrders::
        select(
            'work_orders.*',
            DB::raw('(SELECT COUNT(*) FROM work_orders AS wo INNER JOIN work_time_frame ON work_time_frame.user_id = wo.project_user_id WHERE wo.work_order_type = "preventive" AND wo.status = 1 AND wo.start_date = "' . $currentDateTime->toDateString() . '" AND wo.id = work_orders.id AND work_time_frame.start_time >= "' . $currentDateTime->toTimeString() . '") AS pm_count'),
            DB::raw('
            CASE
                WHEN work_orders.work_order_type = "preventive" THEN
                    DATE_FORMAT(
                        DATE_ADD(
                            CONCAT(work_orders.start_date, " ", COALESCE(wf.start_time, "00:00:00")),
                            INTERVAL (CASE WHEN work_orders.status = 1 THEN 0 ELSE COALESCE(work_orders.wtf_start_time, 0) END) SECOND
                        ), "%d-%m-%Y %H:%i"
                    )
                ELSE
                    DATE_FORMAT(work_orders.created_at, "%d-%m-%Y %H:%i")
            END AS submission_date
        '),
            DB::raw('
            CASE
                WHEN work_orders.worker_id = 0 AND work_orders.job_started_at IS NULL THEN \'' . $no_worker_assigned . '\'
                WHEN work_orders.assigned_to != \'sp_worker\' AND work_orders.job_started_at IS NOT NULL THEN \'' . $started_on_behalf . '\'
                ELSE us.name
            END AS assigned_worker
        '),
            DB::raw("
            CASE
                WHEN contract_type = 'regular' THEN
                    CASE work_orders.status
                        WHEN 1 THEN '$statusOpen'
                        WHEN 2 THEN '$statusInProgress'
                        WHEN 3 THEN '$statusOnHold'
                        WHEN 4 THEN '$statusClosed'
                        WHEN 5 THEN '$statusDeleted'
                        WHEN 6 THEN '$statusReopen'
                        WHEN 7 THEN '$statusScheduled'
                        WHEN 8 THEN '$statusScheduled'
                        ELSE ''
                    END
                ELSE
                    CASE
                        WHEN work_orders.status = 4 THEN '$statusClosed'
                        ELSE '$statusWarranty'
                    END
            END AS status_html
    "),
            'mr.phone as mr_phone'

        )
            ->leftJoin('work_time_frame as wf', 'work_orders.project_user_id', '=', 'wf.user_id')
            ->leftJoin('maintanance_request as mr', 'work_orders.maintanance_request_id', '=', 'mr.id')
            ->leftJoin('users as us', 'work_orders.worker_id', '=', 'us.id')
            ->with([
                'contract',
                'propertyBuilding.property',
                'worker',
                'chatMessages',
                'relatedWorkOrders',
                'chatMessages',
                'slaAssetCategory',
                'contractPriority',
                'frequencyMaster.frequencies.contractFrequencies',
                'workTimeFrame'
            ])
            ->where('work_orders.is_deleted', '!=', 'yes');

        if ($page == "pending-work-order-list") {
            // Apply user-specific conditions based on user types using the helper function
            $work_orders_query = Helper::applyBMWorkOrderUserConditions($work_orders_query, $loggedInUser, $loggedInUser->id, $service_provider_id);
        } else if ($page == "request-work-order-list") {
            // Apply user-specific conditions based on user types using the helper function
            $work_orders_query = Helper::applyWaitingForActionWorkOrderUserConditions($work_orders_query, $loggedInUser, $loggedInUser->id, $service_provider_id);
        } else {
            // Use the helper function to apply page and service provider conditions
            $work_orders_query = Helper::applyPageConditions($work_orders_query, $page, $service_provider_id);

            // Apply user-specific conditions based on user types using the helper function
            $work_orders_query = Helper::applyWorkOrderUserConditions($work_orders_query, $loggedInUser, $loggedInUser->id, $service_provider_id);
        }

        // Get the filtered list of work orders using the applyFilters function
        $work_orders_query = Helper::applyFilters($work_orders_query, $search, $asset_id);

        $cloned_work_orders_query = clone $work_orders_query; // Clone the $work_orders_query query to preserve the original $work_orders_query

        // Define the statuses you want to count
        $statusesToCount = [1, 2, 3, 4, 5, 6, 7, 8];

        $statusCounts = [];
        foreach ($statusesToCount as $status) {
            $searchFiltered['status'] = $status;
            $statusKey = strtolower(WorkOrders::getStatusString($status)) . '_row_count'; // Or use any other logic to generate the key
            // Apply the conditions based on status using the helper function
            $statusCounts[$statusKey] = Helper::applyStatusConditions($work_orders_query, $status)->count();
        }
        // Apply the conditions based on status using the helper function
        $cloned_work_orders_query = Helper::applyStatusConditions($cloned_work_orders_query, $search['status']);
        $columnIndex = (int) $columnIndex;


        // Get the count
        $count = $cloned_work_orders_query->having('pm_count', '=', 0)->count();



        // Apply sorting logic based on column index
        $final_query = Helper::applySorting($cloned_work_orders_query, $columnIndex, $sortDirection);

        if (isset($search['start']) && isset($search['length'])) {
            $final_query->skip($search['start'])->take($search['length']);
        }



        $work_orders = $final_query->get();
        return [
            'count' => $count,
            'result' => $work_orders,
            'statusCounts' => $statusCounts
        ];
    }

    /**
     * Generate DataTable using Yajra DataTables package.
     *
     * @param  mixed $sqlList
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public static function generateDatatable($sqlList, $data)
    {
        return Datatables::of($sqlList)
            ->editColumn('selecting', function ($sqlList) {
                return self::renderCheckbox($sqlList->id);
            })
            ->editColumn('project_admin_id', function ($sqlList) {
                return self::getProjectAdminLinkHtml($sqlList->project_user_id, $sqlList->id);
            })
            ->editColumn('work_order_id', function ($sqlList) {
                return self::generateWorkOrderIdColumnHtml($sqlList);
            })
            ->editColumn('description', function ($sqlList) {
                return self::getDescriptionColumnHtml($sqlList->description);
            })
            ->editColumn('property', function ($sqlList) {
                return self::getFormattedPropertyName($sqlList->propertyBuilding);
            })
            ->editColumn('supervisor_name', function ($sqlList) {
                return self::getFormattedSupervisorsOrServiceProviderName(
                    $sqlList->supervisor_id,
                    $sqlList->assigned_to,
                    $sqlList->worker_id,
                    $sqlList->service_provider_id
                );
            })
            // ->editColumn('assigned_worker', function ($sqlList) {
            //     return self::generateAssignedWorkerColumnContent($sqlList);
            // })
            ->editColumn('workorder_journey', function ($sqlList) {
                if ($sqlList->status == 4) {
                    $sqlList->workorder_journey = "finished";
                }
                return self::generateWorkorderJourneyColumnContent($sqlList->workorder_journey);
            })
            ->editColumn('mr_place', function ($sqlList) {
                $mr = MaintenanceRequest::where('id', $sqlList->maintanance_request_id)->first();
                return !empty($mr->place) ? $mr->place: '-';
            })
            ->editColumn('apartment_villa', function ($sqlList) {
                // Find user by phone
                $user = User::where('phone', $sqlList->mr_phone)->first();

                // Check if $userID is set and valid, reassign $user if needed
                if (isset($userID) && $userID !== null && $userID != 0) {
                    $user = User::where('id', $userID)->first();
                }

                // Safely check if $user exists
                return !is_null($user) && !is_null($user->apartment) ? $user->apartment : '-';
            })
            ->editColumn('status', function ($sqlList) {
                //return $sqlList->status_html;
                return self::generateStatusColumnContent($sqlList);
            })
            ->editColumn('response_time', function ($sqlList) {
                return self::generateResponseTimeColumnContent($sqlList);
            })
            ->editColumn('pass_fail', function ($sqlList) {
                return self::generatePassFailColumnContent($sqlList);
            })
            // ->editColumn('submission_date', function ($sqlList) {
            //     return self::formatSubmissionDate($sqlList);
            // })
            ->editColumn('target_date', function ($sqlList) {
                return self::generateTargetDateColumnContent($sqlList);
            })
            ->editColumn('floor', function ($sqlList) {
                return !empty($sqlList->floor) ? $sqlList->floor : '-';
            })
            ->editColumn('room', function ($sqlList) {
                return !empty($sqlList->room) ? $sqlList->room : '-';
            })
            ->editColumn('closed_at', function ($sqlList) {
                return $sqlList->job_completion_date
                    ? date('d-m-Y H:i', strtotime($sqlList->job_completion_date))
                    : '---';
            })
            ->with([
                'open_row_count' => $data['open_row_count'],
                'closed_row_count' => $data['closed_row_count'],
                'progress_row_count' => $data['progress_row_count'],
                'hold_row_count' => $data['hold_row_count'],
                'reopen_row_count' => $data['reopen_row_count'],
                'warranty_row_count' => $data['warranty_row_count'],
                'scheduled_row_count' => $data['scheduled_row_count'] // Fix the syntax error here
            ])
            ->rawColumns(['selecting', 'project_admin_id', 'work_order_id', 'description', 'supervisor_name', 'workorder_journey','place','apartment_villa', 'response_time', 'status', 'pass_fail', 'submission_date', 'target_date'])
            ->setTotalRecords($data['total_row_count'])
            ->skipPaging()
            ->make(true);
    }

    /**
     * Get formatted supervisor names with deleted status.
     *
     * @param string $supervisor_ids Comma-separated supervisor IDs.
     * @return string Formatted names of supervisors with deleted status.
     */
    public static function getFormattedSupervisorNamesWithDeletedStatus($supervisor_ids)
    {
        // Retrieve users based on provided supervisor IDs
        $users = User::select('name', 'deleted_at')
            ->whereIn('id', explode(',', $supervisor_ids))
            ->orderBy('id', 'desc')
            ->get();

        // Process and format users' names with deleted status
        $names = $users->map(function ($user) {
            $deleted_at = !empty($user->deleted_at) ? __('general_sentence.modal.deleted') : '';
            return $user->name . $deleted_at;
        })->implode(', ');

        // Return the formatted names
        return $names;
    }

    /**
     * Get the formatted supervisor names or service provider name.
     *
     * @param mixed $supervisors Supervisor IDs or service provider ID.
     * @param string $assigned_to The assigned role ('supervisor' or 'sp_worker').
     * @param int $worker_id Worker ID.
     * @return string Formatted supervisor names or service provider name.
     */
    public static function getFormattedSupervisorsOrServiceProviderName($supervisors, $assigned_to, $worker_id, $service_provider_id)
    {
        if (
            (isset($supervisors) && $supervisors != NULL && $assigned_to == 'supervisor') ||
            (count(explode(',', $supervisors)) == 1 && $assigned_to == 'sp_worker')
        ) {
            $formattedSupervisors = $supervisors;
        } else {
            $formattedSupervisors = $service_provider_id;
        }

        $formattedSupervisors = self::getFormattedSupervisorNamesWithDeletedStatus($formattedSupervisors);

        if ($formattedSupervisors != '' && ($worker_id != 0 || $assigned_to != 'sp_worker')) {
            return $formattedSupervisors;
        } else {
            return __('work_order.table.no_action_taken_from_sps');
        }
    }

    /**
     * Get the formatted property name based on property type.
     *
     * @param object $propertyBuilding Property Building object.
     * @return string Formatted property name.
     */
    public static function getFormattedPropertyName($propertyBuilding)
    {
        $isDeleted = ($propertyBuilding->property->deleted_at != null || $propertyBuilding->deleted_at) ? __('general_sentence.modal.deleted') : null;
        if ($propertyBuilding->property->property_type == 'complex') {
            return $propertyBuilding->property->complex_name . ' ' . $propertyBuilding->building_name . $isDeleted;
        } else {
            return $propertyBuilding->building_name . $isDeleted;
        }
    }

    /**
     * Get the HTML for a project admin link.
     *
     * @param int $project_user_id Project user ID.
     * @param string $url URL for the project admin link.
     * @return string Formatted HTML for the project admin link.
     */
    public static function getProjectAdminLinkHtml($project_user_id, $id)
    {
        $url = route('workorder.show', Crypt::encryptString($id));
        return '<div class="d-flex">
                    <div class="userDatatable-inline-title">
                        <a href="' . $url . '">
                            <span class="fw-400 badge badge-round badge-grey badge-lg badge">' . Helper::getProjectNameByAdmin($project_user_id) . '</span>
                        </a>
                    </div>
                </div>';
    }

    /**
     * return checkbox Html.
     * @return string Formatted HTML checkbox.
     */
    public static function renderCheckbox($description = null)
    {
        return '<td class="d-flex align-items-center check-hover w-30px">
        <span class="projectDatatable-title d-flex align-items-center"><input onclick="checkBox()" type="checkbox" name="select_wo" value="' . $description . '" id="' . $description . '" class="mt-1 mb-1"></span>
        </td>';
    }

    /**
     * Get the HTML for a description column with title attribute.
     *
     * @param string $description Description text.
     * @return string Formatted HTML for the description column.
     */
    public static function getDescriptionColumnHtml($description)
    {
        return '<span class="max-td" title="' . $description . '">' . $description . ' </span>';
    }

    public static function hasUnresolvedRequestedItem($work_order_id)
    {
        $serviceProviderMissingItemCount = ServiceProviderMissingItemRequest::query()
            ->where('status', 'requested')
            ->where('work_order_id', $work_order_id)
            ->count();

        $workOrderItemRequestCount = WorkOrderItemRequest::query()
            ->where('status', 'requested')
            ->where('work_order_id', $work_order_id)
            ->count();

        return (bool) ($serviceProviderMissingItemCount + $workOrderItemRequestCount);
    }

    /**
     * Generate the HTML for the work order ID column.
     *
     * @param object $sqlList The work order data.
     * @return string HTML markup for the work order ID column.
     */
    public static function generateWorkOrderIdColumnHtml($sqlList)
    {
        $user_id = Auth::user()->id;
        $url = '';

        if (Helper::checkLoggedinUserPrivileges('view', 'workorder')['success']) {
            $url = route('workorder.show', Crypt::encryptString($sqlList->id));
        }

        $related_wos = $sqlList->relatedWorkOrders()->count();
        $badgeClass = $related_wos > 1 ? 'badge-success' : 'badge-primary';
        $badgeClass = static::hasUnresolvedRequestedItem($sqlList->id) ? 'badge-warning' : $badgeClass;

        $styleOverride = '';

        if ($sqlList->status == 2) {
            $styleOverride = 'style="background: #e0f0ff; color: #5eb1ff; border-color: #5eb1ff"';
        }

        $unreadMsgNoti = '<span class="unreadMsgNoti">' . $sqlList->setIsReadAttribute($user_id) . '</span>';
        $workOrderIdLink = '<a href="' . $url . '"><span ' . $styleOverride . ' class="fw-400 badge badge-round ' . $badgeClass . ' badge-lg badge-outlined">' . $sqlList->work_order_id . '</span></a>';

        return '<div class="d-flex">' . $unreadMsgNoti . '
                <div class="userDatatable-inline-title">' . $workOrderIdLink . '</div>
            </div>';
    }

    /**
     * Generate the content for the assigned worker column.
     *
     * @param object $sqlList The work order data.
     * @return string Content for the assigned worker column.
     */
    public static function generateAssignedWorkerColumnContent($sqlList)
    {
        if ($sqlList->worker_id == 0 && $sqlList->job_started_at == NULL) {
            return __('work_order.table.no_worker_assigned');
        } elseif ($sqlList->assigned_to != 'sp_worker' && $sqlList->job_started_at != NULL) {
            return __('work_order.table.started_on_behalf');
        } else {
            return $sqlList->worker ? $sqlList->worker->name : __('work_order.table.started_on_behalf');
        }
    }

    /**
     * Generate the content for the workorder journey column.
     *
     * @param string $workorderJourney The work order journey value.
     * @return string Content for the workorder journey column.
     */
    public static function generateWorkorderJourneyColumnContent($workorderJourney)
    {
        $journeyMap = [
            'submitted' => __('work_order.table.submitted'),
            'job_execution' => __('work_order.table.job_execution'),
            'job_evaluation' => __('work_order.table.Job_Evaluation'),
            'job_approval' => __('work_order.table.Job_Approval'),
            'finished' => __('work_order.table.finished'),
        ];

        return $journeyMap[$workorderJourney] ?? '';
    }

    /**
     * Generate the content for the status column.
     *
     * @param object $sqlList The work order data.
     * @return string Content for the status column.
     */
    public static function generateStatusColumnContent($sqlList)
    {
        if ($sqlList->contract_type == "regular") {
            switch ($sqlList->status) {
                case 1:
                    return '<span class="bg-opacity-success color-success rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.open') . '</span>';
                case 2:
                    return '<span class="badge-in-progress rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.in_progress') . '</span>';
                case 3:
                    return '<span class="bg-opacity-warning color-warning rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.on_hold') . '</span>';
                case 4:
                    return '<span class="bg-opacity-dark color-light rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.closed') . '</span>';
                case 5:
                    return '<span class="bg-opacity-danger color-danger rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.deleted') . '</span>';
                case 6:
                    return '<span class="bg-opacity-primary color-primary rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.re_open') . '</span>';
                case 7:
                    return '<span class="bg-opacity-dark color-dark rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.scheduled') . '</span>';
                case 8:
                    return __('work_order.bread_crumbs.scheduled');
                default:
                    return '';
            }
        } else {
            if ($sqlList->status == 4) {
                return '<span class="bg-opacity-dark color-light rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.closed') . '</span>';
            } else {
                return '<span class="bg-opacity-secondary color-secondary rounded-pill userDatatable-content-status active">' . __('work_order.bread_crumbs.warrenty') . '</span>';
            }
        }
    }

    /**
     * Generate the content for the response time column.
     *
     * @param object $sqlList The work order data.
     * @return string Content for the response time column.
     */
    public static function generateResponseTimeColumnContent($sqlList)
    {
        $workOrderSlaInfo = self::getWorkOrderSlaInfo($sqlList);
        $timeLeft = self::calculateTimeLeft($sqlList, $workOrderSlaInfo['response_time'], $workOrderSlaInfo['response_time_type']);

        if ($timeLeft == "-") {
            return "-";
        }

        if ($sqlList->response_time === 'On time' || $sqlList->response_time === 'Late') {
            $responseTime = $sqlList->response_time;
        } else {
            $responseTime = $timeLeft > 0 ? $timeLeft : 'Late';
        }

        if ($sqlList->sp_approove === 0) {
            return __('work_order.bread_crumbs.automatically_assigned');
        }

        if ($sqlList->work_order_type === OrderType::PREVENTIVE->value) {
            $created_at = $sqlList->start_date . ' ' . $sqlList->wtf_start_time;
        } elseif ($sqlList->work_order_type === OrderType::REACTIVE->value) {
            $startDate  = Carbon::parse($sqlList->start_date);
            $targetDate = Carbon::parse($sqlList->target_date);
            // Skip counting if start_date is in the future OR target_date has not been reached
            if (! $startDate->isToday() || $targetDate->isFuture()) {
                return '-';
            }
            $created_at = $sqlList->start_date . ' ' . $sqlList->wtf_start_time;
        }

        $datetime1 = strtotime($created_at);
        $datetime2 = strtotime($sqlList->job_started_at);
        $interval = abs($datetime2 - $datetime1);

        if ($interval === 0) {
            return __('work_order.bread_crumbs.automatically_assigned');
        }

        if ($sqlList->contract_type === "warranty" || $sqlList->status === "5") {
            return '-';
        }

        if ($responseTime === 'On time') {
            return __('work_order.bread_crumbs.on_time');
        }

        if ($responseTime === 'Late') {
            return __('work_order.bread_crumbs.Late');
        }

        $ret = '';
        $interval = $responseTime * 60;
        $days = intval($interval / (3600 * 24));

        if ($days > 0) {
            $ret .= "$days " . __("configration_assets.comminucation_table.Days") . ' ';
        }

        $hours = ($interval / 3600) % 24;

        if ($hours > 0) {
            $ret .= "$hours " . __("configration_assets.comminucation_table.Hours") . ' ';
        }

        $minutes = ($interval / 60) % 60;

        if ($minutes > 0) {
            $ret .= "$minutes " . __("configration_assets.comminucation_table.Minutes") . ' ';
        }

        return $ret;
    }

    /**
     * Generate the content for the pass/fail column.
     *
     * @param object $sqlList The work order data.
     * @return string HTML badge element representing pass/fail status.
     */
    public static function generatePassFailColumnContent($sqlList)
    {
        if ($sqlList->pass_fail != 'pending') {
            $pass_fail = $sqlList->pass_fail;
        } elseif ($sqlList->workorder_journey != 'job_approval') {
            $pass_fail = 'pending';
        }

        if ($sqlList->contract_type == "warranty" || $sqlList->status == "5") {
            return '-';
        }

        if (isset($pass_fail ) && $pass_fail == 'pass') {
            $color = 'color-white bg-success';
            $passFailStatus = __('work_order.bread_crumbs.pass');
        } elseif (isset($pass_fail ) && $pass_fail == 'fail') {
            $color = 'color-white bg-danger';
            $passFailStatus = __('work_order.bread_crumbs.fail');
        } else {
            $color = 'color-white bg-light';
            $passFailStatus = __('work_order.bread_crumbs.pending');
        }

        return '<span class="media-badge ' . $color . '">' . $passFailStatus . '</span>';
    }

    /**
     * Format the submission date.
     *
     * @param object $sqlList The work order data.
     * @return string Formatted submission date.
     */
    public static function formatSubmissionDate($sqlList)
    {
        // Get the current time
        $currentTime = now();

        if ($sqlList->work_order_type == "preventive") {
            // Retrieve the work time frame settings for the user
            $wtfs = $sqlList->workTimeFrame;

            // Determine the appropriate start time based on work order type
            $startTime = $sqlList->status == 1
                ? ($wtfs ? $wtfs->start_time : "00:00:00")
                : ($sqlList->wtf_start_time ?: ($wtfs ? $wtfs->start_time : "00:00:00"));

            // Calculate the submission date
            $createdAt = $sqlList->start_date . ' ' . $startTime;
            $submissionDate = date('Y-m-d H:i', strtotime($createdAt));

            // Format the submission date
            return date('d-m-Y H:i', strtotime($submissionDate));
        } else {
            // For non-preventive work orders, use the created_at field
            $submissionDate = $sqlList->created_at;

            // Format the submission date
            return date('d-m-Y H:i', strtotime($submissionDate));
        }
    }


    /**
     * Generate the content for the target date column.
     *
     * @param object $sqlList The work order data.
     * @return string Formatted target date or other status text.
     */
    public static function generateTargetDateColumnContent($sqlList)
    {
        $targetDate = self::generateTargetDate($sqlList);

        if ($sqlList->status == "5") {
            return '-';
        } elseif ($sqlList->contract_type == "warranty") {
            return date('d-m-Y H:i', strtotime($sqlList->target_date));
        } elseif ($sqlList->job_started_at == NULL) {
            return __('work_order.table.Waiting_for_supervisor');
        } else {
            return date('d-m-Y H:i', strtotime($targetDate));
        }
    }

    public static function generateTargetDate($sqlList)
    {
        // Check if the job is not started or has an approval issue
        if ($sqlList->job_started_at == NULL || $sqlList->bm_approve_issue == 2) {
            // If so, keep the original target date as is
            return $sqlList->target_date;
        } else {
            // Get work order SLA information
            $workOrderSlaInfo = self::getWorkOrderSlaInfo($sqlList);

            // Calculate the new target date based on service window
            $newTargetDate = Carbon::parse($sqlList->job_started_at)
                ->addMinutes($workOrderSlaInfo['service_window'] * self::getServiceWindowMultiplier($workOrderSlaInfo['service_window_type']));

            // Format the new target date
            return $newTargetDate->format('d-m-Y H:i');
        }
    }

    // Helper function to get the service window multiplier based on type
    private static function getServiceWindowMultiplier($type)
    {
        switch ($type) {
            case 'days':
                return 1440; // Convert days to minutes
            case 'hours':
                return 60; // Convert hours to minutes
            case 'minutes':
            default:
                return 1; // Keep minutes as is
        }
    }

    /**
     * Retrieve SLA-related information for a given work order.
     *
     * @param \App\Models\WorkOrder $sqlList The work order instance.
     * @return array An array containing the calculated SLA-related values.
     */
    public static function getWorkOrderSlaInfo($sqlList)
    {
        // Initialize default values
        $response_time = 0;
        $service_window = 0;
        $response_time_type = 'hours';
        $service_window_type = 'minutes';
        $contractPriority = null;

        // Get the contract number associated with the work order
        $contract_number = $sqlList->contract()->value('contract_number');

        // Access the related slaAssetCategory for each work order
        $slaAssetCategory = $sqlList->slaAssetCategory()->where('contract_number', $contract_number)->first();

        // Calculate SLA information based on work order type
        if ($sqlList->work_order_type == "reactive") {
            Log::info('Checking Priority conditions');
            if (!empty($sqlList->wo_priority_id) && $sqlList->wo_priority_id != 0) {
                $contractPriority = self::getContractPriority($sqlList->wo_priority_id);
                Log::info('Priority Id: '.$sqlList->wo_priority_id);
            } else if ($slaAssetCategory) {
                $contractPriority = $slaAssetCategory->contractPriority()->where('contract_number', $contract_number)->first();
                Log::info('Skipped the first condition');
            }
            if (isset($contractPriority) && $contractPriority) {
                $response_time = $contractPriority->response_time ?? 0;
                $service_window = $contractPriority->service_window ?? 0;
                $response_time_type = $contractPriority->response_time_type ?? NULL;
                $service_window_type = $contractPriority->service_window_type ?? NULL;
            }
        } elseif ($sqlList->work_order_type == "preventive" && $sqlList->priority_id != 0) {
            $contractPriority = $sqlList->contractPriority()->where('contract_number', $contract_number)->first();
            $response_time = $contractPriority->response_time ?? 0;
            $service_window = $contractPriority->service_window ?? 0;
            $response_time_type = $contractPriority->response_time_type ?? NULL;
            $service_window_type = $contractPriority->service_window_type ?? NULL;
        } else {
            $contractFrequency = $sqlList->frequencyMaster;
            if ($contractFrequency) {
                $contractFrequency = $contractFrequency->frequencies;
                foreach ($contractFrequency as $frequency) {
                    $contractFrequencies = $frequency->contractFrequencies;
                    foreach ($contractFrequencies as $contractFrequency) {
                        $contractFrequencies = $contractFrequency->where('contract_number', $contract_number)->first();
                    }
                }
                $response_time = $contractFrequencies->response_time ?? 0;
                $service_window = $contractFrequencies->service_window ?? 0;
                $response_time_type = $contractFrequencies->response_time_type ?? NULL;
                $service_window_type = $contractFrequencies->service_window_type ?? NULL;
            }
        }

        // Return the calculated values
        return [
            'response_time' => $response_time,
            'service_window' => $service_window,
            'response_time_type' => $response_time_type,
            'service_window_type' => $service_window_type,
        ];
    }

    // Function to get contract priority
    protected static function getContractPriority($priorityId)
    {
        return DB::table('priorities')
            ->where('id', $priorityId)
            ->orderBy('id', 'desc')
            ->first();
    }

    /**
     * Calculate the time left for a work order based on response time and creation time.
     *
     * @param array $workOrder The work order data.
     * @param int $responseTime The response time in the specified units.
     * @param string $responseTimeType The response time unit ("days" or "hours").
     * @return int The time left in minutes.
     */
    public static function calculateTimeLeft($workOrder, $responseTime, $responseTimeType)
    {
        // Retrieve the work time frame settings for the user
        $wtfs = $workOrder->workTimeFrame;

        // Determine the appropriate start time based on work order type
        $startTime = $workOrder['status'] == 1
            ? ($wtfs ? $wtfs->start_time : "00:00:00")
            : ($workOrder['wtf_start_time'] ?: ($wtfs ? $wtfs->start_time : "00:00:00"));

        $createdAt = $workOrder['created_at'];
        if ($workOrder['work_order_type'] == OrderType::PREVENTIVE->value) {
            $createdAt = $workOrder['start_date'] . ' ' . $startTime;
        }
        elseif($workOrder['work_order_type'] == OrderType::REACTIVE->value) {
            $startDate  = Carbon::parse($workOrder['start_date']);
            $targetDate = Carbon::parse($workOrder['target_date']);
            // Only calculate response time if start_date is today AND target_date has passed
            if (! $startDate->isToday() || $targetDate->isFuture()) {
                return '-'; // Skip calculation if scheduled for a future date/time
            }
            $createdAt = $workOrder['start_date'] . ' ' . $startTime;
        }

        // Calculate the time difference between now and work order creation time
        // Assuming $createdTime and $currentTime are valid Carbon instances
        $createdTime = Carbon::parse($createdAt); // Convert to Carbon instance
        $currentTime = Carbon::now(); // Current time as a Carbon instance

        // Check if $createdTime is greater than $currentTime
        if ($createdTime->gt($currentTime)) {
            // If $createdTime is in the future, return a hyphen
            return '-';
        }

        $timeInterval = $currentTime->diffInMinutes($createdTime);

        // Convert response time to minutes based on the specified type
        if ($responseTimeType == "days") {
            $responseTime *= 1440;
        } elseif ($responseTimeType == "hours") {
            $responseTime *= 60;
        }

        // Calculate the time left
        $timeLeft = $responseTime - $timeInterval;
        return $timeLeft;
    }

    /**
     * Get the route name based on the given type.
     *
     * @param string $type The type value
     * @return string The route name
     */
    public static function getRedirectRouteBasedOnType($type)
    {
        // Map between type and route name
        $typeToRouteMap = [
            'all' => 'workorder.workorders.list',
            'open' => 'workorder.workorders.open.list',
            'in-progress' => 'workorder.workorders.in_progress.list',
            'spare-parts' => 'workorder.workorders.spare_parts.list',
            'under-evaluation' => 'workorder.workorders.under_evaluation.list',
            'deleted' => 'workorder.workorders.list',
            'closed' => 'workorder.workorders.closed.list'
        ];

        // Get the route name from the map, or use a default route if not found
        return $typeToRouteMap[$type] ?? 'workorder.workorders.list';
    }

    /**
     * Get notifications for a worker.
     *
     * @param  int     $worker_id          The ID of the worker
     * @param  int     $count              Number of notifications to skip
     * @param  string  $selected_language  The selected language for translations
     * @return \Illuminate\Support\Collection
     */
    public static function notifications($worker_id, $count, $selected_language,$isRead)
    {
        // Authenticate user
        $user = auth()->user();
        $result = array();
        // Build notifications query
        $notificationsQuery = Notification::with(['workOrder'])
            ->where('section_type', 'worker_app')
            ->whereRaw("find_in_set($worker_id, user_id)")
            ->orderBy('created_at', 'desc');

            if($isRead != '')
            {
                $notificationsQuery = $notificationsQuery->where('is_read', $isRead);
            }

        $countUnreadnotification = clone $notificationsQuery;

        // Apply pagination
        if ($count > 0) {
            $notificationsQuery->offset($count * 10)->limit(10);
        } else {
            $notificationsQuery->limit(10);
        }

        // Get notifications
        $notifications = $notificationsQuery->get();

        // Transform and process notifications
        if ($notifications->isNotEmpty()) {
            foreach ($notifications as $notification) {
                $notification = self::transformNotification($notification, $selected_language);
            }
        }

        return [
            'unread_notification_count' => $countUnreadnotification->whereNull('is_read')->count(),
            'notifications' => $notifications
        ];
    }

    /**
     * Transform the notification by adding additional information.
     *
     * @param  \App\Models\Notification  $notification      The notification instance
     * @param  string                    $selected_language The selected language for translations
     * @return \App\Models\Notification  The transformed notification instance
     */
    public static function transformNotification($notification, $selected_language)
    {
        // Check if the notification is associated with a work order
        if ($notification->workOrder) {
            $notification->work_order_id = trim($notification->workOrder->work_order_id);
            // Determine and set the work order's status
            $workorderStatus = self::determineStatus($notification->workOrder, $notification->notification_type, $selected_language);
            $notification->workorder_status = $workorderStatus;
            if ($notification->notification_type == "maintenance_request_accepted") {
                $notification->section_id = $notification->workOrder->maintanance_request_id;
                $notification->work_order_id = trim($notification->workOrder->maintanance_request_id) != "" ? '#' . trim($notification->workOrder->maintanance_request_id) : '';
            }
        } else {
            // Set work order ID and status to empty if not associated
            $notification->work_order_id = trim($notification->section_id) != "" ? '#' . trim($notification->section_id) : '';
            if (in_array($notification->notification_type, array('approve_availability_change_re', 'approve_availability_change_request', 'reject_availability_change_req', 'reject_availability_change_request'))) {
                $notification->work_order_id = "";
            }

            if (in_array($notification->notification_type, array('approve_availability_change_re', 'approve_availability_change_request'))) {
                $notification->notification_sub_type = "approve_availability_change_request";
            }
            //$workorderStatus = self::determineStatus($notification->workOrder, $notification->notification_type, $selected_language);
            $notification->workorder_status = '';
            if ($notification->notification_type == "maintenance_request_rejected") {
                $notification->workorder_status = 'rejected';
            }
        }

        if ($notification->notification_type == "maintenance_request_accepted" || $notification->notification_type == "maintenance_request_rejected") {
            $notification->notification_sub_type = null;
        }

        $notification->clock_out_property_name = '';
        $notification->attendance_id = '';
        $notification->clock_out_datetime = '';

        if ($notification->notification_type == 'clock_out_by_sp') {
            $attendance_data = WorkerAttendances::where('id', $notification->additional_param)->first();
            if ($attendance_data) {
                $notification->clock_out_property_name = PropertyBuildings::where('id', $attendance_data->clock_out_property_id)->pluck('building_name')->first();
                ;
                $notification->attendance_id = $attendance_data->attendance_id;
                $notification->clock_out_datetime = $attendance_data->clock_out_datetime;
            }
        }
        if (in_array($notification->notification_type,['approve_availability_change_re', 'approve_availability_change_request', 'reject_availability_change_req', 'reject_availability_change_request','leave_request_expire','leave_request_end','leave_request_start','leave_terminated'])) {

            $notification->work_order_id = ManageWorkerAvailabilityStatus::where('id', $notification->section_id)->value('leave_request_id');
        }

        // Translate the notification message based on the selected language
        $notification->message = self::translateMessage($notification->message, $selected_language);

        // Get and set the project name for the notification
        $notification->project_name = self::getProjectName($notification);

        // Format and set the posted date of the notification
        $notification->posted_date = self::formatPostedDate($notification->created_at);

        return $notification;
    }

    /**
     * Determine the status for a given work order data.
     *
     * @param  \App\Models\WorkOrders  $wo_data  The work order instance
     * @param  string|null             $notification_type The type of the notification
     * @param  string|null             $selected_language The selected language for translations
     * @return string  The determined work order status
     */
    public static function determineStatus($wo_data, $notification_type = null, $selected_language = null)
    {
        // Check if the work order is related to a maintenance request notification
        if ($notification_type && in_array($notification_type, ["maintenance_request_accepted", "maintenance_request_rejected"])) {
            // Handle maintenance request notification
            if ($notification_type === "maintenance_request_accepted") {
                return $selected_language === 'ar' ? 'approved' : 'approved';
            } else {
                return $selected_language === 'ar' ? 'rejected' : 'rejected';
            }
        }

        // Check if the work order is related to a spart part request notification
        // if ($notification_type && $wo_data->worker_started_at == NULL && in_array($notification_type, ["wo_item_request_accepted", "wo_item_request_rejected", "wo_item_request_partially_given"])) {
        //     // Handle maintenance request notification
        //         return 'examine';
        // }

        if ($wo_data->worker_started_at == NULL && $wo_data->examine_button_clicked_at != NULL) {
            // Handle maintenance request notification
            return 'examine';
        }

        // Status determination logic for regular work orders
        if ($wo_data->status == 2 && ($wo_data->workorder_journey == 'job_execution' || $wo_data->workorder_journey == 'job_evaluation') && ($wo_data->bm_approve_job == 1 || $wo_data->sp_approve_job == 1)) {
            return 'Rejected';
        } elseif ($wo_data->status == 6) {
            return 'Re Opened';
        } elseif ($wo_data->status == 3 && ($wo_data->workorder_journey == 'job_execution' || $wo_data->workorder_journey == 'job_evaluation')) {
            return 'On Hold';
        } elseif ($wo_data->target_date < now() && $wo_data->status == 2 && $wo_data->workorder_journey == 'job_execution') {
            return strtotime($wo_data->worker_started_at) > 0 ? 'Inprogress' : 'Over Due';
        } elseif (strtotime($wo_data->job_submitted_at) > 0 || $wo_data->job_completed_by === 'worker' || $wo_data->job_completed_by === 'SP') {
            //return $wo_data->status == 2 ? '' : 'Completed';
            return 'Completed';
        } elseif ($wo_data->status == 2 && $wo_data->workorder_journey == 'job_execution' && $wo_data->sp_approve_job == 0 && $wo_data->bm_approve_job == 0 && strtotime($wo_data->worker_started_at) > 0) {
            return 'Inprogress';
        } else {
            return '';
        }
    }

    /**
     * Translate a given message based on the selected language.
     *
     * @param  string  $message  The message to be translated
     * @param  string  $selected_language  The selected language for translations
     * @return string  The translated message
     */
    public static function translateMessage($message, $selected_language)
    {
        // Define translation mappings based on language
        $translations = [
            'ar' => [
                'A work order rejected' => 'تم رفض أمر العمل',
                'A work order has been rejected' => 'تم رفض أمر العمل',
                'ورک آرڈر مسترد کر دیا گیا ہے' => 'تم رفض أمر العمل',
                'تم رفض أمر العمل' => 'تم رفض أمر العمل',

                'A new work order assigned' => 'أمر عمل جديد تم تعيينه',
                'A new work order has been assigned' => 'أمر عمل جديد تم تعيينه',
                'نیا ورک آرڈر دیا گیا ہے' => 'أمر عمل جديد تم تعيينه',
                'أمر عمل جديد تم تعيينه' => 'أمر عمل جديد تم تعيينه',

                'A work order has been reopened' => 'تمت إعادة فتح أمر العمل',
                'ورک آرڈر دوبارہ کھول دیا گیا ہے' => 'تمت إعادة فتح أمر العمل',
                'تمت إعادة فتح أمر العمل' => 'تمت إعادة فتح أمر العمل',

                'Spare part request has been Approved' => 'تم رفض طلب قطع الغيار',
                'اسپیئر پارٹ کی درخواست منظور کر لی گئی ہے' => 'تم رفض طلب قطع الغيار',
                'تم رفض طلب قطع الغيار' => 'تم رفض طلب قطع الغيار',

                'Your status request to offline has been rejected' => 'تم رفض طلب تغيير الحالة الى غير متواجد',
                'آپ کی آف لائن اسٹیٹس کی درخواست کو مسترد کر دیا گیا ہے' => 'تم رفض طلب تغيير الحالة الى غير متواجد',
                'تم رفض طلب تغيير الحالة الى غير متواجد' => 'تم رفض طلب تغيير الحالة الى غير متواجد',

                'Your status request to offline has been approved' => 'تم قبول طلب تغيير الحالة الى غير متواجد',
                'آپ کی آف لائن حیثیت کی درخواست منظور کر لی گئی ہے' => 'تم قبول طلب تغيير الحالة الى غير متواجد',
                'تم قبول طلب تغيير الحالة الى غير متواجد' => 'تم قبول طلب تغيير الحالة الى غير متواجد',

                'Your status has been set to offline by the supervisor' => 'المشرف غير الحالة الى غير متواجد',
                'المشرف غير الحالة الى غير متواجد' => 'المشرف غير الحالة الى غير متواجد',
                'آپ کا اسٹیٹس سپروائزر کے ذریعہ آف لائن پر سیٹ کر دیا گیا ہے' => 'المشرف غير الحالة الى غير متواجد',
                'Your status have been set to offline by supervisor' => 'المشرف غير الحالة الى غير متواجد',

                'Your status has been changed to "Vacation"' => 'تم تغيير حالتك إلى "إجازة”',
                'آپ کی حیثیت کو "چھٹی" میں تبدیل کر دیا گیا ہے' => 'تم تغيير حالتك إلى "إجازة”',
                'تم تغيير حالتك إلى "إجازة”' => 'تم تغيير حالتك إلى "إجازة”',

                'Your status has been changed to "Not Available”' => 'تم تغيير حالتك إلى "غير متاح”',
                'آپ کی حیثیت کو "دستیاب نہیں" میں تبدیل کر دیا گیا ہے' => 'تم تغيير حالتك إلى "غير متاح”',
                'تم تغيير حالتك إلى "غير متاح”' => 'تم تغيير حالتك إلى "غير متاح”',

                'Your status has been changed to "Work Injury”' => 'تم تغيير حالتك إلى "إصابة عمل”',
                'آپ کی حیثیت کو "کام کی چوٹ" میں تبدیل کر دیا گیا ہے' => 'تم تغيير حالتك إلى "إصابة عمل”',
                'تم تغيير حالتك إلى "إصابة عمل”' => 'تم تغيير حالتك إلى "إصابة عمل”',

                'Spare part request has been Rejected' => 'تمت الموافقة على طلب قطع الغيار',
                'اسپیئر پارٹ کی درخواست مسترد کر دی گئی ہے' => 'تمت الموافقة على طلب قطع الغيار',
                'تمت الموافقة على طلب قطع الغيار' => 'تمت الموافقة على طلب قطع الغيار',

                'The maintenance request has been Approved' => 'بلاغ الصيانة المرسل تم قبوله',
                'بلاغ الصيانة المرسل تم قبوله' => 'بلاغ الصيانة المرسل تم قبوله',
                'مینٹیننس کی درخواست منظور کر دی گئی ہے' => 'بلاغ الصيانة المرسل تم قبوله',
                'The maintenance request has been Rejected' => 'بلاغ الصيانة المرسل تم رفضه',
                'بلاغ الصيانة المرسل تم رفضه' => 'بلاغ الصيانة المرسل تم رفضه',
                'مینٹیننس کی درخواست مسترد کر دی گئی ہے' => 'بلاغ الصيانة المرسل تم رفضه',
                'لاغ الصيانة المرسل تم رفضه' => 'بلاغ الصيانة المرسل تم رفضه',

                'Your ongoing leave request has been terminated' => 'لقد تم إنهاء طلب المغادرة الخاص بك',
                'آپ کی جاری چھٹی کی درخواست منسوخ کر دی گئی ہے' => 'لقد تم إنهاء طلب المغادرة الخاص بك',
                'لقد تم إنهاء طلب المغادرة الخاص بك' => 'لقد تم إنهاء طلب المغادرة الخاص بك',

                'Your leave request time has started' => 'لقد بدأ وقت المغادرة',
                'آپ کی چھٹی کی درخواست کا وقت شروع ہو چکا ہے' => 'لقد بدأ وقت المغادرة',
                'لقد بدأ وقت المغادرة' => 'لقد بدأ وقت المغادرة',

                'Your leave request time has ended' => 'لقد انتهى وقت المغادرة',
                'آپ کی چھٹی کی درخواست کا وقت ختم ہو گیا ہے' => 'لقد انتهى وقت المغادرة',
                'لقد انتهى وقت المغادرة' => 'لقد انتهى وقت المغادرة',

                'Your leave request has been approved' => 'تمت الموافقة على طلب المغادرة الخاص بك',
                'آپ کی چھٹی کی درخواست منظور کر لی گئی ہے' => 'تمت الموافقة على طلب المغادرة الخاص بك',
                'تمت الموافقة على طلب المغادرة الخاص بك' => 'تمت الموافقة على طلب المغادرة الخاص بك',

                'Your leave request has been rejected' => 'تم رفض طلب المغادرة الخاص بك',
                'آپ کی چھٹی کی درخواست مسترد کر دی گئی ہے' => 'تم رفض طلب المغادرة الخاص بك',
                'تم رفض طلب المغادرة الخاص بك' => 'تم رفض طلب المغادرة الخاص بك',

                'Your leave request has been expired' => 'انتهت صلاحية طلب المغادرة الخاص بك',
                'آپ کی چھٹی کی درخواست کی میعاد ختم ہو چکی ہے' => 'انتهت صلاحية طلب المغادرة الخاص بك',
                'انتهت صلاحية طلب المغادرة الخاص بك' => 'انتهت صلاحية طلب المغادرة الخاص بك',

                'You have been clocked out by your supervisor' => 'لقد تم تسجيل خروجك من قبل المشرف الخاص بك',
                'آپ اپنے سپروائزر کی جانب سے خروج کر دیےگئے ہیں۔' => 'لقد تم تسجيل خروجك من قبل المشرف الخاص بك',
                'لقد تم تسجيل خروجك من قبل المشرف الخاص بك' => 'لقد تم تسجيل خروجك من قبل المشرف الخاص بك',

                'A scheduled work order has been assigned' => 'تم تعيين أمر عمل مجدول',
                'تم تعيين أمر عمل مجدول' => 'تم تعيين أمر عمل مجدول',
                'ایک طے شدہ ورک آرڈر تفویض کیا گیا ہے۔' => 'تم تعيين أمر عمل مجدول',

                'Team Leader has taken over the Work Order You are no longer assigned to it.' => 'قام مشرف الفريق باستلام أمر العمل ولم تعد مكلّفًا به الآن',
                'قام مشرف الفريق باستلام أمر العمل ولم تعد مكلّفًا به الآن' => 'قام مشرف الفريق باستلام أمر العمل ولم تعد مكلّفًا به الآن',
                'ٹیم لیڈر نے ورک آرڈر سنبھال لیا ہے، آپ اب اس کے لیے مقرر نہیں ہیں۔' => 'قام مشرف الفريق باستلام أمر العمل ولم تعد مكلّفًا به الآن',

                'Work Order has been reassigned to you by the Team Leader.' => 'تمت إعادة تعيين أمر العمل لك من قبل مشرف الفريق',
                'تمت إعادة تعيين أمر العمل لك من قبل مشرف الفريق' => 'تمت إعادة تعيين أمر العمل لك من قبل مشرف الفريق',
                'ورک آرڈر آپ کو ٹیم لیڈر کی جانب سے دوبارہ تفویض کر دیا گیا ہے۔' => 'تمت إعادة تعيين أمر العمل لك من قبل مشرف الفريق',
            ],
            'ur' => [
                'A work order rejected' => 'ورک آرڈر مسترد کر دیا گیا ہے',
                'A work order has been rejected' => 'ورک آرڈر مسترد کر دیا گیا ہے',
                'ورک آرڈر مسترد کر دیا گیا ہے' => 'ورک آرڈر مسترد کر دیا گیا ہے',
                'تم رفض أمر العمل' => 'ورک آرڈر مسترد کر دیا گیا ہے',

                'A new work order assigned' => 'نیا ورک آرڈر دیا گیا ہے',
                'A new work order has been assigned' => 'نیا ورک آرڈر دیا گیا ہے',
                'نیا ورک آرڈر دیا گیا ہے' => 'نیا ورک آرڈر دیا گیا ہے',
                'أمر عمل جديد تم تعيينه' => 'نیا ورک آرڈر دیا گیا ہے',

                'A work order has been reopened' => 'ورک آرڈر دوبارہ کھول دیا گیا ہے',
                'ورک آرڈر دوبارہ کھول دیا گیا ہے' => 'ورک آرڈر دوبارہ کھول دیا گیا ہے',
                'تمت إعادة فتح أمر العمل' => 'ورک آرڈر دوبارہ کھول دیا گیا ہے',

                'Spare part request has been Approved' => 'اسپیئر پارٹ کی درخواست منظور کر لی گئی ہے',
                'اسپیئر پارٹ کی درخواست منظور کر لی گئی ہے' => 'اسپیئر پارٹ کی درخواست منظور کر لی گئی ہے',
                'تم رفض طلب قطع الغيار' => 'اسپیئر پارٹ کی درخواست منظور کر لی گئی ہے',

                'Spare part request has been Rejected' => 'اسپیئر پارٹ کی درخواست مسترد کر دی گئی ہے',
                'اسپیئر پارٹ کی درخواست مسترد کر دی گئی ہے' => 'اسپیئر پارٹ کی درخواست مسترد کر دی گئی ہے',
                'تمت الموافقة على طلب قطع الغيار' => 'اسپیئر پارٹ کی درخواست مسترد کر دی گئی ہے',

                'Your status request to offline has been rejected' => 'آپ کی آف لائن اسٹیٹس کی درخواست کو مسترد کر دیا گیا ہے',
                'آپ کی آف لائن اسٹیٹس کی درخواست کو مسترد کر دیا گیا ہے' => 'آپ کی آف لائن اسٹیٹس کی درخواست کو مسترد کر دیا گیا ہے',
                'تم رفض طلب تغيير الحالة الى غير متواجد' => 'آپ کی آف لائن اسٹیٹس کی درخواست کو مسترد کر دیا گیا ہے',

                'Your status request to offline has been approved' => 'آپ کی آف لائن حیثیت کی درخواست منظور کر لی گئی ہے',
                'آپ کی آف لائن حیثیت کی درخواست منظور کر لی گئی ہے' => 'آپ کی آف لائن حیثیت کی درخواست منظور کر لی گئی ہے',
                'تم قبول طلب تغيير الحالة الى غير متواجد' => 'آپ کی آف لائن حیثیت کی درخواست منظور کر لی گئی ہے',

                'Your status has been set to offline by the supervisor' => 'آپ کا اسٹیٹس سپروائزر کے ذریعہ آف لائن پر سیٹ کر دیا گیا ہے',
                'المشرف غير الحالة الى غير متواجد' => 'آپ کا اسٹیٹس سپروائزر کے ذریعہ آف لائن پر سیٹ کر دیا گیا ہے',
                'آپ کا اسٹیٹس سپروائزر کے ذریعہ آف لائن پر سیٹ کر دیا گیا ہے' => 'آپ کا اسٹیٹس سپروائزر کے ذریعہ آف لائن پر سیٹ کر دیا گیا ہے',
                'Your status have been set to offline by supervisor' => 'آپ کا اسٹیٹس سپروائزر کے ذریعہ آف لائن پر سیٹ کر دیا گیا ہے',

                'Your status has been changed to "Vacation"' => 'آپ کی حیثیت کو "چھٹی" میں تبدیل کر دیا گیا ہے',
                'آپ کی حیثیت کو "چھٹی" میں تبدیل کر دیا گیا ہے' => 'آپ کی حیثیت کو "چھٹی" میں تبدیل کر دیا گیا ہے',
                'تم تغيير حالتك إلى "إجازة”' => 'آپ کی حیثیت کو "چھٹی" میں تبدیل کر دیا گیا ہے',

                'Your status has been changed to "Not Available”' => 'آپ کی حیثیت کو "دستیاب نہیں" میں تبدیل کر دیا گیا ہے',
                'آپ کی حیثیت کو "دستیاب نہیں" میں تبدیل کر دیا گیا ہے' => 'آپ کی حیثیت کو "دستیاب نہیں" میں تبدیل کر دیا گیا ہے',
                'تم تغيير حالتك إلى "غير متاح”' => 'آپ کی حیثیت کو "دستیاب نہیں" میں تبدیل کر دیا گیا ہے',

                'Your status has been changed to "Work Injury”' => 'آپ کی حیثیت کو "کام کی چوٹ" میں تبدیل کر دیا گیا ہے',
                'آپ کی حیثیت کو "کام کی چوٹ" میں تبدیل کر دیا گیا ہے' => 'آپ کی حیثیت کو "کام کی چوٹ" میں تبدیل کر دیا گیا ہے',
                'تم تغيير حالتك إلى "إصابة عمل”' => 'آپ کی حیثیت کو "کام کی چوٹ" میں تبدیل کر دیا گیا ہے',

                'The maintenance request has been Approved' => 'مینٹیننس کی درخواست منظور کر دی گئی ہے',
                'بلاغ الصيانة المرسل تم قبوله' => 'مینٹیننس کی درخواست منظور کر دی گئی ہے',
                'مینٹیننس کی درخواست منظور کر دی گئی ہے' => 'مینٹیننس کی درخواست منظور کر دی گئی ہے',

                'The maintenance request has been Rejected' => 'مینٹیننس کی درخواست مسترد کر دی گئی ہے',
                'بلاغ الصيانة المرسل تم رفضه' => 'مینٹیننس کی درخواست مسترد کر دی گئی ہے',
                'مینٹیننس کی درخواست مسترد کر دی گئی ہے' => 'مینٹیننس کی درخواست مسترد کر دی گئی ہے',
                'لاغ الصيانة المرسل تم رفضه' => 'مینٹیننس کی درخواست مسترد کر دی گئی ہے',



                'Your ongoing leave request has been terminated' => 'آپ کی جاری چھٹی کی درخواست منسوخ کر دی گئی ہے',
                'آپ کی جاری چھٹی کی درخواست منسوخ کر دی گئی ہے' => 'آپ کی جاری چھٹی کی درخواست منسوخ کر دی گئی ہے',
                'لقد تم إنهاء طلب المغادرة الخاص بك' => 'آپ کی جاری چھٹی کی درخواست منسوخ کر دی گئی ہے',

                'Your leave request time has started' => 'آپ کی چھٹی کی درخواست کا وقت شروع ہو چکا ہے',
                'آپ کی چھٹی کی درخواست کا وقت شروع ہو چکا ہے' => 'آپ کی چھٹی کی درخواست کا وقت شروع ہو چکا ہے',
                'لقد بدأ وقت المغادرة' => 'آپ کی چھٹی کی درخواست کا وقت شروع ہو چکا ہے',

                'Your leave request time has ended' => 'آپ کی چھٹی کی درخواست کا وقت ختم ہو گیا ہے',
                'آپ کی چھٹی کی درخواست کا وقت ختم ہو گیا ہے' => 'آپ کی چھٹی کی درخواست کا وقت ختم ہو گیا ہے',
                'لقد انتهى وقت المغادرة' => 'آپ کی چھٹی کی درخواست کا وقت ختم ہو گیا ہے',

                'Your leave request has been approved' => 'آپ کی چھٹی کی درخواست منظور کر لی گئی ہے',
                'آپ کی چھٹی کی درخواست منظور کر لی گئی ہے' => 'آپ کی چھٹی کی درخواست منظور کر لی گئی ہے',
                'تمت الموافقة على طلب المغادرة الخاص بك' => 'آپ کی چھٹی کی درخواست منظور کر لی گئی ہے',

                'Your leave request has been rejected' => 'آپ کی چھٹی کی درخواست مسترد کر دی گئی ہے',
                'آپ کی چھٹی کی درخواست مسترد کر دی گئی ہے' => 'آپ کی چھٹی کی درخواست مسترد کر دی گئی ہے',
                'تم رفض طلب المغادرة الخاص بك' => 'آپ کی چھٹی کی درخواست مسترد کر دی گئی ہے',

                'Your leave request has been expired' => 'آپ کی چھٹی کی درخواست کی میعاد ختم ہو چکی ہے',
                'آپ کی چھٹی کی درخواست کی میعاد ختم ہو چکی ہے' => 'آپ کی چھٹی کی درخواست کی میعاد ختم ہو چکی ہے',
                'انتهت صلاحية طلب المغادرة الخاص بك' => 'آپ کی چھٹی کی درخواست کی میعاد ختم ہو چکی ہے',

                'You have been clocked out by your supervisor' => 'آپ اپنے سپروائزر کی جانب سے خروج کر دیےگئے ہیں۔',
                'آپ اپنے سپروائزر کی جانب سے خروج کر دیےگئے ہیں۔' => 'آپ اپنے سپروائزر کی جانب سے خروج کر دیےگئے ہیں۔',
                'لقد تم تسجيل خروجك من قبل المشرف الخاص بك' => 'آپ اپنے سپروائزر کی جانب سے خروج کر دیےگئے ہیں۔',

                'A scheduled work order has been assigned' => 'ایک طے شدہ ورک آرڈر تفویض کیا گیا ہے۔',
                'تم تعيين أمر عمل مجدول' => 'ایک طے شدہ ورک آرڈر تفویض کیا گیا ہے۔',
                'ایک طے شدہ ورک آرڈر تفویض کیا گیا ہے۔' => 'ایک طے شدہ ورک آرڈر تفویض کیا گیا ہے۔',


                'Team Leader has taken over the Work Order You are no longer assigned to it.' => 'ٹیم لیڈر نے ورک آرڈر سنبھال لیا ہے، آپ اب اس کے لیے مقرر نہیں ہیں۔',
                'قام مشرف الفريق باستلام أمر العمل ولم تعد مكلّفًا به الآن' => 'ٹیم لیڈر نے ورک آرڈر سنبھال لیا ہے، آپ اب اس کے لیے مقرر نہیں ہیں۔',
                'ٹیم لیڈر نے ورک آرڈر سنبھال لیا ہے، آپ اب اس کے لیے مقرر نہیں ہیں۔' => 'ٹیم لیڈر نے ورک آرڈر سنبھال لیا ہے، آپ اب اس کے لیے مقرر نہیں ہیں۔',

                'Work Order has been reassigned to you by the Team Leader.' => 'ورک آرڈر آپ کو ٹیم لیڈر کی جانب سے دوبارہ تفویض کر دیا گیا ہے۔',
                'تمت إعادة تعيين أمر العمل لك من قبل مشرف الفريق' => 'ورک آرڈر آپ کو ٹیم لیڈر کی جانب سے دوبارہ تفویض کر دیا گیا ہے۔',
                'ورک آرڈر آپ کو ٹیم لیڈر کی جانب سے دوبارہ تفویض کر دیا گیا ہے۔' => 'ورک آرڈر آپ کو ٹیم لیڈر کی جانب سے دوبارہ تفویض کر دیا گیا ہے۔',
            ],
            'en' => [
                'A work order rejected' => 'A work order has been rejected',
                'A work order has been rejected' => 'A work order has been rejected',
                'ورک آرڈر مسترد کر دیا گیا ہے' => 'A work order has been rejected',
                'تم رفض أمر العمل' => 'A work order has been rejected',

                'A new work order assigned' => 'A new work order has been assigned',
                'A new work order has been assigned' => 'A new work order has been assigned',
                'نیا ورک آرڈر دیا گیا ہے' => 'A new work order has been assigned',
                'أمر عمل جديد تم تعيينه' => 'A new work order has been assigned',

                'A work order has been reopened' => 'A work order has been reopened',
                'ورک آرڈر دوبارہ کھول دیا گیا ہے' => 'A work order has been reopened',
                'تمت إعادة فتح أمر العمل' => 'A work order has been reopened',


                'Spare part request has been Approved' => 'Spare part request has been Approved',
                'اسپیئر پارٹ کی درخواست منظور کر لی گئی ہے' => 'Spare part request has been Approved',
                'تم رفض طلب قطع الغيار' => 'Spare part request has been Approved',

                'Spare part request has been Rejected' => 'Spare part request has been Rejected',
                'اسپیئر پارٹ کی درخواست مسترد کر دی گئی ہے' => 'Spare part request has been Rejected',
                'تمت الموافقة على طلب قطع الغيار' => 'Spare part request has been Rejected',

                'Your status request to offline has been rejected' => 'Your status request to offline has been rejected',
                'آپ کی آف لائن اسٹیٹس کی درخواست کو مسترد کر دیا گیا ہے' => 'Your status request to offline has been rejected',
                'تم رفض طلب تغيير الحالة الى غير متواجد' => 'Your status request to offline has been rejected',

                'Your status request to offline has been approved' => 'Your status request to offline has been approved',
                'آپ کی آف لائن حیثیت کی درخواست منظور کر لی گئی ہے' => 'Your status request to offline has been approved',
                'تم قبول طلب تغيير الحالة الى غير متواجد' => 'Your status request to offline has been approved',

                'Your status has been set to offline by the supervisor' => 'Your status has been set to offline by the supervisor',
                'المشرف غير الحالة الى غير متواجد' => 'Your status has been set to offline by the supervisor',
                'آپ کا اسٹیٹس سپروائزر کے ذریعہ آف لائن پر سیٹ کر دیا گیا ہے' => 'Your status has been set to offline by the supervisor',
                'Your status have been set to offline by supervisor' => 'Your status has been set to offline by the supervisor',

                'Your status has been changed to "Vacation"' => 'Your status has been changed to "Vacation"',
                'آپ کی حیثیت کو "چھٹی" میں تبدیل کر دیا گیا ہے' => 'Your status has been changed to "Vacation"',
                'تم تغيير حالتك إلى "إجازة”' => 'Your status has been changed to "Vacation"',

                'Your status has been changed to "Not Available”' => 'Your status has been changed to "Not Available”',
                'آپ کی حیثیت کو "دستیاب نہیں" میں تبدیل کر دیا گیا ہے' => 'Your status has been changed to "Not Available”',
                'تم تغيير حالتك إلى "غير متاح”' => 'Your status has been changed to "Not Available”',

                'Your status has been changed to "Work Injury”' => 'Your status has been changed to "Work Injury”',
                'آپ کی حیثیت کو "کام کی چوٹ" میں تبدیل کر دیا گیا ہے' => 'Your status has been changed to "Work Injury”',
                'تم تغيير حالتك إلى "إصابة عمل”' => 'Your status has been changed to "Work Injury”',

                'The maintenance request has been Approved' => 'The maintenance request has been Approved',
                'بلاغ الصيانة المرسل تم قبوله' => 'The maintenance request has been Approved',
                'مینٹیننس کی درخواست منظور کر دی گئی ہے' => 'The maintenance request has been Approved',
                'The maintenance request has been Rejected' => 'The maintenance request has been Rejected',
                'بلاغ الصيانة المرسل تم رفضه' => 'The maintenance request has been Rejected',
                'مینٹیننس کی درخواست مسترد کر دی گئی ہے' => 'The maintenance request has been Rejected',
                'لاغ الصيانة المرسل تم رفضه' => 'The maintenance request has been Rejected',


                'Your ongoing leave request has been terminated' => 'Your ongoing leave request has been terminated',
                'آپ کی جاری چھٹی کی درخواست منسوخ کر دی گئی ہے' => 'Your ongoing leave request has been terminated',
                'لقد تم إنهاء طلب المغادرة الخاص بك' => 'Your ongoing leave request has been terminated',

                'Your leave request time has started' => 'Your leave request time has started',
                'آپ کی چھٹی کی درخواست کا وقت شروع ہو چکا ہے' => 'Your leave request time has started',
                'لقد بدأ وقت المغادرة' => 'Your leave request time has started',

                'Your leave request time has ended' => 'Your leave request time has ended',
                'آپ کی چھٹی کی درخواست کا وقت ختم ہو گیا ہے' => 'Your leave request time has ended',
                'لقد انتهى وقت المغادرة' => 'Your leave request time has ended',

                'Your leave request has been approved' => 'Your leave request has been approved',
                'آپ کی چھٹی کی درخواست منظور کر لی گئی ہے' => 'Your leave request has been approved',
                'تمت الموافقة على طلب المغادرة الخاص بك' => 'Your leave request has been approved',

                'Your leave request has been rejected' => 'Your leave request has been rejected',
                'آپ کی چھٹی کی درخواست مسترد کر دی گئی ہے' => 'Your leave request has been rejected',
                'تم رفض طلب المغادرة الخاص بك' => 'Your leave request has been rejected',

                'Your leave request has been expired' => 'Your leave request has been expired',
                'آپ کی چھٹی کی درخواست کی میعاد ختم ہو چکی ہے' => 'Your leave request has been expired',
                'انتهت صلاحية طلب المغادرة الخاص بك' => 'Your leave request has been expired',

                'You have been clocked out by your supervisor' => 'You have been clocked out by your supervisor',
                'آپ اپنے سپروائزر کی جانب سے خروج کر دیےگئے ہیں۔' => 'You have been clocked out by your supervisor',
                'لقد تم تسجيل خروجك من قبل المشرف الخاص بك' => 'You have been clocked out by your supervisor',

                'A scheduled work order has been assigned' => 'A scheduled work order has been assigned',
                'تم تعيين أمر عمل مجدول' => 'A scheduled work order has been assigned',
                'ایک طے شدہ ورک آرڈر تفویض کیا گیا ہے۔' => 'A scheduled work order has been assigned',

                'Team Leader has taken over the Work Order You are no longer assigned to it.' => 'Team Leader has taken over the Work Order You are no longer assigned to it.',
                'قام مشرف الفريق باستلام أمر العمل ولم تعد مكلّفًا به الآن' => 'Team Leader has taken over the Work Order You are no longer assigned to it.',
                'ٹیم لیڈر نے ورک آرڈر سنبھال لیا ہے، آپ اب اس کے لیے مقرر نہیں ہیں۔' => 'Team Leader has taken over the Work Order You are no longer assigned to it.',

                'Work Order has been reassigned to you by the Team Leader.' => 'Work Order has been reassigned to you by the Team Leader.',
                'تمت إعادة تعيين أمر العمل لك من قبل مشرف الفريق' => 'Work Order has been reassigned to you by the Team Leader.',
                'ورک آرڈر آپ کو ٹیم لیڈر کی جانب سے دوبارہ تفویض کر دیا گیا ہے۔' => 'Work Order has been reassigned to you by the Team Leader.',
            ]
        ];

        // Translate the message if mapping exists for the selected language
        if (array_key_exists($selected_language, $translations)) {
            if (array_key_exists($message, $translations[$selected_language])) {
                return $translations[$selected_language][$message];
            }
        }

        // Return the original message if no translation is available
        return $message;
    }

    /**
     * Get the project name based on the given identifier.
     *
     * @param  mixed  $identifier  The identifier (work order ID, maintenance request ID, or project ID)
     * @return string|null  The project name, or null if not found
     */
    public static function getProjectName($workOrder)
    {
        $projectName = null;
        // Check if the identifier is not empty and not equal to "null"
        if (!empty($workOrder->section_id) && $workOrder->section_id !== "null") {

            // Try to get it by maintenance request ID
            $projectName = Helper::getProjectNameByMaintenanceRequestID($workOrder->section_id);

            // If project name not found, try to get it by work order ID
            if (empty($projectName)) {
                $projectName = Helper::getProjectNameByWorkOrderID($workOrder->section_id);
            }
        }

        // If project name still not found, try to get it by project ID
        if (empty($projectName)) {
            $projectName = Helper::getProjectNameByProjectID($workOrder->maintenance_section_id);
        }
        return $projectName;
    }

    /**
     * Format the posted date for display.
     *
     * @param  string  $created_at  The created_at date
     * @return string  The formatted date
     */
    public static function formatPostedDate($created_at)
    {
        return Carbon::parse($created_at)->format('d-m-Y h:i A');
    }

    /**
     * Get the flattened work order data for the logged-in tenant user.
     *
     * @param object $loggedInUser The logged-in user object.
     * @return array|null Flattened work order data or null if no work order found.
     */
    public static function getFlattenedWorkOrderForTenant($loggedInUser)
    {
        $workOrder = MaintenanceRequest::with([
            'workOrders' => function ($query) {
                $query->select(
                    'description',
                    'room AS space_no',
                    'id',
                    'maintanance_request_id',
                    'work_order_id',
                    'workorder_journey',
                    'created_by',
                    'work_order_type',
                    'created_at',
                    'created_by AS created_by_user_id' // Alias for the user's ID
                )
                    ->with([
                        'createdByUser' => function ($query) {
                            $query->select('id', 'email', 'name'); // Select user ID, email, and name
                        }
                    ])
                    ->orderBy('id', 'desc');
            }
        ])
            ->where('phone', $loggedInUser->phone)
            ->where('building_id', $loggedInUser->building_ids)
            ->whereIn('status', ['In Progress', 'Completed', 'Finished'])
            ->orderByRaw("CASE
                                    WHEN status = 'In Progress' THEN 1
                                    WHEN status = 'Completed' THEN 2
                                    WHEN status = 'Finished' THEN 3
                                  END")
            ->orderBy('id', 'desc')
            ->first();
        if ($workOrder && $workOrder->workOrders->count() > 0) {
            // Access the first work order from the relationship
            $workOrder = $workOrder->workOrders->first();

            // Create the desired flattened array
            return [
                'description' => $workOrder->description,
                'space_no' => $workOrder->space_no,
                'id' => $workOrder->id,
                'maintanance_request_id' => $workOrder->maintanance_request_id,
                'work_order_id' => $workOrder->work_order_id,
                'workorder_journey' => $workOrder->workorder_journey,
                'created_by' => $workOrder->created_by,
                'work_order_type' => $workOrder->work_order_type,
                'created_at' => $workOrder->created_at,
                'created_by_user_id' => $workOrder->created_by_user_id,
                'status' => $workOrder->status,
                'email' => $workOrder->createdByUser->email,
                'name' => $workOrder->createdByUser->name,
            ];
        }
        return null; // No work order found
    }

    /**
     * Get the flattened work order data for the logged-in BM user.
     *
     * @param object $loggedInUser The logged-in user object.
     * @return array|null Flattened work order data or null if no work order found.
     */
    public static function getFlattenedWorkOrderForTenantBM($user_id, $property_id)
    {
        $bmEmployees = User::where('sp_admin_id', $user_id)
            ->where('status', 1)
            ->where('is_deleted', 'no')
            ->pluck('id')
            ->toArray();

        $bmEmployees[] = $user_id;

        $workOrder = WorkOrders::select('work_orders.description', 'work_orders.room AS space_no', 'work_orders.id', 'work_orders.maintanance_request_id', 'work_orders.work_order_id', 'work_orders.workorder_journey', 'work_orders.created_by', 'work_orders.work_order_type', 'work_orders.created_at')
            ->with('createdByUser') // Load the relationship for created by user
            ->where('property_id', $property_id)
            ->whereIn('created_by', $bmEmployees)
            ->orderBy('id', 'desc')
            ->first();

        if ($workOrder) {
            // Create the desired flattened array
            return [
                'description' => $workOrder->description,
                'space_no' => $workOrder->space_no,
                'id' => $workOrder->id,
                'maintanance_request_id' => $workOrder->maintanance_request_id,
                'work_order_id' => $workOrder->work_order_id,
                'workorder_journey' => $workOrder->workorder_journey,
                'created_by' => $workOrder->created_by,
                'work_order_type' => $workOrder->work_order_type,
                'created_at' => $workOrder->created_at,
                'created_by_user_id' => $workOrder->created_by_user_id,
                'status' => $workOrder->status,
                'email' => $workOrder->createdByUser->email,
                'name' => $workOrder->createdByUser->name,
            ];
        }
        return null; // No work order found
    }

    /**
     * Get the flattened work order data for the logged-in BM user.
     *
     * @param object $loggedInUser The logged-in user object.
     * @return array|null Flattened work order data or null if no work order found.
     */
    public static function getFlattenedWorkOrderForTenantSP($project_user_id, $property_id)
    {
        $workOrder = WorkOrders::select('work_orders.description', 'work_orders.room AS space_no', 'work_orders.id', 'work_orders.maintanance_request_id', 'work_orders.work_order_id', 'work_orders.workorder_journey', 'work_orders.created_by', 'work_orders.work_order_type', 'work_orders.created_at')
            ->with('createdByUser') // Load the relationship for created by user
            ->where('property_id', $property_id)
            ->where('project_user_id', $project_user_id)
            ->orderBy('id', 'desc')
            ->first();

        if ($workOrder) {
            // Create the desired flattened array
            return [
                'description' => $workOrder->description,
                'space_no' => $workOrder->space_no,
                'id' => $workOrder->id,
                'maintanance_request_id' => $workOrder->maintanance_request_id,
                'work_order_id' => $workOrder->work_order_id,
                'workorder_journey' => $workOrder->workorder_journey,
                'created_by' => $workOrder->created_by,
                'work_order_type' => $workOrder->work_order_type,
                'created_at' => $workOrder->created_at,
                'created_by_user_id' => $workOrder->created_by_user_id,
                'status' => $workOrder->status,
                'email' => $workOrder->createdByUser->email,
                'name' => $workOrder->createdByUser->name,
            ];
        }
        return null; // No work order found
    }

    /**
     * Get checklist tasks with related data for a specific checklist and work order.
     *
     * @param int $id The ID of the checklist.
     * @param int $w_id The ID of the work order.
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getCheckListTask($id, $w_id)
    {
        // Retrieve checklist tasks along with related data using Eloquent relationships
        $checklistTasks = ChecklistTasks::with(['checklist', 'noChecklistActions'])
            ->whereHas('checklist', function ($query) use ($id) {
                $query->where('id', $id);
            })
            ->whereHas('noChecklistActions', function ($query) use ($w_id) {
                $query->where('work_order_id', $w_id);
            })
            ->groupBy('checklist_tasks.id')
            ->orderByDesc('checklist_tasks.id')
            ->get()->toArray();

        if (!empty($checklistTasks)) {
            foreach ($checklistTasks as &$task) {
                // Extract noChecklistActions data for the task
                $noChecklistActions = $task['no_checklist_actions'];

                // Find the first action that matches the work_order_id
                $filteredAction = null;
                foreach ($noChecklistActions as $action) {
                    if ($action['work_order_id'] == $w_id) {
                        $filteredAction = $action;
                        break;
                    }
                }

                // If a matching action is found, update task with action data
                if ($filteredAction) {
                    $task['id'] = $filteredAction['id'];
                    $task['work_order_id'] = $filteredAction['work_order_id'];
                    $task['worker_id'] = $filteredAction['worker_id'];
                    $task['comment'] = $filteredAction['comment'];
                    $task['photos'] = $filteredAction['photos'];
                    $task['feedback_options'] = $filteredAction['feedback_options'];
                    $task['checklist_task_id'] = $filteredAction['checklist_task_id'];
                    $task['created_at'] = $filteredAction['created_at'];
                    $task['check_id'] = $filteredAction['id'];
                }

                // Extract checklist data and unset checklist and no_checklist_actions
                $checklistData = $task['checklist'];
                unset($task['checklist']);
                unset($task['no_checklist_actions']);

                // Merge checklist data into task array
                $task = array_merge($task, [
                    'list_id' => $checklistData['list_id'],
                    'country_id' => $checklistData['country_id'],
                    'city_id' => $checklistData['city_id'],
                    'properties' => $checklistData['properties'],
                    'asset_category_id' => $checklistData['asset_category_id'],
                    'asset_id' => $checklistData['asset_id'],
                    'frequency_id' => $checklistData['frequency_id'],
                    'checklist_title' => $checklistData['checklist_title'],
                    'status' => $checklistData['status'],
                    'deleted_at' => $checklistData['deleted_at'],
                    'submitted' => $checklistData['submitted'],
                ]);
            }
        }

        return $checklistTasks;
    }

    /**
     * Get data for a specific  work orders.
     *
     * @param int $id The ID of the checklist.
     * @param int $w_id The ID of the work order.
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function exportWorkOrdersCsv($request)
    {
        // Retrieve work orders data along with related information using Eloquent relationships
        $work_orders_query = WorkOrders::with([
            'contract',
            'propertyBuilding.property',
            'worker',
            'chatMessages',
            'relatedWorkOrders',
            'chatMessages',
            'slaAssetCategory',
            'contractPriority',
            'frequencyMaster.frequencies.contractFrequencies',
            'workTimeFrame'
        ])
            ->whereIn('id', $request->ids);
        $work_orders = $work_orders->orderByDesc('start_date')->get();
        $work_orders = json_decode(json_encode($work_orders), true);
        $property_builiding_ids = [];
        return null;
    }

    /**
     * Get data for a specific  work orders for worker app api.
     *
     * @param int $id The ID of the work order.
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function details($id)
    {
        $details = WorkOrders::with([
            'contract:id,contract_number,user_id',
            'propertyBuilding:id,building_name,property_id',
            'createdByUser:id',
            'selectedAssetName:id,asset_name',
            'asset',
        ])->where('id', $id)->first();

        if (isset($details)) {
            $details->created_by = "";
            $details->contract_number = "";
            $details->latitude = "";
            $details->longitude = "";
            $details->location = "";
            $details->property_type = "";
            $details->complex_name = null;
            $details->project_id = "";
            $details->project = "";
            $details->asset_name = "";
            $details->building_name = "";

            if (is_object($details->createdByUser) && $details->createdByUser != "NULL" && $details->createdByUser != NULL) {
                $details->created_by = $details->createdByUser->id;
            }

            if (is_object($details->contract) && $details->contract != "NULL" && $details->contract != NULL) {
                $poadata = User::where('id', $details->contract->user_id)->first();
                if (isset($poadata)) {
                    $details->project_id = $poadata->project_id;

                    $projectdata = ProjectsDetails::where('id', $poadata->project_id)->first();
                    if (isset($projectdata)) {
                        $details->project = $projectdata->project_name;
                    }
                }
                $details->contract_number = $details->contract->contract_number;
            }

            if (is_object($details->selectedAssetName) && $details->selectedAssetName != NULL && $details->selectedAssetName != "NULL") {
                $details->asset_name = $details->selectedAssetName->asset_name;
            }

            if ($details->wo_images != "") {
                //$wo_images = "";
                foreach (explode(',', trim($details->wo_images)) as $k => $v) {
                    $extension = pathinfo($v)['extension'];
                    if ($extension != 'pdf') {
                        $wo_images[$k] = ImagesUploadHelper::displayImage($v, 'uploads/workorder');
                    } else {
                        $wo_images[$k] = url('uploads/workorder/' . $v);
                    }

                }
                $details->wo_images = $wo_images;
            }

            if (is_object($details->propertyBuilding) && $details->propertyBuilding != "NULL" && $details->propertyBuilding != NULL) {
                $details->building_name = $details->propertyBuilding->building_name;
                $propertydata = Property::where('id', $details->propertyBuilding->property_id)->first();
                if (isset($propertydata)) {
                    $details->latitude = $propertydata->latitude;
                    $details->longitude = $propertydata->longitude;
                    $details->location = $propertydata->location;
                    $details->property_type = $propertydata->property_type;
                    $details->complex_name = $propertydata->complex_name;
                }
            }

            $details->asset_tag = "";
            $details->barcode_value = null;
            $details->asset_symbol = null;
            $details->asset_number = null;

            if (is_object($details->asset) && $details->asset != "NULL" && $details->asset != NULL) {
                $details->asset_tag = $details->asset->asset_tag;
                $details->barcode_value = $details->asset->barcode_value;
                $details->asset_symbol = $details->asset->asset_symbol;
                $details->asset_number = $details->asset->asset_number;
            }

            //$details->asset_name = $details->selected_asset_name->asset_name;
            unset($details->contract);
            $details->asset_category_name = '';
            $asset_categories_data = AssetCategory::where('id', $details->asset_category_id)->first();
            if (isset($asset_categories_data)) {
                $deleted = "";
                $details->asset_category_name = $asset_categories_data->asset_category . $deleted;
            }

            $details->project_name = Helper::getProjectNameByWorkOrderID($details->id);
            $wtfs = WorkTimeFrame::select('start_time', 'end_time', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
                ->where('user_id', $details->project_user_id)
                ->first();

            $time = $details->wtf_start_time;
            if ($details->wtf_start_time == '') {
                //If work order time frame has added then set 00:00:00
                $time = !isset($wtfs) ? "00:00:00" : $wtfs->start_time;
            }

            //If work_order_type is preventive
            $details->reported_at = $details->work_order_type == "preventive" ? $details->start_date . ' ' . $time : $details->created_at;

        }
        $sla_asset_categories = ContractAssetCategories::where('asset_category_id', $details->asset_category_id)
            ->where('contract_number', $details->contract_number)
            ->orderBy('id', 'desc')
            ->first();

        $response_time = 0;
        $service_window = 0;
        $response_time_type = 'hours';
        $service_window_type = 'minutes';
        if ($details->work_order_type == "reactive") {
            if (!empty($sla_asset_categories->priority_id)) {
                $contract_priorities = ContractPriority::where('priority_id', $sla_asset_categories->priority_id)
                    ->where('contract_number', $details->contract_number)
                    ->orderBy('id', 'desc')
                    ->first();
                $response_time = $contract_priorities->response_time;
                $service_window = $contract_priorities->service_window;
                $response_time_type = $contract_priorities->response_time_type;
                $service_window_type = $contract_priorities->service_window_type;
            }
        } elseif ($details->work_order_type == "preventive" && $details->priority_id != 0) {
            $contract_priorities = ContractPriority::where('priority_id', $details->priority_id)
                ->where('contract_number', $details->contract_number)
                ->orderBy('id', 'desc')
                ->first();
            $response_time = $contract_priorities->response_time;
            $service_window = $contract_priorities->service_window;
            $response_time_type = $contract_priorities->response_time_type;
            $service_window_type = $contract_priorities->service_window_type;
        } else {
            $contract_frequencies = FrequencyMaster::select('contract_frequencies.response_time', 'contract_frequencies.service_window', 'contract_frequencies.response_time_type', 'contract_frequencies.service_window_type')
                ->leftjoin('frequencies', 'frequencies.frequency_type', '=', 'frequencies_master.id')
                ->leftjoin('contract_frequencies', 'contract_frequencies.frequency_id', '=', 'frequencies.id')
                ->where('frequencies_master.id', $details->frequency_id)
                ->where('contract_frequencies.contract_number', $details->contract_number)
                ->first();
            $response_time = isset($contract_frequencies->response_time) ? $contract_frequencies->response_time : 0;
            $service_window = isset($contract_frequencies->service_window) ? $contract_frequencies->service_window : '';
            $response_time_type = isset($contract_frequencies->response_time_type) ? $contract_frequencies->response_time_type : 'hours';
            $service_window_type = isset($contract_frequencies->service_window_type) ? $contract_frequencies->service_window_type : 'minutes';
        }
        $created_at = $details->created_at;
        $tdate = date('Y-m-d H:i:s');
        $datetime1 = strtotime($created_at);
        $datetime2 = strtotime($tdate);
        $interval = abs($datetime2 - $datetime1);
        $minutes = round($interval / 60);
        if ($response_time_type == "days") {
            $response_time = $response_time * 1440;
        } elseif ($response_time_type == "hours") {
            $response_time = $response_time * 60;
        }
        $time_left = $response_time - $minutes;

        if ($details->job_started_at != NULL) {
            $details->target_date = date('Y-m-d H:i:s', strtotime('+' . $service_window . ' ' . $service_window_type, strtotime($details->job_started_at)));
        }

        if ($details->job_started_at != '') {
            $details->pass_fail = strtotime($details->target_date) >= strtotime($tdate) ? 'pass' : 'fail';
        }

        if ($details->response_time != 'On time' || $details->response_time != 'Late') {
            $details->response_time = $time_left >= 0 ? $time_left : 'Late';
        }

        $details->building = $details->property_type == 'complex' ? $details->complex_name . ' - ' . $details->building_name : $details->building_name;

        $details->zone = $details->floor;
        $details->unit = $details->room;

        $details->asset_tag = $details->asset_symbol . $details->asset_number;

        $active_task = WorkOrders::active_task($details->worker_id);

        $details->has_active_task = false;
        if (!empty((array) $active_task) && count($active_task) >= 3) {
            foreach ($active_task as $atkey => $at) {
                if ($at['id'] != $details->id) {
                    $details->has_active_task = true;
                }
            }
        }
        $action = WorkOrders::check_no_checklist_actions_completed($id);
        if ($details->checklist_id == 0) {
            $details->has_checklist = false;
            $details->total_checklists = 1;
            $details->checklist_actions_left = $action == 0 ? 1 : 0;
        } else {
            $checklist_tasks_count = count(WorkOrders::checklist_tasks($details->checklist_id, $id));
            $details->has_checklist = true;
            // if($checklist_tasks_count == 0)
            // {
            //   $details->has_checklist = false;
            // }
            $details->total_checklists = $checklist_tasks_count;
            $checklist_actions_left = $checklist_tasks_count - $action;
            $details->checklist_actions_left = $checklist_actions_left <= 0 ? 0 : $checklist_actions_left;

            $details->checklist_tasks = WorkOrders::checklist_tasks($details->checklist_id, $id);
        }

        $details->screen_status = self::getWorkorderdetailsScreenstatus($details);

        $details->over_due = false;
        if ($details->target_date < now()) {
            $details->over_due = true;
        }
        //$details->target_date = date('d/m/Y h:i A', strtotime($details->target_date));
        $details->current_time = date('Y-m-d H:i:s');

        return $details;
    }

    /**
     * Get status of work order for work order details api
     *
     * @param int $id The ID of the work order.
     */
    public static function getWorkorderdetailsScreenstatus($details)
    {
        $screen_status = '';
        if ($details->status == 2 && ($details->workorder_journey == 'job_execution' || $details->workorder_journey == 'job_evaluation') && ($details->bm_approve_job == 1 || $details->sp_approve_job == 1)) {
            $screen_status = 'Rejected';
        } elseif ($details->status == 2 && $details->workorder_journey == 'job_execution' && $details->sp_reopen_status == 2 && $details->worker_started_at == NULL) {
            $screen_status = 'Re Opened';
        } elseif ($details->status == 3 && ($details->workorder_journey == 'job_execution' || $details->workorder_journey == 'job_evaluation')) {
            $screen_status = 'On Hold';
        } elseif ($details->target_date < now() && ($details->status == 2) && ($details->workorder_journey == 'job_execution')) {
            $screen_status = 'Over Due';
        }

        return $screen_status;
    }

    /**
     * Check if any worker assigned to a work order has not finished their job.
     *
     * @param int $workOrderId
     * @return bool
     */
    public static function anyWorkerNotFinished($workOrderId)
    {
        // Check if any worker has not finished their job for the specified work order
        $workOrder = WorkOrders::with([
            'workers' => function ($query) {
                $query->whereNull('end_time');
            }
        ])->find($workOrderId);

        return $workOrder->workers->isNotEmpty();
    }

    /**
     * Get all workers with start and end timings and service provider name for a work order.
     *
     * @param int $workOrderId
     * @return \Illuminate\Support\Collection
     */
    public static function getWorkersDetailsForWorkOrder($workOrderId)
    {
        // Retrieve the work order with assigned workers, start and end timings, and service provider name
        $workOrder = WorkOrders::with([
            'assignedWorkers',
            'serviceProvider',
            'workerTimings' // Include the workerTimings relationship
        ])->find($workOrderId);

        // Process the data as needed
        $workersDetails = $workOrder->assignedWorkers->map(function ($worker) use ($workOrder) {
            $timing = $workOrder->workerTimings
                ->where('worker_id', $worker->id) // Filter by worker_id
                ->where('work_order_id', $workOrder->id) // Filter by work_order_id
                ->first(); // Get the first matching timing // Get timings for the worker
            return [
                'worker_id' => $worker->id,
                'worker_name' => $worker->name,
                'start_time' => $timing ? $timing->start_time : null, // Check if timing exists
                'end_time' => $timing ? $timing->end_time : null,     // Check if timing exists
                'service_provider_name' => $worker->serviceProvider->name,
                'checklist_id' => $workOrder->checklist_id,
                'work_order_id' => $workOrder->id,
                'is_result' => NoChecklistAction::where('work_order_id', $workOrder->id)
                    ->where('worker_id', $worker->id)
                    ->exists() ? 1 : 0
            ];
        });

        return $workersDetails;
    }

    /**
     * Save availability request record
     */
    public static function changeAvailabilityStatus($reason_type, $reason_description, $attachment, $from_datetime, $to_datetime)
    {
        $lastRequest = ManageWorkerAvailabilityStatus::latest('id')->first();
        $lastNumber = $lastRequest ? $lastRequest->id : 0;
        $newNumber = $lastNumber + 1;
        $worker_id = Auth::user()->id;
        $leave_request_id = 'LR-' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
        $response_array = array('worker_id' => $worker_id, 'leave_request_id' => $leave_request_id, 'reason_type_id' => $reason_type, 'attachments' => $attachment, 'reason_description' => $reason_description, 'approval_status' => 'Pending', 'from_datetime' => $from_datetime, 'to_datetime' => $to_datetime, 'created_at' => date('Y-m-d H:i:s'));
        $availability_request_id = ManageWorkerAvailabilityStatus::insertGetId($response_array);
        if ($availability_request_id) {
            $result['availability_request_id'] = $availability_request_id;
            $result['leave_request_id'] = $leave_request_id;
            return $result;
        }
        return $availability_request_id;
    }


    /**
     * Check pending availability request record

     */
    public static function checkPendingAvailabilityStatus()
    {
        $worker_id = Auth::user()->id;
        $result = ManageWorkerAvailabilityStatus::where('worker_id', $worker_id)
            ->where('approval_status', 'pending')
            ->where('to_datetime', '>=', now())
            ->first();

        return $result;
    }


    /**
     * Send notification to SPA & SP for changee availability request of worker

     */
    public static function sendAvailabilityRequestChangeNotification($availability_request_id, $leave_request_id)
    {
        $worker_id = Auth::user()->id;
        $spa_id = Auth::user()->sp_admin_id;
        $sps_id = Auth::user()->supervisor_id;
        if ($spa_id) {
            //Send Notification To SP Admin
            $result_array = array('user_id' => $spa_id, 'message' => '' . $leave_request_id . ' New leave request has been received', 'message_ar' => '' . $leave_request_id . ' طلب مغادرة جديد تم استلامه', 'section_type' => 'user', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'new_availability_change_request', 'notification_sub_type' => 'new_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => $worker_id, 'created_at' => date('Y-m-d H:i:s'));
            Notification::insert($result_array);
        }

        if ($sps_id) {
            //Send Notification To SPS
            $result_array_sps = array('user_id' => $sps_id, 'message' => '' . $leave_request_id . ' New leave request has been received', 'message_ar' => '' . $leave_request_id . ' طلب مغادرة جديد تم استلامه', 'section_type' => 'user', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'new_availability_change_request', 'notification_sub_type' => 'new_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => $worker_id, 'created_at' => date('Y-m-d H:i:s'));
            Notification::insert($result_array_sps);
        }
    }



    /**
     * Fetch  availability reason type for change status request

     */
    public static function fetchAvailabilityRequestReasonType()
    {
        $result = WorkerAvailabilityRequestReasonType::all();

        return $result;
    }


    /**
     * Create availability request record
     */
    public static function createAvailabilityRequest($worker_id, $reason_type, $reason_description, $attachment, $from_datetime, $to_datetime)
    {
        $response_array = array('worker_id' => $worker_id, 'reason_type_id' => $reason_type, 'attachments' => $attachment, 'reason_description' => $reason_description, 'approval_status' => 'Approved', 'from_datetime' => $from_datetime, 'to_datetime' => $to_datetime, 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s'));
        $availability_request_id = ManageWorkerAvailabilityStatus::insertGetId($response_array);

        return $availability_request_id;
    }


    /**
     * Check if there are item requests by a worker based on user type.
     *
     * @param  int  $w_id  The ID of the worker.
     * @return string  Returns 'yes' or 'no' based on conditions for SP_ADMIN or SUPERVISOR.
     *                Returns 'admin_yes' or 'admin_no' based on conditions for ADMIN.
     *                Returns 'no' for other user types or unknown conditions.
     */
    public static function checkItemRequestsByWorker($w_id)
    {
        // Get the currently authenticated user
        $user = Auth::user();

        if ($user->user_type === 'sp_admin' || $user->user_type === 'supervisor') {
            // Check conditions for SP_ADMIN or SUPERVISOR
            $count = WorkOrderItemRequest::where('work_order_id', $w_id)
                ->where('status', 'requested')
                ->where('sent_to_project_owner', 0)
                ->count();

            return $count > 0 ? 'yes' : 'no';
        } elseif ($user->user_type === 'admin' || $user->user_type === 'admin_employee') {
            // Check conditions for ADMIN
            $count = WorkOrderItemRequest::where('work_order_id', $w_id)
                ->where('status', 'requested')
                ->where('sent_to_project_owner', 1)
                ->count();

            return $count > 0 ? 'admin_yes' : 'admin_no';
        }

        // Default case: unknown user type or conditions
        return 'no';
    }

    /**
     * Check if there are item requests by a worker based on user type are approved.
     *
     * @param  int  $w_id  The ID of the worker.
     * @return string  Returns 'yes' or 'no' based on conditions.
     */
    public static function checkItemRequestsByWorkerApprovedOrRejected($w_id)
    {
        // Check conditions for WorkOrderItemRequest
        $workOrderItemCount = WorkOrderItemRequest::where('work_order_id', $w_id)
            ->whereIn('status', ['partially_given', 'accepted', 'rejected'])
            ->count();

        // Check conditions for ServiceProviderMissingItemRequest
        $missingItemRequestCount = ServiceProviderMissingItemRequest::where('work_order_id', $w_id)
            ->whereIn('status', ['partially_given', 'accepted', 'rejected'])
            ->count();

        if ($workOrderItemCount > 0) {
            return 'worker';
        } elseif ($missingItemRequestCount > 0) {
            return 'serviceprovider';
        } else {
            return 'no';
        }
    }


    /**
     * Check if there are item requests by a sp
     *
     * @param  int  $w_id  The ID of the work order.
     * @return string  Returns 'yes' or 'no' based on conditions for SP_ADMIN or SUPERVISOR.
     *                Returns 'admin_yes' or 'admin_no' based on conditions for ADMIN.
     *                Returns 'no' for other user types or unknown conditions.
     */
    public static function checkItemRequestsForPo($w_id)
    {
        $count = ServiceProviderMissingItemRequest::where('work_order_id', $w_id)
            ->where('status', 'requested')
            ->count();
        return $count > 0 ? 'yes' : 'no';
    }

    public static function ItemsRequestedBySp($w_id)
    {
        $workOrderRequestedItems = ServiceProviderMissingItemRequest::with('requestedItems.contractUsableItem', 'user')
            ->where('work_order_id', $w_id)
            ->first();

        // Check if requestedItems is set
        if (isset($workOrderRequestedItems->requestedItems)) {
            // Initialize an array to track unique item_ids
            $uniqueItemIds = [];

            // Access the relationship data
            foreach ($workOrderRequestedItems->requestedItems as $key => $requestedItem) {
                // Get the $contractUsableItem object
                if ($requestedItem->contractUsableItem !== null) {
                    $contractUsableItem = $requestedItem->contractUsableItem->getItem();
                } else {
                    $contractUsableItem = null; // or some default value
                }

                // Fetch the item data
                $itemData = $requestedItem->getItem();

                // Check if $itemData->items is not null and is an instance of a collection
                $itemsArray = $itemData->items ? $itemData->items->toArray() : [];

                // Check if there are any items
                if (!empty($itemsArray)) {
                    // Get the first item from the array
                    $firstItem = $itemsArray[0];

                    // Get the warehouse_id and warehouse name
                    $warehouse_id = $firstItem['warehouse_id'] ?? null;
                    $getWarehouseName = $firstItem['warehouse']['name'] ?? null;
                } else {
                    $getWarehouseName = $requestedItem->getWarehouse()->name ?? '';
                    $warehouse_id = $requestedItem->getWarehouse()->id ?? 0;
                }

                // Assign the name property to the requestedItem
                $requestedItem->name = isset($contractUsableItem->name) ? $contractUsableItem->name : '';
                $requestedItem->category_name = isset($contractUsableItem->category->name) ? $contractUsableItem->category->name : '';
                $requestedItem->sale_price_formatted = isset($contractUsableItem->sale_price_formatted) ? $contractUsableItem->sale_price_formatted : '';
                $requestedItem->sale_price_vat_formatted = isset($contractUsableItem->sale_price_vat_formatted) ? $contractUsableItem->sale_price_vat_formatted : '';
                $requestedItem->country_of_origin_formatted = isset($contractUsableItem->country_of_origin_formatted) ? $contractUsableItem->country_of_origin_formatted : '';

                $requestedItem->status = $workOrderRequestedItems->status;
                $requestedItem->approved_by = $workOrderRequestedItems->user->name ?? '';
                $requestedItem->warehouse_name = $getWarehouseName;
                $requestedItem->warehouse_id = $warehouse_id;

                // Check if item_id is unique, and skip if it's a duplicate
                if (!in_array($requestedItem->item_id, $uniqueItemIds)) {
                    // Add the item_id to the uniqueItemIds array
                    $uniqueItemIds[] = $requestedItem->item_id;
                } else {
                    // Remove the current requestedItem if item_id is a duplicate
                    // This assumes you have a collection object (e.g., Eloquent collection)
                    $workOrderRequestedItems->requestedItems->forget($key);
                }
            }
        }

        // Return the modified requestedItems if needed
        return $workOrderRequestedItems ?? [];
    }


    /**
     * Reject availability request record
     */
    public static function rejectAvailabilityRequest($availability_request_id, $approve_reject_reason)
    {
        $response_array = array('approval_status' => 'Rejected', 'approve_reject_reason' => $approve_reject_reason, 'update_by' => Auth::user()->id, 'updated_at' => date('Y-m-d H:i:s'));

        $update_result = ManageWorkerAvailabilityStatus::where("id", $availability_request_id)->update($response_array);

        return $update_result;
    }


    /**
     * Accept availability request record
     */
    public static function acceptAvailabilityRequest($availability_request_id,$contract_id)
    {
        $response_array = array('approval_status' => 'Approved', 'contract_id' => $contract_id, 'update_by' => Auth::user()->id, 'updated_at' => date('Y-m-d H:i:s'));

        $update_result = ManageWorkerAvailabilityStatus::where("id", $availability_request_id)->update($response_array);

        return $update_result;
    }


    /**
     * Send notification to SPA & SP for changee availability request of worker

     */
    public static function sendCreateAvailabilityRequestNotification($availability_request_id, $worker_id)
    {
        //Send Notification To SP Admin
        $result_array = array('user_id' => $worker_id, 'message' => 'Your status has been set to offline by the supervisor', 'message_ar' => 'المشرف غير الحالة الى غير متواجد', 'section_type' => 'worker_app', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'new_availability_change_request', 'notification_sub_type' => 'new_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => $worker_id, 'created_at' => date('Y-m-d H:i:s'));
        Notification::insert($result_array);
    }



    /**
     * Send reject request notification to worker

     */
    public static function sendRejectAvailabilityRequestNotification($availability_request_id, $worker_id)
    {
        //Send Notification To Worker
        $result_array = array('user_id' => $worker_id, 'message' => 'Your status request to offline has been rejected', 'message_ar' => 'تم رفض طلب تغيير الحالة الى غير متواجد', 'section_type' => 'worker_app', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'reject_availability_change_request', 'notification_sub_type' => 'reject_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => Auth::user()->id, 'created_at' => date('Y-m-d H:i:s'));
        Notification::insert($result_array);
    }

    /**
     * Send approved request notification to worker

     */
    public static function sendApprovedAvailabilityRequestNotification($availability_request_id, $worker_id, $reason_type_id)
    {
        if ($reason_type_id == 1) {
            //Vacation
            $result_array = array('user_id' => $worker_id, 'message' => 'Your status has been changed to "Vacation"', 'message_ar' => 'تم تغيير حالتك إلى "إجازة”', 'section_type' => 'worker_app', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'approve_availability_change_request', 'notification_sub_type' => 'approve_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => Auth::user()->id, 'created_at' => date('Y-m-d H:i:s'));
        } elseif ($reason_type_id == 2) {
            //Not available
            $result_array = array('user_id' => $worker_id, 'message' => 'Your status has been changed to "Not Available”', 'message_ar' => 'تم تغيير حالتك إلى "غير متاح”', 'section_type' => 'worker_app', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'approve_availability_change_request', 'notification_sub_type' => 'approve_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => Auth::user()->id, 'created_at' => date('Y-m-d H:i:s'));
        } elseif ($reason_type_id == 3) {
            //Work Injury
            $result_array = array('user_id' => $worker_id, 'message' => 'Your status has been changed to "Work Injury”', 'message_ar' => 'تم تغيير حالتك إلى "إصابة عمل”', 'section_type' => 'worker_app', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'approve_availability_change_request', 'notification_sub_type' => 'approve_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => Auth::user()->id, 'created_at' => date('Y-m-d H:i:s'));
        } else {
            $result_array = array('user_id' => $worker_id, 'message' => 'Your status request to offline has been approved', 'message_ar' => 'تم قبول طلب تغيير الحالة الى غير متواجد', 'section_type' => 'worker_app', 'building_ids' => auth()->user()->building_ids, 'notification_type' => 'approve_availability_change_request', 'notification_sub_type' => 'approve_availability_change_request', 'additional_param' => $availability_request_id, 'created_by' => Auth::user()->id, 'created_at' => date('Y-m-d H:i:s'));
        }
        //Send Notification To Worker
        Notification::insert($result_array);
    }

    public static function getAllWarehouseList()
    {
        $resp = new Akaunting();
        $resp = $resp->inventory()->warehouses()->listWarehouses(1, 100)->json('data', []);
        $warehousesList = collect($resp ?? []);
        return $warehousesList;
    }

    public static function getAllWarehousesByCompanyId($company_id)
    {
        $akaunting = new Akaunting();
        $response = $akaunting->inventory()->warehouses()->listWarehouses(1, 100, $company_id);
        $warehouses = $response->json('data', []);
        return collect($warehouses);
    }

    /**
     * Change availability request record
     */
    public static function changeAvailabilityRequest($availability_request_id)
    {
        $response_array = array('is_leave_terminated' => 1, 'update_by' => Auth::user()->id, 'updated_at' => date('Y-m-d H:i:s'));

        $update_result = ManageWorkerAvailabilityStatus::where("id", $availability_request_id)->update($response_array);

        return $update_result;
    }

    public static function getFilteredCollection($allItems)
    {
        $tempArr = [];
        foreach ($allItems as $key => $value) {
            if ($value != null && isset($value->id)) {
                $newArr = [];
                $newArr['id'] = $value->id;
                $newArr['name'] = $value->name;
                $newArr['stock'] = $value->total_stock;
                $newArr['isChecked'] = 0;
                $newArr['missingQuantity'] = 0;
                $newArr['sale_price'] = $value->sale_price;
                $newArr['sale_price_formatted'] = $value->sale_price_formatted;
                $newArr['sale_price_vat_formatted'] = $value->sale_price_vat_formatted;
                $newArr['category'] = $value->category;
                $newArr['stock'] = $value->total_stock;
                $newArr['country_of_origin_formatted'] = $value->country_of_origin_formatted;
                array_push($tempArr, $newArr);
            }
        }
        return $tempArr;
    }

    public static function addWorkerLocationLog($data)
    {
        $data['timestamp'] = Carbon::now();
        $data['created_at'] = Carbon::now();

        $locationLog = WorkerLocationLog::create($data)->toSql();
        return $locationLog;
    }

    public static function deleteWorkerLogs($workerId)
    {
        $deleteLogs = WorkerLocationLog::where('worker_id', $workerId)->where('status', '')->where('is_on_property', true)->whereDate('created_at', now()->toDateString())->delete();
        return $deleteLogs;
    }


    /**
     * Read a availability request notification and fetch associated details.
     *
     * @param string|null $notification_read
     * @param string|null $notification_id
     * @param string|null $maintenance_request_id
     * @return array
     */
    public static function readAvailabilityRequestNotification($notification_read = null, $notification_id = null, $availability_request_id = null, $loggedInUser)
    {
        // Get the current user's ID
        $user_id = $loggedInUser->id;

        // Decrypt the notification ID
        $notification_id = Crypt::decryptString($notification_id);

        // Decrypt the request ID
        $availability_request_id = Crypt::decryptString($availability_request_id);

        // Retrieve the notification by its ID
        $notification = Notification::find($notification_id);

        // Create an array of users who have read the notification
        $read_user_arr = !empty($notification->is_read_by_users) ? explode(',', $notification->is_read_by_users) : [];

        // Add the current user to the list if not already present
        if (!in_array($user_id, $read_user_arr)) {
            $read_user_arr[] = $user_id;
        }

        // Update the list of users who have read the notification
        $notification->update(['is_read_by_users' => implode(',', $read_user_arr)]);



        // Retrieve availability request details
        $result = ManageWorkerAvailabilityStatus::where('id', $availability_request_id)
            ->first();

        if (isset($result)) {
            if ($result->approval_status == 'pending') {
                $result->approval_status = $result->from_datetime > now() ? 'pending' : 'expired';
            }

            $data['notification_request_status'] = $result->approval_status;
            $data['notification_request_id'] = $availability_request_id;
        } else {
            $data['notification_request_status'] = "-";
            $data['notification_request_id'] = "-";
        }

        return $data;
    }

    /**
     * Get checklist data for the specified task ID.
     *
     * @param int $taskId The ID of the checklist task.
     * @return array|null The checklist data or null if not found.
     */
    public static function getChecklistData($taskId)
    {
        // Retrieve checklist data from the database based on the task ID
        // Example code to fetch data from the database
        $checklistData = Checklists::find($taskId);

        return $checklistData;
    }

    /**
     * Get submitted data for the specified checklist task.
     *
     * @param int $taskId The ID of the checklist task.
     * @return array|null The submitted data or null if not found.
     */
    public static function getSubmittedDataForTask($taskId, $workOrderId, $workerId)
    {
        // Retrieve submitted data for the checklist task from the database
        $submittedData = ChecklistTasks::with([
            'noChecklistAction' => function ($query) use ($workOrderId, $workerId) {
                $query->where('work_order_id', $workOrderId)
                    ->where('worker_id', $workerId);
            },
            'checklistSubtasks.SubtaskActionsList'
        ])
            ->where('checklist_id', $taskId)
            ->whereHas('noChecklistAction', function ($query) use ($workOrderId, $workerId) {
                $query->where('work_order_id', $workOrderId)
                    ->where('worker_id', $workerId);
            })
            ->get();

        if (count($submittedData) > 0) {
            foreach ($submittedData as $checklistTasks) {
                if (count($checklistTasks->checklistSubtasks) > 0) {
                    foreach ($checklistTasks->checklistSubtasks as $subKey => $checklistSubtasks) {
                        $noChecklistSubtask = NoChecklistSubtask::where('checklist_subtask_id', $checklistSubtasks->id)->first();
                        // Assign uploaded photos and comment directly to the $checklistSubtasks object
                        $checklistSubtasks->uploadedPhotos = $noChecklistSubtask->photos;
                        $checklistSubtasks->uploadedComment = $noChecklistSubtask->comment;
                        if (count($checklistSubtasks->SubtaskActionsList) > 0) {
                            foreach ($checklistSubtasks->SubtaskActionsList as $actionKey => $subtaskActionsList) {
                                $noChecklistSubtaskAction = NoChecklistSubtaskAction::where('action_id', $subtaskActionsList->id)->where('checklist_subtask_id', $subtaskActionsList->checklist_subtask_id)->where('worker_id', $workerId)->where('no_checklist_actions_id', $checklistTasks->noChecklistAction->id)->first();
                                // Assign uploaded photos and comment directly to the $SubtaskActionsList object
                                $subtaskActionsList->toggle_quantity = $noChecklistSubtaskAction->toggle_value;
                            }
                        }
                    }
                }
            }
        }

        return $submittedData;
    }

    /**
     * Get general submitted data (without task association).
     *
     * @return array|null The general submitted data or null if not found.
     */
    public static function getGeneralSubmittedData($workOrderId, $workerId)
    {
        // Retrieve general submitted data from the database (without task association)
        // Example code to fetch data from the database
        $generalSubmittedData = NoChecklistAction::where('work_order_id', $workOrderId)->where('worker_id', $workerId)->first();

        return $generalSubmittedData;
    }


    /**
     * Get clear cache
     *
     * @param int $id The ID of the work order.
     */
    public static function clearCache()
    {
        // Clear Cache
        try {
            Artisan::call('cache:clear');
            Artisan::call('view:clear');
            Artisan::call('config:clear');

            Log::info('Cache cleared successfully!');
        } catch (\Exception $e) {
            Log::error('Error clearing cache: ' . $e->getMessage());
        }
    }



    public static function countMaintananceRequests(array $worker_ids)
    {
        $pending_orders = MaintenanceRequest::whereIn('maintanance_request.user_id', $worker_ids);
        $all_rejected_orders_count = clone $pending_orders;
        $all_approved_orders_count = clone $pending_orders;
        $all_pending_orders_count = clone $pending_orders;

        $all_rejected_orders_count = $all_rejected_orders_count->where('status', 'Rejected')->count();
        $all_approved_orders_count = $all_approved_orders_count->whereIn('status', ['In Progress', 'Completed'])->count();
        $all_pending_orders_count = $all_pending_orders_count->where('status', 'Pending')->count();

        $all_count = $all_rejected_orders_count + $all_approved_orders_count + $all_pending_orders_count;

        $pending_orders_count = [
            ['type' => 'all', 'value' => $all_count],
            ['type' => 'pending', 'value' => $all_pending_orders_count],
            ['type' => 'approved', 'value' => $all_approved_orders_count],
            ['type' => 'rejected', 'value' => $all_rejected_orders_count],
        ];

        return $pending_orders_count;
    }



    public static function fetchMaintananceRequest(array $worker_ids, $status, $search, $count)
    {
        $pending_orders = MaintenanceRequest::select('*')
            ->whereIn('maintanance_request.user_id', $worker_ids);
        if ($search) {
            $pending_orders->where('maintanance_request.id', 'like', '%' . $search . '%');
        }

        if ($status == "rejected") {
            $status = "Rejected";
            $pending_orders = $pending_orders->where('status', 'rejected');
        } elseif ($status == "pending") {
            $status = "Pending";
            $pending_orders = $pending_orders->where('status', 'Pending');
        } elseif ($status == "approved") {
            $status = "Approved";
            $pending_orders = $pending_orders->whereIn('status', ['In Progress', 'Completed']);
        }

        if ($search) {
            $pending_orders->where('maintanance_request.id', 'like', '%' . $search . '%');
        }
        $pending_orders = $pending_orders->orderBy('id', 'DESC');

        if ($count > 0) {
            $pending_orders = $pending_orders->offset($count * 10)->limit(10);
        } else {
            $pending_orders = $pending_orders->limit(10);
        }
        $pending_orders = $pending_orders->get();

        if (count($pending_orders) > 0) {
            foreach ($pending_orders as $key => $order) {
                $pending_orders[$key]->image1 = !empty($row->image1) ? ImagesUploadHelper::displayImageMobileApp($order->image1, 'uploads/maintanance_request') : '';
                $pending_orders[$key]->image2 = !empty($row->image2) ? ImagesUploadHelper::displayImageMobileApp($order->image2, 'uploads/maintanance_request') : '';
                $pending_orders[$key]->image3 = !empty($row->image3) ? ImagesUploadHelper::displayImageMobileApp($order->image3, 'uploads/maintanance_request') : '';
                $pending_orders[$key]->location = $order->building_name . ' ' . $order->floor . ' ' . $order->space_no;
                $pending_orders[$key]->name = $order->property_details->property_tag ?? null;
                unset($pending_orders[$key]->property_details);
            }
        }
        $pending_orders = json_decode(json_encode($pending_orders), true);
        $pending_orders = array_values($pending_orders);
        return $pending_orders;
    }


    public static function fetchLeaveRequest($worker_id, $status, $search, $count, $selected_language)
    {


        $leave_requests = ManageWorkerAvailabilityStatus::select('*')
            ->where('worker_id', $worker_id)->where('leave_request_id', '!=', '');

        if ($status == "rejected") {
            $status = "Rejected";
            $leave_requests = $leave_requests->where('approval_status', 'rejected');
        } elseif ($status == "pending") {
            $status = "Pending";
            $leave_requests = $leave_requests->where('approval_status', 'pending')->where('from_datetime', '>', now());
        } elseif ($status == "approved") {
            $status = "Approved";
            $leave_requests = $leave_requests->where('approval_status', 'approved');
        } elseif ($status == "expired") {
            $status = "Expired";
            $leave_requests = $leave_requests->where('approval_status', 'pending')->where('from_datetime', '<=', now());
        }

        if ($search) {
            $leave_requests->where('leave_request_id', 'like', '%' . $search . '%');
        }

        $leave_requests = $leave_requests->orderBy('id', 'DESC');

        if ($count > 0) {
            $leave_requests = $leave_requests->offset($count * 10)->limit(10);
        } else {
            $leave_requests = $leave_requests->limit(10);
        }
        $leave_requests = $leave_requests->get();
        if (count($leave_requests) > 0) {
            foreach ($leave_requests as $key => $order) {
                $leave_requests[$key]->reason_type_name = '';
                if ($order['approval_status'] == 'pending') {
                    $leave_requests[$key]->approval_status = $leave_requests[$key]->from_datetime > now() ? 'pending' : 'expired';
                }
                $reason_data = WorkerAvailabilityRequestReasonType::where('id', $leave_requests[$key]->reason_type_id)->first();
                if (isset($reason_data)) {

                    $leave_requests[$key]->reason_type_name = $selected_language == 'en' ? trim($reason_data->reason_type_en) : ($selected_language == 'ur' ? trim($reason_data->reason_type_ur) : trim($reason_data->reason_type_ar));
                }
            }
        }
        $leave_requests = json_decode(json_encode($leave_requests), true);
        $leave_requests = array_values($leave_requests);
        return $leave_requests;
    }


    public static function countLeaveRequests()
    {
        $leave_requests = ManageWorkerAvailabilityStatus::where('worker_id', auth()->user()->id)->where('leave_request_id', '!=', '');
        $all_rejected_orders_count = clone $leave_requests;
        $all_approved_orders_count = clone $leave_requests;
        $all_pending_orders_count = clone $leave_requests;
        $all_expired_orders_count = clone $leave_requests;

        $all_rejected_orders_count = $all_rejected_orders_count->where('approval_status', 'rejected')->count();
        $all_approved_orders_count = $all_approved_orders_count->where('approval_status', 'approved')->count();
        $all_pending_orders_count = $all_pending_orders_count->where('approval_status', 'pending')->where('from_datetime', '>', now())->count();
        $all_expired_orders_count = $all_expired_orders_count->where('approval_status', 'pending')->where('from_datetime', '<=', now())->count();

        $all_count = $all_rejected_orders_count + $all_approved_orders_count + $all_pending_orders_count + $all_expired_orders_count;

        $leave_requests_count = [
            ['type' => 'all', 'value' => $all_count],
            ['type' => 'pending', 'value' => $all_pending_orders_count],
            ['type' => 'approved', 'value' => $all_approved_orders_count],
            ['type' => 'rejected', 'value' => $all_rejected_orders_count],
            ['type' => 'expired', 'value' => $all_expired_orders_count],
        ];

        return $leave_requests_count;
    }


    public static function fetchLeaveRequestDetails($worker_id, $request_id, $selected_language)
    {
        $leave_requests = ManageWorkerAvailabilityStatus::select('*')
            ->where('worker_id', $worker_id)->where('id', $request_id)
            ->first();

        if (!empty($leave_requests)) {
            $leave_requests->reason_type_name = '';
            if ($leave_requests->approval_status == 'pending') {
                $leave_requests->approval_status = $leave_requests->from_datetime > now() ? 'pending' : 'expired';
            }
            if (trim($leave_requests->attachments) != "[]") {
                foreach (json_decode($leave_requests->attachments) as $k => $v) {
                    if (Str::endsWith($v, '.pdf')) {
                        $attachments[$k] = url('storage/worker_availability_status/' . preg_replace('/\[|\]|"/', '', $v));
                    } else {
                        $attachments[$k] = ImagesUploadHelper::displayImage($v, 'worker_availability_status');
                    }
                }

                $leave_requests->attachments = $attachments;
            } else {
                $leave_requests->attachments = '';
            }

            $reason_data = WorkerAvailabilityRequestReasonType::where('id', $leave_requests->reason_type_id)->first();
            if (isset($reason_data)) {

                $leave_requests->reason_type_name = $selected_language == 'en' ? trim($reason_data->reason_type_en) : ($selected_language == 'ur' ? trim($reason_data->reason_type_ur) : trim($reason_data->reason_type_ar));
            }
        }

        return $leave_requests;
    }



    /**
     * Check active approved offline request record
     */
    public static function checkActiveApprovedOfflineRequest()
    {
        $worker_id = Auth::user()->id;
        $result = ManageWorkerAvailabilityStatus::where('worker_id', $worker_id)
            ->where('approval_status', 'approved')
            ->where('from_datetime', '<=', now())
            ->where('to_datetime', '>=', now())
            ->where('is_leave_terminated', 0)
            ->first();

        return $result;
    }


    public static function manageWorkerLocation($workerId, $latitude, $longitude, $type)
    {
        $locationData = [
            'worker_id' => $workerId,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'is_on_property' => true,
            'logged_at' => Carbon::now(),
            'timestamp' => Carbon::now()
        ];

        $workerLocation = WorkerLocation::where('worker_id', $workerId)->whereDate('logged_at', now()->toDateString())->first();
        if (!$workerLocation) {
            $workerLocation = WorkerLocation::create($locationData);
        }
        $locationData['status'] = $type;
        self::addWorkerLocationLog($locationData);
        return true;
    }


    public static function workerClockIn($workerId, $latitude, $longitude, $property_id)
    {
        $lastRequest = WorkerAttendances::latest('id')->first();
        $lastNumber = $lastRequest ? $lastRequest->id : 0;
        $newNumber = $lastNumber + 1;
        $new_attendance_id = 'IO-' . str_pad($newNumber, 4, '0', STR_PAD_LEFT);

        $attendaneData = [
            'worker_id' => $workerId,
            'attendance_id' => $new_attendance_id,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'clock_in_property_id' => $property_id,
            'clock_in_datetime' => Carbon::now(),
            'created_at' => Carbon::now()
        ];

        $attendance_id = WorkerAttendances::insertGetId($attendaneData);

        $result = WorkerAttendances::where('id', $attendance_id)->first();

        return $result;
    }


    public static function workerClockOut($clock_in_datetime, $workerId, $property_id, $lastrecordid)
    {
        $startTime = Carbon::parse($clock_in_datetime);
        $endTime = Carbon::parse(Carbon::now());

        $diffInMinutes = $startTime->diffInMinutes($endTime);

        if ($diffInMinutes >= 0) {
            $hours = floor($diffInMinutes / 60);
            $minutes = $diffInMinutes % 60;

            $hours = str_pad($hours, 2, '0', STR_PAD_LEFT);
            $minutes = str_pad($minutes, 2, '0', STR_PAD_LEFT);

            $total_hrs = $hours . ':' . $minutes;
        } else {
            $total_hrs = '00:00';
        }

        $attendaneData = [
            'clock_out_property_id' => $property_id,
            'clock_out_by' => $workerId,
            'total_hrs' => $total_hrs,
            'clock_out_datetime' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];

        $UpdateAttendance = WorkerAttendances::where('id', $lastrecordid)->update($attendaneData);

        $result = WorkerAttendances::where('id', $lastrecordid)->first();

        return $result;
    }



    public static function fetchClockInHrs($clock_in_datetime)
    {
        $startTime = Carbon::parse($clock_in_datetime);
        $endTime = Carbon::parse(Carbon::now());

        $diffInSeconds = $startTime->diffInSeconds($endTime);

        if ($diffInSeconds >= 0) {
            $hours = floor($diffInSeconds / 3600);
            $minutes = floor(($diffInSeconds % 3600) / 60);
            $seconds = $diffInSeconds % 60;

            $hours = str_pad($hours, 2, '0', STR_PAD_LEFT);
            $minutes = str_pad($minutes, 2, '0', STR_PAD_LEFT);

            $hours = str_pad($hours, 2, '0', STR_PAD_LEFT);
            $minutes = str_pad($minutes, 2, '0', STR_PAD_LEFT);
            $seconds = str_pad($seconds, 2, '0', STR_PAD_LEFT);

            $total_hrs = $hours . ':' . $minutes . ':' . $seconds;
        } else {
            $total_hrs = '00:00';
        }

        return $total_hrs;
    }



    public static function fetchAttendanceHistory($worker_id, $approve_by, $search, $count, $dateRange)
    {
        // Calculate values
        $start_date = null;
        $end_date = null;
        if ($dateRange) {
            $dateRange = explode(',', $dateRange);
            $start_date = date('Y-m-d', strtotime($dateRange[0])) . ' 00:00:00';
            $end_date = date('Y-m-d', strtotime($dateRange[1])) . ' 23:59:00';
        }
        $Mainhistory = WorkerAttendances::select('*')->where('worker_attendances.worker_id', $worker_id)->whereNotNull('clock_out_datetime');

        $history_count = clone $Mainhistory;
        $history_count = $history_count->count();

        $history = clone $Mainhistory;

        if ($search) {
            $history->where('worker_attendances.attendance_id', 'like', '%' . $search . '%');
        }

        if ($approve_by == "supervisor") {
            $history = $history->where('clock_out_by', '!=', $worker_id);
        }

        if ($start_date && $end_date) {
            $history = $history->whereBetween('clock_in_datetime', [$start_date, $end_date]);
        }

        $history = $history->orderBy('id', 'DESC');

        if ($count > 0) {
            $history = $history->offset($count * 10)->limit(10);
        } else {
            $history = $history->limit(10);
        }
        $history = $history->get();

        if (count($history) > 0) {
            foreach ($history as $key => $order) {
                $history[$key]->clock_out_by_role = $order->clock_out_by != $worker_id ? 'Supervisor' : 'Worker';
                $history[$key]->clock_in_building_name = PropertyBuildings::where('id', $order->clock_in_property_id)->pluck('building_name')->first();
                $history[$key]->clock_out_building_name = PropertyBuildings::where('id', $order->clock_out_property_id)->pluck('building_name')->first();
            }
        }
        $history = json_decode(json_encode($history), true);
        $history = array_values($history);

        $historydata['count'] = $history_count;
        $historydata['data'] = $history;
        return $historydata;
    }


    /**
     * Clock Out by sp
     */
    public static function clockOutBySp($clock_in_datetime, $clock_out_datetime, $property_id, $request_id)
    {
        $startTime = Carbon::parse($clock_in_datetime);
        $endTime = Carbon::parse($clock_out_datetime);

        $diffInMinutes = $startTime->diffInMinutes($endTime);

        if ($diffInMinutes >= 0) {
            $hours = floor($diffInMinutes / 60);
            $minutes = $diffInMinutes % 60;

            $hours = str_pad($hours, 2, '0', STR_PAD_LEFT);
            $minutes = str_pad($minutes, 2, '0', STR_PAD_LEFT);

            $total_hrs = $hours . ':' . $minutes;
        } else {
            $total_hrs = '00:00';
        }

        $attendaneData = [
            'clock_out_property_id' => $property_id,
            'clock_out_by' => Auth::user()->id,
            'total_hrs' => $total_hrs,
            'clock_out_datetime' => $clock_out_datetime,
            'updated_at' => Carbon::now()
        ];

        $UpdateAttendance = WorkerAttendances::where('id', $request_id)->update($attendaneData);

        $result = WorkerAttendances::where('id', $request_id)->first();

        return $result;
    }

    public static function sumTimeArray($times)
    {
        $totalMinutes = 0;
        foreach ($times as $time) {
            list($hours, $minutes) = explode(':', $time);
            $totalMinutes += (int) $hours * 60 + (int) $minutes;
        }

        $totalHours = str_pad(floor($totalMinutes / 60), 2, '0', STR_PAD_LEFT);
        $minutes = str_pad($totalMinutes % 60, 2, '0', STR_PAD_LEFT);


        $total_hrs = $totalHours . ':' . $minutes;

        return $total_hrs;
    }



    public static function sumfullTimeArray($times)
    {
        $totalSeconds = 0;
        foreach ($times as $time) {
            list($hours, $minutes, $seconds) = explode(':', $time);

            // Convert to total seconds
            $totalSeconds += (int) $hours * 3600 + (int) $minutes * 60 + (int) $seconds;
        }

        // Calculate total hours, minutes, and seconds from total seconds
        $totalHours = floor($totalSeconds / 3600);
        $remainingSeconds = $totalSeconds % 3600;
        $totalMinutes = floor($remainingSeconds / 60);
        $totalSeconds = $remainingSeconds % 60;

        // Format the result
        $formattedHours = str_pad($totalHours, 2, '0', STR_PAD_LEFT);
        $formattedMinutes = str_pad($totalMinutes, 2, '0', STR_PAD_LEFT);
        $formattedSeconds = str_pad($totalSeconds, 2, '0', STR_PAD_LEFT);

        $totalTime = $formattedHours . ':' . $formattedMinutes . ':' . $formattedSeconds;

        return $totalTime;

    }


    public static function updateAndFetchWorkorderTracking($work_order_id, $job_type)
    {
        // Fetch all tracking records for the work order with the necessary conditions in one query
        $trackingRecords = WorkerWorkOrderTimeTracking::where('work_order_id', $work_order_id)
           // ->where('worker_id', Auth::id())
            ->where('is_job_complete', 0)
            ->get();
        // Separate the ongoing record (with null 'end_datetime') and completed ones (with non-null 'end_datetime')
        $ongoingRecord = $trackingRecords->firstWhere('end_datetime', null);

        // Update the ongoing record if it exists
        if ($ongoingRecord) {
            $time_spent_minutes = Carbon::parse($ongoingRecord->start_datetime)->diffInMinutes(Carbon::now());
            $time_spent = self::fetchClockInHrs($ongoingRecord->start_datetime);

            $ongoingRecord->update([
                'time_spent' => $time_spent,
                'time_spent_minutes' => $time_spent_minutes,
                'end_datetime' => now(),
                'updated_at' => now()
            ]);
        }
        if ($job_type == 'complete') {
            $completedRecords = $trackingRecords->whereNotNull('end_datetime');
            // Calculate and return the time tracking details from the same fetched data
            $result = self::workorderTrackingTimedetails($completedRecords);

            WorkerWorkOrderTimeTracking::where('work_order_id', $work_order_id)
              //  ->where('worker_id', Auth::user()->id)
                ->where('is_job_complete', 0)
                ->update(['is_job_complete' => 1]);

            return $result;
        }

    }


    public static function workorderTrackingTimedetails($completedRecords)
    {

        $result = [
            'time_spent' => "00:00:00",
            'time_spent_minutes' => "00"
        ];
        // Calculate the total time spent and total minutes from completed records
        if ($completedRecords->isNotEmpty()) {
            $time_spent = $completedRecords->pluck('time_spent')->toArray();
            $result['time_spent'] = self::sumfullTimeArray($time_spent);
            $result['time_spent_minutes'] = $completedRecords->sum('time_spent_minutes');
        }

        return $result;
    }



    public static function getWorkorderstarttime($wtf_start_time, $preventive_start_time)
    {

        if (!is_null($preventive_start_time) && $preventive_start_time !== '' && $preventive_start_time !== '00:00:00' && !empty($preventive_start_time)) {
            return $preventive_start_time;
        } else {
            // Handle case when the value is null, empty, blank, or 00:00:00
            return $wtf_start_time; // or any other default value
        }
    }



    public static function getWorkorderscount($from_date, $to_date, $search, $property_id, $zone_id, $unit_id, $service_type, $asset_name_id)
    {
        $worker_id = Auth::user()->id;
        $tdate = now(); // Use Carbon for better date handling
        $loginuser_asset_categories = Auth::user()->asset_categories ? explode(',', Auth::user()->asset_categories) : [0];

        $workOrdersQuery = function ($conditions) use ($worker_id, $loginuser_asset_categories) {
            return WorkOrders::where('worker_id', $worker_id)
                ->whereIn('asset_category_id', $loginuser_asset_categories)
                ->whereRaw($conditions);
        };

        // Define queries using the helper function
        $rejected_order_id = $workOrdersQuery("(status = 2 and sp_reopen_status != 2 and
                                       (workorder_journey = 'job_execution' OR workorder_journey = 'job_evaluation')
                                       and (bm_approve_job = 1 OR sp_approve_job = 1
                                       OR (bm_approve_job = 0 AND sp_approve_job = 0 AND workorder_journey = 'job_execution' AND reason != '')))");

        $re_open_order_id = $workOrdersQuery("(status = 2 and workorder_journey = 'job_execution' and sp_reopen_status = 2)")
            ->whereNull('worker_started_at');

        $on_hold_order_id = $workOrdersQuery("(status = 3 and (workorder_journey = 'job_execution' or workorder_journey = 'job_evaluation'))");

        $over_due_order_id = $workOrdersQuery("(target_date < '$tdate' and status = 2 and workorder_journey = 'job_execution'
                                        and bm_approve_job != 1 AND sp_approve_job != 1 and sp_reopen_status != 2)");

        $completed_order_id = $workOrdersQuery("job_submitted_at IS NOT NULL")
            ->whereRaw('UNIX_TIMESTAMP(job_submitted_at) > 0')
            ->whereIn('job_completed_by', ['worker', 'SP']);

        if (isset($from_date) || isset($to_date)) {
            $start_date = Carbon::parse($from_date)->startOfDay();
            $end_date = Carbon::parse($to_date)->endOfDay();

            $dateCondition = function ($query) use ($start_date, $end_date) {
                $query->whereRaw("(IF(work_order_type != 'preventive', created_at, start_date) BETWEEN ? AND ?)", [$start_date, $end_date]);
            };

            $rejected_order_id->when($start_date && $end_date, $dateCondition);
            $re_open_order_id->when($start_date && $end_date, $dateCondition);
            $on_hold_order_id->when($start_date && $end_date, $dateCondition);
            $over_due_order_id->when($start_date && $end_date, $dateCondition);
            $completed_order_id->when($start_date && $end_date, $dateCondition);
        }

        // Additional Filters
        $filters = [
            'property_id' => 'property_id',
            'unit_id' => 'unit_id',
            'zone_id' => 'floor',
            'service_type' => 'service_type',
            'asset_name_id' => 'asset_name_id',
        ];

        foreach ($filters as $filter => $column) {
            if (!empty($$filter)) {
                $values = explode(',', $$filter);
                foreach ([$rejected_order_id, $re_open_order_id, $on_hold_order_id, $over_due_order_id, $completed_order_id] as $query) {
                    $query->whereIn($column, $values);
                }
            }
        }

        // Search Filter
        if ($search) {
            foreach ([$rejected_order_id, $re_open_order_id, $on_hold_order_id, $over_due_order_id, $completed_order_id] as $query) {
                $query->where('work_order_id', 'like', "%{$search}%");
            }
        }

        // Result Count
        $result = [
            ['type' => 'completed', 'value' => $completed_order_id->count()],
            ['type' => 'overdue', 'value' => $over_due_order_id->count(),],
            ['type' => 'onhold', 'value' => $on_hold_order_id->count()],
            ['type' => 'reopen', 'value' => $re_open_order_id->count()],
            ['type' => 'rejected', 'value' => $rejected_order_id->count()],
        ];
        return $result;

    }


    public static function getWorkorderstotalcountforAssignedWorkers()
    {
        $worker_id = Auth::user()->assigned_workers ? explode(',', Auth::user()->assigned_workers) : [0];
        $tdate = now(); // Use Carbon for better date handling
        $loginuser_asset_categories = Auth::user()->asset_categories ? explode(',', Auth::user()->asset_categories) : [0];
        $workOrdersQuery = function ($conditions) use ($worker_id, $loginuser_asset_categories) {
            return WorkOrders::whereIn('worker_id', $worker_id)
                //->whereIn('asset_category_id', $loginuser_asset_categories)
                ->whereRaw($conditions);
        };

        // Define queries using the helper function
        $rejected_order_id = $workOrdersQuery("(status = 2 and sp_reopen_status != 2 and
                                       (workorder_journey = 'job_execution' OR workorder_journey = 'job_evaluation')
                                       and (bm_approve_job = 1 OR sp_approve_job = 1
                                       OR (bm_approve_job = 0 AND sp_approve_job = 0 AND workorder_journey = 'job_execution' AND reason != '')))");

        $re_open_order_id = $workOrdersQuery("(status = 2 and workorder_journey = 'job_execution' and sp_reopen_status = 2)")
            ->whereNull('worker_started_at');

        $on_hold_order_id = $workOrdersQuery("(status = 3 and (workorder_journey = 'job_execution' or workorder_journey = 'job_evaluation'))");

        $over_due_order_id = $workOrdersQuery("(target_date < '$tdate' and status = 2 and workorder_journey = 'job_execution'
                                        and bm_approve_job != 1 AND sp_approve_job != 1 and sp_reopen_status != 2)");

        $completed_order_id = $workOrdersQuery("job_submitted_at IS NOT NULL")
            ->whereRaw('UNIX_TIMESTAMP(job_submitted_at) > 0')
            ->whereIn('job_completed_by', ['worker', 'SP']);


        // Result Count
        $result = [
            ['type' => 'completed', 'value' => $completed_order_id->count()],
            ['type' => 'overdue', 'value' => $over_due_order_id->count(),],
            ['type' => 'onhold', 'value' => $on_hold_order_id->count()],
            ['type' => 'reopen', 'value' => $re_open_order_id->count()],
            ['type' => 'rejected', 'value' => $rejected_order_id->count()],
        ];
        return $result;

    }


    
public static function getTeamLeaderUpcomingTaskCount($examine = NULL)
  {
    $tdate = date('Y-m-d');
    $worker_id = Auth::user()->assigned_workers ? explode(',', Auth::user()->assigned_workers) : [0];
    $loginuser_asset_categories = trim(Auth::user()->asset_categories) != "" ? Auth::user()->asset_categories : 0;
    $upcoming_orders = WorkOrders::with('workerTimings');
    
      $upcoming_orders = $upcoming_orders->select('work_orders.id', 'work_orders.work_order_id', 'poa.project_id', 'work_orders.property_id', 'work_orders.service_type', 'work_orders.asset_name_id', 'work_orders.sp_reopen_status', 'work_orders.worker_id', 'work_orders.old_worker_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'assets.asset_tag', 'assets.asset_symbol', 'assets.asset_number', 'contracts.contract_number', 'properties.latitude', 'properties.longitude', 'properties.location', 'property_buildings.building_name', 'work_orders.asset_category_id', 'work_orders.work_order_type', 'work_orders.frequency_id', 'projects_details.project_name as project', 'work_orders.priority_id', 'work_orders.response_time', 'work_orders.worker_started_at', 'work_orders.sp_approve_job', 'work_orders.bm_approve_job')
      ;
    $upcoming_orders = $upcoming_orders->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->join('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->join('properties', 'properties.id', '=', 'property_buildings.property_id')
                        ->join('users', 'users.id', '=', 'work_orders.created_by')
                        ->leftjoin('users as poa', 'poa.id', '=', 'contracts.user_id')
                        ->leftjoin('projects_details', 'projects_details.id', '=', 'poa.project_id')
                        ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
                        //->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
                        ->where('job_status', '<>', 'active')
                        ->where(fn($query) =>
                        $query->where(function ($query) use ($tdate, $examine) {

                          if($examine == NULL) {
                              // If $examine is null, apply based on work_orders.type
                              $query->where(function ($q) use ($tdate) {
                                $q->where(function ($inner) use ($tdate) {
                                    $inner->where('work_orders.work_order_type', 'reactive')
                                          ->where(function ($cond) use ($tdate) {
                                              $cond->where('work_orders.start_date', '>=', $tdate)
                                                  ->orWhere('work_orders.start_date', '<=', $tdate);
                                          });
                                })
                                ->orWhere(function ($inner) use ($tdate) {
                                    $inner->where('work_orders.work_order_type', 'preventive')
                                          ->where('work_orders.start_date', '<=', $tdate);
                                });
                            });
                          }
                          else
                          {
                              // If $examine is not null, apply both conditions
                              $query->where(function ($q) use ($tdate) {
                                $q->where('work_orders.start_date', '>=', $tdate)
                                  ->orWhere('work_orders.start_date', '<=', $tdate);
                            });
                          }
                          // $q->where('work_orders.start_date', '>=', date('Y-m-d'))
                          //       ->orWhere('work_orders.start_date', '<=', date('Y-m-d'));
                        })
                                    ->orWhere(fn($subQuery) =>
                                        $subQuery->where('work_orders.assign_type', AssignType::Smart->value)
                                                ->where('work_orders.job_started_at', '<=', date('Y-m-d') . ' 23:59:59')
                                    ));
                        if($examine == NULL) {
                          $upcoming_orders = $upcoming_orders->whereRaw("( (work_orders.status = 1 and workorder_journey = 'job_execution') OR ((work_orders.status = 2 or work_orders.status = 3) and (workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 and (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1))))");
                        }
                        else
                        {
                          $upcoming_orders = $upcoming_orders->where('workorder_journey', 'job_execution')->whereRaw("(work_orders.status = 2 or work_orders.status = 3) and (work_orders.bm_approve_job != 1 and (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1))");
                        }

                        $upcoming_orders = $upcoming_orders->where(function ($query) use ($worker_id) {
                          $query->where('work_orders.worker_id', $worker_id)->whereNull('worker_started_at')
                                ->orWhere(function ($query) use ($worker_id) {
                                    $query->whereIn('work_order_workers.worker_id', $worker_id)
                                          ->where('work_orders.is_collaborative', 1);
                                })
                                ->where(function ($query) use ($worker_id) {
                                    $query->whereDoesntHave('workerTimings', function ($query) use ($worker_id) {
                                        $query->whereIn('worker_id', $worker_id);
                                    })
                                    ->orWhereDoesntHave('workerTimings');
                                });
                        });
        if($examine == NULL) {
          $upcoming_orders = $upcoming_orders->whereNull('examine_button_clicked_at');
        } else {
          $upcoming_orders = $upcoming_orders->whereNotNull('examine_button_clicked_at');
        }
        
    return $upcoming_orders->count();
  }







  public static function getWorkerLocationData()
  {
    $workerIds = Auth::user()->assigned_workers ? explode(',', Auth::user()->assigned_workers) : [0];

    $openWorkerIds = WorkerAttendances::whereIn('worker_id', $workerIds)
    ->whereNull('clock_out_datetime')
    ->pluck('worker_id')
    ->unique()
    ->values()
    ->toArray();

    if(count($openWorkerIds) > 0)
    {
        $workerIds = $openWorkerIds;
    }
    $locationData = WorkerLocation::with(['worker' => function ($q) {
        $q->select('id', 'profile_img','name','email as worker_id','profession_id'); // Fetch only what's needed
    },'worker.profession'])
    ->whereIn('worker_id', $workerIds)
    ->orderBy('worker_id')
    ->orderByDesc('created_at') // or 'id' if that tracks latest
    ->get()
    ->unique('worker_id')       // keeps only the first record per worker
    ->values();
    // Append the accessor so it's included in JSON
    $locationData->each(function ($location) {
        if ($location->worker) {
            $location->worker->profile_img_url = ImagesUploadHelper::displayImageMobileApp($location->worker->profile_img, 'uploads/profile_images', 0, true);
        }
    });

    return $locationData;
  }


    public static function getFetchWorkorderbyStatus($status, $count, $from_date, $to_date, $search, $property_id, $zone_id, $unit_id, $service_type, $asset_name_id)
    {
        $worker_id = Auth::user()->id;
        $tdate = date('Y-m-d H:i:s');
        $loginuser_asset_categories = !empty(trim(Auth::user()->asset_categories)) ? explode(',', Auth::user()->asset_categories) : [0];

        // Start building the query
        $query = WorkOrders::with([
            'propertyBuilding' => function ($query) {
                $query->select('id', 'building_name', 'property_id');
            }
        ])->whereIn('asset_category_id', $loginuser_asset_categories)
            ->where('worker_id', $worker_id);
        // Status conditions
        switch ($status) {
            case 'rejected':
                $query->whereRaw("(work_orders.status = 2 and work_orders.sp_reopen_status != 2 and (work_orders.workorder_journey = 'job_execution' OR work_orders.workorder_journey = 'job_evaluation') and (work_orders.bm_approve_job = 1 OR work_orders.sp_approve_job = 1 OR (work_orders.bm_approve_job = 0 AND work_orders.sp_approve_job = 0 AND work_orders.workorder_journey = 'job_execution' AND work_orders.reason != '')))");
                break;

            case 'reopen':
                $query->whereRaw("(work_orders.status = 2 and work_orders.workorder_journey = 'job_execution' and work_orders.sp_reopen_status = 2 )")->whereNull('worker_started_at');
                break;

            case 'completed':
                $query->whereNotNull('job_submitted_at')
                    ->whereRaw('UNIX_TIMESTAMP(job_submitted_at) > 0')
                    ->WhereIn('job_completed_by', ['worker', 'SP']);
                break;

            case 'onhold':
                $query->whereRaw("(work_orders.status = 3 and (work_orders.workorder_journey = 'job_execution' or work_orders.workorder_journey = 'job_evaluation'))");
                break;

            case 'overdue':
                $query->whereRaw("(work_orders.target_date < '$tdate' and (work_orders.status = 2) and (work_orders.workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 AND work_orders.sp_approve_job != 1) and (work_orders.sp_reopen_status != 2))");
        }



        if ((isset($from_date) && !empty($from_date)) || (isset($to_date) && !empty($to_date))) {
            if (isset($from_date) && !empty($from_date)) {
                $start_date = Carbon::parse($from_date)->startOfDay();
                $query->where(function ($q) use ($start_date) {
                    $q->whereRaw('IF(work_orders.work_order_type != "preventive", work_orders.created_at, work_orders.start_date) >= ?', [$start_date]);
                });
            }

            if (isset($to_date) && !empty($to_date)) {
                $end_date = Carbon::parse($to_date)->endOfDay();
                $query->where(function ($q) use ($end_date) {
                    $q->whereRaw('IF(work_orders.work_order_type != "preventive", work_orders.created_at, work_orders.start_date) <= ?', [$end_date]);
                });
            }
        }

        if ($search != null) {
            $query->where('work_orders.work_order_id', 'like', '%' . $search . '%');
        }

        if ($property_id != null && $property_id != "") {
            $query->whereIn('work_orders.property_id', explode(',', $property_id));
        }

        if ($unit_id != null && $unit_id != "") {
            $query->whereIn('work_orders.unit_id', explode(',', $unit_id));
        }


        if ($zone_id != null && $zone_id != "") {
            $query->whereIn('work_orders.floor', explode(',', $zone_id));
        }

        if ($service_type != null && $service_type != "") {
            $query->whereIn('work_orders.service_type', explode(',', $service_type));
        }

        if ($asset_name_id != null && $asset_name_id != "") {
            $query->whereIn('work_orders.asset_name_id', explode(',', $asset_name_id));
        }

       /*if ($count > 0) {
            $query->offset($count * 10)->limit(10);
        } else {
            $query->limit(10);
        }*/

        //new addition for pagination issue
        $offset = ($count - 1) * 10;

        if($count > 0){
            $query->offset($offset)->limit(10);
        }

        else{
            $query->limit(10);
        }
        
        // Fetching the results
        $workOrders = $query->get();

        // Transforming the results to include property_name and status_label
        $workOrders = $workOrders->map(function ($workOrder) use($status) {
            $workOrder->property_name = $workOrder->propertyBuilding->building_name ?? null;
            $workOrder->status_label = $status;  // Assuming you have a method to map status to label
            unset($workOrder->propertyBuilding);
            return $workOrder;
        });

        return $workOrders;
    }



    public static function getPropertyFilterValues()
    {

        $worker_id = Auth::user()->id;
        $loginuser_asset_categories = !empty(trim(Auth::user()->asset_categories)) ? explode(',', Auth::user()->asset_categories) : [0];

        try {
            $untsdata = WorkOrders::with([
                'propertyBuilding' => function ($query) {
                    $query->select('id', 'building_name', 'property_id');
                }
            ])->whereIn('asset_category_id', $loginuser_asset_categories)
                ->where('worker_id', $worker_id);
            $result['zone'] = $untsdata->pluck('floor')->unique()->toArray();
            $unit_ids = array_values($untsdata->pluck('unit_id')->unique()->toArray());
            $result['unit'] = [];

            if(isset($unit_ids) && count($unit_ids) > 0){
                $result['unit'] = RoomsTypeFloors::select('id','room')->whereIn('id',$unit_ids)->get()->toArray();
            }

            return $result;
        } catch (\Throwable $th) {
            Log::error("getPropertyFilterValues error: " . $th);
        }
    }




    public static function updatePauseWorkorderdata($work_order_id)
    {
        // Fetch pause work order details
        $ongoingRecord = WorkOrders::where('id', $work_order_id)
            ->where('status',3)
            ->whereNotNull('pause_start_time')
            ->whereNull('pause_end_time')
            ->first();

        // Update the ongoing record if it exists
        if ($ongoingRecord) {
            $time_spent_minutes = Carbon::parse($ongoingRecord->pause_start_time)->diffInMinutes(Carbon::now());
            $time_spent = self::fetchClockInHrs($ongoingRecord->pause_start_time);

            $ongoingRecord->update([
                'pause_time_spent' => $time_spent,
                'pause_time_spent_minutes' => $time_spent_minutes,
                'status' => $ongoingRecord->workorder_journey === 'submitted' ? 1 : 2,
                'pause_end_time' => now()
            ]);
        }

    }


    public static function getWorkorderPpmreportstatus($workorder_unique_id)
    {
        $getReportId = PpmRequestWorkordersMapping::where('workorder_unique_id',$workorder_unique_id)->orderby('id','desc')->pluck('ppm_request_id')->first();
        if($getReportId)
        {
            $reportStatus = ManagePpmRequest::where('id',$getReportId)->pluck('status')->first();
            if($reportStatus)
            {
                return $reportStatus;
            }
        }
        return 'not found';
    }





    public static function hasErpEnabledWorkerProjects()
    {
            $user = Auth::user();

            // Fetch project IDs mapped to the service provider
            $projectIds = ServiceProviderProjectMapping::where('service_provider_id', $user->service_provider)
                ->pluck('project_id');

            // If no projects are mapped, return false
            if ($projectIds->isEmpty()) {
                $projectIds = [0];
            }

            // Check if any project has ERP module enabled
            return ProjectsDetails::whereIn('id', $projectIds)
                ->where('use_erp_module', 1)
                ->pluck('id')
                ->toArray();
    }




    public static function checkPropertyOsoolplusProject($building_id)
    {
        $erpEnabledProjects = self::hasErpEnabledWorkerProjects();

        if (empty($erpEnabledProjects)) {
            return false;
        }

        $building = PropertyBuildings::find($building_id);

        if (!$building) {
            return false;
        }
        $property = Property::find($building->property_id);

        if (!$property) {
            return false;
        }

        $property_user_id = $property->user_id;

        $exists = ProjectSettings::whereIn('project_id', $erpEnabledProjects)
            ->where('user_id', $property_user_id)
            ->exists();

        return $exists;
    }
}
