<?php

namespace App\Http\Livewire\Accounting\Bill;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Log;
use App\Services\Finance\FinanceBillService;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\Finance\BillExport;
class Bill extends Component
{
    use WithPagination;


    public $search = '';
    public $perPage = 10;
    public $sortField = 'bill_number';
    public $sortDirection = 'asc';
    public $from_date;
    public $to_date;
    public $searchVendor;
    public $searchStatus;

    // API response data
    public $listdata = [];
    public $total = 0;
    public $currentPage = 1;
    public $lastPage = 1;
    public $loading = false;
    public $error = null;
    public $documentNumber='';
    public $documentID;
    public $vendors = [];
    public $statuses = [];

    public $showFullView = true;
   
   public function applyFilters()
    {
        $this->fetch();
    }

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'currentPage' => ['except' => 1, 'as' => 'page'],
    ];

    protected $listeners = [
        'delete' => 'delete',
    ];

    public function mount($showFullView)
    {
        $this->showFullView = $showFullView;
        $this->fetch();
    }

    public function updatingSearch()
    {
        $this->resetPage();
        $this->fetch();
    }
   

    public function updatingPerPage()
    {
        $this->resetPage();
        $this->fetch();
    }
      

    /**
     * Reset pagination to first page
     */
    public function resetPage()
    {
        $this->currentPage = 1;
    }

  
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
        $this->fetch();
    }

  
    public function fetch()
    {
        $finance = app(FinanceBillService::class);
        $params = [
            'page' => $this->currentPage,
            'per_page' => $this->perPage,
            'search' => $this->search,
            'sort_by' => $this->sortField,
            'sort_dir' => $this->sortDirection,
            'from_date' => $this->from_date,
            'to_date' => $this->to_date,
            'status' => $this->searchStatus,
            'vendor' => $this->searchVendor,
        ];
      
        $params = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });
        $response = $finance->list($params);
        $dropdownlist = $finance->filterDatalist();

        $this->statuses = $dropdownlist['statuses'] ?? [];
        $this->vendors = $dropdownlist['vendors'] ?? [];


        //dd($dropdownlist);
        if (isset($response['status']) && ($response['status'] === 'success') && count($response['data']['items'])) {
            $responseData = $response['data'] ?? [];
            $this->listdata = $responseData['items'] ?? [];
            $this->total = $responseData['total'] ?? 0;
            $this->currentPage = $responseData['current_page'] ?? 1;
            $this->lastPage = ceil($this->total / $this->perPage);
        }
    }


   
    

    /**
     * Navigate to next page
     */
    public function nextPage()
    {
        if ($this->currentPage < $this->lastPage) {
            $this->currentPage++;
            $this->fetch();
        }
    }

    /**
     * Navigate to previous page
     */
    public function previousPage()
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
            $this->fetch();
        }
    }

    /**
     * Navigate to specific page
     */
    public function gotoPage($page)
    {
        if ($page >= 1 && $page <= $this->lastPage) {
            $this->currentPage = $page;
            $this->fetch();
        }
    }
    public function refresh()
    {

        $this->reset([
            'search',
            'from_date',
            'to_date',
            'searchStatus',
            'searchVendor',
        ]);

        $this->fetch();
    }

    public function render()
    {
        // Create pagination info object for the view
        $paginationInfo = (object) [
            'data' => $this->listdata,
            'total' => $this->total,
            'per_page' => $this->perPage,
            'current_page' => $this->currentPage,
            'last_page' => $this->lastPage,
        ];
        return view('livewire.accounting.bill.index', [
            'data' => collect($this->listdata),
            'pagination' => $paginationInfo,
            'loading' => $this->loading,
            'error' => $this->error,
        ]);
    }


    public function goToCreatePage()
    {
        return redirect()->route('finance.bill.create');
    }

    public function goToEdit($id)
    {
        return redirect()->route('finance.bill.edit', ['id' => $id]);
    }

    public function goToView($id)
    {
        return redirect()->route('finance.bill.view', ['id' => $id]);
    }



    public function openDeleteModal($id, $customerName)
    {
        $this->emit('confirmDelete', $id, $customerName, 'delete');
    }

    public function delete($id)
    {

        $this->loading = true;
        $this->error = null;
        $finance = app(FinanceBillService::class);
        $response = $finance->delete($id);
        
        if (isset($response['status']) && $response['status'] === 'success') {
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'success',
                'message' => $response['message']
            ]);

        } else {
            $this->error = $response['message'] ?? __('customers.messages.failed_to_delete_customer');
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'error',
                'message' => $response['message']
            ]);
        }

        $this->fetch();
        $this->dispatchBrowserEvent('close-modal');
       
    }



    public function copyURL( $id )
    {

        $url = route( 'finance.bill', ['id'=>$id ] );
        $this->dispatchBrowserEvent('copyToClipboard', ['text' => $url]);
        $this->dispatchBrowserEvent( 'show-toast', [
        'url' => $url,
        'type' => 'success',
        'message' => __('accounting.url_copied'),
        ] );
    }

    public function OpenConfirmDuplicateModal($id, $documentNumber)
    {   
        $this->documentID = $id;
        $this->dispatchBrowserEvent('open-modal', [
            'modalId' => 'confirmDuplicateDocumentModal',
            'id' => $id,
            'documentNumber' => $documentNumber,
        ]);
    }

    public function duplicateDocument() {

        $this->documentID;
        $finance= app( FinanceBillService::class );
        $response = $finance->duplicate( $this->documentID );
        if (isset($response['status']) && $response['status'] == 'success') {
            $this->dispatchBrowserEvent( 'show-toast', [
                'type' => 'success',
                'message' => $response['message']
            ] );
         }else{
            $this->dispatchBrowserEvent( 'show-toast', [
                'type' => 'error',
                'message' =>$response['message']
            ] );
         }
        $this->fetch();
        $this->dispatchBrowserEvent('close-modal', [
            'modalId' => 'confirmDuplicateDocumentModal'
        ]);
        $this->dispatchBrowserEvent('reload-page');
    }


    // public function export()
    // {
    //     try {
    //         $this->dispatchBrowserEvent('export-start');
    //         $file = Excel::download(new BillExport(collect($this->listdata)), 'bill.xlsx');
    //         // Emit event to hide loading after the download response is triggered
    //         $this->dispatchBrowserEvent('export-end');
    //         return $file;
    //     } catch (\Exception $e) {
    //         $this->dispatchBrowserEvent('export-end');
    //         $this->dispatchBrowserEvent('show-toast', [
    //             'type' => 'error',
    //             'message' => ''
    //         ]);
    //         Log::error('Error exporting bill: ' . $e->getMessage());
    //     }
    // }

    public function export()
    {
        $finance = app(FinanceBillService::class);
        $response = $finance->export();
         $base64 = base64_encode($response->getContent());
         $mime = 'application/xlsx';
        $this->dispatchBrowserEvent('download-blob', [
            'fileName' => 'downloaded-file.xlsx',
            'base64'   => $base64,
            'mime'     => $mime
        ]);
    }

   


}
