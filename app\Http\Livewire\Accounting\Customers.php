<?php

namespace App\Http\Livewire\Accounting;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Log;
use App\Services\Finance\FinanceCustomerService;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\Finance\CustomersExport;

class Customers extends Component
{
    use WithPagination;

    public $viewMode = 'list'; // 'list' or 'cards'
    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';

    // API response data
    public $customers = [];
    public $total = 0;
    public $currentPage = 1;
    public $lastPage = 1;
    public $loading = false;
    public $error = null;
    public $customerId;

    public $name, $email, $contact, $tax_number, $billing_name,$billing_phone,$billing_address, $billing_city, $billing_state, $billing_country, $billing_zip;
    public $shipping_name,$shipping_phone,$shipping_address, $shipping_city, $shipping_state, $shipping_country, $shipping_zip;
    public $electronic_address, $electronic_address_scheme, $password;

    protected $queryString = [
        'search' => ['except' => ''],
        'viewMode' => ['except' => 'list'],
        'perPage' => ['except' => 10],
        'currentPage' => ['except' => 1, 'as' => 'page'],
    ];

    protected $listeners = [
        'refreshCustomers' => 'fetchCustomers',
        'customerCreated' => 'fetchCustomers',
        'customerUpdated' => 'fetchCustomers',
        'customerDeleted' => 'fetchCustomers',
        'deleteCustomer' => 'deleteCustomer',
    ];

    public function mount()
    {
        $this->fetchCustomers();
    }

    public function updatingSearch()
    {
        $this->resetPage();
        $this->fetchCustomers();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
        $this->fetchCustomers();
    }

    /**
     * Reset pagination to first page
     */
    public function resetPage()
    {
        $this->currentPage = 1;
    }

    public function switchView($mode)
    {
        $this->viewMode = $mode;
    }

    public function toggleView()
    {
        $this->viewMode = $this->viewMode === 'list' ? 'cards' : 'list';
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
        $this->fetchCustomers();
    }

    public function fetchCustomers()
    {
        try {
            $this->loading = true;
            $this->error = null;

            $financeCustomerService = app(FinanceCustomerService::class);

            // Prepare API parameters
            $params = [
                'page' => $this->currentPage,
                'per_page' => $this->perPage,
                'search' => $this->search,
                'sort_by' => $this->sortField,
                'sort_order' => $this->sortDirection,
            ];

            // Remove empty parameters
            $params = array_filter($params, function($value) {
                return $value !== null && $value !== '';
            });

            $response = $financeCustomerService->list($params);

            if (isset($response['status']) && $response['status'] === 'success') {
                $responseData = $response['data'] ?? [];
                $this->customers = $responseData['items'] ?? [];
                $this->total = $responseData['total'] ?? 0;
                $this->currentPage = $responseData['current_page'] ?? 1;
                $this->lastPage = ceil($this->total / $this->perPage);

                $this->customers = collect($this->customers)->map(function ($customer) {
                    return array_merge($customer, [
                        'initials' => $this->generateInitials($customer['name'] ?? ''),
                        'avatar_color' => $this->generateAvatarColor($customer['id'] ?? 0),
                        'salesperson' => $customer['salesperson'] ?? '-',
                        'activities' => $customer['activities'] ?? '-',
                        'display_id' => $customer['customer_id'] ?? '',
                        'phone' => $customer['mobile_no'] ?? '',
                        'balance' => $customer['balance'] ?? 0,
                        'total_sales' => $customer['total_sales'] ?? 0,
                    ]);
                })->toArray();

            } else {
                $this->error = $response['message'] ?? __('customers.messages.failed_to_fetch');
                $this->customers = [];
                $this->total = 0;
            }

        } catch (\Exception $e) {
            Log::error('Error fetching customers: ' . $e->getMessage());
            $this->error = __('customers.messages.error_occurred');
            $this->customers = [];
            $this->total = 0;
        } finally {
            $this->loading = false;
        }
    }

    /**
     * Navigate to next page
     */
    public function nextPage()
    {
        if ($this->currentPage < $this->lastPage) {
            $this->currentPage++;
            $this->fetchCustomers();
        }
    }

    /**
     * Navigate to previous page
     */
    public function previousPage()
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
            $this->fetchCustomers();
        }
    }

    /**
     * Navigate to specific page
     */
    public function gotoPage($page)
    {
        if ($page >= 1 && $page <= $this->lastPage) {
            $this->currentPage = $page;
            $this->fetchCustomers();
        }
    }

    /**
     * Generate initials from name
     */
    private function generateInitials($name)
    {
        $words = explode(' ', trim($name));
        $initials = '';
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }
        return substr($initials, 0, 2);
    }

    /**
     * Generate avatar color based on ID
     */
    private function generateAvatarColor($id)
    {
        $colors = ['bg-warning', 'bg-new-primary', 'bg-loss', 'bg-hold', 'bg-primary', 'bg-win'];
        return $colors[$id % count($colors)];
    }

    public function export()
    {
        try {
            $this->dispatchBrowserEvent('export-start');
            $file = Excel::download(new CustomersExport(collect($this->customers)), 'customers.xlsx');
            // Emit event to hide loading after the download response is triggered
            $this->dispatchBrowserEvent('export-end');
            return $file;
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('export-end');
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'error',
                'message' => __('customers.messages.export_failed')
            ]);
            Log::error('Error exporting customers: ' . $e->getMessage());
        }
    }

    public function openEditModal($customerId)
    {
        
        $financeCustomerService = app(FinanceCustomerService::class);
        $response = $financeCustomerService->edit($customerId);
        $this->customerId = $customerId;

        foreach ($response['data'] as $key => $value) {
            if (property_exists($this, $key)) {
                $this->$key = $value;
            }
        }

        $this->dispatchBrowserEvent('show-edit-modal');
    }
    public function copyBillingToShipping()
    {
        $this->shipping_name = $this->billing_name;
        $this->shipping_phone = $this->billing_phone;
        $this->shipping_address = $this->billing_address;
        $this->shipping_city = $this->billing_city;
        $this->shipping_state = $this->billing_state;
        $this->shipping_country = $this->billing_country;
        $this->shipping_zip = $this->billing_zip;
    }


    public function update()
    { 
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->validate([
            'name' => 'required',
            'billing_name' => 'required',
            'billing_phone' => 'required',
            'billing_address' => 'required',
            'billing_city' => 'required',
            'billing_state' => 'required',
            'billing_country' => 'required',
            'billing_zip' => 'required',
        ]);
        $financeCustomerService = app(FinanceCustomerService::class);
        $data=[
            'name' => $this->name,
            //'email' => $this->email,
            'contact' => $this->contact,
            'tax_number' => $this->tax_number,
            'billing_name' => $this->billing_name,
            'billing_phone' => $this->billing_phone,
            'billing_address' => $this->billing_address,
            'billing_city' => $this->billing_city,
            'billing_state' => $this->billing_state,
            'billing_country' => $this->billing_country,
            'billing_zip' => $this->billing_zip,
            'shipping_name' => $this->shipping_name,
            'shipping_phone' => $this->shipping_phone,
            'shipping_address' => $this->shipping_address,
            'shipping_city' => $this->shipping_city,
            'shipping_state' => $this->shipping_state,
            'shipping_country' => $this->shipping_country,
            'shipping_zip' => $this->shipping_zip,
            //'electronic_address' => $this->electronic_address,
            //'electronic_address_scheme' => $this->electronic_address_scheme,
            'password' => $this->password,
        ];
        $response = $financeCustomerService->update($this->customerId,$data);
        if (isset($response['status']) && $response['status'] == 'success') {
            $this->dispatchBrowserEvent( 'show-toast', [
                'type' => 'success',
                'message' => $response['message']
            ] );
            $this->fetchCustomers();
            $this->dispatchBrowserEvent('hide-edit-modal');
         }else{
            $this->dispatchBrowserEvent( 'show-toast', [
                'type' => 'error',
                'message' =>$response['message']
            ] );
         }

    }


    public function openDeleteModal($customerId, $customerName)
    {
        $this->emit('confirmDelete', $customerId, $customerName, 'deleteCustomer');
    }

    public function deleteCustomer($customerId)
    {
        try {
            $this->loading = true;
            $this->error = null;

            $financeCustomerService = app(FinanceCustomerService::class);
            $response = $financeCustomerService->delete($customerId);

            if (isset($response['status']) && $response['status'] === 'success') {
                $this->dispatchBrowserEvent('show-toast', [
                    'type' => 'success',
                    'message' => __('customers.messages.customer_deleted_successfully')
                ]);

                // Refresh the customer list
                $this->fetchCustomers();
            } else {
                $this->error = $response['message'] ?? __('customers.messages.failed_to_delete_customer');
                $this->dispatchBrowserEvent('show-toast', [
                    'type' => 'error',
                    'message' => $this->error
                ]);
            }
        } catch (\Exception $e) {
            $this->error = __('customers.messages.error_occurred_while_deleting');
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'error',
                'message' => $this->error
            ]);
            Log::error('Error deleting customer: ' . $e->getMessage());
        } finally {
            $this->loading = false;
        }
    }

    public function render()
    {
        // Create pagination info object for the view
        $paginationInfo = (object) [
            'data' => $this->customers,
            'total' => $this->total,
            'per_page' => $this->perPage,
            'current_page' => $this->currentPage,
            'last_page' => $this->lastPage,
        ];

        return view('livewire.accounting.customers', [
            'customers' => collect($this->customers),
            'pagination' => $paginationInfo,
            'loading' => $this->loading,
            'error' => $this->error,
        ]);
    }
}
