<?php

namespace App\Http\Livewire\Accounting;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Log;
use App\Services\Finance\FinanceRevenueService;

class FinanceRevenue extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'date';
    public $sortDirection = 'desc';

    // Filter properties
    public $filterDate = '';
    public $filterAccount = '';
    public $filterCustomer = '';
    public $filterCategory = '';

    // API response data
    public $incomeTransactions = [];
    public $total = 0;
    public $currentPage = 1;
    public $lastPage = 1;
    public $loading = false;
    public $error = null;

    // Dropdown data
    public $bankAccounts = [];
    public $customers = [];
    public $categories = [];



    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'currentPage' => ['except' => 1, 'as' => 'page'],
        'filterDate' => ['except' => ''],
        'filterAccount' => ['except' => ''],
        'filterCustomer' => ['except' => ''],
        'filterCategory' => ['except' => ''],
    ];

    protected $listeners = [
        'refreshIncomeList' => 'fetchIncomeTransactions',
        'deleteConfirmed' => 'deleteIncomeTransaction',
    ];

    public function mount()
    {
        $this->fetchDropdownData();
        $this->fetchIncomeTransactions();
    }

    public function updatedSearch()
    {
        $this->currentPage = 1;
        $this->fetchIncomeTransactions();
    }

    public function updatedPerPage()
    {
        $this->currentPage = 1;
        $this->fetchIncomeTransactions();
    }

    public function updatedFilterDate()
    {
        // Convert date to YYYY-MM-DD format if it's not empty
        if (!empty($this->filterDate)) {
            $this->filterDate = $this->formatDateToYmd($this->filterDate);
        }
        $this->currentPage = 1;
        $this->fetchIncomeTransactions();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->fetchIncomeTransactions();
    }

    public function applyFilters()
    {
        $this->currentPage = 1;
        $this->fetchIncomeTransactions();
    }

    public function resetFilters()
    {
        $this->filterDate = '';
        $this->filterAccount = '';
        $this->filterCustomer = '';
        $this->filterCategory = '';
        $this->currentPage = 1;
        $this->fetchIncomeTransactions();
    }

    public function fetchIncomeTransactions()
    {
        try {
            $this->loading = true;
            $this->error = null;

            // Ensure dropdown data is available before processing transactions
            if (empty($this->bankAccounts)) {
                $this->fetchDropdownData();
            }

            $financeRevenueService = app(FinanceRevenueService::class);

            $params = [
                'page' => $this->currentPage,
                'per_page' => $this->perPage,
                'search' => $this->search,
                'sort_field' => $this->sortField,
                'sort_direction' => $this->sortDirection,
                'date' => !empty($this->filterDate) ? $this->formatDateToYmd($this->filterDate) : '',
                'account' => $this->filterAccount,
                'customer' => $this->filterCustomer,
                'category' => $this->filterCategory,
            ];

            $response = $financeRevenueService->list($params);

            if (isset($response['status']) && $response['status'] === 'success') {
                $this->incomeTransactions = $response['data']['items'] ?? [];
                $this->total = $response['data']['total'] ?? 0;
                $this->currentPage = $response['data']['current_page'] ?? 1;

                // Calculate last page from total and per_page
                $perPage = $response['data']['per_page'] ?? $this->perPage;
                $this->lastPage = $this->total > 0 ? ceil($this->total / $perPage) : 1;

                // Process income transactions data
                $this->incomeTransactions = collect($this->incomeTransactions)->map(function ($transaction) {
                    return array_merge($transaction, [
                        'initials' => $this->generateInitials($transaction['description'] ?? ''),
                        'avatar_color' => $this->generateAvatarColor($transaction['id'] ?? 0),
                        'display_amount' => number_format($transaction['amount'] ?? 0, 2),
                        'formatted_date' => date('M d, Y', strtotime($transaction['date'] ?? now())),
                        'category_name' => $transaction['category'] ?? '-',
                        'contact_name' => $transaction['customer'] ?? '-',
                        'account_name' => $this->getAccountNameFromData($transaction['account_id'] ?? '', $this->bankAccounts),
                        'reference' => $transaction['reference'] ?? '-',
                        'receipt' => $transaction['receipt'] ?? $transaction['add_receipt'] ?? null,
                    ]);
                })->toArray();

            } else {
                $this->error = $response['message'] ?? __('income.messages.failed_to_fetch');
                $this->incomeTransactions = [];
                $this->total = 0;
            }

        } catch (\Exception $e) {
            $this->error = __('income.messages.error_occurred_while_fetching');
            $this->incomeTransactions = [];
            $this->total = 0;
            Log::error('Error fetching income transactions: ' . $e->getMessage());
        } finally {
            $this->loading = false;
        }
    }

    public function fetchDropdownData()
    {
        try {
            $financeRevenueService = app(FinanceRevenueService::class);
            $response = $financeRevenueService->dropdowns();

            if (isset($response)) {
                $data = $response['data'] ?? $response;

                // Store bank accounts data
                $this->bankAccounts = $data['bank_accounts'] ?? [];

                // Store customers data
                $this->customers = $data['customers'] ?? [];

                // Store categories data
                $this->categories = $data['categories'] ?? [];

            } else {
                Log::error('Failed to fetch dropdown data: ' . ($response['message'] ?? 'Invalid response'));
            }
        } catch (\Exception $e) {
            Log::error('Error fetching dropdown data: ' . $e->getMessage());
        }
    }

    private function generateInitials($text)
    {
        $words = explode(' ', trim($text));
        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
        } elseif (count($words) === 1) {
            return strtoupper(substr($words[0], 0, 2));
        }
        return 'IN';
    }

    private function generateAvatarColor($id)
    {
        $colors = [
            'bg-primary', 'bg-success', 'bg-info', 'bg-warning',
            'bg-danger', 'bg-secondary', 'bg-dark'
        ];
        return $colors[$id % count($colors)];
    }

    private function formatDateToYmd($date)
    {
        if (empty($date)) {
            return '';
        }

        try {
            // Try to parse the date and convert to Y-m-d format
            $dateTime = new \DateTime($date);
            return $dateTime->format('Y-m-d');
        } catch (\Exception $e) {
            // If parsing fails, try common date formats
            $formats = ['d/m/Y', 'm/d/Y', 'd-m-Y', 'm-d-Y', 'Y/m/d', 'Y-m-d'];

            foreach ($formats as $format) {
                $dateTime = \DateTime::createFromFormat($format, $date);
                if ($dateTime !== false) {
                    return $dateTime->format('Y-m-d');
                }
            }

            // If all parsing attempts fail, return the original date
            Log::warning('Unable to parse date format: ' . $date);
            return $date;
        }
    }

    private function getAccountName($accountId)
    {
        if (empty($accountId)) {
            return '-';
        }

        $account = collect($this->bankAccounts)->firstWhere('id', $accountId);

        if ($account) {
            // Ensure both holder_name and bank_name exist
            $holderName = $account['holder_name'] ?? '';
            $bankName = $account['bank_name'] ?? '';

            if (!empty($holderName) && !empty($bankName)) {
                return trim($holderName . ' - ' . $bankName);
            } elseif (!empty($holderName)) {
                return $holderName;
            } elseif (!empty($bankName)) {
                return $bankName;
            }
        }

        return '-';
    }

    private function getCustomerName($customerId)
    {
        if (empty($customerId)) {
            return '-';
        }

        $customer = collect($this->customers)->firstWhere('id', $customerId);
        return $customer['name'] ?? '-';
    }

    private function getCategoryName($categoryId)
    {
        if (empty($categoryId)) {
            return '-';
        }

        $category = collect($this->categories)->firstWhere('id', $categoryId);
        return $category['name'] ?? '-';
    }

    private function getAccountNameFromData($accountId, $bankAccounts)
    {
        if (empty($accountId)) {
            return '-';
        }

        $account = collect($bankAccounts)->firstWhere('id', $accountId);

        if ($account) {
            // Ensure both holder_name and bank_name exist
            $holderName = $account['holder_name'] ?? '';
            $bankName = $account['bank_name'] ?? '';

            if (!empty($holderName) && !empty($bankName)) {
                return trim($holderName . ' - ' . $bankName);
            } elseif (!empty($holderName)) {
                return $holderName;
            } elseif (!empty($bankName)) {
                return $bankName;
            }
        }

        return '-';
    }

    private function getCustomerNameFromData($customerId, $customers)
    {
        if (empty($customerId)) {
            return '-';
        }

        $customer = collect($customers)->firstWhere('id', $customerId);
        return $customer['name'] ?? '-';
    }

    private function getCategoryNameFromData($categoryId, $categories)
    {
        if (empty($categoryId)) {
            return '-';
        }

        $category = collect($categories)->firstWhere('id', $categoryId);
        return $category['name'] ?? '-';
    }

    public function previousPage()
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
            $this->fetchIncomeTransactions();
        }
    }

    public function nextPage()
    {
        if ($this->currentPage < $this->lastPage) {
            $this->currentPage++;
            $this->fetchIncomeTransactions();
        }
    }

    public function gotoPage($page)
    {
        $this->currentPage = $page;
        $this->fetchIncomeTransactions();
    }

    public function export()
    {
        try {
            $this->dispatchBrowserEvent('export-start');

            // Redirect to the export route which will handle the file download
            $this->dispatchBrowserEvent('export-end');
            $this->dispatchBrowserEvent('redirect-to-export', [
                'url' => route('finance.income.export')
            ]);
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('export-end');
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => __('income.messages.export_failed')
            ]);
            Log::error('Error exporting income transactions: ' . $e->getMessage());
        }
    }

    public function openCreateModal()
    {
        $this->emit('showCreateRevenueModal');
    }

    public function openEditModal($incomeId)
    {
        $this->emit('showEditRevenueModal', $incomeId);
    }

    public function viewRevenue($incomeId)
    {
        return redirect()->route('finance.income.show', $incomeId);
    }

    public function openDeleteModal($incomeId, $description)
    {
        $this->emit('confirmDelete', $incomeId, $description, 'deleteConfirmed');
    }



    public function deleteIncomeTransaction($incomeId)
    {
        try {
            $this->loading = true;
            $financeRevenueService = app(FinanceRevenueService::class);

            $response = $financeRevenueService->delete($incomeId);

            if (isset($response['status']) && $response['status'] === 'success') {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => __('income.messages.deleted_successfully')
                ]);
                $this->fetchIncomeTransactions();
            } else {
                $this->error = $response['message'] ?? __('income.messages.failed_to_delete');
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $this->error
                ]);
            }
        } catch (\Exception $e) {
            $this->error = __('income.messages.error_occurred_while_deleting');
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $this->error
            ]);
            Log::error('Error deleting income transaction: ' . $e->getMessage());
        } finally {
            $this->loading = false;
        }
    }

    public function render()
    {
        // Create pagination info object for the view
        $paginationInfo = (object) [
            'data' => $this->incomeTransactions,
            'total' => $this->total,
            'per_page' => $this->perPage,
            'current_page' => $this->currentPage,
            'last_page' => $this->lastPage,
        ];

        return view('livewire.accounting.finance-revenue', [
            'incomeTransactions' => collect($this->incomeTransactions),
            'pagination' => $paginationInfo,
            'loading' => $this->loading,
            'error' => $this->error,
            'bankAccounts' => $this->bankAccounts,
            'customers' => $this->customers,
            'categories' => $this->categories,
        ]);
    }
}
