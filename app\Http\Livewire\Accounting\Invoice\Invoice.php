<?php

namespace App\Http\Livewire\Accounting\Invoice;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Log;
use App\Services\Finance\FinanceInvoiceService;
class Invoice extends Component
{
    use WithPagination;


    public $search = '';
    public $perPage = 10;
    public $sortField = 'issue_date';
    public $sortDirection = 'desc';
    public $from_date;
    public $to_date;
    public $searchCustomer;
    public $searchStatus;

    // API response data
    public $listdata = [];
    public $total = 0;
    public $currentPage = 1;
    public $lastPage = 1;
    public $loading = false;
    public $error = null;
    public $customers = [];
    public $statuses = [];
   
   public function applyFilters()
    {
        $this->fetch();
    }

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'currentPage' => ['except' => 1, 'as' => 'page'],
    ];

    protected $listeners = [
        'delete' => 'delete',
    ];

    public function mount()
    {
        $this->fetch();
    }

    public function updatingSearch()
    {
        $this->resetPage();
        $this->fetch();
    }
   

    public function updatingPerPage()
    {
        $this->resetPage();
        $this->fetch();
    }
      

    /**
     * Reset pagination to first page
     */
    public function resetPage()
    {
        $this->currentPage = 1;
    }

  
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
        $this->fetch();
    }

  
    public function fetch()
    {
        $finance = app(FinanceInvoiceService::class);
        $params = [
            'page' => $this->currentPage,
            'per_page' => $this->perPage,
            'search' => $this->search,
            'sort_by' => $this->sortField,
            'sort_dir' => $this->sortDirection,
            'issue_from_date' => $this->from_date,
            'issue_to_date' => $this->to_date,
            'status' => $this->searchStatus,
            'customer' => $this->searchCustomer,
        ];
      
        $params = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });
        $response = $finance->list($params);
        $dropdownlist = $finance->filterDatalist();
        //dd($response);
        $this->statuses = $dropdownlist['statuses'] ?? [];
        $this->customers = $dropdownlist['customers'] ?? [];
        if (isset($response['status']) && ($response['status'] === 'success') && count($response['data']['items'])) {
            $responseData = $response['data'] ?? [];
            $this->listdata = $responseData['items'] ?? [];
            $this->total = $responseData['total'] ?? 0;
            $this->currentPage = $responseData['current_page'] ?? 1;
            $this->lastPage = ceil($this->total / $this->perPage);
        }
    }


   

    /**
     * Navigate to next page
     */
    public function nextPage()
    {
        if ($this->currentPage < $this->lastPage) {
            $this->currentPage++;
            $this->fetch();
        }
    }

    /**
     * Navigate to previous page
     */
    public function previousPage()
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
            $this->fetch();
        }
    }

    /**
     * Navigate to specific page
     */
    public function gotoPage($page)
    {
        if ($page >= 1 && $page <= $this->lastPage) {
            $this->currentPage = $page;
            $this->fetch();
        }
    }
    public function refresh()
    {

        $this->reset([
            'search',
            'from_date',
            'to_date',
            'searchStatus',
            'searchCustomer',
        ]);

        $this->fetch();
    }

    public function render()
    {
        // Create pagination info object for the view
        $paginationInfo = (object) [
            'data' => $this->listdata,
            'total' => $this->total,
            'per_page' => $this->perPage,
            'current_page' => $this->currentPage,
            'last_page' => $this->lastPage,
        ];
        return view('livewire.accounting.invoice.index', [
            'data' => collect($this->listdata),
            'pagination' => $paginationInfo,
            'loading' => $this->loading,
            'error' => $this->error,
        ]);
    }


    public function goToCreatePage()
    {
        return redirect()->route('finance.invoice.create');
    }

    public function goToEdit($id)
    {
        return redirect()->route('finance.invoice.edit', ['id' => $id]);
    }

    public function goToView($id)
    {
        return redirect()->route('finance.invoice.view', ['id' => $id]);
    }



    public function openDeleteModal($id, $customerName)
    {
        $this->emit('confirmDelete', $id, $customerName, 'delete');
    }

    public function delete($id)
    {

        $this->loading = true;
        $this->error = null;
        $finance = app(FinanceInvoiceService::class);
        $response = $finance->delete($id);
        
        if (isset($response['status']) && $response['status'] === 'success') {
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'success',
                'message' => $response['message']
            ]);

        } else {
            $this->error = $response['message'] ?? __('customers.messages.failed_to_delete_customer');
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'error',
                'message' => $response['message']
            ]);
        }

        $this->fetch();
        $this->dispatchBrowserEvent('close-modal');
       
    }


    public function export()
    {
        $finance = app(FinanceInvoiceService::class);
        $response = $finance->export();
         $base64 = base64_encode($response->getContent());
         $mime = 'application/xlsx';
        $this->dispatchBrowserEvent('download-blob', [
            'fileName' => 'downloaded-file.xlsx',
            'base64'   => $base64,
            'mime'     => $mime
        ]);
    }


}
