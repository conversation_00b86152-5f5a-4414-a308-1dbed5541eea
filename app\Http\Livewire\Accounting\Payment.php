<?php

namespace App\Http\Livewire\Accounting;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Log;
use App\Services\Finance\FinancePaymentService;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\Finance\PaymentExport;
use Livewire\WithFileUploads;
class Payment extends Component
{
    use WithPagination, WithFileUploads;

    public $attachmentt, $temporaryUrl;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'date';
    public $sortDirection = 'desc';
    public $from_date;
    public $to_date;
    public $searchAccount;
    public $searchVendor;
    public $searchCategory;

    // API response data
    public $listdata = [];
    public $bankAccounts = [];
    public $vendors = [];
    public $categories = [];
    public $total = 0;
    public $currentPage = 1;
    public $lastPage = 1;
    public $loading = false;
    public $error = null;


    public $date;
    public $amount;
    public $account_id;
    public $vendor_id;
    public $vendor;
    public $category;
    public $account;
    public $description;
    public $category_id;
    public $reference;
    public $add_receipt;
    public $receipt_url;

    public $showFullView = true;

   public function applyFilters()
    {
        $this->fetch();
    }

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'currentPage' => ['except' => 1, 'as' => 'page'],
    ];

    protected $listeners = [
        'delete' => 'delete',
    ];

    public function mount($showFullView)
    {
        $this->showFullView = $showFullView;
        $this->fetch();
    }

    public function updatingSearch()
    {
        $this->resetPage();
        $this->fetch();
    }
    public function refresh()
    {

        $this->reset([
            'search',
            'from_date',
            'to_date',
            'searchAccount',
            'searchVendor',
            'searchCategory',
        ]);

        $this->fetch();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
        $this->fetch();
    }

    /**
     * Reset pagination to first page
     */
    public function resetPage()
    {
        $this->currentPage = 1;
    }

  
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
        $this->fetch();
    }

    public function fetch()
    {
        $this->loading = true;
        $this->error = null;
        $finance = app(FinancePaymentService::class);

        $params = [
            'page' => $this->currentPage,
            'per_page' => $this->perPage,
            'search' => $this->search,
            'sort_by' => $this->sortField,
            'sort_dir' => $this->sortDirection,
            'from_date' => $this->from_date,
            'to_date' => $this->to_date,
            'account' => $this->searchAccount,
            'vendor' => $this->searchVendor,
            'category' => $this->searchCategory,
        ];
        
        $params = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });
        $response = $finance->list($params);
        $dropdownlist = $finance->dropdownlist();

        $this->bankAccounts = $dropdownlist['bank_accounts'] ?? [];
        $this->vendors = $dropdownlist['vendors'] ?? [];
        $this->categories = $dropdownlist['categories'] ?? [];
        if (isset($response['status']) && ($response['status'] === 'success') && count($response['data']['items'])) {
            $responseData = $response['data'] ?? [];
            $this->listdata = $responseData['items'] ?? [];
            $this->total = $responseData['total'] ?? 0;
            $this->currentPage = $responseData['current_page'] ?? 1;
            $this->lastPage = ceil($this->total / $this->perPage);
        }
    }


   
    

    /**
     * Navigate to next page
     */
    public function nextPage()
    {
        if ($this->currentPage < $this->lastPage) {
            $this->currentPage++;
            $this->fetch();
        }
    }

    /**
     * Navigate to previous page
     */
    public function previousPage()
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
            $this->fetch();
        }
    }

    /**
     * Navigate to specific page
     */
    public function gotoPage($page)
    {
        if ($page >= 1 && $page <= $this->lastPage) {
            $this->currentPage = $page;
            $this->fetch();
        }
    }

    public function render()
    {
        // Create pagination info object for the view
        $paginationInfo = (object) [
            'data' => $this->listdata,
            'total' => $this->total,
            'per_page' => $this->perPage,
            'current_page' => $this->currentPage,
            'last_page' => $this->lastPage,
        ];

        return view('livewire.accounting.payment.index', [
            'data' => collect($this->listdata),
            'pagination' => $paginationInfo,
            'loading' => $this->loading,
            'error' => $this->error,
        ]);
    }



    public function resetForm()
    {
        $this->date = null;
        $this->amount = null;
        $this->account_id = null;
        $this->vendor_id = null;
        $this->description = null;
        $this->category_id = null;
        $this->reference = null;
    }

    public function save()
    {
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->validate([
            'date' => 'required',
            'amount' => 'required',
            'account_id' => 'required',
            'vendor_id' => 'required',
            'description' => 'required',
            'category_id' => 'required',
            'reference' => 'required',
        ]);

        $data=[
            'date' => $this->date,
            'amount' => $this->amount,
            'account_id' => $this->account_id,
            'vendor_id' => $this->vendor_id,
            'description' => $this->description,
            'category_id' => $this->category_id,
            'reference' => $this->reference,
        ];
       
        $finance = app(FinancePaymentService::class);

        $filename = null;
        $absolutePath = null;
        if ($this->add_receipt) {
            $filename = $this->add_receipt->getClientOriginalName();
            $filePath = 'uploads/stream/' . $filename;
            $this->add_receipt->storeAs('uploads/stream', $filename, 'local');
            $absolutePath = storage_path('app/' . $filePath);
            $this->temporaryUrl = $this->add_receipt->temporaryUrl();
        }
    
        $response = $finance->create($data,$absolutePath, $filename);
        if (isset($response['status']) && $response['status'] == 'success') {
            $this->dispatchBrowserEvent( 'show-toast', [
                'type' => 'success',
                'message' => $response['message']
            ] );
            $this->resetForm(); 
            $this->resetPage();
            $this->fetch();
            $this->dispatchBrowserEvent('close-modal');
         }else{
            $this->dispatchBrowserEvent( 'show-toast', [
                'type' => 'error',
                'message' =>$response['message']
            ] );
         }
    }


    public function openDeleteModal($id, $customerName)
    {
        $this->emit('confirmDelete', $id, $customerName, 'delete');
    }

    public function delete($id)
    {

        $this->loading = true;
        $this->error = null;
        $finance = app(FinancePaymentService::class);
        $response = $finance->delete($id);
        
        if (isset($response['status']) && $response['status'] === 'success') {
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'success',
                'message' => $response['message']
            ]);

        } else {
            $this->error = $response['message'] ?? __('customers.messages.failed_to_delete_customer');
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'error',
                'message' => $response['message']
            ]);
        }

        $this->fetch();
        $this->dispatchBrowserEvent('close-modal');
       
    }

    public function openEditModal($id)
    {
        
        $finance = app(FinancePaymentService::class);
        $response = $finance->edit($id);
        $this->edit_id = $id;
        foreach ($response['payment'] as $key => $value) {
            if (property_exists($this, $key)) {
                $this->$key = $value;
            }
        }
        $this->dispatchBrowserEvent('show-edit-modal');
    }

   
    public function update()
    { 
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->validate([
            'date' => 'required',
            'amount' => 'required',
            'account_id' => 'required',
            'vendor_id' => 'required',
            'description' => 'required',
            'category_id' => 'required',
            'reference' => 'required',
        ]);

        $data=[
            'date' => $this->date,
            'amount' => $this->amount,
            'account_id' => $this->account_id,
            'vendor_id' => $this->vendor_id,
            'description' => $this->description,
            'category_id' => $this->category_id,
            'reference' => $this->reference,
        ];
        $finance= app(FinancePaymentService::class);


        $filename = null;
        $absolutePath = null;
        if ($this->add_receipt) {
            $filename = $this->add_receipt->getClientOriginalName();
            $filePath = 'uploads/stream/' . $filename;
            $this->add_receipt->storeAs('uploads/stream', $filename, 'local');
            $absolutePath = storage_path('app/' . $filePath);
            $this->temporaryUrl = $this->add_receipt->temporaryUrl();
        }
    
        $response = $finance->update($this->edit_id,$data,$absolutePath, $filename);

        if (isset($response['status']) && $response['status'] == 'success') {
            $this->dispatchBrowserEvent( 'show-toast', [
                'type' => 'success',
                'message' => $response['message']
            ] );
            $this->fetch();
            $this->dispatchBrowserEvent('hide-edit-modal');
         }else{
            $this->dispatchBrowserEvent( 'show-toast', [
                'type' => 'error',
                'message' =>$response['message']
            ] );
         }

    }

     public function openViewModal($id)
    {
        
        $finance = app(FinancePaymentService::class);
        $response = $finance->view($id);
        foreach ($response['payment'] as $key => $value) {
            if (property_exists($this, $key)) {
                $this->$key = (string)$value;
            }
        }
        $this->dispatchBrowserEvent('show-view-modal');
    }
    public function export()
    {
        $finance = app(FinancePaymentService::class);
        $response = $finance->export();
         $base64 = base64_encode($response->getContent());
         $mime = 'application/xlsx';
        $this->dispatchBrowserEvent('download-blob', [
            'fileName' => 'downloaded-file.xlsx',
            'base64'   => $base64,
            'mime'     => $mime
        ]);
    }

    // public function export()
    // {
    //     try {
    //         $this->dispatchBrowserEvent('export-start');
    //         $file = Excel::download(new PaymentExport(collect($this->listdata)), 'payment.xlsx');
    //         // Emit event to hide loading after the download response is triggered
    //         $this->dispatchBrowserEvent('export-end');
    //         return $file;
    //     } catch (\Exception $e) {
    //         $this->dispatchBrowserEvent('export-end');
    //         $this->dispatchBrowserEvent('show-toast', [
    //             'type' => 'error',
    //             'message' => ''
    //         ]);
    //         Log::error('Error exporting payment: ' . $e->getMessage());
    //     }
    // }

}
