<?php

namespace App\Http\Livewire\AdvanceContracts;

use Livewire\Component;
use App\Models\Contracts;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Illuminate\Support\Facades\Storage;
use App\Services\AdvanceContractService;

class AdvanceContractDetail extends Component
{
    public $uuid; // holds the encrypted id passed from URL
    public $data = [];
    public $contract;
    public $subcontracts;
    public $performanceIndicatorData;
    public $overallPerformance;
    public $startDate;
    public $endDate;
    public $percentage = 0;
    public $serviceProvider;
    public $cityNames = [];
    public $regionNames = [];
    public $cityNamesString = '';
    public $regionNamesString = '';
    public $propertyNames = [];
    public $propertyNamesLimited = [];
    public $serviceType;
    public $assetsName;
    public $items;
    public $installmentsCount = 0;
    public $perMonthAmount = 0;
    public $selected_properties = [];
    public $buildings ;
    public $totalUnits;
    public $totalZones;
    public $total; 
    public $totalPages; 
    public $propertyPage = 1;
    public $perPage = 5;
    public $groupedIndicators;
    public $assetCategories = [];
    public $smartAssignedServices = [];
    public $allowedRolesForVariationOrder = ['sp_admin', 'building_manager', 'admin'];
    public $create_variation_order_url;
    public $variation_order_history;
    public $view_variation_order;
    public $contractId;
    public $attachment;
    protected $contractService;
    public $alertMessage = null;

    // Inject the service into the component
    public function __construct()
    {
        $this->contractService = new AdvanceContractService();
    }

    /**
     * The mount method initializes the component.
     *
     * @param mixed $id Encrypted contract id from URL
     */
    public function mount($id)
    {   
        // Fetch contract data via the service
        $contractData = $this->contractService->getContractData($id);
        $this->alertMessage = $this->contractService->getApprovalMessage();
        // Set the values for the Livewire component
        if ($contractData) {
            $this->contractId  =  $contractData['id'];
            $this->overallPerformance = $contractData['overallPerformance'];
            $this->data['id'] = $contractData['id'];
            $this->contract = $contractData['contract'];
            $this->groupedIndicators = $contractData['groupedIndicators'];
            $this->buildings = $contractData['buildings'];
            $this->totalUnits = $contractData['totalUnits'];
            $this->totalZones = $contractData['totalZones'];
            $this->total = $contractData['total'];
            $this->installmentsCount = $contractData['installmentsCount'];
            $this->serviceProvider = $contractData['serviceProvider'];
            $this->startDate = $contractData['startDate'];
            $this->endDate = $contractData['endDate'];
            $this->perMonthAmount =    $contractData['perMonthAmount'];
            $this->serviceType = $contractData['serviceType'];
            $this->assetsName = $contractData['assetsName'];
            $this->percentage = $contractData['percentage'];
            $this->assetCategories = $contractData['assetCategories'];
            $this->smartAssignedServices = $contractData['smartAssignedServices'];
            $this->items = $contractData['items'];
            $this->cityNames = $contractData['cityNames'];
            $this->regionNames = $contractData['regionNames'];
            $this->propertyNames = $contractData['propertyNames'];
            $this->propertyNamesLimited = $contractData['propertyNamesLimited'];
            
            $this->attachment = $this->contract->picture ? Storage::disk('oci')->url($this->contract->picture) : null;
        }
     
        // Generate the variation order URLs
        $this->create_variation_order_url = route('variation.create', $this->contract->reference_uuid);
        // Log::info("Variation order url", ['create_variation_order_url' => $this->create_variation_order_url]);

        // Get the variation order history
        $this->variation_order_history = $this->contract->advanceContractDraft()
                                                ->withTrashed()
                                                ->where('is_variation_order', 1)
                                                ->whereNotNull('status')
                                                ->where('status', '!=', '')
                                                ->whereNotIn('status', ['draft', 'incomplete'])
                                                ->with(['approvals.approver', 'approvals.serviceProvider'])
                                                ->get();
                                                
        // If contract not found, redirect to contracts list
        if (!$this->contract) {
            return redirect()->route('data.contracts.list');
        }
    }

    public function setPage($page)
    {
        $this->propertyPage = $page;
    }

    public function createVariationOrder()
    {
        if (AdvanceContractDraft::hasPendingVariationOrder($this->contractId)) {
            $this->dispatchBrowserEvent('show-toastr', ['type' => 'error', 'message' => __('advance_contracts.variation_order.variation_create_validation')]);
            return;
        }

        return redirect()->to($this->create_variation_order_url);
    }

    /**
     * Render the Livewire view.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.advance-contracts.advance-contract-detail');
    }

    
}
