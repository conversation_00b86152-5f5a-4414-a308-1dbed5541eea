<?php
namespace App\Http\Livewire\AdvanceContracts;

use Carbon\Carbon;
use Livewire\Component;
use App\Models\Contracts;
use Illuminate\Support\Str;
use App\Http\Helpers\Helper;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Illuminate\Support\Facades\Auth;
use App\Http\Helpers\AdvanceContractHelper;
use App\Repositories\AdvanceContractRepository;

class StepOne extends Component
{
    public $uuid;
    public $contract_name;
    public $amount;
    public $interval;
    public $start_date;
    public $end_date;
    public $months = [];
    public $contract_with;
    public $companyList = [];
    public bool $is_subcontract = false;
    public array $subcontractOptions = [];
    public $selectedSubcontractId = null;
    public $route_prefix;
    public $isVariation = false;
    public function rules()
    {
        return $this->getValidationRules();
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);

        if (in_array($propertyName, ['amount','interval', 'start_date', 'end_date'])) {
            $this->generateInstallments();
        }
    }

    public function getValidationRules()
    {
        $rules = [
            'contract_with' => 'required|numeric|min:1',
            'contract_name' => 'required|string|max:255|unique:contracts,contract_number',
            'amount' => 'required|numeric|min:1',
            'interval' => 'required|in:monthly,quarterly,semi_annually,annually',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'months' => 'required|array|min:1',
            'months.*.month' => 'required|string',
            'months.*.amount' => 'required|numeric|min:0',
        ];

        
        

        // Skip uniqueness check if same as existing
        if ($this->uuid) {
            $draft = AdvanceContractDraft::withTrashed()->where('uuid', $this->uuid)->first();
            if ($draft) {
                $existing = $draft->main_information_data;
                if (($existing['contract_name'] ?? null) === $this->contract_name) {
                    $rules['contract_name'] = 'required|string|max:255';
                }
            }
        }

        return $rules;
    }



    public function updatedIsSubcontract($value)
    {
        if ($value) {
            $this->subcontractOptions = Contracts::where('status', 1)
                ->where('allow_subcontract', 1)
                ->get()
                ->map(function ($contract) {
                    return [
                        'id' => $contract->id,
                        'name' => $contract->contract_number, // use your relevant column here
                    ];
                })
                ->toArray();
        } else {
            $this->subcontractOptions = [];
            $this->selectedSubcontractId = null;
        }
    }
    
    private function intervalInMonths()
    {
        return match ($this->interval) {
            'monthly' => 1,
            'quarterly' => 3,
            'semi_annually' => 6,
            'annually' => 12,
            default => null,
        };
    }

    public function mount(AdvanceContractRepository $advanceContractRepository, $uuid = null, $prefix = null)
    {
        $this->uuid = $uuid;
        $this->route_prefix = $prefix;
        $this->isVariation = request()->routeIs('variation.*');
        $this->months = [];

        // Load company list
        $this->companyList = collect(Helper::getServiceProvidersList())
                                ->map(fn($company) => is_array($company) ? $company : (array) $company)
                                ->toArray();
      
        if ($uuid) {
            $draft = $advanceContractRepository->findByUuid($this->uuid);
            if ($draft) {
                $data = $draft->main_information_data;

                $this->contract_with = $data['contract_with'] ?? '';
                $this->contract_name = $data['contract_name'] ?? '';
                $this->amount = $data['amount'] ?? '';
                $this->interval = $data['interval'] ?? '';
                $this->start_date = $data['start_date'] ?? '';
                $this->end_date = $data['end_date'] ?? '';
                $this->is_subcontract = $data['is_subcontract'] ?? false;
                $this->updatedIsSubcontract($this->is_subcontract);
                $this->selectedSubcontractId = $data['selected_subcontract_id'] ?? null;
                $this->months = $data['months'] ?? [];
            }
        }
    }



    private function generateInstallments()
    {
        if (!$this->start_date || !$this->end_date || !$this->interval || !$this->amount) {
            $this->months = [];
            return;
        }
    
        $start = Carbon::parse($this->start_date);
        $end = Carbon::parse($this->end_date);
        $intervalMonths = $this->intervalInMonths();
    
        if (!$intervalMonths || $end->lt($start)) {
            $this->months = [];
            return;
        }
    
        $diffMonths = $start->diffInMonths($end);
        if ($diffMonths % $intervalMonths !== 0) {
            $this->months = [];
            return;
        }
    
        $numInstallments = $diffMonths / $intervalMonths;
        $baseAmount = floor($this->amount / $numInstallments);
        $remainder = $this->amount - ($baseAmount * $numInstallments);
    
        $months = [];
    
        for ($i = 0; $i < $numInstallments; $i++) {
            $date = $start->copy()->addMonths($i * $intervalMonths);
            $label = $date->format('F Y');
    
            // Add remainder to the FIRST installment instead of last
            $installmentAmount = $baseAmount;
            if ($i === 0) {
                $installmentAmount += $remainder;
            }
    
            $months[] = [
                'month' => $label,
                'amount' => $installmentAmount,
            ];
        }
    
        $this->months = $months;
    }
    
    public function removeMonth($index)
    {
        unset($this->months[$index]);
        $this->months = array_values($this->months);
    }

    public function submit(AdvanceContractRepository $advanceContractRepository)
    {
        $this->validate();
       
        // Generate or reuse UUID before using it
        $uuid = $this->uuid ?: (string) Str::uuid();
        $contractTypeId = ($this->is_subcontract && $this->selectedSubcontractId) ? 7 : 6;
        $payload = [            
            'main_information_data' => [
                'contract_with' => $this->contract_with,
                'contract_name' => $this->contract_name,
                'amount' => $this->amount,
                'interval' => $this->interval,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'is_subcontract' => $this->is_subcontract,
                'selected_subcontract_id' => $this->selectedSubcontractId,
                'contract_type_id' => $contractTypeId,
                'months' => $this->months,
            ],
            'initiated_by' => Auth::id(),
            'initiator_role' => Auth::user()->user_type,
        ];

        if($this->isVariation) {
            $original = $advanceContractRepository->findByUuid($this->uuid);
            if (!in_array($original->status, ['draft', 'pending', 'incomplete']) || $original->trashed()) {
                $uuid = (string) Str::uuid();
                $payload['data_agreement_kpi_data'] = $original->data_agreement_kpi_data;
                $payload['assets_ppm_data'] = $original->assets_ppm_data;
                $payload['workforce_team_data'] = $original->workforce_team_data;
                $payload['extra_data'] = $original->extra_data;
                $payload['is_variation_order'] = 1;
                $payload['contract_id'] = $original->contract_id;
                $payload['status'] = 'incomplete';
                $payload['previous_contract_id'] = $original->id;
            }           
        }
        
        $advanceContractRepository->updateOrCreateByUuid($uuid,$payload);

        $this->dispatchBrowserEvent('show-toastr', ['type' => 'success', 'message' =>  __('advance_contracts.general.info_saved')]);
        return redirect()->route($this->route_prefix . 'openCreateDataAgreement', ['uuid' => $uuid]);

    }
    
    

    public function render()
    {
        return view('livewire.advance-contracts.step-one');
    }
}
