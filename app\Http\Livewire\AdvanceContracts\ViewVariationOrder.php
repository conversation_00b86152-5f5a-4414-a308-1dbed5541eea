<?php

namespace App\Http\Livewire\AdvanceContracts;

use App\Models\User;
use Livewire\Component;


use App\Models\Contracts;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Services\AdvanceContractService;
use App\Models\AdvanceContractDraftApproval;
use App\Notifications\AdvanceContracts\VariationOrderApprovalNotification;

class ViewVariationOrder extends Component
{
  

    public $uuid; // holds the encrypted id passed from URL
    public $data = [];
    public $contract;
    public $draft;
    public $subcontracts;
    public $dataProvider;
    public $performanceIndicatorData;
    public $invoices;
    public $overallPerformance;
    public $startDate;
    public $endDate;
    public $percentage = 0;
    public $serviceProvider;
    public $cityNames = [];
    public $regionNames = [];
    public $cityNamesString = '';
    public $regionNamesString = '';
    public $propertyNames = [];
    public $propertyNamesLimited = [];
    public $serviceType;
    public $assetsName;
    public $items;
    public $installmentsCount = 0;
    public $perMonthAmount = 0;
    public $selected_properties = [];
    public $buildings ;
    public $totalUnits;
    public $totalZones;
    public $total; 
    public $totalPages; 
    public $propertyPage = 1;
    public $perPage = 5;
    public $groupedIndicators;
    public $assetCategories = [];
    public $smartAssignedServices = [];
    public $allowedRolesForVariationOrder = ['sp_admin', 'building_manager', 'admin'];
    public $create_variation_order_url;
    public $variation_order_history;
    public $view_variation_order;
    public $contractId;
    public $variationOrder;


    public $totalDifference = 0;

    public $visibleRegions = [];
    public $hiddenRegions = [];
    public $visibleCities = [];
    public $hiddenCities = [];
    public $visibleProperties = [];
    public $hiddenProperties = [];


    public $pictureData;
    public $commentData;
    public $contractNumberData;
    public $contractValueData;
    public $intervalTypeData;
    public $numberOfPaymentData;
    public $totalAmountPerTimeData;
    public $startDateData;
    public $endDateData;
    public $percentageData;
    public $unitRecievalData;
    public $assetCategoriesWithDiff;
    public $subContractData;
    public $smartAssignData;
    public $smartAssignCategoriesWithDiff;
    public $comparedItems;
    public $comparedPriorities;
    public $comparedWorkforce;
    public $comparedIndicators;
    public $comparedBuildings;
    public $contractServiceKpisDiff;
    public $assetNameDiffs;

    public $totalZonesData;
    public $totalUnitData;
    public $approvals;

    protected $contractService;
    public $showApprovalActions;

    public $approveComment = null;
    public $cancelComment = null;
    public $rejectComment = null;
    public $userApprovalStatus  = null;
    public $nextApproval;
    public $contractDraft;
    public $contractOriginalData;
    public $showCancelButton = false;

    protected $rules = [
        'cancelComment' => 'required|string|min:3',
        'rejectComment' => 'required|string|min:3',
    ];

    // Inject the service into the component
    public function __construct()
    {
        $this->contractService = new AdvanceContractService();
    }

    public function mount($uuid){
        $user = Auth::user();
        $this->draft = AdvanceContractDraft::withTrashed()
                                ->where('uuid', $uuid)
                                ->firstOrFail();
      
        $this->variationOrder = $this->contractService->transformDraftData($this->draft);


        if ($this->draft->trashed()) {
             $this->userApprovalStatus = 'cancelled';
        }else{
            $this->setApprovalVisibility();
            $statusRecord = $this->getCurrentUserApprovalStatus();
           
            if ($statusRecord) {
                $this->userApprovalStatus = $statusRecord->status;
            } 
        }
        $hasFinalizedApproval = $this->draft->approvals
            ->whereIn('status', ['approved', 'rejected', 'cancelled']) // include 'cancelled'
            ->isNotEmpty();

        $this->showCancelButton = (
            $this->draft->initiated_by == $user->id && !$hasFinalizedApproval && $this->draft->deleted_at === null
        );

        $this->contract = Contracts::with(['serviceProvider'])->findOrFail($this->draft->contract_id);

        $this->contractDraft = AdvanceContractDraft::withTrashed()->where('id',  $this->draft->previous_contract_id)->firstOrFail();
        $this->contractOriginalData = $this->contractService->transformDraftData($this->contractDraft );
   
      
        $this->serviceProvider =  $this->contract->serviceProvider;


       $this->comparedIndicators = $this->compareSelectedKpisFieldWise($this->contractOriginalData['groupedIndicators'],$this->variationOrder['groupedIndicators']);

        $this->contractNumberData = $this->getFieldWithDiff( $this->contractOriginalData['contract_number'], $this->variationOrder['contract_number']);
        $this->startDateData = $this->getFieldWithDiff($this->contractOriginalData['startDate'], $this->variationOrder['startDate']);
        $this->endDateData = $this->getFieldWithDiff($this->contractOriginalData['endDate'], $this->variationOrder['endDate']);
        $this->percentageData = $this->getFieldWithDiff($this->contractOriginalData['percentage'], $this->variationOrder['percentage']);

        $this->contractValueData = $this->getFieldWithDiff( $this->contractOriginalData['contractValue'], $this->variationOrder['contractValue']);
        $this->intervalTypeData = $this->getFieldWithDiff( $this->contractOriginalData['paymentInterval'], $this->variationOrder['paymentInterval']);
        $this->numberOfPaymentData = $this->getFieldWithDiff($this->contractOriginalData['installmentsCount'], $this->variationOrder['installmentsCount']);
        $this->totalAmountPerTimeData = $this->getFieldWithDiff($this->contractOriginalData['perMonthAmount'], $this->variationOrder['perMonthAmount']);

        $this->unitRecievalData = $this->getFieldWithDiff($this->contractOriginalData['use_form_of_unit_receival'], $this->variationOrder['use_form_of_unit_receival']);
        $originalUnitReceivalCategories     = array_map('trim', $this->contractOriginalData['assetCategories']); 
        $variationUnitReceivalCategories    = $this->variationOrder['assetCategories'] ?? []; 

        $this->assetCategoriesWithDiff = $this->getAssetCategoriesWithDiff($originalUnitReceivalCategories, $variationUnitReceivalCategories);

        $this->subContractData = $this->getFieldWithDiff($this->contractOriginalData['allowSub'], $this->variationOrder['allowSub']);

        $this->pictureData = $this->getFileFieldWithDiff($this->contractOriginalData['picture'], $this->variationOrder['picture']);
        $this->commentData = $this->getFieldWithDiff($this->contractOriginalData['comment'], $this->variationOrder['comment']);


        $this->smartAssignData = $this->getFieldWithDiff($this->contractOriginalData['use_smart_assigning'], $this->variationOrder['use_smart_assigning']);
        $originalSmartAssignCategories =  array_map('trim', array_values($this->contractOriginalData['smartAssignedServices'] ?? []));
        $variationSmartAssignCategories = array_map('trim', array_values($this->variationOrder['smartAssignedServices'] ?? []));
        $this->smartAssignCategoriesWithDiff = $this->getAssetCategoriesWithDiff($originalSmartAssignCategories, $variationSmartAssignCategories);
        
        $originalRegions =  $this->contractOriginalData['regionNames'] ?? [];
        $variationRegions = $this->variationOrder['regionNames'] ?? [];
        $regionsWithStatus = $this->prepareItemsWithDiff($originalRegions, $variationRegions);
        $this->visibleRegions = array_slice($regionsWithStatus, 0, 5);
        $this->hiddenRegions = array_slice($regionsWithStatus, 5);

        $originalCities =   $this->contractOriginalData['cityNames'] ?? [];
        $variationCities = $this->variationOrder['cityNames'] ?? [];
        $citiesWithStatus = $this->prepareItemsWithDiff($originalCities, $variationCities);
        $this->visibleCities = array_slice($citiesWithStatus, 0, 5);
        $this->hiddenCities = array_slice($citiesWithStatus, 5);

        $originalProperties = $this->contractOriginalData['propertyNamesLimited'] ?? [];
        $variationProperties = $this->variationOrder['propertyNamesLimited'] ?? [];
     
        $propertiesWithStatus = $this->prepareItemsWithDiffById($originalProperties, $variationProperties);
        $this->visibleProperties = array_slice($propertiesWithStatus, 0, 5);
        $this->hiddenProperties = array_slice($propertiesWithStatus, 5);

        $originalItems = $this->contractOriginalData['items'] ?? collect();
        $originalItems = is_array($originalItems) ? $originalItems : (array) $originalItems;
        $variationItems = $this->variationOrder['items'] ?? collect();
        $variationItems = is_array($variationItems) ? $variationItems : (array) $variationItems;
        $this->comparedItems = $this->compareItemsFieldWise($originalItems, $variationItems);

        $original = collect($this->contractOriginalData['contractPriorities'])->map(function ($cp) {
                            return [
                                'priority_id' => (int) $cp['priority_id'],
                                'priority_level' => $cp['exportPriority']['priority_level'] ?? 'N/A', // 👈 add this
                                'service_window' => (int) $cp['service_window_input'],
                                'service_window_type' => strtolower($cp['service_window_select']),
                                'response_time' => (int) $cp['response_time_input'],
                                'response_time_type' => strtolower($cp['response_time_select']),
                                'name' => (int) $cp['priority_id'], // keep for comparison key
                            ];
                        })->toArray();
        $variation = collect($this->variationOrder['contractPriorities'])->map(function ($cp) {
                            return [
                                'priority_id' => (int) $cp['priority_id'],
                                'priority_level' => $cp['exportPriority']['priority_level'] ?? 'N/A', // 👈 add this
                                'service_window' => (int) $cp['service_window_input'],
                                'service_window_type' => strtolower($cp['service_window_select']),
                                'response_time' => (int) $cp['response_time_input'],
                                'response_time_type' => strtolower($cp['response_time_select']),
                                'name' => (int) $cp['priority_id'], // keep for comparison key
                            ];
                        })->toArray();
        $this->comparedPriorities = $this->compareItemsFieldWise($original, $variation, 'priority_id');

        
        $worforceTeam = collect($this->contractOriginalData['workforce_team'])->map(function ($member) {
                                        return [
                                            'key' => $member['role'] . '|' . $member['proficiency'], // Use array access here
                                            'role' => $member['role'],
                                            'proficiency' => $member['proficiency'],
                                            'quantity' => (int) $member['quantity'],
                                            'deduction_rate' => (float) $member['deduction_rate'],
                                            'working_days' => (int) $member['working_days'],
                                            'localization_target' => (int) $member['localization_target'],
                                            'working_hours' => (int) $member['working_hours'],
                                            'attendance_mandatory' => (int) $member['attendance_mandatory'],
                                            'minimum_wage' => (int) $member['minimum_wage'],
                                            'uniform_and_tools_mandatory' => (bool) $member['uniform_and_tools'],
                                        ];
                                    })->keyBy('key')->values()->toArray();

        $variationWorkforceTeam = collect($this->variationOrder['workforce_team'])->map(function ($member) {
                                        return [
                                            'key' => $member['role'] . '|' . $member['proficiency'], // Use array access here
                                            'role' => $member['role'],
                                            'proficiency' => $member['proficiency'],
                                            'quantity' => (int) $member['quantity'],
                                            'deduction_rate' => (float) $member['deduction_rate'],
                                            'working_days' => (int) $member['working_days'],
                                            'localization_target' => (int) $member['localization_target'],
                                            'working_hours' => (int) $member['working_hours'],
                                            'attendance_mandatory' => (int) $member['attendance_mandatory'],
                                            'minimum_wage' => (int) $member['minimum_wage'],
                                            'uniform_and_tools_mandatory' => (bool) $member['uniform_and_tools'],
                                        ];
                                    })->keyBy('key')->values()->toArray();
        
        $comparedWorkforce = $this->compareItemsFieldWise($worforceTeam, $variationWorkforceTeam, 'key');
        
        $this->comparedWorkforce = collect($comparedWorkforce)->map(function ($item) {
            $item['displayName'] = $item['fields']['role']['variation'] . ' (' . $item['fields']['proficiency']['variation'] . ')';
            return $item;
        })->toArray();

        $buildingVariation = collect($this->variationOrder['buildings'])->map(function ($building) {
                        return [
                            'building_id' => $building['building_id'],
                            'building_name' => $building['building_name'],
                            'complex_name' => $building['complex_name'],
                            'property_type' => $building['property_type'],
                            'units_count' => (int) $building['units_count'],
                            'zones_count' => (int) $building['zones_count'],
                            'name' => $building['building_id'], // key used for comparison
                        ];
                    })->toArray();

        $buildingOriginal = collect($this->contractOriginalData['buildings'])->map(function ($building) {
                        return [
                            'building_id' => $building['building_id'],
                            'building_name' => $building['building_name'],
                            'complex_name' => $building['complex_name'],
                            'property_type' => $building['property_type'],
                            'units_count' => (int) $building['units_count'],
                            'zones_count' => (int) $building['zones_count'],
                            'name' => $building['building_id'], // key used for comparison
                        ];
                    })->toArray();
        $this->comparedBuildings = $this->compareItemsFieldWise($buildingOriginal, $buildingVariation, 'building_id');
        
        $this->totalZonesData = $this->getFieldWithDiff($this->contractOriginalData['totalZones'], $this->variationOrder['totalZones']);
        $this->totalUnitData = $this->getFieldWithDiff($this->contractOriginalData['totalUnits'], $this->variationOrder['totalUnits']);

        $serviceVariationData = collect($this->variationOrder['contractServiceKpis'])->map(function ($item) {
            return [
                'service_id' => $item['service_id'],
                'service_name' => $item['assetCategory']['service_name'] ?? 'N/A',
                'kpi_ids' => implode(',', collect($item['kpi_ids'])->sort()->values()->all()),
                'price' => $item['price'],
                'description' => $item['description'],
            ];
        })->toArray();

        $serviceOriginalData =collect($this->contractOriginalData['contractServiceKpis'])->map(function ($item) {
            return [
                'service_id' => $item['service_id'],
                'service_name' => $item['assetCategory']['service_name'] ?? 'N/A',
                'kpi_ids' => implode(',', collect($item['kpi_ids'])->sort()->values()->all()),
                'price' => $item['price'],
                'description' => $item['description'],
            ];
        })->toArray();

        $this->contractServiceKpisDiff = $this->compareItemsFieldWise($serviceOriginalData, $serviceVariationData, 'service_id');

        $originalAssets = collect($this->contractOriginalData['assetsName'])
                        ->pluck('asset_name')
                        ->toArray();
        $variationAssets = collect($this->variationOrder['assetsName'])
                            ->pluck('asset_name')
                            ->toArray();
        $this->assetNameDiffs = $this->prepareItemsWithDiff($originalAssets, $variationAssets);


    }    

    public function compareSelectedKpisFieldWise(array $original, array $variation): array
    {
        $originalKpis = collect($original)->keyBy('class');
        $variationKpis = collect($variation)->keyBy('class');
        $allClasses = $originalKpis->keys()->merge($variationKpis->keys())->unique();

        $result = [];

        foreach ($allClasses as $className) {
            $originalKpi = $originalKpis->get($className);
            $variationKpi = $variationKpis->get($className);

            // KPI Added
            if (!$originalKpi && $variationKpi) {
                $result[] = [
                    'class' => $className,
                    'status' => 'added',
                    'fields' => $this->formatKpiFields(null, $variationKpi, 'added'),
                ];
                $this->totalDifference++;
            }
            // KPI Deleted
            elseif ($originalKpi && !$variationKpi) {
                $result[] = [
                    'class' => $className,
                    'status' => 'deleted',
                    'fields' => $this->formatKpiFields($originalKpi, $originalKpi, 'deleted'),
                ];
                $this->totalDifference++;
            }
            // Field-wise compare
            else {
                $fields = $this->formatKpiFields($originalKpi, $variationKpi);
                $diffCount = collect($fields)
                    ->flatMap(fn($f) => is_array($f) && isset($f['class']) ? [$f['class']] : [])
                    ->filter(fn($class) => $class === 'text-warning')
                    ->count();

                $this->totalDifference += $diffCount;

                $result[] = [
                    'class' => $className,
                    'status' => $diffCount > 0 ? 'modified' : 'unchanged',
                    'fields' => $fields,
                ];
            }
        }
        return $result;
    }


    private function formatKpiFields($original, $variation, $rowStatus = null): array
    {
        $fields = [];

        // 1. Compare class & percentage_type
        foreach (['class', 'percentage_type'] as $key) {
            $originalVal = $original[$key] ?? null;
            $variationVal = $variation[$key] ?? null;

            $isDifferent = $originalVal !== $variationVal;

            $fields[$key] = [
                'original' => $originalVal,
                'variation' => $variationVal,
                'class' => match ($rowStatus) {
                    'deleted' => 'text-loss',
                    'added' => 'text-warning',
                    default => ($isDifferent ? 'text-warning' : ''),
                },
            ];
        }

        // 2. Compare ranges (penalty/deduction)
        $rangeKeys = collect($original['ranges'] ?? [])
            ->keys()
            ->merge(collect($variation['ranges'] ?? [])->keys())
            ->unique();

        $rangeData = [];

        foreach ($rangeKeys as $range) {
            $originalRange = $original['ranges'][$range] ?? ['penalty' => null, 'deduction' => null];
            $variationRange = $variation['ranges'][$range] ?? ['penalty' => null, 'deduction' => null];

            $rangeData[$range] = [
                'penalty' => [
                    'original' => $originalRange['penalty'],
                    'variation' => $variationRange['penalty'],
                    'class' => match ($rowStatus) {
                        'deleted' => 'text-loss',
                        'added' => 'text-warning',
                        default => (
                            $this->normalizeValue($originalRange['penalty']) !== $this->normalizeValue($variationRange['penalty'])
                                ? 'text-warning' : ''
                        ),
                    },
                ],
                'deduction' => [
                    'original' => $originalRange['deduction'],
                    'variation' => $variationRange['deduction'],
                    'class' => match ($rowStatus) {
                        'deleted' => 'text-loss',
                        'added' => 'text-warning',
                        default => (
                            $this->normalizeValue($originalRange['deduction']) !== $this->normalizeValue($variationRange['deduction'])
                                ? 'text-warning' : ''
                        ),
                    },
                ],
            ];
        }

        $fields['ranges'] = $rangeData;

        return $fields;
    }
    
    public function performAction($type)
    {
        $user = Auth::user();

        if ($type === 'cancelled') {
            return $this->handleCancellation($user);
        }

        if (!$this->isAuthorized($user)) {
            $this->dispatchUnauthorizedError();
            return;
        }

        $comment = $this->getActionComment($type);
        $this->validateAction($type);

        DB::beginTransaction();

        try {
            $this->handleBuildingManagerLocking($user);
            $this->softDeleteOtherBuildingManagers($user);

            $this->nextApproval->update([
                'status' => $type,
                'comment' => $comment,
                'acted_at' => now(),
            ]);

            $this->handleDraftUpdates($type);
            DB::commit();

            $this->userApprovalStatus = $type;
            $this->showApprovalActions = false;

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __('advance_contracts.variation_order.variation_success_message'),
            ]);

            $this->dispatchBrowserEvent('close-modals');
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Variation Order Approval Failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'approval_id' => $this->nextApproval->id ?? null,
            ]);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => 'Something went wrong. Please try again.',
            ]);
        }
    }

    private function handleCancellation($user)
    {
        if ($this->draft->initiated_by !== $user->id) {
            $this->dispatchUnauthorizedError();
            return;
        }

        $this->validateOnly('cancelComment');

        DB::beginTransaction();
        try {
            $this->draft->soft_delete_reason = $this->cancelComment;
            $this->draft->save();
            $this->draft->delete();

            $this->showCancelButton = false;
            $this->userApprovalStatus = 'cancelled';

            DB::commit();

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __('advance_contracts.variation_order.variation_success_message'),
            ]);

            $this->dispatchBrowserEvent('close-modals');
        } catch (\Throwable $e) {
            DB::rollBack();

            Log::error('Variation Order Cancel Failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'draft_id' => $this->draft->id,
            ]);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => 'Something went wrong. Please try again.',
            ]);
        }
    }

    private function isAuthorized($user): bool
    {
        return match (true) {
            $this->nextApproval->role === 'sp_admin' => $user->service_provider_id == $this->draft->service_provider_id,
            $this->draft->initiated_by == $user->id     => true,
            default                                     => $this->nextApproval->approver_id == $user->id,
        };
    }

    private function dispatchUnauthorizedError(): void
    {
        $this->dispatchBrowserEvent('show-toastr', [
            'type' => 'error',
            'message' => __('advance_contracts.variation_order.not_authorized'),
        ]);
    }

    private function getActionComment($type): ?string
    {
        return match ($type) {
            'approved'  => $this->approveComment,
            'rejected'  => $this->rejectComment,
            default     => null,
        };
    }

    private function validateAction($type): void
    {
        if ($type === 'rejected') {
            $this->validateOnly('rejectComment');
        }
    }

    private function handleBuildingManagerLocking($user): void
    {
        if ($user->user_type !== 'building_manager') return;

        $approvalSequence = $this->nextApproval->approval_sequence;

        $pendingApprovals = AdvanceContractDraftApproval::where('advance_contract_draft_id', $this->draft->id)
            ->where('approval_sequence', $approvalSequence)
            ->where('role', 'building_manager')
            ->where('status', 'pending')
            ->lockForUpdate()
            ->get();

        $this->nextApproval = $pendingApprovals->where('id', $this->nextApproval->id)->first();

        if (!$this->nextApproval) {
            DB::rollBack();
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => 'This action has already been taken by another approver.',
            ]);
            throw new \Exception('Action already taken');
        }
    }

    private function softDeleteOtherBuildingManagers($user): void
    {
        if ($user->user_type !== 'building_manager') return;

        AdvanceContractDraftApproval::where('advance_contract_draft_id', $this->draft->id)
            ->where('approval_sequence', $this->nextApproval->approval_sequence)
            ->where('role', 'building_manager')
            ->where('id', '!=', $this->nextApproval->id)
            ->whereNull('deleted_at')
            ->delete();
    }

    private function handleDraftUpdates($type): void
    {
        if ($type === 'rejected') {
            $this->draft->status = 'rejected';
            $this->draft->save();

            $this->notifyUser(
                'Your Variation Order request has been rejected.',
                'تم رفض طلب أمر التغيير الخاص بك.',
                'variation_order_rejected'
            );
        }

        if ($type === 'approved') {
            $this->notifyUser(
                'Your Variation Order request has been approved.',
                'تمت الموافقة على طلب أمر التغيير الخاص بك.',
                'variation_order_approved'
            );

            $pendingApprovals = $this->draft->approvals()->where('status', 'pending')->count();
            $anyRejected = $this->draft->approvals()->where('status', 'rejected')->exists();

            if ($pendingApprovals === 0 && !$anyRejected) {
                $this->contractService->applyVariationOrder($this->draft);
            }
        }
    }


    protected function notifyUser($message_en,$message_ar,$notificationType){
        $requester = User::find($this->draft->initiated_by);
        $requester->notify(new VariationOrderApprovalNotification(
            $this->draft,
            $requester,
            $message_en,
            $message_ar,
            $notificationType
        ));
    }


    protected function getCurrentUserApprovalStatus()
    {
     
        $user = Auth::user();
        $query = $this->draft->approvals->where('advance_contract_draft_id', $this->draft->id);

        if ($user->user_type === 'sp_admin') {
            $query = $query->where('approver_id', $user->service_provider);
        } else {
            $query = $query->where('approver_id', $user->id);
        }

        return $query->where('status', '!=', 'pending')->sortBy('approval_sequence')->first();

    }

    protected function setApprovalVisibility()
    {
        // If the draft is soft deleted, no approval actions are shown
        if ($this->draft->trashed()) {
            $this->showApprovalActions = false;
            return;
        }

        $user = Auth::user();
        $anyRejected = $this->draft->approvals->contains('status', 'rejected');

        // Get pending approvals, sorted by sequence level
        $pendingApprovals = $this->draft->approvals
                                ->where('status', 'pending')
                                ->sortBy('approval_sequence');

        if ($pendingApprovals->isEmpty() || $anyRejected) {
            $this->showApprovalActions = false;
            return;
        }
     
        // Get the next approval level (e.g., level 1, 2, etc.)
        $firstPendingApproval = $pendingApprovals->first();
        $nextSequence = $firstPendingApproval->approval_sequence;
      
        if($user->user_type != 'building_manager' && $firstPendingApproval->role != 'building_manager' ){
            $this->nextApproval =  $firstPendingApproval;
            if ($firstPendingApproval->role === 'sp_admin') {
                // Compare service provider ID (assuming service_provider holds the ID)
                $this->showApprovalActions = ($user->service_provider == $firstPendingApproval->approver_id);
            } else {
                // Compare logged-in user's ID with approver_id
                $this->showApprovalActions = ($user->id === $firstPendingApproval->approver_id);
            }
        }else{
            $nextLevelApprovals = $pendingApprovals->where('approval_sequence', $nextSequence);
            foreach ($nextLevelApprovals as $approval) {
                if ($user->id  === $approval->approver_id) {
                    $this->nextApproval =  $approval;
                    $this->showApprovalActions = true;
                    break;
                }
            }
        }
        return;
    }



    public function getFileFieldWithDiff($original, $variation): array
    {
        $isDifferent = $original !== $variation;
        if ($isDifferent) {
            $this->totalDifference++;
        }

        return [
            'value' => $variation,
            'class' => $isDifferent ? 'p-2 border-warning rounded' : '',
            'url' => $variation ? Storage::disk('oci')->url($variation) : null,
        ];
    }


    public function compareItemsFieldWise($originalItems, $variationItems, string $keyBy = 'name'): array
    {
        $originalIndexed = collect($originalItems)->keyBy($keyBy);
        $variationIndexed = collect($variationItems)->keyBy($keyBy);

        $allKeys = $originalIndexed->keys()->merge($variationIndexed->keys())->unique();
        $result = [];
        foreach ($allKeys as $keyValue) {

            
            $original = $originalIndexed->get($keyValue);
            $variation = $variationIndexed->get($keyValue);
 
            if ($original && !$variation) {
                 
                $result[] = [
                    $keyBy => $keyValue,
                    'status' => 'deleted',
                    'fields' => $this->formatFields($original, $original, 'deleted'),
                ];
                $this->totalDifference++;
            } elseif (!$original && $variation) {
                
                $result[] = [
                    $keyBy => $keyValue,
                    'status' => 'added',
                    'fields' => $this->formatFields(null, $variation, 'added'),
                ];
                $this->totalDifference++;
            } else {
                  
                $fields = $this->formatFields($original, $variation);

              
                $diffCount = collect($fields)->filter(fn ($f) => $f['class'] === 'text-warning')->count();
                $this->totalDifference += $diffCount;
                $result[] = [
                    $keyBy => $keyValue,
                    'status' => $diffCount > 0 ? 'modified' : 'unchanged',
                    'fields' => $fields,
                ];
            }
        }

        return $result;
    }


    private function formatFields($original, $variation, $rowStatus = null): array
    {
        $keys = collect($original ?? [])->keys()
            ->merge(collect($variation ?? [])->keys())
            ->unique();

        $fields = [];

        foreach ($keys as $key) {
            $originalVal = $original[$key] ?? null;
            $variationVal = $variation[$key] ?? null;

            $normalizedOriginal = $this->normalizeValue($originalVal);
            $normalizedVariation = $this->normalizeValue($variationVal);

            $isDifferent = $normalizedOriginal !== $normalizedVariation;
            $displayOriginal = $this->formatValue($key, $originalVal);
            $displayVariation = $this->formatValue($key, $variationVal);

            $fields[$key] = [
                'original' => $displayOriginal,
                'variation' => $displayVariation,
                'class' => match ($rowStatus) {
                    'deleted' => 'text-loss',
                    'added'   => 'text-warning',
                    default   => ($isDifferent ? 'text-warning' : ''),
                },
            ];
        }

        return $fields;
    }



    private function normalizeValue($value)
    {
        if (is_bool($value)) {
            return $value ? 1.0 : 0.0;
        }

        if ($value === null || $value === '' || $value === '0' || $value === 0 || $value === 0.0 || $value === '0.00') {
            return 0.0;
        }

        if (is_numeric($value)) {
            return (float) $value;
        }

        return $value;
    }

    private function formatValue($key, $value)
    {
        // Force formatting for price and penalty only
        if (in_array($key, ['price', 'penalty'])) {
            return number_format((float) $value, 2, '.', '');
        }

        return $value;
    }


    public function getAssetCategoriesWithDiff($original, $variation): array
    {
        $diffs = $this->prepareItemsWithDiff($original, $variation);

        return array_map(function ($item) {
            return [
                'name' => $item['name'],
                'class' => $item['status'] == 'added' 
                    ? 'text-warning' 
                    : ($item['status'] == 'deleted' ? 'text-loss' : ''),
            ];
        }, $diffs);
    }


    public function getFieldWithDiff($original, $variation): array
    {
        $isDifferent = $original !== $variation;
        if($isDifferent){
            $this->totalDifference++;
        }
       
        return [
            'value' => $variation,
            'class' => $isDifferent ? 'text-warning' : '',
        ];
    }



    public function prepareItemsWithDiffById($original, $variation, $originalKey = 'building_id', $variationKey = 'building_id'): array
    {
        // Index original and variation by ID for fast lookup
        $originalIndexed = collect($original)->keyBy($originalKey);
        $variationIndexed = collect($variation)->keyBy($variationKey);

        $allIds = collect(array_merge(
            array_keys($originalIndexed->all()),
            array_keys($variationIndexed->all())
        ))->unique();

        $items = $allIds->map(function ($id) use ($originalIndexed, $variationIndexed) {
            $isAdded = !$originalIndexed->has($id) && $variationIndexed->has($id);
            $isDeleted = $originalIndexed->has($id) && !$variationIndexed->has($id);
            if ($isAdded || $isDeleted) {
                $this->totalDifference++;
            }
            $building = $variationIndexed[$id] ?? $originalIndexed[$id];

            return [
                'id' => $id,
                'name' => $building['building_name'] ?? $building['building_tag'] ?? 'N/A',
                'status' => $isAdded ? 'added' : ($isDeleted ? 'deleted' : 'unchanged'),
            ];
        })->values()->toArray();

        return  $items;
    }
    public function prepareItemsWithDiff($original, $variation): array
    {
        $added = array_diff($variation, $original);
        $deleted = array_diff($original, $variation);
        $merged = array_unique(array_merge($variation, $original));
        $this->totalDifference =   $this->totalDifference + count($added) + count($deleted);
        return array_map(function ($item) use ($added, $deleted) {
            return [
                'name' => $item,
                'status' => in_array($item, $added) ? 'added'
                        : (in_array($item, $deleted) ? 'deleted' : 'unchanged'),
            ];
        }, $merged);
    }

    public function render()
    {
        return view('livewire.advance-contracts.view-variation-order');
    }
}
