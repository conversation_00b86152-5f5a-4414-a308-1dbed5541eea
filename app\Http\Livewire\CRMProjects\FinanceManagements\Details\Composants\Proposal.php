<?php
    namespace App\Http\Livewire\CRMProjects\FinanceManagements\Details\Composants;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Services\CRM\Projects\FinanceServices;
    use App\Http\Traits\FunctionsTrait;

    class Proposal extends Component{
        use FunctionsTrait;
        
        public $projectID;
        public $documentId;
        public $documentType;
        public $projectDetails;
        public $documentDetails;

        public function render(){
            if (isset($this->projectDetails) && $this->projectDetails[ 'status' ] == 'success') {
                return view('livewire.c-r-m-projects.finance-managements.details.composants.proposal');
            }

            else {
                return view('livewire.c-r-m-projects.empty-project');
            }
        }

        public function mount() {
            try {
                $this->initProjectDetails();
                $this->initDocumentDetails();
            } 
            
            catch (\Throwable $th) {
                Log::error('mount error: '.$th);
            }
        }

        public function initProjectDetails() {
            try {
                $financeService = app(FinanceServices::class);
                $this->projectDetails = $financeService->getProjectDetails($this->projectID);
            } 
            
            catch (\Throwable $th) {
                Log::error('initProjectDetails error: '.$th);
            }
        }

        public function initDocumentDetails() {
            try {
                $financeService = app(FinanceServices::class);
                $this->documentDetails = $financeService->proposalDetail($this->projectID, $this->documentType, $this->documentId);
            } 
            
            catch (\Throwable $th) {
                Log::error('initDocumentDetails error: '.$th);
            }
        }
    }
?>