<?php

namespace App\Http\Livewire\CRMProjects\FinanceManagements\Details;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Log;
use App\Services\CRM\Projects\FinanceServices;
use App\Http\Traits\FunctionsTrait;
use PDF2 as MPDF;
class FinanceBill extends Component
{
    use WithFileUploads, FunctionsTrait;
    public $projectID;
    public $documentType;
    public $documentId;
    public $invoiceDeliveryDetails;
    public $currentDate;
    public $invoiceDetails;
    public $date;
    public $amount;
    public $account;
    public $reference;
    public $description;
    public $file;
    protected $listeners = ['setAccount', 'setDate'];
    public $selectedPayment;
    public function mount()
    {
   
        try {
                $this->initProjectDetails();
                $this->initInvoiceDeliveryDetails();
                $this->initCurrentDate();
                $this->initInvoiceDetails();
            } 
            
        catch (\Throwable $th) {
            Log::error('mount error: '.$th);
        }
        
    }


    public function render()
    {
        if (isset($this->projectDetails) && $this->projectDetails['status'] == 'success') {
            return view('livewire.c-r-m-projects.finance-managements.details.finance-bill');
        }
        else {
            return view('livewire.c-r-m-projects.empty-project');
        }
        
    }

     public function initProjectDetails() {
            try {
                $financeService = app(FinanceServices::class);
                $this->projectDetails = $financeService->getProjectDetails($this->projectID);

            } 
            
            catch (\Throwable $th) {
                Log::error('initProjectDetails error: '.$th);
            }
    }
    public function initInvoiceDeliveryDetails() {
            try {
                $financeService = app(FinanceServices::class);
                $this->invoiceDeliveryDetails = $financeService->invoiceDelivery($this->projectID, $this->documentType, $this->documentId);
                       // echo '<pre>';print_r($this->invoiceDeliveryDetails);die;

            } 
            
            catch (\Throwable $th) {
                Log::error('initInvoiceDeliveryDetails error: '.$th);
            }
    }

    public function initCurrentDate() {
            try {
                $this->currentDate = $this->getCurrentDateWithChangedFormat('d/m/Y');
            } 
            
            catch (\Throwable $th) {
                Log::error('initCurrentDate error: '.$th);
            }
    }

    public function initInvoiceDetails() {
        try {
            $financeService = app(FinanceServices::class);
            $this->invoiceDetails = $financeService->getBillDetails($this->projectID, $this->documentType, $this->documentId);
//echo '<pre>';print_r($this->invoiceDetails);die;
        } 
       
        catch (\Throwable $th) {
            Log::error('initInvoiceDetails error: '.$th);
        }
    }



    public function submitPaymentForm() {
       // dd($this);
            $validatedData = $this->validate( [
                'date' => 'required',
                'amount' => 'required|integer',
                'account' => 'required',
                'reference' => 'required',
                'description' => 'required',
                'file' => 'nullable|file'
            ],
            [
                'date.required' => __('finace_manage.common.date_required'),
                'amount.required' => __('finace_manage.common.amount_required'),
                'amount.integer' => __('finace_manage.common.amount_invalid'),
                'account.required' => __('finace_manage.common.account_required'),
                'reference.required' => __('finace_manage.common.reference_required'),
                'description.required' => __('finace_manage.common.description_required'),
                'file.required' => __('finace_manage.common.file_required'),
                'file.file' => __('finace_manage.common.file_invalid')
            ] );

           try {
                if($validatedData){
                    $data = [
                        'date' => $this->changeDateFormat('Y-m-d', $this->date),
                        'amount' => $this->amount,
                        'account_id' => $this->account,
                        'reference' => $this->reference,
                        'description' => $this->description
                    ]; 
                    
                    $extension = $this->file->getClientOriginalExtension();
                    $filename = time().".".$extension;
                    $filePath = $this->file->storeAs('uploads/payments', $filename, 'local');
                    $absolutePath = storage_path('app/' . $filePath);

                    $response = $this->initCreateInvoicePayment($absolutePath, $filename, $data);
                    //dd($response);
                    if(isset($response) && $response['status'] == 'success'){
                        $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'success',
                            'message' =>$response['message']
                        ]);
                        $this->mount();
                        $this->resetPaymentForm();
                        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'add-payment-modal' ] );
                    }

                    else{
                        $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'error',
                            'message' =>$response['message']
                        ]);
                        $this->resetPaymentForm();
                        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'add-payment-modal' ] );
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error('submitPaymentForm error: '.$th);
            }

             
        }

        public function initCreateInvoicePayment($filePath, $filename, $data) {
            try {
                $financeService = app(FinanceServices::class);
                return $financeService->createBillPayment($this->projectID, $this->documentType, $this->documentId, $filePath, $filename, $data['date'], $data['amount'], $data['account_id'], $data['reference'], $data['description']);
            } 
            
            catch (\Throwable $th) {
                Log::error('initCreateInvoicePayment error: '.$th);
            }
        }

       

        public function resetPaymentForm() {
            try {
                $this->setAmount(null);
                $this->resetAccount();
                $this->setReference(null);
                $this->setDescription(null);
                $this->resetFile();
                $this->resetDate();
            } 
            
            catch (\Throwable $th) {
                Log::error('resetPaymentForm error: '.$th);
            }
        }


        public function setDate($value) {
            try {
                $this->date = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setDate error: '.$th);
            }
        }

        public function setAmount($value) {
            try {
                $this->amount = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setAmount error: '.$th);
            }
        }
        public function setAccount($value) {
            try {
                $this->account = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setAccount error: '.$th);
            }
        }

        public function setReference($value) {
            try {
                $this->reference = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setReference error: '.$th);
            }
        }

        public function setDescription($value) {
            try {
                $this->description = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setDescription error: '.$th);
            }
        }

        public function setFile($value) {
            try {
                $this->file = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setFile error: '.$th);
            }
        }



        public function setSelectedPayment($value) {
            try {
                $this->selectedPayment = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error('setSelectedPayment error: '.$th);
            }
        }

        public function deletePayment() {
            try {
                $financeService = app(FinanceServices::class);
                $response =  $financeService->deletePaymentByValues($this->projectID, $this->documentType, $this->documentId, $this->selectedPayment);

                if(isset($response) && $response['status'] == 'success'){
                     $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'success',
                            'message' =>$response['message']
                        ]);
                    $this->initInvoiceDetails();
                    $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'confirm-delete-payment' ] );
                }

                else{
                    $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'error',
                            'message' =>$response['message']
                    ]);
                    $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'confirm-delete-payment' ] );
                }
            } 
            
            catch (\Throwable $th) {
                Log::error('deleteAttachment error: '.$th);
            }

           
        }


    public function resendBill() {
        try {
                $financeService = app(FinanceServices::class);
                $response =  $financeService->resendBill($this->projectID, $this->documentType, $this->documentId);

                if(isset($response) && $response['status'] == 'success'){
                     $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'success',
                            'message' =>$response['message']
                        ]);
                }

                else{
                    $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'error',
                            'message' =>$response['message']
                    ]);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error('deleteAttachment error: '.$th);
            }
    }

    public function sendDocument() {
           try {
                $response = $this->initSendDocument();

                if(isset($response) && $response['status'] == 'success'){
                    $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'success',
                            'message' =>$response['message']
                        ]);
                    $this->initInvoiceDetails();
                }

                else{
                    $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'error',
                            'message' =>$response['message']
                    ]);
                }
            } 
            
            catch (\Throwable $th) {
                Log::error('sendDocument error: '.$th);
            }
        }
      public function initSendDocument() {
            try {
                $financeService = app(FinanceServices::class);
                return $financeService->billSendMail($this->projectID,$this->documentType, $this->documentId);
            } 
            
            catch (\Throwable $th) {
                Log::error('initSendDocument error: '.$th);
            }
        }

        public function initAmount() {
            try {
                isset($this->invoiceDeliveryDetails) && $this->invoiceDeliveryDetails['status'] == "success" ? $this->setAmount($this->invoiceDeliveryDetails['data']['total']) : 0;
            } 
            
            catch (\Throwable $th) {
                Log::error('initAmount error: '.$th);
            }
        }

         public function resetAccount() {
            try {
                $this->setAccount(null);
                $this->callJsFunction('resetAccount');
            } 
            
            catch (\Throwable $th) {
                Log::error('resetAccount error: '.$th);
            }
        }

        public function callJsFunction($key) {
            try {
                $this->dispatchBrowserEvent($key);
            } 
            
            catch (\Throwable $th) {
                Log::error("callJsFunction error: ".$th);
            }
        }

        public function resetFile() {
            try {
                $this->setFile(null);
                $this->callJsFunction('resetFile');
            } 
            
            catch (\Throwable $th) {
                Log::error('resetFile error: '.$th);
            }
        }

        public function resetDate() {
            try {
                $this->setDate(null);
                $this->callJsFunction('resetDate');
            } 
            
            catch (\Throwable $th) {
                Log::error('resetDate error: '.$th);
            }
        }
}




