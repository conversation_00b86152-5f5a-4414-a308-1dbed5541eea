<?php

namespace App\Http\Livewire\CRMProjects\Modals;

use App\Http\Helpers\Helper;
use Livewire\Component;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestImage;
use App\Http\Helpers\ImagesUploadHelper;
use App\Models\VendorServiceCategory;
use App\Services\CRM\Projects\ProjectTemplateService;
use App\Services\CRM\Sales\ProjectService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Contracts\Encryption\DecryptException;

class UpdateCalendarTask extends Component
{
    public $project, $milestone, $title, $priority, $assign_to = [], $stage, $startDate, $endDate, $description, $projectId, $itemId, $filter_start_date, $filter_end_date;
    public $projects = [], $milestones = [], $priorities = [], $users = [], $stages = [];

    protected $listeners = ['resetForm', 'openUpdateCalendarTaskModal', 'calendarEventUpdated'];

    protected $rules = [
        'project' => 'required',
        'milestone' => 'required',
        'title' => 'required',
        'priority' => 'required',
        'assign_to' => 'required',
        'stage' => 'required',
        'startDate' => 'required',
        'endDate' => 'required',
        'description' => 'required',
    ];

    public function mount()
    {
        try {
            $this->projectId = decrypt(request()->id);
        } catch (DecryptException $e) {
            return abort(404);
        }
    }

    public function calendarEventUpdated($data)
    {
        $this->itemId = $data['eventId'];
        $this->startDate = Carbon::parse($data['start'])->format('Y-m-d h:i A');
        $this->endDate = Carbon::parse($data['end'])->format('Y-m-d h:i A');

        $this->submit();
    }

    public function openUpdateCalendarTaskModal($id)
    {
        $this->itemId = $id;
        $this->fetchProjects();
        $this->fetchMilestones();
        $this->fetchPriorities();
        $this->fetchUsers();
        $this->fetchStages();
        $this->fetchData();

        $this->dispatchBrowserEvent('open-modal', ['modalId' => 'updateCalendarTaskModal']);
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function fetchData()
    {
        $service =  app(ProjectService::class);
        $response = $service->taskData($this->projectId, $this->itemId);
        if (@$response['status'] == "success") {
            $data = $response['data'];
            $this->project = $data['project_id'];
            $this->milestone = $data['milestone_id'];
            $this->title = $data['title'];
            $this->priority = $data['priority'];
            $this->assign_to = $data['assign_to'];
            $this->stage = $data['status'];
            $this->description = $data['description'] ?? '';
            $this->startDate = Carbon::parse($data['start_date'])->format('Y-m-d h:i A');
            $this->endDate = Carbon::parse($data['due_date'])->format('Y-m-d h:i A');
        }
    }

    public function fetchProjects()
    {
        $service =  app(ProjectService::class);
        $response = $service->projectList();
        if (@$response['status'] == "success") {
            $this->projects = $response['data'];
        }
    }

    public function fetchMilestones()
    {
        $service =  app(ProjectService::class);
        $response = $service->milestoneList($this->projectId);
        if (@$response['status'] == "success") {
            $this->milestones = $response['data'];
        }
    }

    public function fetchPriorities()
    {
        $service =  app(ProjectService::class);
        $response = $service->priorityList();
        if (@$response['status'] == "success") {
            $this->priorities = $response['data'];
        }
    }

    public function fetchUsers()
    {
        $service =  app(ProjectService::class);
        $response = $service->userList($this->projectId);
        if (@$response['status'] == "success") {
            $this->users = $response['data'];
        }
    }

    public function fetchStages()
    {
        $service =  app(ProjectService::class);
        $response = $service->stageList();
        if (@$response['status'] == "success") {
            $this->stages = $response['data'];
        }
    }

    public function resetForm()
    {
        $this->reset(['project', 'milestone', 'title', 'priority', 'assign_to', 'stage', 'startDate', 'endDate', 'description']);
        $this->resetErrorBag();
    }

    public function submit()
    {
        $this->validate();
        $service =  app(ProjectService::class);
        $response = $service->updateTaskData($this->projectId, $this->itemId, [
            'title' => $this->title,
            'priority' => $this->priority,
            'milestone_id' => $this->milestone,
            'start_date' => Carbon::parse($this->startDate)->format('Y-m-d h:i A'),
            'due_date' => Carbon::parse($this->endDate)->format('Y-m-d h:i A'),
            'assign_to' => $this->assign_to,
            'project_id' => $this->project,
            'stage_id' => $this->stage,
            'description' => $this->description,
            'task_type' => 'untangible'
        ]);

        if ($response['status'] === 'success') {
            $this->dispatchBrowserEvent('close-modal', ['modalId' => 'updateCalendarTaskModal']);
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => $response['message']
            ]);
            $this->dispatchBrowserEvent('calendar-event-update-approved', [
                'id' => $this->itemId,
                'title' => $this->title,
                'start' => Helper::formatDateForLocale($this->startDate),
                'end' => Helper::formatDateForLocale($this->endDate),
            ]);
            if ($this->projectId != $this->project) {
                $this->dispatchBrowserEvent('remove-event', [
                    'id' => $this->itemId,
                ]);
            }
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $response['message']
            ]);
        }
        $this->dispatchBrowserEvent('hideLoader');
    }

    public function render()
    {
        return view('livewire.c-r-m-projects.modals.update-calendar-task');
    }
}
