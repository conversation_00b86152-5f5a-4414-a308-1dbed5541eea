<?php

namespace App\Http\Livewire\CRMProjects;

use Livewire\Component;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use App\Services\CRM\Projects\ProjectServices;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Crypt;

class ProjectsList extends Component
 {
    public $viewMode = 'cards';
    public $projects = [];
    public $start_date;
    public $end_date;
    public $filter_start_date;
    public $filter_end_date;
    public $search;
    public $budget ;
    public $project_type='';
    public $nextPageUrl;
    public $prevPageUrl;
    public $status;
    public $projectID;
    public $projecTid;
    public $UsersforCreateProject = [];
    public $users = [];
    public $projectType = [];
    public $priorityLevel = [];
    public $priority_level ;
    public $clients = [];
    public $vendors = [];
    public $vendor = [];
    public $selectedUsers = [];
    public $selectedUsersForInvite = [];
    public $usersAlreadyInvited = [];
    public $selectedvendorsForShare = [];
    public $vendorsAlreadyInProject = [];
    public $selectedclientsForShare = [];
    public $clientssAlreadyInProject = [];

    public $name, $description, $ProjectTemplateName;
    protected $listeners = [ /* 'fetchData', */ /* 'deleteItem' => 'delete',  */'filterByDate' ];
    /* Clonne Option */
    public $project = '';
    public $statuss;
    public $bug = [];
    public $task = [];
    public $activity = [];
    public $user = [];
    public $client = [];
    public $milestone = [];
    public $project_file = [];
    public $building_manager = [];

    /* Paginator */
    public $page = 1;
    public $totalPages = 1;
    public $perPage = 8;
    public $totalItems = 0;
    public $isRefreshButton = true;
    protected $queryString = [
        'status' => [ 'except' => '' ],
        'search' => [ 'except' => '' ],
        'filter_start_date' => [ 'except' => '' ],
        'filter_end_date' => [ 'except' => '' ],
        'page' => [ 'except' => '' ],
    ];
    public $sortDirection = 'desc';
    public $sortField = '';
    public function mount($projectID = null)
 {
        $this->workspaceSlug = auth()->user()->workspace ?? 'default-workspace' ;

        $this->viewMode = session()->get( 'viewMode', 'cards' );
        $page = request()->query( 'page', 1 );
        $this->fetchData( $page );
        $this->projectID = $projectID;

    }

    public function handleButtonClick()
 {
        if ( $this->isRefreshButton ) {
            $this->refreshData();

        } else {
            $this->resetAllFilter();

        }

    }

    public function HasFiltersProperty()
 {
        $hasFilters = !empty( $this->search ) || !empty( $this->filter_start_date ) || !empty( $this->filter_end_date ) || !empty( $this->status );
        return   $hasFilters;
    }

    public function handleButtonClickResetOrRefresh()
 {
        if ( $this->HasFiltersProperty() ) {
            $this->resetAllFilter();

        } else {
            $this->refreshData();

        }
    }

    public function refreshData()
 {
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );
    }

    public function resetAllFilter()
 {
        $this->status = '';
        $this->search = '';
        $this->filter_start_date = '';
        $this->filter_end_date = '';
        $this->fetchData();
        $this->isRefreshButton = true;
    }

    public function checkTaskgroupe()
 {
        if ( in_array( 'sub_task', $this->task ) || in_array( 'task_comment', $this->task ) || in_array( 'task_files', $this->task ) ) {
            if ( !in_array( 'task', $this->task ) ) {
                $this->task[] = 'task';
            }
        }

        $this->updateCheckAllStatus();
    }

    public function updatedPerPage( $value )
 {
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );
    }

    public function checkBugGroupe()
 {
        if ( in_array( 'bug_comment', $this->bug ) || in_array( 'bug_files', $this->bug ) ) {
            if ( !in_array( 'bug', $this->bug ) ) {
                $this->bug[] = 'bug';
            }
        }

        $this->updateCheckAllStatus();
    }

    public function checkAll()
 {
        if ( $this->project == 'all' ) {
            $this->task = [ 'task', 'sub_task', 'task_comment', 'task_files' ];
            $this->bug = [ 'bug', 'bug_comment', 'bug_files' ];
            $this->activity = [ 'activity' ];
            $this->user = [ 'user' ];
            $this->client = [ 'client' ];
            $this->vendor = [ 'vendor' ];
            $this->milestone = [ 'milestone' ];
            $this->project_file = [ 'project_file' ];
            $this->building_manager = [ 'building_manager' ];
        } else {
            $this->task = [];
            $this->bug = [];
            $this->activity = [];
            $this->user = [];
            $this->client = [];
            $this->vendor = [];
            $this->milestone = [];
            $this->project_file = [];
            $this->building_manager = [];
        }
        $this->updateCheckAllStatus();
    }

    public function updateCheckAllStatus()
 {
        // Check if all individual checkboxes are checked
        $isAllChecked = true;

        // Check if all options are selected ( task group )
        $taskCheckboxes = [ 'task', 'sub_task', 'task_comment', 'task_files' ];
        foreach ( $taskCheckboxes as $checkbox ) {
            if ( !in_array( $checkbox, $this->task ) ) {
                $isAllChecked = false;
                break;
            }
        }

        // Check if all options are selected ( bug group )
        $bugCheckboxes = [ 'bug', 'bug_comment', 'bug_files' ];
        foreach ( $bugCheckboxes as $checkbox ) {
            if ( !in_array( $checkbox, $this->bug ) ) {
                $isAllChecked = false;
                break;
            }
        }

        // Check if all options are selected ( activity, user, client, milestone, project_file, etc. )
        $individualCheckboxes = [
            'activity' => $this->activity,
            'user' => $this->user,
            'client' => $this->client,
            'milestone' => $this->milestone,
            'project_file' => $this->project_file,
            'vendor' => $this->vendor,
            'building_manager' => $this->building_manager,
        ];

        // Loop through all individual checkboxes and check if they're selected
    foreach ($individualCheckboxes as $key => $checkboxArray) {
        if (empty($checkboxArray)) {
            $isAllChecked = false;
            break;
        }
    }

    // If all are checked, set the "Check All" box to checked, otherwise unchecked
    $this->project = $isAllChecked ? 'all' : '';
}


    public function getFormatDateForFilter( $date )
 {
        try {
            if ( empty( $date ) || !is_string( $date ) || is_null( $date ) ) {
                return null;
            }
            return Carbon::parse( $date )->format( 'Y-m-d' );
        } catch ( \Exception $e ) {
            return null;
        }
    }

    public function getUserList(): array
 {
        $crmProjectsService = app( ProjectServices::class );
        $response = $crmProjectsService->getAllUsers( $this->workspaceSlug );
        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            return $response[ 'data' ]?? [];
        }
        return [];
    }


    public function getProjectType(): array
    {
        $crmProjectsService = app( ProjectServices::class );
        $response = $crmProjectsService->getProjectType( $this->workspaceSlug );
        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            return $response[ 'data' ]?? [];
        }
        return [];
    }

    public function getPriorityLevel(): array
    {
        $crmProjectsService = app( ProjectServices::class );
        $response = $crmProjectsService->getPriorityLevel( $this->workspaceSlug );
        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            return $response[ 'data' ]?? [];
        }
        return [];
    }

    public function getClientsList(): array
 {
        $crmProjectsService = app( ProjectServices::class );
        $response = $crmProjectsService->getAllClients( $this->workspaceSlug );
        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            return $response[ 'data' ] ?? [];
        }
        return [];
    }

    public function getAllVendors(): array
 {
        $crmProjectsService = app( ProjectServices::class );
        $response = $crmProjectsService->getAllVendors( $this->workspaceSlug );
        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            return $response[ 'data' ] ?? [];
        }
        return [];
    }


    public function sortBy( $field )
    {
        if ( $this->sortField === $field ) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->fetchData();
    }

    public function fetchData( $page = 1 )
 {

        if ( !is_numeric( $page ) || intval( $page ) <= 0 ) {
            $page = 1;
        }
        $this->page = intval( $page );

        $crmProjectsService = app( ProjectServices::class );
        $hasFilters = !empty( $this->search ) || !empty( $this->filter_start_date ) || !empty( $this->filter_end_date ) || !empty( $this->status );
        if ( $hasFilters ) {
            $data = $crmProjectsService->searchProjects(
                $this->workspaceSlug,
                [
                    'search' => $this->search,
                    'start_date' => $this->getFormatDateForFilter( $this->filter_start_date ),
                    'end_date' => $this->getFormatDateForFilter( $this->filter_end_date ),
                    'status' => $this->status,
                ],
                $page,
                $this->perPage
            );
        }elseif(!empty( $this->sortField )){
             $data = $crmProjectsService->getProjectswithPaginationFilter(
                $this->workspaceSlug,
                [
                    'sort_by'=>$this->sortField,
                    'sort_order'=>$this->sortDirection,
                ],
                $page,
                $this->perPage
            );
        } else {
            $data = $crmProjectsService->getProjectswithPagination(
                $this->workspaceSlug,
                $page,
                $this->perPage
            );  
        }

        if ( @$data[ 'status' ] == 'success' ) {
            $this->projects = $data[ 'data' ][ 'items' ] ?? [];
            $this->totalItems = $data[ 'data' ][ 'total' ] ?? 0;
            $this->totalPages = ceil( $this->totalItems / $this->perPage );
            $this->users = $data[ 'data' ][ 'all_users' ] ?? [];
            $this->projectType = $data[ 'data' ][ 'project_types' ] ?? [];
            $this->priorityLevel = $data[ 'data' ][ 'priority_levels' ] ?? [];

            $this->clients = $data[ 'data' ][ 'all_clients' ] ?? [];
            $this->vendors = $data[ 'data' ][ 'all_vendors' ] ?? [];

            // Update totalPages based on total items
            $this->nextPageUrl = $data[ 'data' ][ 'next_page_url' ];
            $this->prevPageUrl = $data[ 'data' ][ 'prev_page_url' ];

        }
        $this->dispatchBrowserEvent( 'updateProjectAvatar' );
    }

    public function goToPage( $page )
 {

        if ( $page >= 1 && $page <= $this->totalPages ) {
            $this->dispatchBrowserEvent( 'show-loader' );

            $this->page = $page;
            $this->fetchData( $page );
            $this->dispatchBrowserEvent( 'hide-loader' );
        }
    }

    public function filterByStatus( $status )
 {
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->status = $status;
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );
    }

    public function filterByDate( $startDate, $endDate )
 {
        $this->filter_start_date = $startDate;
        $this->filter_end_date = $endDate;
        $this->fetchData();
    }

    public function switchView( $view )
 {
        $this->viewMode = $view;
        session()->put( 'viewMode', $view );
        $this->fetchData();
    }

    public function delete( )
 {
        if ( $this->projectID  ) {
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'deleteConfirmModal' ] );
            $this->dispatchBrowserEvent( 'show-loader' );
            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->deleteProject( $this->workspaceSlug, $this->projectID );

            $successMessage = __( 'CRMProjects.common.project_deleted_success' );
            if ( $response[ 'status' ] === 'success' ) {

                $this->fetchData();
                $this->dispatchBrowserEvent('hide-loader');
                $this->dispatchBrowserEvent( 'show-toastr', [
                    'type' => 'success',
                    'message' => $successMessage
                ] );
            } else {
                $errorMessage = __('CRMProjects.common.error_message');


            if ($response['message'] === "There are some Task and Bug on Project, please remove it first!") {
                $errorMessage = __('CRMProjects.common.error_task_bug_on_project');
            }

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $errorMessage
            ]);
            $this->dispatchBrowserEvent('hide-loader');
            }
        }

    }

    public function openEditModal( $id )
 {
    $this->reset(['name', 'description', 'start_date', 'end_date', 'statuss', 'budget', 'project_type','priority_level']);

   $crmProjectsService = app(ProjectServices::class);
        $response = $crmProjectsService->getProjectDetails($this->workspaceSlug, $id);

        if ($response['status'] === 'success') {
            $data = $response['data']['project'];
            $this->project_type = $data['project_type'];
            $this->priority_level = $data['priority_level'];
            $this->name = $data['title'];
            $this->description = $data['description'];
            $this->start_date = $data['start_date'];
            $this->end_date = $data['end_date'];
            $this->statuss = $data['status'];
            $this->budget =  (float) preg_replace('/[^\d.]/', '', $data['budget']);
            $this->projecTid = $data['id'];

        } else {
            session()->flash('error', 'Failed to load lead details.');
        }
        $SelectplaceholderText = __( 'CRMProjects.common.choose_user' )?? __( 'CRMProjects.common.please_select' );
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'edit-project' ] );
        $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'usersListforeditProject', 'placeholderText'=>$SelectplaceholderText ] );

    }


    public function openCreateModal()
 {
    $this->resetCreateForm();
        $SelectplaceholderText = __( 'CRMProjects.common.choose_user' )?? __( 'CRMProjects.common.please_select' );
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'create-project' ] );
        $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'usersListforcreateProject', 'placeholderText'=>$SelectplaceholderText ] );

    }

    public function openShareToClientModal( $id ,$usersJson=[])
 {
    $this->projectID = $id;
    $users = $usersJson ? json_decode($usersJson, true) : [];
    $this->clientssAlreadyInProject = collect($users)->pluck('email')->toArray();

        $SelectplaceholderText = __( 'CRMProjects.common.choose_client' )?? __( 'CRMProjects.common.please_select' );
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'share-clients' ] );
        $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'selectedclientsForShare', 'placeholderText'=>$SelectplaceholderText ] );

    }

    public function openShareToVendorModal( $id,$usersJson=[] )
 {
    $this->projectID = $id;
    $users = $usersJson ? json_decode($usersJson, true) : [];
    $this->vendorsAlreadyInProject = collect($users)->pluck('email')->toArray();
        $SelectplaceholderText = __( 'CRMProjects.common.choose_vendor' )?? __( 'CRMProjects.common.please_select' );
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'share-vendors' ] );
        $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'selectedvendorsForShare', 'placeholderText'=>$SelectplaceholderText ] );

    }

    public function openInviteUserModal( $id ,$usersJson=[])
    {
        $this->projectID = $id;
        $users = $usersJson ? json_decode($usersJson, true) : [];
        $this->usersAlreadyInvited = collect($users)->pluck('email')->toArray();

        $SelectplaceholderText = __( 'CRMProjects.common.choose_user' )?? __( 'CRMProjects.common.please_select' );
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'invite-users' ] );
        $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'selectedUsersForInvite', 'placeholderText'=>$SelectplaceholderText ] );

    }

    public function openDeleteModal( $id ,$name)
 {
        $this->projectID = $id;
        $this->name = $name;


        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'deleteConfirmModal' ] );


    }

    public function openDuplicateProjectModal( $id, $name )
  {
    $this->resetValidation();
    $this->projectID = $id;
    $this->name = $name.' Copy';

        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'duplcate-project' ] );

    }
    public function resetDuplicateForm()
  {
    $this->project = '';
    $this->bug = [];
    $this->task = [];
    $this->activity= [];
    $this->user= [];
    $this->client= [];
    $this->milestone= [];
    $this->project_file= [];
    $this->vendor= [];
    $this->building_manager= [];


    }
    public function resetCreateForm()
    {

        $this->name = '';
        $this->budget = '';
        $this->start_date = '';
        $this->end_date = '';
        $this->description = '';
        $this->UsersforCreateProject = [];
        $this->resetErrorBag();


    }

    public function closeDuplicateForm(){
        $this->resetDuplicateForm();
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'duplcate-project' ] );
    }

    public function openSaveProjectAsTemplateModal( $id )
 {
        $this->resetValidation();
        $this->projectID = $id;
        $this->ProjectTemplateName='';
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'saveProjectAsTemplate' ] );

    }

    public function inviteUsers() {


        $this->validate([
            'selectedUsersForInvite' => 'required',

        ]);

        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'invite-users' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
        $successMessage = __( 'CRMProjects.common.users_invited_successfully' );
       $errorMessage = __( 'CRMProjects.common.failed_to_invite_user' );
        if ( $this->projectID ) {
            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->inviteUsers( $this->workspaceSlug,  $this->projectID, $this->selectedUsersForInvite );
            $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
          $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

        $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => $type,
            'message' => $message
        ] );

        $this->selectedUsersForInvite=[];
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );


        } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' => $errorMessage
            ] );
            $this->fetchData();
            $this->dispatchBrowserEvent( 'hide-loader' );
        }



    }
    public function shareToClients() {

        $this->validate([
            'selectedclientsForShare' => 'required',

        ]);
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'share-clients' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
        $successMessage = __( 'CRMProjects.common.project_shared_with_users_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_share_project' );


        if ( $this->projectID ) {

            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->shareWithClients( $this->workspaceSlug,  $this->projectID, $this->selectedclientsForShare );

            $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
          $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

        $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => $type,
            'message' => $message
        ] );

        $this->selectedclientsForShare=[];

        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );

        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $errorMessage
            ]);
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'share-clients' ] );
            $this->dispatchBrowserEvent( 'hide-loader' );
        }



    }
    public function shareToVendors() {

        $this->validate([
            'selectedvendorsForShare' => 'required',

        ]);
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'share-vendors' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
     $successMessage = __( 'CRMProjects.common.project_shared_with_vendor_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_share_project' );

        if ( $this->projectID ) {
            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->shareWithVendors( $this->workspaceSlug,  $this->projectID, $this->selectedvendorsForShare );
            $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
          $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

        $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => $type,
            'message' => $message
        ] );

        $this->selectedvendorsForShare=[];

        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );


        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $errorMessage
            ]);
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'share-vendors' ] );
            $this->dispatchBrowserEvent( 'hide-loader' );
        }



    }

    public function updateProject() {
        $this->validate([
            'priority_level' => 'required',
            'project_type' => 'required',
            'name' => 'required',
            'budget' => 'required|numeric|gt:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'description' => 'required|string|max:5000',
        ],[
            'end_date.after_or_equal' => __('CRMProjects.common.start_end_validation_date'),
            'budget.gt' => __('CRMProjects.common.budget_must_be_positive'),
            'description.max' => __('CRMProjects.common.description_max_limit'),
        ]);
        $crmProjectsService = app( ProjectServices::class );
        //dd($this->start_date);
        $response = $crmProjectsService->updateProject( $this->workspaceSlug,  $this->projecTid, [
            'priority_level' =>  $this->priority_level,
            'project_type' =>  $this->project_type,
            'name' => $this->name,
            'budget' =>$this->budget,
            'start_date' => Carbon::parse($this->start_date)->format('Y-m-d'),
            'end_date' => Carbon::parse($this->end_date)->format('Y-m-d'),
           'status'=> $this->statuss,
           'description' => $this->description,
        ] );
        if ($response['status'] === 'success') {
            $this->fetchData();
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'edit-project' ] );
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => __('CRMProjects.common.project_updated_successfully')
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $response['message']
            ]);
        }
    }


    public function createProject() {


        $this->validate([
            'priority_level'=> 'required',
            'project_type' => 'required',
            'name' => 'required',
            'budget' => 'required|numeric|gt:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'description' => 'required|string|max:5000',
            'UsersforCreateProject' => 'required',
        ], [
            'end_date.after_or_equal' => __('CRMProjects.common.start_end_validation_date'),
            'budget.gt' => __('CRMProjects.common.budget_must_be_positive'),
            'description.max' => __('CRMProjects.common.description_max_limit'),
        ]);

        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'create-project' ] );

        $this->dispatchBrowserEvent( 'show-loader' );

        $successMessage = __( 'CRMProjects.common.project_created_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_create_project' );



         $data = [
            'priority_level' => $this->priority_level,
            'project_type' => $this->project_type,
            'name' => $this->name,
            'budget' =>$this->budget,
            'start_date' => Carbon::parse($this->start_date)->format('Y-m-d'),
            'end_date' => Carbon::parse($this->end_date)->format('Y-m-d'),
            'description' => $this->description,
            'users_list' => $this->UsersforCreateProject,
        ];

         $crmProjectsService = app( ProjectServices::class );
         $response = $crmProjectsService->createProject( $this->workspaceSlug,   $data );
         if (!isset($response['status']) || $response['status'] !== 'success') {
            Log::info('Failed to create project', [
                'workspaceSlug' => $this->workspaceSlug,
                'data' => $data,
                'response' => $response,
            ]);

            $message = $response['message'];
        }else {
             $message = ($response['status'] === 'success') ? $successMessage : $errorMessage;
         }
        $type = ($response['status'] === 'success') ? 'success' : 'error';

        $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => $type,
            'message' => $message
        ] );

        $this->resetCreateForm();
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );


    }

    public function saveAsTemplates()
 {
      $this->validate([
        'ProjectTemplateName' => 'required',
     ]);

       $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'saveProjectAsTemplate' ] );
       $this->dispatchBrowserEvent( 'show-loader' );
        $successMessage = __( 'CRMProjects.common.project_template_has_been_created_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_save_as_template' );

        if ( !$this->projectID ) {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' => $errorMessage
            ] );
        }

        $crmProjectsService = app( ProjectServices::class );
        $data = [
            'name' => $this->ProjectTemplateName,
            'type' => 'template',
        ];

        $response = $crmProjectsService->saveProjectsAsTemplates( $this->workspaceSlug, $this->projectID, $data );
        if($response[ 'status' ] == 'success'){
            $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => 'success',
            'message' => $response[ 'message' ]
            ] );
        }else{
            $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => 'error',
            'message' => $response[ 'message' ]
            ] );
        }
        // $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
        // $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

        // $this->dispatchBrowserEvent( 'show-toastr', [
        //     'type' => $type,
        //     'message' => $message
        // ] );

        $this->ProjectTemplateName='';
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );

    }

    public function resetDateFilter()
    {
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->filter_start_date = '';
        $this->filter_end_date = '';
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );
    }

    public function submitDateFilter()
    {
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );
    }

    public function duplicateProject(){

        $this->validate([
            'name' => 'required',
        ]);


        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'duplcate-project' ] );
        $data = [
            'name' => $this->name,
            'project' => $this->project,
            'task' => $this->task,
            'bug' => $this->bug,
            'activity' => $this->activity,
            'user' => $this->user,
            'client' => $this->client,
            'milestone' => $this->milestone,
            'project_file' => $this->project_file,
            'vendor' => $this->vendor,
            'building_manager' => $this->building_manager,
        ];


        Log::info('Duplicate Project: ' . json_encode($data));



        $this->dispatchBrowserEvent( 'show-loader' );
        $successMessage = __( 'CRMProjects.common.project_has_been_duplicated_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_duplicate_project' );
         if ( !$this->projectID ) {
             $this->dispatchBrowserEvent( 'show-toastr', [
                 'type' => 'error',
                 'message' => $errorMessage
             ] );
         }

         $crmProjectsService = app( ProjectServices::class );


         $response = $crmProjectsService->duplicateProject( $this->workspaceSlug, $this->projectID, $data );


         $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
         $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

         $this->dispatchBrowserEvent( 'show-toastr', [
             'type' => $type,
             'message' => $message
         ] );

         $this->resetDuplicateForm();
        //  $this->fetchData();
         $this->dispatchBrowserEvent( 'hide-loader' );

        $id = $response['data']['id'] ?? null;

        if ($id) {
            return redirect()->to(route('CRMProjects.details', Crypt::encrypt($id)));
        }
    }

    public function render()
 {
        return view('livewire.c-r-m-projects.projects-list');
    }

}
