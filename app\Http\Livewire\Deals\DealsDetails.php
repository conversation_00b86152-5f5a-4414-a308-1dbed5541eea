<?php

namespace App\Http\Livewire\Deals;

use Illuminate\Support\Facades\Log;
use App\Services\CRM\CRMDealService;
use Illuminate\Support\Facades\Session;
use App\Services\CRM\CRMUserService;
use App\Services\CRM\CRMDealStageService;
use Livewire\Component;

class DealsDetails extends Component
{
    public $dealID = [];
    public $workspaceSlug;
    public $user_id;
    public $name;
    public $price;
    public $phone;
    public $order;
    public $selectedStage;
    public $note_content;
    public $isModalOpen = false;
    public $selectedUserId;
    public $precentage;
    public $clients;


    public $deal;
    public $users = [];

    protected $crmDealService;
    protected $listeners = ['openModal', 'closeModal', 'refreshList','editDeal','editStage', 'updateDeadStage'];

    public function mount($id, CRMDealService $crmDealService)
    {
        $this->crmDealService = $crmDealService;
        $this->dealID = $id;
        $this->workspaceSlug = auth()->user()->workspace;

        $this->loadLeads();
    }

    public function loadLeads()
    {
        $crmUserService = app(CRMUserService::class);
        $crmStageervice = app(CRMDealStageService::class);
        $crmDealService = app(CRMDealService::class);
        $this->users = $crmUserService->getUsers($this->workspaceSlug)['data'];
        $response = $crmDealService->getDealDetails(auth()->user()->workspace, $this->dealID);
        $this->deal = $response['data']['deal'] ?? [];
        $this->precentage = $response['data']['precentage'] ?? [];
        $response = $crmStageervice->getStages($this->workspaceSlug);
        $this->stages = $response['data']['items'][$this->deal['pipeline']] ?? [];
    }
    public function editDeal($dealID)
    {
  
            $this->name = $this->deal['name'];
            $this->order = $this->deal['order'];
            $this->phone = $this->deal['phone'];
            $this->price = $this->deal['price'];
            $this->clients = collect($this->deal['clients'])->pluck('id')->toArray();
            $this->dealID = $dealID;
            $this->isModalOpen = true;
            $this->dispatchBrowserEvent('open-modal');

    }
    public function editStage($DeadId)
    {
        $this->selectedStage = $this->deal['stage'];
        $this->dealID = $DeadId;
        $this->isModalOpenStage = true;
        $this->dispatchBrowserEvent('open-modal-stage');
    }

    public function updateDeadStage()
    {
        $selectedStageData = collect($this->stages)->firstWhere('name', $this->selectedStage);

        if ($selectedStageData) {
            $newStageId = $selectedStageData['id'];

            $crmDealsService = app(CRMDealService::class);
            $response = $crmDealsService->changeStage($this->workspaceSlug, $this->dealID, [
                'stage_id' => $newStageId,
            ]);
            if ($response['status'] === 'success') {
                $this->emit('refreshList');
                session()->flash('message', $response['message']);
                $this->dispatchBrowserEvent('close-modal-stage');
            } else {
                $this->addError('form_error', $response['message'] ?? 'Failed to update deal stage.');
            }
        } else {
            session()->flash('error', 'Stage not found.');
        }
    }

    public function updateDeal()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'phone' => 'required|regex:/^\+966\d{9}$/',
        ]);


        $crmDealsService = app(CRMDealService::class);

        $details = $crmDealsService->getDealDetails($this->workspaceSlug, $this->dealID);
        $response = $crmDealsService->updateDeal( $this->workspaceSlug, $this->dealID, [
            'name' => $this->name,
            'clients' =>  $this->clients,
            'phone' => $this->phone,
            'price' => $this->price,
            'pipeline_id'=> $details['data']['deal']['pipeline_id'],
            'stage_id'=>$details['data']['deal']['stage_id'],

        ]);

        if ($response['status'] === 'success') {

            $this->emit('refreshList');
            session()->flash('message', 'Deal created successfully.');

            $this->reset(['name', 'phone', 'price', 'user_id']);
            $this->dispatchBrowserEvent('close-modal');

        } else {
            $this->addError('form_error', $response['message'] ?? 'Failed to create deal.');
        }
    }

    public function createNote()
    {
        // Validate the input
        $this->validate([
            'note_content' => 'required|string|max:1000',
        ]);

        $crmDealsService = app(CRMDealService::class);
        $response = $crmDealsService->createNote($this->workspaceSlug, $this->dealID, [
            'notes' => $this->note_content,
        ]);

        if ($response['status'] === 'success') {
            session()->flash('message', 'Note created successfully.');
            $this->dispatchBrowserEvent('close-modal-note');
            $this->emit('refreshList');
        } else {
            session()->flash('error', 'Failed to create note.');
        }

        $this->reset('note_content');
    }

    public function addUser()
    {
        if (!$this->selectedUserId) {
            session()->flash('error', __('lead.please_select_user'));
            return;
        }
        $existingUsers = collect($this->deal['users'])->pluck('id')->toArray();
        if (in_array($this->selectedUserId, $existingUsers)) {
            session()->flash('error', 'user is already exist');
            return;
        }
        $service = app(CRMDealService::class);
        $response = $service->addUserToDeal($this->workspaceSlug, $this->dealID, [
            'users' => [$this->selectedUserId],
        ]);
        if ($response['status'] === 'success') {
            session()->flash('message', __('lead.user_added_successfully'));
            $this->dispatchBrowserEvent('close-modal-users');
            $this->emit('refreshList');

        } else {
            session()->flash('error', $response['message'] ?? __('lead.failed_to_add_user'));
        }

        $this->reset('selectedUserId');
    }

    public function deleteUser($userId)
    {
        $service = app(CRMDealService::class);
        $response = $service->deleteDealUser($this->workspaceSlug, $this->dealID, $userId);
        if ($response['status'] === 'success') {
            session()->flash('message', __('lead.user_delete_successfully'));
            $this->dispatchBrowserEvent('close-modal');
            $this->emit('refreshList');
        } else {
            session()->flash('error', $response['message'] ?? __('lead.failed_to_delete_user'));
        }

    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->dispatchBrowserEvent('close-modal');
    }
    public function render()
    {
        return view('livewire.deals.view');
    }

    public function refreshList()
    {
        $this->loadleads();
    }
}
