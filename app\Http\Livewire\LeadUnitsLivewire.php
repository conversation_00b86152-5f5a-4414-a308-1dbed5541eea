<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\RoomsTypeFloors;
use App\Models\UnitsByLead;

class LeadUnitsLivewire extends Component
{



    public $isLoading = false;
    public $products = [];
    public $selectedProductId = [];
    public $isModalOpen = false;
    public $leadProducts = [];
    public $leadProductArray;

    protected $listeners = ['openModal', 'closeModal', 'refreshList' => '$refresh'];


    private $crmLeadProductService;
    private $crmLeadService;

    public function mount($leadId, $pageType, $leadProductArray)
    {
        $this->leadId = $leadId;
        $this->pageType = $pageType;
        $this->leadProducts = $leadProductArray;
        $this->loadLeadProducts();
        $this->loadProducts();
    }

    public function loadProducts()
    {
        $products = RoomsTypeFloors::whereIn('unit_usage_type', ['Rentable', 'Rentable-or-Sellable'])->get();

        $this->products = $products->toArray(); 
        $this->productNames = $products->pluck('room', 'id')->toArray(); 
    }


    public function loadLeadProducts()
    { 
        $this->leadProducts =UnitsByLead::where('lead_id','=',$this->leadId)->get()->toArray();
    }

    public function openModal()
    {
        $this->loadProducts();
        $this->isModalOpen = true;
    }


    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->reset(['selectedProductId']);
        $this->dispatchBrowserEvent('close-modal');
    }

    public function save()
    {
           $this->validate([
            'selectedProductId'=> 'required|array|min:1',
           ]);


        $created = false;

        foreach ($this->selectedProductId as $unitId) {
            $exists = UnitsByLead::where('lead_id', $this->leadId)
                                ->where('unit_id', $unitId)
                                ->exists();

            if (!$exists) {
                UnitsByLead::create([
                    'lead_id' => $this->leadId,
                    'unit_id' => $unitId,
                ]);
                $created = true;
            }
        }

        if ($created) {
            session()->flash('message', 'Units added successfully to the ' . $this->pageType);

        } else {
            session()->flash('error', 'No new units were added — they already exist.');
        }

        $this->emit('hideLoader');
        $this->closeModal();
        $this->loadProducts();
        $this->loadLeadProducts();
        $this->emit('refreshList');
    }


    public function deleteProduct($productId)
    {
        $this->emit('showLoader');
        $response = UnitsByLead::where('id','=',$productId)->delete();
        if ($response) {
            session()->flash('message', 'Product deleted successfully.');
        } else {
            session()->flash('error', $response['message'] ?? 'Failed to delete the product.');
        }
        $this->emit('hideLoader');
        $this->loadProducts();
        $this->loadLeadProducts();
        $this->emit('refreshList');
    }


    public function render()
    {
        return view('livewire.lead-units-livewire', [
            'products' => $this->products,
            'leadProducts' => $this->leadProducts,
        ]);
    }
}
