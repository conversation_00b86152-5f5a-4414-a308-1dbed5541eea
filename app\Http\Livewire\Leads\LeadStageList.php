<?php

namespace App\Http\Livewire\Leads;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Session;
use Livewire\Component;
use App\Services\CRM\CRMPipelineService;
use App\Services\CRM\CRMStageLeadService;

class LeadStageList extends Component
{
    public $workspaceSlug = 'test-workspace';
    public $leads = [];
    public $pipelines = [];
    public $name;
    public $pipeline_id;
    public $isEdit = false;
    public $leadStageToEdit = [];
    public $isModalOpen = false;

    protected $crmLeadService;
    protected $listeners = ['openModal', 'closeModal', 'refreshList','editLead', 'deleteLead', 'updateLeadStageOrder' => 'handleOrderUpdate'

    ];

    protected $rules = [
        'name' => 'required|string|max:255',
        'pipeline_id' => 'required'
    ];


    public function mount(){
        $this->workspaceSlug = auth()->user()->workspace;
    }

    public function loadLeads()
    {
        $service = app(CRMStageLeadService::class);
        $pipelineService = app(CRMPipelineService::class);
        $response = $service->getStages($this->workspaceSlug);
        $pipelinesApi = $pipelineService->listPipelines($this->workspaceSlug);

        $this->pipelines = $pipelinesApi['data']['items'] ?? [];
        $leads = $response['data']['items'] ?? [];
        $groupedLeads = [];

        foreach ($leads  as $key => $lead) {
            $groupedLeads[$key] = $lead;
        }

        foreach ($groupedLeads as $pipelineName => &$stages) {
            $allZero = collect($stages)->every(function ($stage) {
                return ($stage['order'] ?? 0) === 0;
            });

            if ($allZero) {
                foreach ($stages as $index => &$stage) {
                    $stage['order'] = $index + 1;
                }
            } else {
                usort($stages, function ($a, $b) {
                    return ($a['order'] ?? 0) <=> ($b['order'] ?? 0);
                });
            }
        }

        $this->leads = $groupedLeads;
    }

    public function createLeadStage()
    {
        $this->validate();
        $service = app(CRMStageLeadService::class);
        $response = $service->createStage($this->workspaceSlug,[
            'name' => $this->name,
            'pipeline_id' => $this->pipeline_id,
            'order' => 0

        ]);
        if ($response['status'] === 'success') {
            $this->closeModal();
            session()->flash('message', 'Lead stage created successfully.');
            $this->loadLeads();
            $this->dispatchBrowserEvent('close-modal');
        } else {
            session()->flash('error', $response['message'] ?? 'Failed to create lead stage.');
        }

        $this->resetInputFields();
        $this->emit('closeModal');
    }


    private function getPipelineName($pipelineId)
    {
        foreach ($this->pipelines as $pipeline) {
            if ($pipeline['id'] == $pipelineId) {
                return $pipeline['name'];
            }
        }
        return 'Unknown Pipeline';
    }

    public function handleOrderUpdate($newOrder)
    {
        $service = app(CRMStageLeadService::class);

        try {
            foreach ($newOrder as $item) {
                $response = $service->updateLead(
                    $this->workspaceSlug,
                    $item['id'],
                    ['order' => $item['order'], 'name' => $item['name'], 'pipeline_id' => $item['pipeline_id']]
                );

                if ($response['status'] !== 'success') {
                    session()->flash('error', 'Failed to update some stage orders.');
                    return;
                }
            }

            session()->flash('message', 'Lead stage order updated successfully.');
            $this->loadLeads();
        } catch (\Exception $e) {
            session()->flash('error', 'Error updating lead stage order: ' . $e->getMessage());
        }
    }

    public function editLead($id)
    {
        // dd($id);
        $allLeads = collect($this->leads)->flatMap(function ($stages) {
            return $stages;
        });
        $this->leadStageToEdit = $allLeads->firstWhere('id', $id);
        if ($this->leadStageToEdit) {
            $this->name = $this->leadStageToEdit['name'];
            $this->pipeline_id = $this->leadStageToEdit['pipeline_id'];
            $this->isEdit = true;
            $this->isModalOpen = true;
            $this->dispatchBrowserEvent('open-modal');
        } else {
            session()->flash('error', 'Lead stage not found.');
        }


    }

    public function deleteLead($LeadStage){
        
        $crmDealsService = app(CRMStageLeadService::class);
        $response = $crmDealsService->deleteLead($this->workspaceSlug, $LeadStage);
        if ($response['status'] === 'success') {
            $this->loadLeads();
            session()->flash('message', $response['message']);
        } else {
            session()->flash('error', $response['message'] ?? 'Failed to delete lead stage.');
        }
        $this->resetInputFields();
    }

    public function updateLeadStage()
    {
        if (!isset($this->leadStageToEdit['id'])) {
            session()->flash('error', 'Lead stage data is missing.');
            return;
        }
        // dd($this->pipeline_id);
        // dd($this->leadStageToEdit['id']);
        $crmLeadService = app(CRMStageLeadService::class);
        $response = $crmLeadService->updateLead($this->workspaceSlug, $this->leadStageToEdit['id'], [
            'name' => $this->name,
            'pipeline_id' => $this->pipeline_id,
            'order' => 0
        ]);

        if ($response['status'] === 'success') {
            $this->dispatchBrowserEvent('close-modal');
            $this->loadLeads();
            session()->flash('message', 'Lead stage updated successfully.');
        } else {
            session()->flash('error', $response['message'] ?? 'Failed to update lead stage.');
        }

        $this->resetInputFields();
        $this->emit('closeModal');
    }


    public function resetInputFields()
    {
        $this->newLeadStage = ['name' => '', 'pipeline' => ''];
        $this->isEdit = false;
        $this->leadStageToEdit = null;
    }

    public function openModal()
    {
        $this->isModalOpen = true;
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->resetInputFields();
    }

    public function render()
    {
        $this->loadLeads();
        return view('livewire.leads.stage', [
            'leads' => $this->leads,
            'pipelines' => $this->pipelines,
        ]);
    }
}
