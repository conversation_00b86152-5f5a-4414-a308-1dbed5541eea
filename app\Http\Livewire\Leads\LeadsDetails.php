<?php

namespace App\Http\Livewire\Leads;

use Carbon\Carbon;
use Livewire\Component;
use App\Services\CRM\CRMLeadService;
use Illuminate\Support\Facades\Log;
use App\Services\CRM\CRMUserService;
use App\Services\CRM\CRMStageLeadService;
use Illuminate\Support\Facades\Session;
use App\Models\UnitsByLead;


class LeadsDetails extends Component
{
    public $workspaceSlug;
    public $leadID;
    public $name;
    public $email;
    public $phone;
    public $selectedStage;
    public $user_id;
    public $subject;
    public $follow_up_date;
    public $users;
    public $pipelines;
    public $sources;
    public $products;
    public $notes;
    public $lead;
    public $stages;
    public $client_name;
    public $client_email;
    public $client_password;
    public $transfer_tasks = false;
    public $transfer_discussions = false;
    public $transfer_files = false;
    public $transfer_calls = false;
    public $transfer_emails = false;
    public $price;
    public $transfer_products;
    public $transfer_sources;
    public $transfer_notes;
    public $note_content;
    public $selectedUserId;
    public $precentage;

    public $follow_up_statuses;

    public $u_follow_up_status;
    public $current_follow_up_reschedule_date;
    public $u_follow_up_reschedule_date;

    protected $crmLeadService;
    protected $listeners = ['openModal', 'closeModal', 'refreshList','editLead','editStage', 'updateLeadStage', 'convertToDeal', 'addUser', 'deleteUser'];

    public function mount($id , CRMLeadService $crmLeadService)
    {
        $this->crmLeadService = $crmLeadService;
        $this->leadID = $id;
        $this->workspaceSlug = auth()->user()->workspace;
        $this->loadLeads();
    }

    public function loadLeads()
    {
        $crmLeadService = app(CRMLeadService::class);
        $response = $crmLeadService->leadDetails( $this->leadID)['data'] ?? [];
        $this->users = $response['all_user'];
        $this->lead = $response['lead'] ?? [];
        $this->stages = $response['pipeline_list'] ?? []; 
        $this->follow_up_statuses = $response['follow_up_status_list'] ?? [];
        $this->u_follow_up_status = $this->lead['followup_status'] ?? 'no_answer';
        $this->current_follow_up_reschedule_date = Carbon::parse($this->lead['follow_up_date'])->format('m/d/Y') ?? null;
    }

    public function editLead($leadId)
    {

        $this->name = $this->lead['name'];
        $this->email = $this->lead['email'];
        $this->phone = $this->lead['phone'];
        $this->subject = $this->lead['subject'];
        $this->follow_up_date = $this->lead['date'];
        $this->user_id = $this->lead['user_id'];
        $this->leadId = $leadId;
        $this->isEdit = true;
        $this->isModalOpen = true;
        $this->dispatchBrowserEvent('open-modal');
    }

    public function convertToDeal($leadId)
    {
        $this->dispatchBrowserEvent('open-modal-convert');
    }

      public function convertToDealSave()
    {
        $this->validate([
            'client_name' => 'required|string|max:255',
            'client_email' => 'required|email|unique:users,email',
            'client_password' => 'required|string|min:6',
            'price' => 'nullable|numeric|min:0',
        ]);

        $crmLeadService = app(CRMLeadService::class);
        $response = $crmLeadService->convertToDeal($this->leadID, [
            'client_check' => 'new_client',
            'client_name' => $this->client_name,
            'client_email' => $this->client_email,
            'client_password' => $this->client_password,
            'name' =>  $this->lead['name'],
            'price' => $this->price,
            'is_transfer' => array_values(array_filter([
                $this->transfer_products ? 'products' : null,
                $this->transfer_tasks ? 'tasks' : null,
                $this->transfer_sources ? 'sources' : null,
                $this->transfer_files ? 'files' : null,
                $this->transfer_discussions ? 'discussion' : null,
                $this->transfer_notes ? 'notes' : null,
                $this->transfer_calls ? 'calls' : null,
                $this->transfer_emails ? 'emails' : null,
            ])),
        ]);

        if ($response['status'] === 'success') {
            $newDealId = $response['data']['id'] ?? null;

            if ($this->transfer_products && $newDealId) {
                UnitsByLead::where('lead_id', $this->leadID)
                    ->update(['deal_id' => $newDealId]);
            }

            session()->flash('message', 'Lead converted to deal successfully.');
            $this->emit('refreshList');
            $this->dispatchBrowserEvent('close-modal-convert');
        } else {
            session()->flash('error', $response['message']);
        }
    }
    public function editStage($leadId)
    {
            $this->selectedStage = $this->lead['stage'];
            $this->leadId = $leadId;
            $this->isModalOpenStage = true;
            $this->dispatchBrowserEvent('open-modal-stage');
    }


    public function updateLead()
    {
        $this->validate([
            'name' => 'required|string',
            'subject' => 'required|string',
            'phone' => 'required|string|regex:/^\+966\d{9}$/',
            'email' => 'required|email',
            'follow_up_date' => 'required|date',
            'user_id' => 'required'
        ]);
        $crmDealsService = app(CRMLeadService::class);
        $response = $crmDealsService->updateLead( $this->workspaceSlug, $this->leadID, [
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'subject' => $this->subject,
            'user_id' => $this->user_id,
            'follow_up_date' => $this->follow_up_date,
            'pipeline_id' => $this->lead['pipeline_id'],
            'stage_id'=>$this->lead['stage_id'],
        ]);

        if ($response['status'] === 'success') {

            $this->emit('refreshList');
            session()->flash('message',  __('lead.lead_update_successfully'));

            $this->reset(['subject', 'name', 'phone', 'follow_up_date', 'email', 'user_id']);
            $this->dispatchBrowserEvent('close-modal');
        } else {
            $this->addError('form_error', $response['message'] ?? 'Failed to create Lead.');
        }
    }

    public function addUser()
    {
        if (!$this->selectedUserId) {
            session()->flash('error', __('lead.please_select_user'));
            return;
        }
        $existingUsers = collect($this->lead['users'])->pluck('id')->toArray();
        if (in_array($this->selectedUserId, $existingUsers)) {
            session()->flash('error', 'user is already exist');
            return;
        }
        $service = app(CRMLeadService::class);
        $response = $service->addUserToLead( $this->leadID, [
            'users' => [$this->selectedUserId],
        ]);
        if ($response['status'] === 'success') {
            session()->flash('message', __('lead.user_added_successfully'));
            $this->dispatchBrowserEvent('close-modal-users');
            $this->emit('refreshList');
        } else {
            session()->flash('error', $response['message'] ?? __('lead.failed_to_add_user'));
        }

        $this->reset('selectedUserId');
    }

    public function deleteUser($userId)
    {
        $service = app(CRMLeadService::class);
        $response = $service->deleteLeadUser($this->leadID, $userId);
        if ($response['status'] === 'success') {
            session()->flash('message', __('lead.user_delete_successfully'));
            $this->dispatchBrowserEvent('close-modal');
            $this->emit('refreshList');
        } else {
            session()->flash('error', $response['message'] ?? __('lead.failed_to_delete_user'));
        }

    }

    public function updateLeadStage()
    {
        $selectedStageData = collect($this->stages)->firstWhere('name', $this->selectedStage);

        if ($selectedStageData) {
            $newStageId = $selectedStageData['id'];

            $crmDealsService = app(CRMLeadService::class);
            $response = $crmDealsService->changeStage($this->workspaceSlug, $this->leadId, [
                'stage_id' => $newStageId,
            ]);
            if ($response['status'] === 'success') {
                $this->emit('refreshList');
                $this->dispatchBrowserEvent('close-modal-stage');
                session()->flash('message', $response['message']);
            } else {
                $this->addError('form_error', $response['message'] ?? 'Failed to update lead stage.');
            }
        } else {
            session()->flash('error', 'Stage not found.');
        }
    }

    public function createNote()
    {
        // Validate the input
        $this->validate([
            'note_content' => 'required|string|max:1000',
        ]);

        $crmDealsService = app(CRMLeadService::class);
        $response = $crmDealsService->createNote( $this->leadID, [
            'notes' => $this->note_content,
        ]);

        if ($response['status'] === 'success') {
            session()->flash('message', 'Note created successfully.');
            $this->dispatchBrowserEvent('close-modal-note');
            $this->emit('refreshList');
        } else {
            session()->flash('error', 'Failed to create note.');
        }

        $this->reset('note_content');
    }
    public function openModal()
    {
        $this->resetValidation();
        $this->reset(['subject', 'name', 'price', 'phone_number', 'follow_up_date', 'email', 'user_id']);
        $this->isModalOpen = true;
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->dispatchBrowserEvent('close-modal');
    }
    public function render()
    {
//        $this->u_follow_up_status= $this->lead['follow_up_status'] ?? 'no_answer';
        return view('livewire.leads.view');
    }

    public function refreshList()
    {
        $this->loadLeads();
    }

    public function resetUpdateFollowUpModal()
    {
        $this->u_follow_up_status = $this->lead['followup_status'];
//        $this->u_follow_up_reschedule_date = $this->lead['follow_up_date'];
    }

    public function updateFollowUpStatusAction()
    {
        try {
            $crmLeadService = app(CRMLeadService::class);

            if ($this->u_follow_up_status === 'rescheduled') {
                if (empty($this->u_follow_up_reschedule_date)) {
                    session()->flash('error', __('lead.required_rescheduled_date'));
                    return false;
                }
                $response2 = $crmLeadService->rescheduledLeadFollowUp($this->leadID, [
                    'follow_up_date' => Carbon::parse($this->u_follow_up_reschedule_date)->format('Y-m-d'),
                    'followup_status' => $this->u_follow_up_status,
                ]);
                $this->current_follow_up_reschedule_date=Carbon::parse($this->u_follow_up_reschedule_date)->format('Y-m-d');
            }
            $response = $crmLeadService->updateLeadFollowUpStatus($this->leadID, [
                'followup_status' => $this->u_follow_up_status,
            ]);

            if ($response['status'] === 'success') {
                $this->emit('refreshList');
                session()->flash('message', __('lead.lead_update_successfully'));
            } else {
                $this->addError('error', $response['message'] ?? 'Failed to create Lead.');
            }

        } catch (\Exception $exception) {
            $this->addError('error', $exception->getMessage());
        }
    }

}
