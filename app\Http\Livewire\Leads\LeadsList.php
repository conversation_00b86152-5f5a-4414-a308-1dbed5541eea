<?php

namespace App\Http\Livewire\Leads;

use App\Services\CRM\CRMPipelineService;
use App\Services\CRM\CRMPipelineTagsService;
use App\Services\CRM\CRMUserService;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use App\Services\CRM\CRMLeadService;
use App\Services\CRM\CRMWorkspace;
use Illuminate\Support\Facades\Session;

class LeadsList extends Component
{
    public $leads = [];
    public $leadsStages = [];
    public $originalLeadsStages = [];
    public $isModalOpen = false;
    public $name;
    public $email;
    public $phone;
    public $user_id;
    public $subject;
    public $follow_up_date;
    public $pipelines = [];
    public $pipeline_id;
    public $labels;
    public $sources;
    public $products;
    public $stage_id;
    public $search;
    public $notes;
    public $isEdit = false;
    public $leadId;
    public $selected_pipeline;
    public $page = 1;
    public $last_page = 1;
    public $original_last_page = 1;
    public $selectedLeadId ;
    public  $client_email , $client_password;

    protected $listeners = ['openModal', 'closeModal', 'editLead', 'deleteLead', 'updateLeadStage', 'showLoader', 'hideLoader', 'save', 'loadMore', 'refetch', 'refetchUpdated', 'search',
                            'showLeadDetails', 'backToList'];

    protected $crmLeadService;

    public function mount()
    {
        $this->loadLeads();
        $this->originalLeadsStages = $this->leadsStages;
        $this->original_last_page = $this->last_page;
        $this->loadPipelines();
    }

    public function showLeadDetails($leadId)
    {  
        $this->selectedLeadId = $leadId;
        // $this->emitSelf('$refresh'); 
    }

    public function backToList()
    {
        $this->selectedLeadId = null;
    }

    public function updatedSearch($value)
    {
        // If search is cleared, restore the original leadsStages
        if (trim($value) === '') {
            $this->leadsStages = $this->originalLeadsStages;
            return;
        }

        $anyMatchFound = false;

        $filteredStages = [];

        foreach ($this->originalLeadsStages as $stage) {
            $leads = $stage['paginatedLeads']['data'];

            $filteredLeads = array_filter($leads, function ($lead) use ($value) {
                return stripos($lead['name'], $value) !== false;
            });

            if (count($filteredLeads) > 0) {
                $anyMatchFound = true;
            }

            $stage['paginatedLeads']['data'] = array_values($filteredLeads);
            $stage['paginatedLeads']['pagination']['total'] = count($filteredLeads);
            $stage['paginatedLeads']['pagination']['current_page'] = 1;
            $stage['paginatedLeads']['pagination']['last_page'] = 1;

            $filteredStages[] = $stage;
        }

        $this->leadsStages = $filteredStages;

        if (!$anyMatchFound && $this->page < $this->original_last_page) {
            $this->loadLeads(false, $value);
        }
    }

    public function loadMore()
    {
        if ($this->page == $this->original_last_page) {
            return false;
        }
        $this->page += 1;
        $this->loadLeads(true);
        $this->originalLeadsStages = $this->leadsStages;
    }

    public function refetch($data, $stage_id)
    {
        foreach ($this->leadsStages as &$stage) {
            if ($stage['id'] == $stage_id) {
                if (!isset($stage['paginatedLeads']['data'])) {
                    $stage['paginatedLeads']['data'] = [];
                }
                array_unshift($stage['paginatedLeads']['data'], $data);
                $stage['paginatedLeads']['pagination']['total'] = $stage['paginatedLeads']['pagination']['total'] + 1;
                break;
            }
        }
    }

    public function refetchUpdated($data, $stage_id, $lead_id)
    {
        foreach ($this->leadsStages as &$stage) {
            if ($stage['id'] == $stage_id) {
                foreach ($stage['paginatedLeads']['data'] as &$datum) {
                    if ($datum['id'] == $lead_id) {
                        $datum = $data;
                        break;
                    }
                }
            }
        }
    }

    public function deleteUpdated($stage_id, $lead_id)
    {
        foreach ($this->leadsStages as &$stage) {
            if ($stage['id'] == $stage_id) {
                foreach ($stage['paginatedLeads']['data'] as $key =>  &$datum) {
                    if ($datum['id'] == $lead_id) {
                        unset($stage['paginatedLeads']['data'][$key]);
                        $stage['paginatedLeads']['pagination']['total'] = $stage['paginatedLeads']['pagination']['total'] - 1;
                        break;
                    }
                }
            }
        }
    }

    public function changeUpdatedStage($old_stage, $new_stage, $lead_id)
    {
        $changedItem = [];
        foreach ($this->leadsStages as &$stage) {
            if ($stage['id'] == $old_stage) {
                foreach ($stage['paginatedLeads']['data'] as $key =>  &$datum) {
                    if ($datum['id'] == $lead_id) {
                        unset($stage['paginatedLeads']['data'][$key]);
                        $changedItem = $datum;
                        $stage['paginatedLeads']['pagination']['total'] = $stage['paginatedLeads']['pagination']['total'] - 1;
                        break;
                    }
                }
            }
        }
        foreach ($this->leadsStages as &$stage) {
            if ($stage['id'] == $new_stage) {
                if (!isset($stage['paginatedLeads']['data'])) {
                    $stage['paginatedLeads']['data'] = [];
                }
                array_unshift($stage['paginatedLeads']['data'], $changedItem);
                $stage['paginatedLeads']['pagination']['total'] = $stage['paginatedLeads']['pagination']['total'] + 1;
                break;
            }
        }
    }

    public function updatedSelectedPipeline()
    {
        $this->loadLeads();
    }

    public function search($name)
    {
        $this->loadLeads(false, $name);
    }

    public function loadLeads($append = false, $searchedName = null)
    {
        $crmLeadsService = app(CRMLeadService::class);
        $response = $crmLeadsService->getLeads(['pipeline_id' => $this->selected_pipeline, 'page' => $this->page, 'name' => $searchedName]);

        $newStages = $response['data']['lead_stages'] ?? [];

        if ($append) {
            foreach ($newStages as $newStage) {
                $stageId = $newStage['id'];

                // Find if this stage already exists in $this->leadsStages
                $existingStageKey = collect($this->leadsStages)->search(fn($stage) => $stage['id'] === $stageId);

                if ($existingStageKey !== false) {
                    // Merge the new leads into existing stage
                    $this->leadsStages[$existingStageKey]['paginatedLeads']['data'] = array_merge(
                        $this->leadsStages[$existingStageKey]['paginatedLeads']['data'],
                        $newStage['paginatedLeads']['data']
                    );

                    // Optionally update pagination info
                    $this->leadsStages[$existingStageKey]['paginatedLeads']['pagination'] = $newStage['paginatedLeads']['pagination'];
                } else {
                    // If stage not found, just add it
                    $this->leadsStages[] = $newStage;
                }
            }
        } else {
            // First load or pipeline change – replace completely
            $this->leadsStages = $newStages;
            $this->getMaxLastPage($this->leadsStages);
        }

        $this->selected_pipeline = $response['data']['pipeline']['id'] ?? '';

        $this->dispatchBrowserEvent('allowLoadMore', ['allow' => true]);
    }

    public function getMaxLastPage(array $leadStages)
    {
        $this->last_page = collect($leadStages)
            ->pluck('paginatedLeads.pagination.last_page')
            ->max() ?? 1;
    }

    public function loadPipelines()
    {
        $crmPipeLineService = app(CRMPipelineService::class);
        $response = $crmPipeLineService->listPipelines();
        if ($response['status'] === 'success') {
            $this->pipelines = $response['data']['items'];
            if (!$this->selected_pipeline) {
                $this->selected_pipeline = $this->pipelines[0]['id'];
            }
        }
    }

    public function changePipeline($pipeline)
    {
        if ($pipeline != null) {
            Session::put('lead_pipeline', $pipeline);
        }
        $this->dispatchBrowserEvent('reload-page');
    }

    public function deleteLead($leadId, $data)
    {
        $crmDealsService = app(CRMLeadService::class);
        $response = $crmDealsService->deleteLead($leadId);
        if ($response['status'] === 'success') {
            $this->deleteUpdated($data['stage_id'], $leadId);
            $this->dispatchBrowserEvent('close-confirm-modal');

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => $response['message']
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $response['message']
            ]);
        }
    }

    public function updateLeadStage($leadId, $newStageId, $oldStageId)
    {
        $crmDealsService = app(CRMLeadService::class);
        $response = $crmDealsService->changeStage($leadId, [
            'stage_id' => $newStageId,
        ]);

        if ($response['status'] === 'success') {
            $this->changeUpdatedStage($oldStageId, $newStageId, $leadId);
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => $response['message']
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $response['message']
            ]);
        }
    }

    public function render()
    {
         \Log::info('Rendering LeadsList - selectedLeadId = ' . $this->selectedLeadId);
        return view('livewire.leads.list');
    }
}
