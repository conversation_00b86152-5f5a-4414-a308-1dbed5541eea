<?php

namespace App\Http\Livewire\Project\TaskBoard;

use App\Http\Helpers\Helper;
use App\Services\CRM\Sales\ProjectService;
use Livewire\Component;
use Illuminate\Support\Facades\Session;
use App\Models\WorkOrders;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;
use App\Http\Traits\MilestoneNotificationTrait;

class ListView extends Component
 {
    use MilestoneNotificationTrait;

    public $records;
    public $itemId;
    public $priorities = [];
    public $usersForAssign = [];
    public $userAssigned = '';
    public $milestonesList = [];
    public $selectedmilestone = '';
    public $prioritiesList = [];
    public $selectedprioritie = '';
    public $work_order_type ;
    public $viewType ;
 public $Bulk_assign_to ;
    public $selected_WorkOrder = '';
    public $work_orders_list = [];
    public $selected_work_order_data = null;

    public $sortField = '';
    public $sortByPriority = '';
    public $sortDirection = 'asc';
    public $start_date = '';
    public $assign_to = '';
    public $end_date = '';
    public $change_status = '';
    public $users = [];
    public $selectedTasks = [];
    public $taskStages = [];
    public $milestones = [];
    public $id_task;
    public $activeTab = 'comments';
    protected $listeners = [ 'fetchData', 'deleteTask', 'currentPage' ,'resetFromChild'];
    public $taskDetails = [
        'id_task' => 0,
        'title' => '',
        'priority' => '',
        'assign_to' => [],
        'milestone' => '',
        'description' => '',
        'start_date' => '',
        'due_date' => '',
        'comments' => [],
        'users' => [],
    ];
    public $taskEdit = [
        'title' => '',
        'priority' => '',
        'selected_priority' => '',
        'assign_to' => [],
        'selected_assign_to' =>[],
        'milestone' =>'',
        'selected_milestone' =>'',
        'description' => '',
        'start_date' => '',
        'due_date' => '',
         'workorder_id' => '',
        'workorder_type' => '',
        'property_name' => '',
    ];


    public $currentPage = [];

    public function currentPage( $data )
 {
        $this->currentPage[ $data[ 'functionName' ] ] = $data[ 'page' ];
    }

    public function applyDateFilters()
 {
        $this->fetchData();
    }

    public function sortBy( $field )
 {
        if ( $this->sortField === $field ) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->fetchData();
    }

    public function sortPriority( $field )
 {
        $this->sortByPriority = $field;
        $this->fetchData();
    }

    public function mount( $id )
 {
        $this->itemId = decrypt( $id );
        $this->fetchData();
        $this->getUsersListForTangibleTask();
        $this->getMilestoneListForTangibleTask();
        $this->getPriorityListForTangibleTask();
        $this->getTaskStages();

    }

    public function updateSelectedStatus()
 {
      $this->validate([
            'change_status' => 'required',
        ]);

        $data_array[ 'task_ids' ] = $this->selectedTasks;
        $data_array[ 'actions' ] = [];

        if ( !empty( $this->change_status ) ) {
            $data_array[ 'actions' ][ 'change_status' ] = $this->change_status;
        }
        $service =  app( ProjectService::class );
        $data = $service->taskBulkAction( $this->itemId, $data_array );

        if ( $data[ 'status' ] === 'success' ) {
            $this->dispatchBrowserEvent( 'close-confirm-modal' );
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'success',
                'message' => $data[ 'message' ]
            ] );

            // Check for milestone completion notifications
            $this->checkAndSendMilestoneCompletionNotifications($data['data']);

        } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' =>  $data[ 'message' ]
            ] );
        }
      $this->dispatchBrowserEvent( 'close-modal-ById', [ 'modalId' => 'updateTaskBulk' ] );
        $this->dispatchBrowserEvent( 'hideLoader' );
       $data_array[ 'actions' ][ 'assign_user' ][] = '';
       $this->selectedTasks = [];
           $this->fetchData();
    }


    public function openModalbulkAssigUserToTask(){
        $this->dispatchBrowserEvent( 'open-modal-ById', [ 'modalId' => 'assignUserToTaskBulk' ] );

    }
    public function openModalBulksUpdateStatus(){
$this->dispatchBrowserEvent( 'open-modal-ById', [ 'modalId' => 'updateTaskBulk' ] );
    }
    public function updateSelectedAssign()
 {
     $this->validate([
            'Bulk_assign_to' => 'required',
        ]);


        $data_array[ 'task_ids' ] = $this->selectedTasks;
        $data_array[ 'actions' ] = [];
        if ( !empty( $this->Bulk_assign_to ) ) {
            $data_array[ 'actions' ][ 'assign_user' ][] = $this->Bulk_assign_to;
        }
        $service =  app( ProjectService::class );
        $data = $service->taskBulkAction( $this->itemId, $data_array );
        if ( $data[ 'status' ] === 'success' ) {

            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'success',
                'message' => $data[ 'message' ]
            ] );
        } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' =>  $data[ 'message' ]
            ] );
        }
          $this->dispatchBrowserEvent( 'close-modal-ById', [ 'modalId' => 'assignUserToTaskBulk' ] );
        $this->dispatchBrowserEvent( 'hideLoader' );
       $data_array[ 'actions' ][ 'assign_user' ][] = '';
       $this->selectedTasks = [];
           $this->fetchData();
    }

    public function fetchData( $page = 1 )
 {
        $page = $this->currentPage[ 'fetchData' ] ??  request()->query( 'page', $page );
        $this->dispatchBrowserEvent( 'showLoader' );
        $service =  app( ProjectService::class );
        $data_array = [ 'page' => $page ];
        if ( !empty( $this->sortByPriority ) ) {
            $data_array[ 'sort_by' ] = $this->sortByPriority;
            $data_array[ 'sort_order' ] = 'desc';
        } elseif ( !empty( $this->sortField ) ) {
            $data_array[ 'sort_by' ] = $this->sortField;
            $data_array[ 'sort_order' ] = $this->sortDirection;
        } elseif ( !empty( $this->sortField ) ) {
            $data_array[ 'sort_by' ] = $this->sortField;
            $data_array[ 'sort_order' ] = $this->sortDirection;
        } elseif ( !empty( $this->start_date ) || !empty( $this->end_date ) || !empty( $this->assign_to ) ) {
            if ( !empty( $this->start_date ) ) {
                $data_array[ 'start_date' ] = $this->start_date .' 00:00:00';
            }
            if ( !empty( $this->end_date ) ) {
                $data_array[ 'due_date' ] = $this->end_date .' 00:00:00';
            }
            if ( !empty( $this->assign_to ) ) {
                $data_array[ 'assign_user' ] = $this->assign_to;
            }
        }
        $data = $service->taskBoardListView( $this->itemId, $data_array );

        // $data = $service->taskBoardListView( $this->itemId, $page );
        if ( @$data[ 'status' ] == 'success' ) {
            $this->records = $data[ 'data' ] ?? [];
            $this->emit( 'refreshPagination', ceil( @$this->records[ 'total' ] / @$this->records[ 'per_page' ] ), $page, 'fetchData', @$this->records[ 'total' ] );
            $this->emit( 'updateUrl', [ 'page' => $page, 'functionName' => 'fetchData' ] );
        }
        if ( !$this->users ) {
            $service =  app( ProjectService::class );
            $response = $service->usersDropdown( $this->itemId );
            if ( $response[ 'status' ] === 'success' ) {
                $this->users = $response[ 'data' ] ?? [];
            }
        }
        if ( !$this->milestones ) {
            $service =  app( ProjectService::class );
            $response = $service->milestonesDropdown( $this->itemId );
            if ( $response[ 'status' ] === 'success' ) {
                $this->milestones = $response[ 'data' ] ?? [];
            }
        }
        if ( !$this->priorities ) {
            $service =  app( ProjectService::class );
            $response = $service->priorityDropdown();
            if ( $response[ 'status' ] === 'success' ) {
                $this->priorities = $response[ 'data' ] ?? [];
            }
        }
        $this->dispatchBrowserEvent( 'hideLoader' );
    }

    public function deleteTask( $id )
 {
        if ( $id ) {
            $service =  app( ProjectService::class );
            $response = $service->deleteTask( $this->itemId, $id );

            if ( $response[ 'status' ] === 'success' ) {
                $this->dispatchBrowserEvent( 'close-confirm-modal' );
                $this->dispatchBrowserEvent( 'show-toastr', [
                    'type' => 'success',
                    'message' => $response[ 'message' ]
                ] );
                $this->fetchData();
            } else {
                $this->dispatchBrowserEvent( 'show-toastr', [
                    'type' => 'error',
                    'message' =>  $response[ 'message' ]
                ] );
            }
        }
    }

    public function getUsersListForTangibleTask() {
        $service =  app( ProjectService::class );
        $response = $service->getAllUsers( $this->itemId );

        if ( $response[ 'status' ] == 'success' ) {
            $this->usersForAssign = $response[ 'data' ] ?? [];
        }
    }

    public function getTaskStages() {
        $service =  app( ProjectService::class );
        $response = $service->getTaskStages( $this->itemId );

        if ( $response[ 'status' ] == 'success' ) {
            $this->taskStages = $response[ 'data' ][ 'items' ] ?? [];
        }
    }

    public function getMilestoneListForTangibleTask() {
        $service =  app( ProjectService::class );
        $response = $service->getAlLMilestones( $this->itemId );

        if ( $response[ 'status' ] == 'success' ) {
            $this->milestonesList = $response[ 'data' ] ?? [];
        }
    }

    public function getPriorityListForTangibleTask() {

        $service =  app( ProjectService::class );
        $response = $service->priorityDropdown();
        /* priorities */
        if ( $response[ 'status' ] == 'success' ) {
            $this->prioritiesList = $response[ 'data' ] ?? [];
        }
    }

    public function initialiseWO()
 {
        $successMessage = __( 'CRMProjects.common.tangible_task_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_create_tangible_task' );

        $this->validate( [
            'userAssigned' => 'required',
            'selectedmilestone' => 'required',
            'selectedprioritie' => 'required',
            'work_order_type' => 'required|in:reactive,preventive',
            'selected_WorkOrder' => 'required',
        ] );

        $service = app( ProjectService::class );
        $workOrder_Data = collect( $this->work_orders_list )
        ->firstWhere( 'id', $this->selected_WorkOrder );

        $data = $workOrder_Data ?? [] ;

        $response = $service->createTask( $this->itemId, [
            'task_type'      =>'tangible',
            'title' => data_get( $data, 'work_order_id' ) ?: data_get( $data, 'pm_title' ),
            'priority'       => $this->selectedprioritie,
            'workorder_id'   => data_get( $data, 'work_order_id' ),
            'workorder_type' => data_get( $data, 'work_order_type' ),
            'milestone_id'   => $this->selectedmilestone,
            'start_date' => data_get( $data, 'start_date' )
            ? date( 'Y-m-d', strtotime( data_get( $data, 'start_date' ) ) )
            : null,

            'due_date' => data_get( $data, 'end_date' )
            ? date( 'Y-m-d', strtotime( data_get( $data, 'end_date' ) ) )
            : null,

            'assign_to'      => [ $this->userAssigned ],
            'description'    => data_get( $data, 'description' ),
        ] );

        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            $task_id = data_get( $response, 'data.id' );
            $workOrderId = data_get( $data, 'id' );
            if ( $task_id !== null && isset( $workOrderId ) ) {
                DB::table( 'work_orders' )
                ->where( 'id', $workOrderId )
                ->update( [ 'workdo_task_id' => $task_id ] );
            }

            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'success',
                'message' => $successMessage,
            ] );


        } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' =>  $response[ 'message' ] ??  $errorMessage
            ] );

        }
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'Task-Initialise-WorkOrder' ] );
        $this->fetchData();
        $this->dispatchBrowserEvent( 'hideLoader' );
        $this->resetTangibleTaskForm();

    }

    public function resetTangibleTaskForm() {
        $this->userAssigned = '';
        $this->selectedmilestone = '';
        $this->selectedprioritie = '';
        $this->selected_WorkOrder = '';

    }

    public function updatedWorkOrderType( $value )
 {

        $this->work_orders_list = WorkOrders::where( 'work_order_type', $value )
        ->select( 'id', 'created_by', 'pm_title', 'work_order_id', 'work_order_type', 'start_date', 'end_date', 'description' )
        ->where( 'created_by', auth()->user()->id )
        ->whereNull( 'workdo_task_id' )
        ->get()->toArray();

    }




    public function showUntangibleTask( $data ) {

        if (is_array($data) && !empty($data)) {
            $this->activeTab='comments';
        $this->id_task = data_get($data,'id_task','---');
                   $start_date_value = data_get($data, 'start_date');
         $start_date =  !empty($start_date_value)
             ? Carbon::parse($start_date_value)->format('d-m-Y')
             : '---';

         $due_date_value = data_get($data, 'due_date');
         $due_date =  !empty($due_date_value)
             ? Carbon::parse($due_date_value)->format('d-m-Y')
             : '---';
         $this->taskDetails = [
                'id_task'=> data_get($data,'id_task','---'),
                'title' => data_get( $data, 'title', '---' ),
                'priority' => data_get( $data, 'priority', '---' ),
                'assign_to' => data_get( $data, 'users', [] ),
                'milestone' =>  data_get( $data, 'milestone', '---' ),
                'description' => data_get( $data, 'description', '---' ),
                'start_date' =>  $start_date,
                'due_date' => $due_date,
                'comments' => data_get( $data, 'comments', '---' ),
                'users' =>  data_get( $data, 'users',  []),

            ];

        $this->dispatchBrowserEvent('openUntangibleTaskModal');

          } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' =>   'Server Eroor '
            ] );

        }


    }


  public function editUntangibleTask($data ){

   if (is_array($data) && !empty($data)) {
    $this->resetTaskEditModal();

        $this->id_task = data_get($data,'id_task','---');
                   $start_date_value = data_get($data, 'start_date');
         $start_date =  !empty($start_date_value)
             ? Carbon::parse($start_date_value)->format('d-m-Y')
             : '---';
         $due_date_value = data_get($data, 'due_date');
         $due_date =  !empty($due_date_value)
             ? Carbon::parse($due_date_value)->format('d-m-Y')
             : '---';

         $this->taskEdit = [
                'id_task'=> data_get($data,'id_task','---'),
                'title' => data_get( $data, 'title', '---' ),
                'selected_priority' => data_get( $data, 'priority', '---' ),
               'selected_assign_to' => collect(data_get($data, 'users', []))->pluck('id')->toArray(),
                'selected_milestone' =>  data_get( $data, 'milestoneID', '' ),
                'description' => data_get( $data, 'description', '---' ),
                'start_date' =>  $start_date,
                'due_date' => $due_date,
                'comments' => data_get( $data, 'comments', '---' ),
                'users' =>  data_get( $data, 'users',  []),

            ];

          $this->dispatchBrowserEvent( 'open-modal-ById', [ 'modalId' => 'edit-untangible-task-modal' ] );

          } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' =>   'Server Eroor '
            ] );

        }
    }
    public function update()
 {
         $this->validate([
        'taskEdit.title' => 'required',
        'taskEdit.start_date' => 'required',
        'taskEdit.due_date' => 'required',
        'taskEdit.description' => 'required',
        'taskEdit.selected_milestone' => 'required',
        'taskEdit.selected_priority' => 'required',
        'taskEdit.selected_assign_to' => 'required',
        ]);

        $service =  app( ProjectService::class );

        $response = $service->updateTaskData( $this->itemId, $this->id_task, [
            'task_type'=> 'untangible',
            'title' => $this->taskEdit[ 'title' ],
            'priority' => $this->taskEdit[ 'selected_priority' ],
            'milestone_id' => $this->taskEdit[ 'selected_milestone' ],
            'start_date' => date( 'Y-m-d', strtotime( $this->taskEdit[ 'start_date' ] ) ),
            'due_date' => date( 'Y-m-d', strtotime( $this->taskEdit[ 'due_date' ] ) ),
            'assign_to' => $this->taskEdit[ 'selected_assign_to' ],
            'description' => $this->taskEdit[ 'description' ],
            'project_id' => $this->itemId
        ] );

        if ( $response[ 'status' ] === 'success' ) {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'success',
                'message' => $response[ 'message' ]
            ] );
            $this->emit( 'fetchData' );
            $this->dispatchBrowserEvent( 'close-modal-ById', [ 'modalId' => 'edit-untangible-task-modal' ] );
        } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' =>  $response[ 'message' ]
            ] );
        }
    }
      public function editTangibleTask( $data ) {

        if ( is_array( $data ) && !empty( $data ) ) {
            $this->resetTaskEditModal();
            $this->activeTab = 'comments';
            $this->id_task = data_get( $data, 'id_task', '---' );
            $start_date_value = data_get( $data, 'start_date' );
            $start_date =  !empty( $start_date_value )
            ? Carbon::parse( $start_date_value )->format( 'd-m-Y' )
            : '---';
            $due_date_value = data_get( $data, 'due_date' );
            $due_date =  !empty( $due_date_value )
            ? Carbon::parse( $due_date_value )->format( 'd-m-Y' )
            : '---';

            $this->taskEdit = [
                'id_task'=> data_get( $data, 'id_task', '---' ),
                'title' => data_get( $data, 'title', '---' ),
                'selected_priority' => data_get( $data, 'priority', '---' ),
                'selected_assign_to' => collect( data_get( $data, 'users', [] ) )->pluck( 'id' )->toArray(),
                'selected_milestone' =>  data_get( $data, 'milestoneID', '---' ),
                'description' => data_get( $data, 'description', '---' ),
                'start_date' =>  $start_date,
                'due_date' => $due_date,
                'comments' => data_get( $data, 'comments', '---' ),
                'users' =>  data_get( $data, 'users',  [] ),
                  'workorder_id' =>  data_get( $data, 'workorder_id',  null ),
                'workorder_type' =>  data_get( $data, 'workorder_type',  null ),
                'property_name' =>  data_get( $data, 'property_name',  null ),

            ];


            $this->dispatchBrowserEvent( 'open-modal-ById', [ 'modalId' => 'edit-tangible-task-modal' ] );

        } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' =>   'Server Eroor '
            ] );

        }
    }
    public function updateTangible()
 {
        $this->validate( [
            'taskEdit.title' => 'required',
            'taskEdit.start_date' => 'required',
            'taskEdit.due_date' => 'required',
            'taskEdit.description' => 'required',
            'taskEdit.selected_milestone' => 'required',
            'taskEdit.selected_priority' => 'required',
            'taskEdit.selected_assign_to' => 'required',
        ] );

        $service =  app( ProjectService::class );
       $tasktype = !empty($this->taskEdit['workorder_id']) ? 'tangible' : 'untangible';
      /*   $tasktype=
        workorder_id workorder_type property_name  */

        $response = $service->updateTaskData( $this->itemId, $this->id_task, [
            'task_type'=>   $tasktype,
            'title' => $this->taskEdit[ 'workorder_id' ] ?? $this->taskEdit[ 'title' ],
            'priority' => $this->taskEdit[ 'selected_priority' ],
            'milestone_id' => $this->taskEdit[ 'selected_milestone' ],
            'start_date' => date( 'Y-m-d', strtotime( $this->taskEdit[ 'start_date' ] ) ),
            'due_date' => date( 'Y-m-d', strtotime( $this->taskEdit[ 'due_date' ] ) ),
            'assign_to' => $this->taskEdit[ 'selected_assign_to' ],
            'description' => $this->taskEdit[ 'description' ],
            'project_id' => $this->itemId,
            'workorder_id' => $this->taskEdit[ 'workorder_id' ],
            'workorder_type' => $this->taskEdit[ 'workorder_type' ],
            'property_name' => $this->taskEdit[ 'property_name' ],
        ] );

        if ( $response[ 'status' ] === 'success' ) {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'success',
                'message' => $response[ 'message' ]
            ] );
            $this->emit( 'fetchData' );
            $this->dispatchBrowserEvent( 'close-modal-ById', [ 'modalId' => 'edit-tangible-task-modal' ] );
        } else {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' =>  $response[ 'message' ]
            ] );
        }
    }
    public function resetFilters()
    {

       $this->assign_to = '';
       $this->start_date = '';
       $this->end_date = '';
        $this->fetchData();
    }

    public function render()
 {
        return view( 'livewire.project.task-board.list-view' );
    }
    public function resetFromChild()
    {

        $this->fetchData();
    }
 public function resetTaskEditModal()
{
    $this->taskEdit = [
        'title' => '',
        'priority' => '',
        'selected_priority' => '',
        'assign_to' => [],
        'selected_assign_to' => [],
        'milestone' => '',
        'selected_milestone' => '',
        'description' => '',
        'start_date' => '',
        'due_date' => '',
        'workorder_id' => '',
        'workorder_type' => '',
        'property_name' => '',
    ];

    $this->resetErrorBag();
    $this->resetValidation();

}
}
