<?php

namespace App\Http\Livewire\Project\TaskBoard\Modals;

use Livewire\Component;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestImage;
use App\Http\Helpers\ImagesUploadHelper;
use App\Models\VendorServiceCategory;
use App\Services\CRM\Sales\ProjectService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Contracts\Encryption\DecryptException;

class CreateTaskForm extends Component
{
    public $milestone, $title, $priority, $users = [], $priorities = [], $milestones = [], $assign_to = [], $start_date, $due_date, $description;
    public $itemId;

    protected $listeners = ['resetForm', 'openCreateTask','openChooseWOTypeMoal'];

    protected $rules = [
        'milestone' => 'required',
        'title' => 'required',
        'priority' => 'required',
        'assign_to' => 'required',
        'start_date' => 'required',
        'due_date' => 'required',
        'description' => 'required',
    ];

    public function openCreateTask($id)
    {
        $this->itemId = decrypt($id);
        if (!$this->milestones) {
            $service =  app(ProjectService::class);
            $response = $service->milestonesDropdown($this->itemId);
            if ($response['status'] === 'success') {
                $this->milestones = $response['data'] ?? [];
            }
        }
        if (!$this->users) {
            $service =  app(ProjectService::class);
            $response = $service->usersDropdown($this->itemId);
            if ($response['status'] === 'success') {
                $this->users = $response['data'] ?? [];
            }
        }
        if (!$this->priorities) {
            $service =  app(ProjectService::class);
            $response = $service->priorityDropdown();
            if ($response['status'] === 'success') {
                $this->priorities = $response['data'] ?? [];
            }
        }
        $this->dispatchBrowserEvent('hideLoader');
        $this->dispatchBrowserEvent('open-modal', ['modalId' => 'superModal']);
    }

    public function resetForm()
    {
        $this->reset(['milestone', 'title', 'priority', 'assign_to', 'start_date', 'due_date', 'description']);
        $this->resetErrorBag();
    }

    public function submit()
    {
        $this->validate();

        $service =  app(ProjectService::class);
        $response = $service->createTask($this->itemId, [
            'task_type'=> 'untangible',
            'title' => $this->title,
            'priority' => $this->priority,
            'milestone_id' => $this->milestone,
            'start_date' => date('Y-m-d', strtotime($this->start_date)),
            'due_date' => date('Y-m-d', strtotime($this->due_date)),
            'assign_to' => $this->assign_to,
            'description' => $this->description,
        ]);

        if ($response['status'] === 'success') {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => $response['message']
            ]);
            $this->emit('fetchData');
            $this->dispatchBrowserEvent('close-modal', ['modalId' => 'superModal']);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $response['message']
            ]);
        }
    }
    public function openChooseWOTypeMoal($id)
    {

        $this->dispatchBrowserEvent('hideLoader');
        $this->dispatchBrowserEvent('open-modal', ['modalId' => 'Task-Initialise-WorkOrder']);
    }

    public function render()
    {
        return view('livewire.project.task-board.modals.create-task-form');
    }
}
