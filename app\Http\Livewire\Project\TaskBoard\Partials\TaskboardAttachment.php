<?php

namespace App\Http\Livewire\Project\TaskBoard\Partials;

use App\Http\Helpers\Helper;
use Livewire\WithFileUploads;
use Illuminate\Http\UploadedFile;
use App\Services\CRM\Sales\ProjectService;
use Livewire\Component;

class TaskboardAttachment extends Component
{
    use WithFileUploads;

    public $projectId;
    public $taskboardId ;
    public $attachment = '';
    public $attachments = [];
    protected $listeners = ['deleteAttachment' => 'deleteAttachment'];


    public function mount($taskboardId,$projectId)
    {
        $this->taskboardId = $taskboardId;
        $this->projectId = $projectId;
        $this->loadAttachments();
    }

    private function loadAttachments()
    {
        $projectService = app(ProjectService::class);
        $response = $projectService->fileDetail($this->taskboardId);

        if ($response['status'] === 'success') {
            $this->attachments = $response['data'] ?? [];
        }
    }

    public function deleteAttachment($id)
        {
            $projectService = app(ProjectService::class);
            $response = $projectService->deleteAttachment($this->taskboardId, $id);

            if ($response['status'] === 'success') {
        $this->loadAttachments();
             
            }
        }

        public function saveAttachment()
        {
       $this->validate([
            'attachment' => 'required|file|mimes:pdf,docx,jpg,jpeg|max:2048',
        ]);
                    $filename = $this->attachment->getClientOriginalName();


            $filePath = 'uploads/tasks/' . $filename;
            $this->attachment->storeAs('uploads/tasks', $filename, 'local');

            $absolutePath = storage_path('app/' . $filePath);
            $this->temporaryUrl = $this->attachment->temporaryUrl();
        $project =  app( ProjectService::class );
        $response = $project->saveAttachment($this->taskboardId,$this->projectId, $absolutePath, $filename);
    
        if ( $response[ 'status' ] == 'success' ) {
            $this->attachment = null;
            $this->dispatchBrowserEvent('reset-file-input');  
            $this->loadAttachments();
        }

        }


    public function render()
    {
        return view('livewire.project.task-board.partials.taskboard-attachment');
    }

}
