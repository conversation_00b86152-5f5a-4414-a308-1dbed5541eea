<?php

namespace App\Http\Livewire\Sale;

use App\Http\Helpers\Helper;
use App\Services\CRM\Sales\AccountService;
use App\Services\CRM\Sales\CaseService;
use App\Services\CRM\Sales\ContactService;
use App\Services\CRM\Sales\OpportunityService;
use App\Services\CRM\Sales\QuoteService;
use Livewire\Component;


class Opportunity extends Component
{
    public $items = []; // To hold API data
    public $perPage;
    public $total;
    public $currentPage;
    public $search;

    public $opportunityStages = [];
    public $contacts = [];
    public $accounts = [];
    public $users = [];
    public $lead_sources = [];
    public $types = [];
    public $priorities = [];
    public $statuses = [];
    public $viewOpportunity  = [];

    public $item_id = '';

    public $editingItem = null;   // Holds item for editing
    public $confirmingDeleteId = null; // Holds ID for delete confirmation

    protected $listeners = ['resetForm', 'fetchData', 'createModalOpened' => ['fetchAllOpportunitiesStages', 'fetchContactList', 'fetchAccountList', 'fetchUserList', 'fetchAllLeadSources'], 'deleteItem' => 'delete'];

    public $name, $account, $contact, $stage, $amount, $probability, $close_date, $lead_source, $user, $description;

    public $currency;

    protected $rules = [
        'name' => 'required',
        'close_date' => 'required',
        'account' => 'required',
        'stage' => 'required',
        'amount' => 'required|',
        'probability' => 'required',
        'contact' => 'required'
    ];

    public function resetForm()
    {
        $this->reset([
            'name',
            'account',
            'contact',
            'stage',
            'amount',
            'probability',
            'close_date',
            'lead_source',
            'user',
            'description',
        ]);

        $this->resetErrorBag(); // Clear validation errors
    }

    public function mount()
    {
        $this->currency = Helper::currency();
        $this->fetchData();
    }

    public function view($id)
    {
        $service =  app(OpportunityService::class);
        $data = $service->getOverview($id);
        if (@$data['status'] == "success") {
            $this->viewOpportunity = $data['data'] ?? [];
            $this->dispatchBrowserEvent('open-modal', ['modalId' => 'view-opportunity']);
        }
    }

    public function fetchData($page = 1)
    {
        $page = request()->query('page', $page);;
        $service =  app(OpportunityService::class);
        $data = $service->getAll($page);
        if (@$data['status'] == "success") {

            $this->items = $data['data']['items'] ?? [];
            $this->perPage = $data['data']['per_page'] ?? 10;
            $this->total = $data['data']['total'] ?? 0;
            $this->currentPage = $data['data']['current_page'];

            $this->emit('refreshPagination', null, $page, 'fetchData', $this->total);
            $this->emit('updateUrl', ['page' => $page, 'functionName' => 'fetchData']);
        }
    }

    public function fetchAllOpportunitiesStages()
    {
        $service =  app(OpportunityService::class);
        $data = $service->getAllOpportunitiesStages();
        if (@$data['status'] == "success") {
            $this->opportunityStages = $data['data'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function fetchAllLeadSources()
    {
        $service =  app(OpportunityService::class);
        $data = $service->getAllLeadSources();
        if (@$data['status'] == "success") {
            $this->lead_sources = $data['data'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function fetchUserList()
    {
        $service =  app(AccountService::class);
        $data = $service->getAllUsers();
        if (@$data['status'] == "success") {
            $this->users = $data['data'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function fetchAccountList()
    {
        $service =  app(ContactService::class);
        $data = $service->getAllAccounts();
        if (@$data['status'] == "success") {
            $this->accounts = $data['data'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function fetchContactList()
    {
        $service =  app(ContactService::class);
        $data = $service->getAll();
        if (@$data['status'] == "success") {
            $this->contacts = $data['data']['items'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function fetchContacts()
    {
        $service =  app(QuoteService::class);
        $data = $service->getBillingContacts();
        if (@$data['status'] == "success") {
            $this->contacts  = $data['data'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function fetchAccounts()
    {
        $service =  app(ContactService::class);
        $data = $service->getAllAccounts();
        if (@$data['status'] == "success") {

            $this->accounts = $data['data'] ?? [];
        }
    }

    public function fetchUsers()
    {
        $service =  app(AccountService::class);
        $data = $service->getAllUsers();
        if (@$data['status'] == "success") {
            $this->users = $data['data'] ?? [];
        }
    }


    public function fetchStatuses()
    {
        $service =  app(CaseService::class);
        $data = $service->getStatuses();
        if (@$data['status'] == "success") {
            $this->statuses  = $data['data'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function fetchPriorities()
    {
        $service =  app(CaseService::class);
        $data = $service->getPriorities();
        if (@$data['status'] == "success") {
            $this->priorities  = $data['data'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function fetchTypes()
    {
        $service =  app(CaseService::class);
        $data = $service->getTypes();
        if (@$data['status'] == "success") {
            $this->types  = $data['data'] ?? [];
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $data['message']
            ]);
        }
    }

    public function openCreateModal()
    {
        $this->fetchAllOpportunitiesStages();
        $this->fetchAllLeadSources();
        $this->fetchUserList();
        $this->fetchAccountList();
        $this->fetchContactList();
        $this->dispatchBrowserEvent('open-modal', ['modalId' => 'create-opportunity']);
    }

    public function createOpportunity()
    {
        $this->validate();

        $service =  app(OpportunityService::class);
        $response = $service->create([
            'name' => $this->name,
            'account' => $this->account,
            'contact' => $this->contact,
            'stage' => $this->stage,
            'amount' => $this->amount,
            'probability' => $this->probability,
            'close_date' => $this->close_date,
            'lead_source' => $this->lead_source,
            'user' => $this->user ?? 0,
            'description' => $this->description,
        ]);

        if (@$response['status'] === 'success' || @$response['statuss'] === 'success') {
            $this->items[] = $response['data'];
            $this->total += 1;
            $this->emit('refreshPagination', null, $this->currentPage, 'fetchData', $this->total);
            $this->dispatchBrowserEvent('close-modal', ['modalId' => 'create-opportunity']);

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => $response['message']
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $response['message']
            ]);
        }
    }

    public function delete($id)
    {
        if ($id) {
            $service =  app(OpportunityService::class);
            $response = $service->delete($id);

            if ($response['status'] === 'success') {
                foreach ($this->items as $index => $item) {
                    if ($item['id'] == $id) {
                        unset($this->items[$index]);
                        $this->total -= 1;
                        $this->emit('refreshPagination', null, $this->currentPage, 'fetchData', $this->total);
                        break;
                    }
                }
                $this->dispatchBrowserEvent('close-confirm-modal');

                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => $response['message']
                ]);
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' =>  $response['message']
                ]);
            }
        }
    }

    public function render()
    {
        return view('livewire.sales.opportunity.index');
    }
}
