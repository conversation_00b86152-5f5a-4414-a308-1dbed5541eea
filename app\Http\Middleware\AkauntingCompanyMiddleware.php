<?php

namespace App\Http\Middleware;

use App\Services\AkauntingService;
use App\Services\Contracts\AkauntingInterface;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class AkauntingCompanyMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        if (! Auth::check()){
            return $next($request);
        }

        if (!auth()->user()->allow_akaunting){
            return $next($request);
        }

        session()->forget('akaunting_error');

         try {
             /** @var AkauntingService $akauntingService */
             $akauntingService = app(AkauntingInterface::class);
             $akauntingService->ensureCompany();
             
             // if user coming from the login page, we need to init the company
             if (Str::of(url()->previous())->contains('/login')) {
                 $companyId = $akauntingService->getCurrentCompanyId();

                 assert($companyId !== null, 'Company ID is null');
                 assert($companyId !== 0, 'Company ID is 0');

                 $akauntingService->initCompany($companyId);
             }
         } catch (\Exception|\Throwable $e){
            if($e->getMessage() !== 'Akaunting company not found')
            {
                session()->put('akaunting_error', true);
                auth()->logout();
            }
             
         }

        return $next($request);
    }
}
