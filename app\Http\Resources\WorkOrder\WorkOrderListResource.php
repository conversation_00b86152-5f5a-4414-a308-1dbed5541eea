<?php

namespace App\Http\Resources\WorkOrder;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class WorkOrderListResource extends JsonResource
{
    /**
     * Transform the work order into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id ?? null, 
            'work_order_id' => $this->work_order_id,
            'status' => $this->getReadableStatus(),
            'created_at' => $this->created_at? Carbon::parse($this->created_at)->format('d/m/Y'): null,
        ];
    }

    /**
     * Add extra data to the response (like pagination).
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function with($request)
    {
        if (method_exists($this->resource, 'toArray') && method_exists($this->resource, 'currentPage')) {
            return [
                'meta' => [
                    'current_page' => $this->resource->currentPage(),
                    'last_page' => $this->resource->lastPage(),
                    'per_page' => $this->resource->perPage(),
                    'total' => $this->resource->total(),
                    'next_page_url' => $this->resource->nextPageUrl(),
                    'prev_page_url' => $this->resource->previousPageUrl(),
                ]
            ];
        }

        return [];
    }


    protected function getReadableStatus(): string
    {
        if ($this->job_submitted_at && in_array($this->job_completed_by, ['worker', 'SP'])) {
            return 'completed';
        }

        if ($this->status == 2 && $this->worker_started_at && empty($this->bm_approve_job)) {
            return 'active';
        }

        if ($this->status == 2 && ($this->bm_approve_job == 1 || $this->sp_approve_job == 1)) {
            return 'examined';
        }

        if ($this->status == 1 && $this->target_date && Carbon::parse($this->target_date)->gt(now())) {
            return 'upcoming';
        }

        if (
            $this->status == 2 &&
            $this->target_date &&
            Carbon::parse($this->target_date)->lt(now()) &&
            $this->bm_approve_job != 1 &&
            $this->sp_approve_job != 1 &&
            $this->sp_reopen_status != 2
        ) {
            return 'overdue';
        }

        if ($this->status == 3 && in_array($this->workorder_journey, ['job_execution', 'job_evaluation'])) {
            return 'on_hold';
        }

        if ($this->status == 2 && $this->workorder_journey == 'job_execution' && $this->sp_reopen_status == 2 && !$this->worker_started_at) {
            return 'reopen';
        }

        if (
            $this->status == 2 &&
            $this->sp_reopen_status != 2 &&
            in_array($this->workorder_journey, ['job_execution', 'job_evaluation']) &&
            (
                $this->bm_approve_job == 1 ||
                $this->sp_approve_job == 1 ||
                ($this->bm_approve_job == 0 && $this->sp_approve_job == 0 && $this->workorder_journey == 'job_execution' && $this->reason != '')
            )
        ) {
            return 'rejected';
        }

        return 'unknown'; // fallback
    }

}
