<?php

namespace App\Http\Traits\CRMProjects;

use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Services\CRM\Sales\ProjectService;
trait WOTasksTrait
 {
    public function enableToCreateTangibleTask()
 {
        $enabled = false;
        $user = auth()->user();
        if ( auth()->user()->workspace && $user && ( $user->user_type === 'building_manager' ||  $user->user_type === 'building_manager_employee' ) ) {
            $enabled = true;
        }

        return  $enabled;
    }

    public function createTangibleTAsk( $start_date = null, $due_date = null, $workorder_id, $workorder_type, $work_order_id, $property_name = null, $description = null )
 {

        if ( !session()->has( 'tangibleTaskData' ) ) {
            return null;
        }

        $tangibleTaskData = session( 'tangibleTaskData' );

        $data = [
            'task_type'      => 'tangible',
            'title'          => $work_order_id,
            'project_id'     => data_get( $tangibleTaskData, 'project_id' ),
            'priority'       => data_get( $tangibleTaskData, 'priority' ),
            'milestone_id'   => data_get( $tangibleTaskData, 'milestone_id' ),
            'start_date'     => $start_date ?? Carbon::today()->toDateString(),
            'due_date'       => $due_date ?? Carbon::tomorrow()->toDateString(),
            'workorder_id'   => $workorder_id,
            'workorder_type' => $workorder_type,
            'property_name'  => $property_name,
            'description'    => $description ?? 'Tangible Task ' . $workorder_id,
            'assign_to'      => data_get( $tangibleTaskData, 'assign_to' ),
        ];

        $service = app( ProjectService::class );

        $response = $service->createTask( $data[ 'project_id' ], [
            'task_type'      => $data[ 'task_type' ],
            'title'          => $data[ 'title' ],
            'priority'       => $data[ 'priority' ],
            'workorder_id'   => $data[ 'workorder_id' ],
            'workorder_type' => $data[ 'workorder_type' ],
            'milestone_id'   => $data[ 'milestone_id' ],
            'start_date'     => date( 'Y-m-d', strtotime( $data[ 'start_date' ] ) ),
            'due_date'       => date( 'Y-m-d', strtotime( $data[ 'due_date' ] ) ),
            'assign_to'      => $data[ 'assign_to' ],
            'description'    => $data[ 'description' ],
        ] );
        /*  return      $response;
        */
        if ( $response[ 'status' ] === 'success' ) {
            return $response[ 'data' ][ 'id' ];
        } else {
            return null;
        }
    }

    public function resetTangibleTaskSession()
 {
        session()->forget( 'tangibleTaskData' );

    }

    public function checkAccountEmail($email)
    {
       $service = app( ProjectService::class );
   $respons = $service->checkAccountEmail([
          'email'=>  $email
        ]);
      return $$respons;
    }

    public function createBmaAcccount( $name, $email, $mobile_no, $password_switch = 'on', $password = '123456' ) {

        $service = app( ProjectService::class );
        $response = $service->createBmaAcccount( [
            'name'      => $name,
            'email'          => $email,
            'mobile_no'          => $mobile_no,
            'password_switch'       => $password_switch,
            'password'   => $password,
        ]);
      
        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            return [
                'created'=>'yes',
                'message' => 'BMA account created successfully.'
            ];
        }else{
              return [
                'created'=>'no',
                'message'=>$response[ 'message' ] ?? 'Error',
            ];

        }
       

       
    }

}

