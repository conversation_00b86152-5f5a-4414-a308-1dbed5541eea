<?php


    namespace App\Http\Traits;
    use App\Http\Helpers\Helper;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Enums\NotificationType;
    use App\Models\Notification;
    use App\Models\User;
    use DB;

    trait NotificationTrait{
        use FunctionsTrait, UserAssetMappingTrait, ContractsTrait;

    public function manageUpdateNotifications($data, $userId){
        try {
            foreach ($data as $row) {
                $readUserArr = !empty($row->is_read_by_users) ? $this->explodeDataFromField($row->is_read_by_users) : [];

                if (!in_array($userId, $readUserArr)) {
                    $readUserArr[] = $userId;
                }

                $readUserList = $this->implodeDataFromField($readUserArr);
                $this->updateNotificationsByNotificationId($row->id, $readUserList);
            }
        } catch (\Throwable $th) {
            Log::error("manageUpdateNotifications error: " . $th);
        }
    }

    public function updateNotificationsByNotificationId($id, $value){
        try {
            return Notification::where('id', $id)->update([
                'is_read_by_users' => $value
            ]);
        } catch (\Throwable $th) {
            Log::error("updateNotificationsByNotificationId error: " . $th);
        }
    }

    public function saveNotifications($data) {
        try {
            return Notification::insertGetId($data);
        } 
        
        catch (\Throwable $th) {
            Log::error("saveNotifications error: " . $th);
        }
    }

    public function getNotificationsListByValues($userType, $userId, $assignedAsset, $accessBuildingsIds, $contractsIds, $currentDate, $currentDateTime) {
        try {
            $notificationstype = ['new_chat_message', 'work_order', 'report'];
            $lastMonth = date('Y-m-d H:i:s', strtotime('-30 days'));
            
            $approverId =   $userId;
            if(in_array($userType, ['sp_admin'])){
                $user = User::find($userId);
                $approverId = $user->service_provider; 
            }
            $notifications = Notification::select('notifications.*','work_orders.work_order_id','work_orders.work_order_type','work_orders.assigned_to','work_orders.supervisor_id')
                ->leftJoin('work_orders','work_orders.id','notifications.section_id')
                ->leftJoin('user_assets_mapping','user_assets_mapping.contract_id','work_orders.contract_id')
                ->where(function ($query) {
                    $query->where('notifications.is_timeline', 'no')
                    ->orWhereNull('notifications.is_timeline');
                })
                ->where(function($query) use ($userId) {
                    $query->whereRaw("NOT find_in_set($userId, notifications.user_id)");
                });

                if(in_array($userType, ['building_manager_employee', 'building_manager'])){
                    $woStatusNotArr = ['new_work_order_created'];
                    $woStatusArr = ['worker_assigned_by_bm','sp_has_approved_wo','new_maintenance_request','sp_has_an_issue','sp_respond_to_bm_rejection','sp_has_did_not_agreed_on_workorder','sp_has_edited_target_date', 'wo_started_wo', 'sp_has_marked_wo_completed', 'sp_has_automatically_approved_wo','pause_workorder'];

                    $notifications = $notifications->where(function($query) use ($assignedAsset, $lastMonth, $currentDateTime) {
                        $query->whereIn('work_orders.asset_category_id', $assignedAsset)
                        ->where('notifications.section_type', 'work_order')
                        ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]) //new
                        ->orWhere(function($query) use ($lastMonth, $currentDateTime) {
                            $query->whereNull('work_orders.asset_category_id')
                                ->where('notifications.notification_sub_type', 'new_maintenance_request')
                                ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]); //new
                        });
                    })
                    ->where(function($query) use ($accessBuildingsIds, $woStatusNotArr, $woStatusArr, $notificationstype, $lastMonth, $currentDateTime) {
                        $query->whereIn('notifications.building_ids', $accessBuildingsIds)
                        ->whereNotIn('notifications.notification_sub_type', $woStatusNotArr)
                        ->whereIn('notifications.notification_sub_type', $woStatusArr)
                        ->whereIn('notifications.section_type', $notificationstype)
                        ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })
                    ->orWhere(function($query) use ($userId, $accessBuildingsIds, $assignedAsset, $lastMonth, $currentDateTime) {
                        $query->where('notifications.notification_sub_type', 'new_chat_message')
                        ->where('notifications.not_receivers_user_id', '!=', $userId)
                        ->whereIn('notifications.building_ids', $accessBuildingsIds)
                        ->whereIn('work_orders.asset_category_id', $assignedAsset)
                        ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]); //new
                    })
                    ->orWhere(function ($query) use ($userId, $lastMonth, $currentDateTime) {
                        $query->whereIn('section_type', ['bookings', 'milestones'])
                            ->whereIn('notifications.user_id', [$userId])
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]); //new
                    }); 
                }

                elseif($userType == 'sp_admin') {
                    $woStatusNotArr = ['worker_assigned_by_bm'];
                    $woStatusArr = [
                        'send_wo_reminder_to_sp',
                        'sp_has_marked_wo_completed',
                        'new_work_order_created',
                        'bm_has_approved_and_evaluated_wo',
                        'wo_completed_wo',
                        'bm_has_reopend_wo',
                        'bm_work_order_rejected',
                        'bm_respond_to_sp_rejection',
                        'bm_has_did_not_agreed_on_workorder',
                        'bm_has_agreed_on_workorder',
                        'wo_started_wo',
                        'wo_paused_wo',
                        'wo_restarted_wo',
                        'bm_has_automatically_approved_wo',
                        'pause_workorder'
                    ];

                    $notifications = $notifications ->where(function($query) use ($accessBuildingsIds, $contractsIds, $notificationstype, $woStatusNotArr, $woStatusArr, $currentDate, $lastMonth, $currentDateTime) {
                        $query->whereIn('notifications.building_ids', $accessBuildingsIds)
                        ->whereIn('user_assets_mapping.contract_id', $contractsIds)
                        ->whereNotIn('notifications.notification_sub_type', $woStatusNotArr)
                        ->whereIn('notifications.notification_sub_type', $woStatusArr)
                        ->whereIn('notifications.section_type', $notificationstype)
                        ->where('work_orders.contract_type', 'regular')
                        ->where('work_orders.start_date', '<=', $currentDate)
                        ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })

                    ->orWhere(function($query) use ($contractsIds, $userId, $accessBuildingsIds, $lastMonth, $currentDateTime) {
                        $query->where('notifications.notification_sub_type', 'new_chat_message')
                            ->where('notifications.not_receivers_user_id', '!=', $userId)
                            ->whereIn('notifications.building_ids', $accessBuildingsIds)
                            ->whereIn('user_assets_mapping.contract_id', $contractsIds)
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })

                    ->orWhere(function($query) use ($userId, $lastMonth, $currentDateTime) {
                        $query->where('notifications.notification_sub_type', 'LIKE', 'marketplace_%')
                            ->where('notifications.user_id', '=', $userId)
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    });
                }

                elseif ($userType == 'supervisor') {
                    $woStatusNotArr = ['worker_assigned_by_bm'];
                    $woStatusArr = ['send_wo_reminder_to_sp','sp_has_marked_wo_completed','new_work_order_created','bm_has_approved_and_evaluated_wo','wo_completed_wo','bm_has_reopend_wo','bm_work_order_rejected','bm_respond_to_sp_rejection','bm_has_did_not_agreed_on_workorder','bm_has_agreed_on_workorder', 'wo_started_wo', 'wo_paused_wo', 'wo_restarted_wo', 'bm_has_automatically_approved_wo', 'sps_has_assigned','pause_workorder'];

                    $notifications = $notifications->where(function($query) use ($accessBuildingsIds, $assignedAsset, $contractsIds, $woStatusNotArr, $woStatusArr, $notificationstype, $currentDate, $lastMonth, $currentDateTime) {
                        $query->whereIn('notifications.building_ids', $accessBuildingsIds)
                        ->whereIn('work_orders.asset_category_id', $assignedAsset)
                        ->whereIn('user_assets_mapping.contract_id', $contractsIds)
                        ->whereNotIn('notifications.notification_sub_type', $woStatusNotArr)
                        ->whereIn('notifications.notification_sub_type', $woStatusArr)
                        ->whereIn('notifications.section_type', $notificationstype)
                        ->where('work_orders.contract_type', 'regular')
                        ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })
                    ->orWhere(function($query) use ($userId, $contractsIds, $assignedAsset, $accessBuildingsIds, $lastMonth, $currentDateTime) {
                        $query->where('notifications.notification_sub_type', 'new_chat_message')
                        ->where('notifications.not_receivers_user_id', '!=', $userId)
                        ->when(isset($accessBuildingsIds) && count($accessBuildingsIds) > 0, function ($subquery) use ($accessBuildingsIds) {
                            $subquery->whereIn('notifications.building_ids', $accessBuildingsIds);
                        })
                        ->whereIn('user_assets_mapping.contract_id', $contractsIds)
                        ->whereIn('work_orders.asset_category_id', $assignedAsset)
                        ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    });
                }

                elseif(in_array($userType, ['super_admin', 'osool_admin', 'admin', 'admin_employee'])){
                    if(in_array($userType, ['admin', 'admin_employee'])){
                        $woStatusArr = ['new_work_order_created','bm_has_approved_and_evaluated_wo','wo_completed_wo','bm_work_order_rejected','bm_has_did_not_agreed_on_workorder','bm_has_reopend_wo', 'wo_started_wo', 'sent_to_project_owner','pause_workorder'];
                    }
                    else
                    {
                        $woStatusArr = ['new_work_order_created','bm_has_approved_and_evaluated_wo','wo_completed_wo','bm_work_order_rejected','bm_has_did_not_agreed_on_workorder','bm_has_reopend_wo', 'wo_started_wo', 'sent_to_project_owner'];
                    }
                    
                    
                    $notifications = $notifications->where(function($query) use ($accessBuildingsIds, $contractsIds, $woStatusArr, $lastMonth, $currentDateTime) {
                        $query->whereIn('notifications.building_ids', $accessBuildingsIds)
                        ->whereIn('user_assets_mapping.contract_id', $contractsIds)
                        ->whereIn('notifications.notification_sub_type', $woStatusArr)
                        ->where('notifications.section_type', 'work_order')
                        ->where('notifications.section_type', '!=', 'new_chat_message')
                        ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })

                    ->orWhere(function ($query) use ($accessBuildingsIds, $lastMonth, $currentDateTime, $userType) {
                        $query->when(in_array($userType, ['admin', 'admin_employee']), function ($subquery) use ($accessBuildingsIds, $lastMonth, $currentDateTime, $userType) {
                            $subquery->whereIn('section_type', ['commercial_contracts'])
                            ->whereIn('notifications.building_ids', $accessBuildingsIds)
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]); //new
                        }); 
                    })
                    
                    ->orWhere(function ($query) use ($userId, $lastMonth, $currentDateTime, $userType) {
                        $query->when($userType == 'admin', function ($subquery) use ($userId, $lastMonth, $currentDateTime, $userType) {
                            $subquery->whereIn('section_type', ['bookings', 'milestones'])
                            ->whereIn('notifications.user_id', [$userId])
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]); //new
                        }); 
                    });
                     
                }

                $notifications = $notifications->orWhere(function($query) use ($userId, $lastMonth, $currentDateTime) {
                    $query->where('notifications.section_type', 'report')
                        ->where('notifications.user_id', $userId)
                        ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })
                    ->orWhere(function($query) use ($userId, $lastMonth, $currentDateTime) {
                        $query->where('notifications.section_type', 'contracts')
                            ->whereRaw("FIND_IN_SET($userId, notifications.user_id)")
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })
                    ->orWhere(function($query) use ($approverId, $lastMonth, $currentDateTime) {
                        $query->where('notifications.section_type', 'variation_order')
                            ->whereRaw("FIND_IN_SET( $approverId, notifications.user_id)")
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })
                    ->orWhere(function($query) use ($approverId, $lastMonth, $currentDateTime) {
                        $query->where('notifications.section_type', 'advance_contracts')
                            ->whereRaw("FIND_IN_SET($approverId, notifications.user_id)")
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    })
                    ->orWhere(function($query) use ($userId, $lastMonth, $currentDateTime) {
                        $query->where('notifications.section_type', 'complaints')
                            ->whereRaw("FIND_IN_SET($userId, notifications.user_id)")
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                });

                if(in_array($userType, ['sp_admin', 'supervisor'])){
                    $notifications = $notifications->orWhere(function($query) use ($userId, $lastMonth, $currentDateTime) {
                        $query->where('notifications.section_type', 'user')
                            ->whereIn(DB::raw('FIND_IN_SET('.$userId.', notifications.user_id)'), [1])
                            ->whereBetween('notifications.created_at', [$lastMonth, $currentDateTime]);//new
                    });
                }

                $notifications = $notifications->groupBy('notifications.id')
                ->orderBy('notifications.created_at', 'DESC');
 
                return $notifications;
        } 
        
        catch (\Throwable $th) {
            Log::error("getNotificationsListByValues error: " . $th);
        }
    }

    public function getPaginatedNotificationsList($userType, $userId, $assignedAsset, $accessBuildingsIds, $perPage, $contractsIds, $currentDate, $currentDateTime) {
        try {
            $collection = $this->getNotificationsListByValues($userType, $userId, $assignedAsset, $accessBuildingsIds, $contractsIds, $currentDate, $currentDateTime)->paginate($perPage, ['*'], 'page'); 
            return $this->getPaginatedNotifications($collection, $userId);
        } 
        
        catch (\Throwable $th) {
            Log::error("getPaginatedNotificationsList error: " . $th);
        }
    }

    public function getSearchedNotificationsList($userType, $userId, $assignedAsset, $accessBuildingsIds, $contractsIds, $currentDate, $currentDateTime) {
        try {
            return $this->getNotificationsListByValues($userType, $userId, $assignedAsset, $accessBuildingsIds, $contractsIds, $currentDate, $currentDateTime)->get();
        } 
        
        catch (\Throwable $th) {
            Log::error("getSearchedNotificationsList error: " . $th);
        }
    }

    public function getLimitedNotificationsList($userType, $userId, $assignedAsset, $accessBuildingsIds, $perPage, $contractsIds, $currentDate, $currentDateTime) {
        try {
            return $this->getNotificationsListByValues($userType, $userId, $assignedAsset, $accessBuildingsIds, $contractsIds, $currentDate, $currentDateTime)->limit($perPage)->get();
        } 
        
        catch (\Throwable $th) {
            Log::error("getLimitedNotificationsList error: " . $th);
        }
    }

    public function getTotalUnReadNotifications($userType, $userId, $assignedAsset, $accessBuildingsIds, $contractsIds, $currentDate, $currentDateTime){
        try {
            $totalUnreadNotification = 0;
            $collection = $this->getSearchedNotificationsList($userType, $userId, $assignedAsset, $accessBuildingsIds, $contractsIds, $currentDate, $currentDateTime); 

            if (isset($collection) && $collection->count()) {
                $totalUnreadNotification = $collection->reject(function ($value) use ($userId) {
                    $isReadByUsers = $this->explodeDataFromField($value->is_read_by_users);
                    return in_array($userId, $isReadByUsers);
                })->count();
            }

           return $totalUnreadNotification;
        } 
        
        catch (\Throwable $th) {
            Log::error("getTotalUnReadNotifications error: " . $th);
        }
    }

    public function getPaginatedNotifications($collection, $userId){
        try {
            if (isset($collection) && $collection->count()) {
                foreach ($collection as $key => $notification) {
                    $collection[$key]->is_read = 'no';
                    $isReadByUsers = $this->explodeDataFromField($notification->is_read_by_users);
 
                    if (in_array($userId, $isReadByUsers)) {
                        $collection[$key]->is_read = 'yes';
                    }

                    if ($notification->assigned_to == 'supervisor' && $notification->supervisor_id <> $userId && $notification->notification_sub_type == 'sps_has_assigned') {
                        unset($collection[$key]);
                    }
                }

                return $collection;
            }
        } 
        
        catch (\Throwable $th) {
            Log::error("getPaginatedNotifications error: " . $th);
        }
    }
}
?>
