<?php

namespace App\Jobs;

use App\Http\Helpers\WorkorderHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\WorkOrders;
use App\Http\Requests\Admin\Workorder\Preventive\LocationRequest as PreventiveLocationRequest;
use App\Http\Requests\Admin\Workorder\Preventive\MaintenanceRequest as PreventiveMaintananceRequest;
use App\Http\Requests\Admin\Workorder\Reactive\LocationRequest as ReactiveLocationReqeust;
use App\Http\Requests\Admin\Workorder\Reactive\MaintenanceRequest as ReactiveMaintananceReg;
use App\Models\AssetCategory as assetCate;
use App\Models\Contracts;
use App\Models\Priorities;
use App\Models\PropertyBuildings;
use App\Models\RoomsTypeFloors;
use Auth;
use Carbon\Carbon;
use DateTime;
use DB;
use Log;
use Helper;


class EditPreventiveWorkOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $session_data;
    private $logged_user;

    public function __construct($session_data,$logged_user)
    {
        $this->session_data = $session_data;
        $this->logged_user = $logged_user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info($this->session_data);
        Log::info($this->session_data);
        if (1) {

            //dd($request->w_unique_id);
            $user = $this->logged_user;
            $asset_name=null;
            $asset_number=null;
            $last_inc=null;
            $work_orderType=null;
            $wrk_typ=null;
            $notify_user=false;
            $r_hs           = $this->session_data;
            $unique_id=$r_hs['w_unique_id'];

            //dd($r_hs);
            $r_hs['unit_id'] = DB::table('room_types_floors')->where([['building_id', $r_hs['p_property_name']],['floor',  $r_hs['floor']],['room',$r_hs['room'] ] ])->value('id');

            
            $sbn_data=[];
            if($r_hs['p_service_type']=="hard_service"){
                $work_orderType="HS";
                $wrk_typ="hard";
            }
            if($r_hs['p_service_type']=="soft_service"){
                 $work_orderType="SS";
                 $wrk_typ="soft";
            }
            /**get the work day information */
            $wtfs=DB::table('work_time_frame')
                ->select('start_time','end_time','monday','tuesday','wednesday','thursday','friday','saturday','sunday','official_vacation_days')
                ->where('user_id',$user->project_user_id)
                ->first();
                // dd($r_hs);
              //$get_the_current
            /**end work day information */
            $contact_arr = explode('-',$r_hs['contract']);
            $contact_id = $contact_arr[0];
            $contract_types = $contact_arr[1];
            $r_hs['contract'] = $contact_id;

            $contract_type = $contract_types;

            $contract_type = 'warranty';
            if ($contract_types == "Regular") {
                $contract_type = 'regular';
            }
            if ($r_hs['p_service_type'] == 'soft_service') {
                $asset_category_id = $r_hs['asset_category'];
            } else {
                $asset_category_id = $r_hs['asset_category'];
            }

            $get_contract_data=DB::table('contracts')
                              ->select('id','contract_number','contract_types','service_provider_id')
                              ->where('status',1)
                              ->where('is_deleted','no')
                              ->where('id',$r_hs['contract'])
                              ->first();

            $service_provider_id = $get_contract_data->service_provider_id;

            $get_sp_admin = DB::table('users')
                              ->select('id','name')
                              ->where('status',1)
                              ->where('is_deleted','no')
                              ->where('service_provider', $service_provider_id)
                              ->where('user_type', 'sp_admin')
                              ->first();

            $get_workorder_last_id = DB::table('work_orders')->select('work_order_id')->orderBy('id', 'desc')->first();

            if ($get_workorder_last_id) {
                $work_order_number = $get_workorder_last_id->work_order_id;
                $work_order_number = preg_replace('/[^0-9]/', '', $work_order_number);
            } else {
                $work_order_number = rand(4,7);
            }

            $service_provider_id = $get_contract_data->service_provider_id;

            $get_sp_admin = DB::table('users')
                ->select('id', 'name')
                ->where('status', 1)
                ->where('is_deleted', 'no')
                ->where('service_provider', $service_provider_id)
                ->where('user_type', 'sp_admin')
                ->first();

            $contract_id = $r_hs['contract'];
            $property_id = $r_hs['p_property_name'];

            $get_supervisors = DB::table('users')
                ->select('users.id', 'users.name')
                ->join('user_assets_mapping', 'user_assets_mapping.user_id', '=', 'users.id')
                ->where('users.status', 1)
                ->where('users.is_deleted', 'no')
                
                ->whereRaw("find_in_set($property_id, users.building_ids)")
                ->whereIn('user_assets_mapping.contract_id', explode(',',$contract_id))
                ->whereIn('user_assets_mapping.asset_id', explode(',',$asset_category_id))
                ->where('user_assets_mapping.user_type', 'supervisor')
                //->whereRaw("find_in_set($asset_category_id, asset_categories)")
                ->where('users.user_type', 'supervisor')
                ->groupBy('users.id')
                ->get();
            //dd(DB::getQueryLog());       
        
            $get_supervisors = json_decode(json_encode($get_supervisors), true);
            $sup_ids         = [];

            if (!empty($get_supervisors)) {
                foreach ($get_supervisors as $sup) {
                    $sup_ids[] = $sup['id'];
                }
            }
        
            $permanent_worker = DB::table('work_orders')
                                ->select('permanent_worker')
                                ->where('unique_id',$unique_id)
                                ->where('contract_id', $r_hs['contract'])
                                ->orderBy('id', 'desc')
                                ->value('permanent_worker');
            //dd($permanent_worker);
            // for custom frequency

            $check_created_date = DB::table('work_orders')
                            ->select('id','created_at','modified_at')
                            ->where('unique_id',$unique_id)
                            ->orderby('created_at','asc')
                            ->first();
            if(isset($check_created_date) && !empty($check_created_date))
            {
                $wo_original_created_date = trim($check_created_date->created_at);
            }
            else
            {
                $wo_original_created_date = NULL;
            }
            if($r_hs['frequency']=='25')
            {
                $history_count = WorkOrders:: where([['unique_id', $unique_id],['start_date','<=',date('Y-m-d')], ['work_orders.is_deleted', '=', "no"], ]);
                
                $history_count = $history_count->orderBy('start_date','desc')->count();
                if($history_count > 0)
                {
                    $wo_is_started = 'yes';
                }
                else
                {
                    $wo_is_started = 'no';
                }
                
                $check_today = DB::table('work_orders')
                            ->where('unique_id',$unique_id)
                            ->where('worker_started_at','!=',NULL)
                            ->where('start_date','=',date('Y-m-d'))
                            ->count();
                //($check_today);
                $custom_dates = $r_hs['w_custom_dates'];            
                //dd($custom_dates);
                $custom_dates = explode(", " , $custom_dates);
                if($check_today > 0)
                {
                    if (($key = array_search(date('d-m-Y'), $custom_dates)) !== false) {
                        unset($custom_dates[$key]);
                    }
                }
                //dd($custom_dates);
                $property_buildings = DB::table('property_buildings')
                ->select('building_name')
                ->where('id', $r_hs['p_property_name'])
                ->first();
                $contract_type = 'warranty';
                if ($contract_types == "Regular") {
                    $contract_type = 'regular';
                }
                $deletedRows = DB::table('work_orders')
                            ->where('unique_id',$unique_id)
                            ->where('worker_started_at','=',NULL)
                            ->delete();
                foreach($custom_dates as $cds){                            
                    $sbn_data = [];
                    $cds = date("Y-m-d", strtotime($cds));
                    $date = $cds;
                    $start_date_sbn = $cds;
                    $time = strtotime($start_date_sbn); 
                    $work_order_number= str_pad($last_inc++, 6, '0', STR_PAD_LEFT);
                    $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number); 
                    $work_order_number = str_pad($work_order_number, 6, '0', STR_PAD_LEFT);

                    // time wor frame work
                    $dont_create = true;

                    if($wo_is_started == 'yes')
                    {
                        $dont_create = false;
                    }
                    else
                    {
                        $dayofweek   = date('w', strtotime($date));
                        switch ($dayofweek) {
                            case 1: //mon
                                if ($wtfs->monday == 0) {
                                    $date        = date('Y-m-d', strtotime('+1 day', $time));
                                    $notify_user = true;
                                } else {
                                    $dont_create = false;
                                }
                                break;
                            case 2: //tue
                                if ($wtfs->tuesday == 0) {
                                    $date        = date('Y-m-d', strtotime('+1 day', $time));
                                    $notify_user = true;
                                } else {
                                    $dont_create = false;
                                }
                                break;
                            case 3: //wed
                                if ($wtfs->wednesday == 0) {
                                    $date        = date('Y-m-d', strtotime('+1 day', $time));
                                    $notify_user = true;
                                } else {
                                    $dont_create = false;
                                }
                                break;
                            case 4: //thu
                                if ($wtfs->thursday == 0) {
                                    $date        = date('Y-m-d', strtotime('+1 day', $time));
                                    $notify_user = true;
                                } else {
                                    $dont_create = false;
                                }
                                break;
                            case 5: //fri
                                if ($wtfs->friday == 0) {
                                    $date        = date('Y-m-d', strtotime('+1 day', $time));
                                    $notify_user = true;
                                } else {
                                    $dont_create = false;
                                }
                                break;
                            case 6: //sat
                                if ($wtfs->saturday == 0) {
                                    $date        = date('Y-m-d', strtotime('+1 day', $time));
                                    $notify_user = true;
                                } else {
                                    $dont_create = false;
                                }
                                break;
                            case 0: //sun
                                if ($wtfs->sunday == 0) {
                                    $date        = date('Y-m-d', strtotime('+1 day', $time));
                                    $notify_user = true;
                                } else {
                                    $dont_create = false;
                                }
                                break;
                        }

                        // Begin : Calculate Date if it contains vacation date
                        if(trim($wtfs->official_vacation_days) != "")
                        {
                            $official_vacation_days = array_map('trim',explode(',',$wtfs->official_vacation_days));
                            $search_vacation_date =  date('d-m-Y', strtotime($date));

                            //$check_flag = 0;
                            // while($check_flag == 0)
                            // {
                                if(in_array($search_vacation_date , $official_vacation_days))
                                {
                                    $date        = date('Y-m-d', strtotime('+1 day', $time));
                                // $search_vacation_date =  date('d-m-Y', strtotime($date));
                                }
                                else
                                {
                                    //$check_flag = 1;
                                    $dont_create = false;
                                }    
                            //}
                        }
                        // End : Calculate Date if it contains vacation date
                    }
                    

                    $get_contract_data = DB::table('contracts')
                        ->select('id', 'contract_number', 'contract_types', 'service_provider_id')
                        ->where('status', 1)
                        ->where('is_deleted', 'no')
                        ->where('id', $r_hs['contract'])
                        ->first();

                    $contract_priorities = DB::table('contract_priorities')
                        ->select('id', 'service_window')
                        ->where('priority_id', $r_hs['priority_id'])
                        ->where('contract_number', $get_contract_data->contract_number)
                        ->first();                        
            
                    $target_date = date('Y-m-d H:i:s', strtotime('+' . $contract_priorities->service_window . 'hours', strtotime($date)));
                    $dayofweek   = date('w', strtotime($target_date));
                    $dayname     = lcfirst(date('l', strtotime($target_date)));
                    
                    if($wo_is_started == 'no')
                    {
                            for ($ki = 0; $ki <= 6; $ki++) {
                                if ($ki == $dayofweek && $wtfs->$dayname == 0) {
                                    $target_date = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($target_date)));
                                    $dayofweek   = date('w', strtotime($target_date));
                                    $dayname     = lcfirst(date('l', strtotime($target_date)));
        
                                    $dont_create == true;
                                }
                            }
                    }
                    


                    if($dont_create == false)
                    {
                        if($r_hs['p_service_type'] == "hard_service") {
                            //if thre is multiple asset number
                            if(!empty($r_hs['asset_number'])){
                                foreach($r_hs['asset_number'] as $ast_number){
                                    $work_order_number= str_pad($last_inc++, 6, '0', STR_PAD_LEFT);
                                    $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number); 
                                    $work_order_number = str_pad($work_order_number, 6, '0', STR_PAD_LEFT);  
                                    //$work_order_number=$r_hs['work_order_id'];
                                    //dd($work_order_number);
    
                                    $sbn_data[] = [
                                        'created_by'          => $user->id,
                                        'project_user_id'     => $user->project_user_id,
                                        'service_provider_id' => isset($get_sp_admin->id)? $get_sp_admin->id : 0,
                                        'supervisor_id'       => implode(',', $sup_ids),
                                        'unique_id'           => $unique_id,
                                        'work_order_id'       => 'PM-'.$work_order_number,
                                        'work_order_type'     => "preventive",
                                        'pm_title'            => $r_hs['pm_title'],
                                        'service_type'        => $wrk_typ,
                                        'property_id'         => $r_hs['p_property_name'],
                                        'unit_id'               => isset($r_hs['unit_id'])? $r_hs['unit_id'] : 0,
    
                                        'floor'               => $r_hs['floor'],
                                        'room'                => $r_hs['room'],
                                        'asset_category_id'   => !empty($asset_category_id) ? $asset_category_id : 0,
                                        'asset_name_id'       => !empty($r_hs['asset_name']) ? $r_hs['asset_name'] : 0,
                                        'asset_number_id'     => !empty($ast_number) ? $ast_number : 0,
                                        'contract_id'         => $r_hs['contract'],
                                        'contract_type'       => $contract_type,
                                        'description'         => !empty($r_hs['work_order_description']) ? $r_hs['work_order_description'] : '',
                                        'checklist_id'        => !empty($r_hs['choose_asset_checklist']) ? $r_hs['choose_asset_checklist'] : 0,
                                        'service_category_id' => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                                        'frequency_id'        => !empty($r_hs['frequency']) ? $r_hs['frequency'] : 0,
                                        //'start_date'=>!empty($r_hs['w_start_date'])? $r_hs['w_start_date'] : '0000-00-00',
                                        //'end_date'=>!empty($r_hs['w_end_date'])? $r_hs['w_end_date'] : '0000-00-00',
                                        'start_date'          => !empty($date) ? $date : date("Y-m-d"),
                                        'end_date'            => !empty($date) ? $date : date("Y-m-d"),
                                        'target_date'         => !empty($target_date) ? $target_date : date("Y-m-d"),
                                        'status'              => 1,
                                        'is_deleted'          => "no",
                                        'created_at'          => date('Y-m-d H:i:s'),
                                        'wo_default_created_at'          => $wo_original_created_date,
                                        'bm_approove'         => isset($r_hs['bm_approove']) ? $r_hs['bm_approove'] : 1,
                                        'priority_id'         => isset($r_hs['priority_id']) ? $r_hs['priority_id'] : 0,
                                        'permanent_worker' =>isset($permanent_worker)? $permanent_worker : 0,
                                        'worker_id' =>isset($permanent_worker)? $permanent_worker : 0,
                                        'wtf_start_time'      => isset($wtfs->start_time) ? $wtfs->start_time : "00:00:00" ,
                                        'wtf_end_time'        => isset($wtfs->end_tme) ? $wtfs->end_tme : "00:00:00"
                                        
    
                                    ];
                                }
                            }//if there is no asset number
                            else{                            
                                $work_order_number= str_pad($last_inc++, 6, '0', STR_PAD_LEFT);
                                $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number); 
                                $work_order_number = str_pad($work_order_number, 6, '0', STR_PAD_LEFT);
                                $sbn_data[] = [
                                    'project_user_id' => $user->project_user_id,
                                    'created_by'          => $user->id,
                                    'service_provider_id' => isset($get_sp_admin->id)? $get_sp_admin->id : 0,
                                    'supervisor_id'       => implode(',', $sup_ids),
    
                                    'unique_id'           => $unique_id,
                                    'work_order_id'       => 'PM-'.$work_order_number,
                                    'work_order_type'     => "preventive",
                                    'pm_title'            => $r_hs['pm_title'],
                                    'service_type'        => $wrk_typ,
                                    'property_id'         => $r_hs['p_property_name'],
                                    'unit_id'               => isset($r_hs['unit_id'])? $r_hs['unit_id'] : 0,
    
                                    'floor'               => $r_hs['floor'],
                                    'room'                => $r_hs['room'],
                                    'asset_category_id'   => !empty($asset_category_id) ? $asset_category_id : 0,
                                    'asset_name_id'       => !empty($r_hs['asset_name']) ? $r_hs['asset_name'] : 0,
                                    'asset_number_id'     => !empty($r_hs['asset_number']) ? $r_hs['asset_number'] : 0,
                                    'contract_id'         => $r_hs['contract'],
                                    'contract_type'       => $contract_type,
                                    'description'         => !empty($r_hs['work_order_description']) ? $r_hs['work_order_description'] : '',
                                    'checklist_id'        => !empty($r_hs['choose_asset_checklist']) ? $r_hs['choose_asset_checklist'] : 0,
                                    'service_category_id' => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                                    'frequency_id'        => !empty($r_hs['frequency']) ? $r_hs['frequency'] : 0,
                                    // 'start_date'=>!empty($r_hs['w_start_date'])? $r_hs['w_start_date'] : '0000-00-00',
                                    // 'end_date'=>!empty($r_hs['w_end_date'])? $r_hs['w_end_date'] : '0000-00-00',
                                    'start_date'          => !empty($date) ? $date : date("Y-m-d"),
                                    'end_date'            => !empty($date) ? $date : date("Y-m-d"),
                                    'target_date'         => !empty($date) ? $date : date("Y-m-d"),
                                    'status'              => 1,
                                    'is_deleted'          => "no",
                                    'created_at'          => date('Y-m-d H:i:s'),
                                    'wo_default_created_at'          => $wo_original_created_date,
                                    'bm_approove'         => isset($r_hs['bm_approove']) ? $r_hs['bm_approove'] : 1,
                                    'priority_id'         => isset($r_hs['priority_id']) ? $r_hs['priority_id'] : 0,
                                    'permanent_worker' =>isset($permanent_worker)? $permanent_worker : 0,
                                    'worker_id' =>isset($permanent_worker)? $permanent_worker : 0,
                                    'wtf_start_time'      => isset($wtfs->start_time) ? $wtfs->start_time : "00:00:00" ,
                                    'wtf_end_time'        => isset($wtfs->end_tme) ? $wtfs->end_tme : "00:00:00"
    
                                ];
    
                            }
    
                        }
                        
                        // if($r_hs['p_service_type']=="soft_service" ){ //start preventive service
                        //     // $work_order_number= 'B'.$property_buildings->building_name.' :'.str_pad($last_inc++, 6, '0', STR_PAD_LEFT);
                        //     // $work_order_number=str_pad($last_inc++, 6, '0', STR_PAD_LEFT);
                        //     $work_order_number= str_pad($last_inc++, 6, '0', STR_PAD_LEFT);
                        //     $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number); 
                        //     $work_order_number = str_pad($work_order_number, 6, '0', STR_PAD_LEFT);  
    
    
                        //     $sbn_data[] = [
                        //         'project_user_id' => $user->project_user_id,
                        //         'supervisor_id'       => implode(',', $sup_ids),
                        //         'created_by'          => $user->id,
                        //         'service_provider_id' => isset($get_sp_admin->id)? $get_sp_admin->id : 0,
                                
    
                        //         'unique_id'           => $unique_id,
                        //         'work_order_id'       => 'PM-'.$work_order_number,
                        //         'work_order_type'     => "preventive",
                        //         'pm_title'            => $r_hs['pm_title'],
                        //         'service_type'        => $wrk_typ,
                        //         'property_id'         => $r_hs['p_property_name'],
                        //         'unit_id'               => isset($r_hs['unit_id'])? $r_hs['unit_id'] : 0,
    
                        //         'floor'               => $r_hs['floor'],
                        //         'room'                => $r_hs['room'],
                        //         'asset_category_id'   => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                        //         'asset_name_id'       => !empty($r_hs['asset_name']) ? $r_hs['asset_name'] : 0,
                        //         'asset_number_id'     => !empty($r_hs['asset_number']) ? $r_hs['asset_number'] : 0,
                        //         'contract_id'         => $r_hs['contract'],
                        //         'contract_type'       => $contract_type,
                        //         'description'         => !empty($r_hs['work_order_description']) ? $r_hs['work_order_description'] : '',
                        //         'checklist_id'        => !empty($r_hs['choose_asset_checklist']) ? $r_hs['choose_asset_checklist'] : 0,
                        //         'service_category_id' => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                        //         'frequency_id'        => !empty($r_hs['frequency']) ? $r_hs['frequency'] : 0,
                        //         // 'start_date'=>!empty($r_hs['w_start_date'])? $r_hs['w_start_date'] : '0000-00-00',
                        //         // 'end_date'=>!empty($r_hs['w_end_date'])? $r_hs['w_end_date'] : '0000-00-00',
                        //         'start_date'          => !empty($date) ? $date : date("Y-m-d"),
                        //         'end_date'            => !empty($date) ? $date : date("Y-m-d"),
                        //         'target_date'         => !empty($date) ? $date : date("Y-m-d"),
                        //         'status'              => 1,
                        //         'is_deleted'          => "no",
                        //         'created_at'          => date('Y-m-d H:i:s'),
                        //         'bm_approove'         => isset($r_hs['bm_approove']) ? $r_hs['bm_approove'] : 1,
                        //         'priority_id'         => isset($r_hs['priority_id']) ? $r_hs['priority_id'] : 0,
                        //         'wtf_start_time'      => isset($wtfs->start_time) ? $wtfs->start_time : "00:00:00" ,
                        //         'wtf_end_time'        => isset($wtfs->end_tme) ? $wtfs->end_tme : "00:00:00"
    
                        //     ];
                        // }
                        //start preventive service
                        if($r_hs['p_service_type']=="soft_service" )
                        {                        
                            $work_order_number= str_pad($last_inc++, 6, '0', STR_PAD_LEFT);
                            $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number); 
                            $work_order_number = str_pad($work_order_number, 6, '0', STR_PAD_LEFT);
                            $sbn_data[] = [
                                'project_user_id' => $user->project_user_id,
                                'supervisor_id'       => implode(',', $sup_ids),
                                'created_by'          => $user->id,
                                'service_provider_id' => isset($get_sp_admin->id)? $get_sp_admin->id : 0,
                                
    
                                'unique_id'           => $unique_id,
                                'work_order_id'       => 'PM-'.$work_order_number,
                                'work_order_type'     => "preventive",
                                'pm_title'            => $r_hs['pm_title'],
                                'service_type'        => $wrk_typ,
                                'property_id'         => $r_hs['p_property_name'],
                                'unit_id'               => isset($r_hs['unit_id'])? $r_hs['unit_id'] : 0,
    
                                'floor'               => $r_hs['floor'],
                                'room'                => $r_hs['room'],
                                'asset_category_id'   => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                                'asset_name_id'       => !empty($r_hs['asset_name']) ? $r_hs['asset_name'] : 0,
                                'asset_number_id'     => !empty($r_hs['asset_number']) ? $r_hs['asset_number'] : 0,
                                'contract_id'         => $r_hs['contract'],
                                'contract_type'       => $contract_type,
                                'description'         => !empty($r_hs['work_order_description']) ? $r_hs['work_order_description'] : '',
                                'checklist_id'        => !empty($r_hs['choose_asset_checklist']) ? $r_hs['choose_asset_checklist'] : 0,
                                'service_category_id' => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                                'frequency_id'        => !empty($r_hs['frequency']) ? $r_hs['frequency'] : 0,
                                // 'start_date'=>!empty($r_hs['w_start_date'])? $r_hs['w_start_date'] : '0000-00-00',
                                // 'end_date'=>!empty($r_hs['w_end_date'])? $r_hs['w_end_date'] : '0000-00-00',
                                'start_date'          => !empty($date) ? $date : date("Y-m-d"),
                                'end_date'            => !empty($date) ? $date : date("Y-m-d"),
                                'target_date'         => !empty($date) ? $date : date("Y-m-d"),
                                'status'              => 1,
                                'is_deleted'          => "no",
                                'created_at'          => date('Y-m-d H:i:s'),
                                'wo_default_created_at'          => $wo_original_created_date,
                                'bm_approove'         => isset($r_hs['bm_approove']) ? $r_hs['bm_approove'] : 1,
                                'priority_id'         => isset($r_hs['priority_id']) ? $r_hs['priority_id'] : 0,
                                'permanent_worker' =>isset($permanent_worker)? $permanent_worker : 0,
                                'worker_id' =>isset($permanent_worker)? $permanent_worker : 0,
                                'wtf_start_time'      => isset($wtfs->start_time) ? $wtfs->start_time : "00:00:00" ,
                                'wtf_end_time'        => isset($wtfs->end_tme) ? $wtfs->end_tme : "00:00:00"
    
                            ];
                        }
                    }

                    
                    $rms=WorkOrders::insert($sbn_data);                            
                }
            }
            else
            {
                $r_hs['w_start_date'] = date("Y-m-d", strtotime($r_hs['w_start_date']));
                $r_hs['w_end_date'] = date("Y-m-d", strtotime($r_hs['w_end_date']));
                $start_date_sbn = $r_hs['w_start_date'];
                $pm_end_date =  $r_hs['w_end_date'];
                
                $time = strtotime($start_date_sbn);
            

                $check_wo_started = WorkOrders::select('work_orders.start_date', 'work_orders.contract_id')
                                            ->where('work_orders.unique_id', '=', $unique_id)
                                            ->where('work_orders.is_deleted', '=', "no")
                                            ->where('work_orders.worker_started_at', '!=', NULL)
                                            ->orderBy('work_orders.start_date', 'desc')->first();
                //dd($check_wo_started);
                $start_date = $r_hs['w_start_date'];
                if(isset($check_wo_started))
                {
                    $start_date = date('Y-m-d', strtotime($check_wo_started->start_date. '+1 day'));
                    $time = strtotime(date('Y-m-d', strtotime($check_wo_started->start_date. '+1 day')));
                }            
                //dd($start_date);
                $from           = Carbon::createFromFormat('Y-m-d', $start_date);
                $to             = Carbon::createFromFormat('Y-m-d', $r_hs['w_end_date']);
                //dd($to);
                $deletedRows = DB::table('work_orders')
                            ->where('unique_id',$unique_id);
                            if(isset($check_wo_started))
                            {
                                $deletedRows = $deletedRows->where('start_date','>=',$start_date);
                            }
                            $deletedRows = $deletedRows->where('worker_started_at','=',NULL)
                            ->delete();
                //dd($deletedRows);
                $diff_in_days   = $from->diffInDays($to);
                $diff_in_week   = $from->diffInWeeks($to);
                $diff_in_month  = $from->diffInMonths($to);
                $diff_in_year   = $from->diffInYears($to);
                $diff_in_quater = $from->diffInQuarters($to);


                switch ($r_hs['frequency']) {
                    case 1:
                        //day
                        $qt = $from->diffInDays($to)+1;
                        break;
                    case 2:
                        //week
                        $qt = $from->diffInWeeks($to)+1;
                        break;
                    case 3:
                        /** Bi-weekly */
                        $qt = 2;
                        break;
                    case 4:
                        /**monthly */
                        $qt = $from->diffInMonths($to) + 1;
                        break;
                    case 5:
                        /**Quarterly */
                        $qt = $from->diffInQuarters($to);
                        break;
                    case 6:
                        /**Half-annually */
                        $qt = 2;
                        break;
                    case 7:
                        /**    Annually*/
                        $qt = $from->diffInYears($to)+1;
                        break;

                    default:
                        $qt = 0;
                        break;
                }
                //$r_hs['asset_number']=['1'];

                $property_buildings = DB::table('property_buildings')
                    ->select('building_name')
                    ->where('id', $r_hs['p_property_name'])
                    ->first();
            
                    
                $contract_type = 'regular';
                if ($contract_types == "warranty") {
                    $contract_type = 'warranty';
                }
                //dd($qt);    
                for ($i = 0; $i < $qt; $i++) {
                    $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number);

                    $date = date('Y-m-d H:i:s', $time);
                    // time wor frame work
                    $dont_create = false;

                    // Step 1: Find a valid working day
                    $getValidWorkingDay = Helper::getValidWorkingDay($date, $wtfs, $r_hs['frequency']);

                    $date = $getValidWorkingDay['date'];
                    $dont_create = $getValidWorkingDay['dont_create'];
                    Log::info($date);
                    Log::info($dont_create);

                    // Step 2: Ensure the date fits within the service window if provided
                    if ($dont_create == false) {
                        $endDateTime = date('Y-m-d H:i:s', strtotime($date . ' ' . $wtfs->end_time));
                        if ($endDateTime >= date('Y-m-d H:i:s')) {
                            $startTime = date('Y-m-d H:i:s', strtotime($date . ' ' . $wtfs->start_time));
                            $date = date('Y-m-d H:i:s', strtotime('+1 day', $startTime));
                        }
                    }

                    if (isset($contractFrequencies->service_window)) {
                        $target_date = date('Y-m-d H:i:s', strtotime('+' . $contractFrequencies->service_window . ' minutes', strtotime($date)));
                    } else {
                        $target_date = date('Y-m-d H:i:s', strtotime($date));
                    }

                    // Step 3: Re-check the target date against working days and vacation days
                    $target_date = Helper::getValidWorkingDay($target_date, $wtfs, $r_hs['frequency'])['date'];

                    $dayTime = date('Y-m-d', strtotime($target_date)) . ' ' . $wtfs->start_time;
                    if (strtotime($dayTime) <= strtotime($target_date)) {
                        $target_date = date('Y-m-d', strtotime('+1 day', strtotime($target_date))) . ' ' . $wtfs->start_time;
                    }

                    if ($r_hs['p_service_type'] == "hard_service" && $dont_create == false) {
                        //if thre is multyple asset number
                        if(!empty($r_hs['asset_number'])){
                            foreach($r_hs['asset_number'] as $key => $ast_number){
                                $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number);
                                //$work_order_number=$r_hs['work_order_id'];
                                //dd($work_order_number);

                                $sbn_data[] = [
                                    'created_by'          => $user->id,
                                    'project_user_id' => $user->project_user_id,
                                    'service_provider_id' => isset($get_sp_admin->id)? $get_sp_admin->id : 0,
                                    'supervisor_id'       => implode(',', $sup_ids),
                                    'unique_id'           => $unique_id,
                                    'work_order_id'       => 'PM-'.$work_order_number + 1,
                                    'work_order_type'     => "preventive",
                                    'pm_title'            => $r_hs['pm_title'],
                                    'service_type'        => $wrk_typ,
                                    'property_id'         => $r_hs['p_property_name'],
                                    'unit_id'               => isset($r_hs['unit_id'])? $r_hs['unit_id'] : 0,
                                    'floor'               => $r_hs['floor'],
                                    'room'                => $r_hs['room'],
                                    'asset_category_id'   => !empty($asset_category_id) ? $asset_category_id : 0,
                                    'asset_name_id'       => !empty($r_hs['asset_name']) ? $r_hs['asset_name'] : 0,
                                    'asset_number_id'     => !empty($ast_number) ? $ast_number : 0,
                                    'contract_id'         => $r_hs['contract'],
                                    'contract_type'       => $contract_type,
                                    'description'         => !empty($r_hs['work_order_description']) ? $r_hs['work_order_description'] : '',
                                    'checklist_id'        => !empty($r_hs['choose_asset_checklist']) ? $r_hs['choose_asset_checklist'] : 0,
                                    'service_category_id' => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                                    'frequency_id'        => !empty($r_hs['frequency']) ? $r_hs['frequency'] : 0,
                                    //'start_date'=>!empty($r_hs['w_start_date'])? $r_hs['w_start_date'] : '0000-00-00',
                                    //'end_date'=>!empty($r_hs['w_end_date'])? $r_hs['w_end_date'] : '0000-00-00',
                                    'start_date'          => !empty($date) ? $date : date("Y-m-d"),
                                    'end_date'            => !empty($date) ? $date : date("Y-m-d"),
                                    'target_date'         => !empty($target_date) ? $target_date : date("Y-m-d"),
                                    'status'              => 1,
                                    'is_deleted'          => "no",
                                    'created_at'          => date('Y-m-d H:i:s'),
                                    'wo_default_created_at'          => $wo_original_created_date,
                                    'bm_approove'         => isset($r_hs['bm_approove']) ? $r_hs['bm_approove'] : 1,
                                    'priority_id'         => isset($r_hs['priority_id']) ? $r_hs['priority_id'] : 0,
                                    'pm_end_date'         => $pm_end_date,
                                    'permanent_worker' =>isset($permanent_worker)? $permanent_worker : 0,
                                    'worker_id' =>isset($permanent_worker)? $permanent_worker : 0,
                                    'wtf_start_time'      => isset($wtfs->start_time) ? $wtfs->start_time : "00:00:00" ,
                                    'wtf_end_time'        => isset($wtfs->end_tme) ? $wtfs->end_tme : "00:00:00"

                                ];
                            }
                        }
                        //if there is no asset number
                        else{
                            $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number);

                            $sbn_data[] = [
                                'project_user_id' => $user->project_user_id,
                                'created_by'          => $user->id,
                                'service_provider_id' => isset($get_sp_admin->id)? $get_sp_admin->id : 0,
                                'supervisor_id'       => implode(',', $sup_ids),

                                'unique_id'           => $unique_id,
                                'work_order_id'       => 'PM-'.$work_order_number,
                                'work_order_type'     => "preventive",
                                'pm_title'            => $r_hs['pm_title'],
                                'service_type'        => $wrk_typ,
                                'property_id'         => $r_hs['p_property_name'],
                                'unit_id'               => isset($r_hs['unit_id'])? $r_hs['unit_id'] : 0,

                                'floor'               => $r_hs['floor'],
                                'room'                => $r_hs['room'],
                                'asset_category_id'   => !empty($asset_category_id) ? $asset_category_id : 0,
                                'asset_name_id'       => !empty($r_hs['asset_name']) ? $r_hs['asset_name'] : 0,
                                'asset_number_id'     => !empty($r_hs['asset_number']) ? $r_hs['asset_number'] : 0,
                                'contract_id'         => $r_hs['contract'],
                                'contract_type'       => $contract_type,
                                'description'         => !empty($r_hs['work_order_description']) ? $r_hs['work_order_description'] : '',
                                'checklist_id'        => !empty($r_hs['choose_asset_checklist']) ? $r_hs['choose_asset_checklist'] : 0,
                                'service_category_id' => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                                'frequency_id'        => !empty($r_hs['frequency']) ? $r_hs['frequency'] : 0,
                                // 'start_date'=>!empty($r_hs['w_start_date'])? $r_hs['w_start_date'] : '0000-00-00',
                                // 'end_date'=>!empty($r_hs['w_end_date'])? $r_hs['w_end_date'] : '0000-00-00',
                                'start_date'          => !empty($date) ? $date : date("Y-m-d"),
                                'end_date'            => !empty($date) ? $date : date("Y-m-d"),
                                'target_date'         => !empty($date) ? $date : date("Y-m-d"),
                                'status'              => 1,
                                'is_deleted'          => "no",
                                'created_at'          => date('Y-m-d H:i:s'),
                                'wo_default_created_at'          => $wo_original_created_date,
                                'bm_approove'         => isset($r_hs['bm_approove']) ? $r_hs['bm_approove'] : 1,
                                'priority_id'         => isset($r_hs['priority_id']) ? $r_hs['priority_id'] : 0,
                                'pm_end_date'         => $pm_end_date,
                                'permanent_worker' =>isset($permanent_worker)? $permanent_worker : 0,
                                'worker_id' =>isset($permanent_worker)? $permanent_worker : 0,
                                'wtf_start_time'      => isset($wtfs->start_time) ? $wtfs->start_time : "00:00:00" ,
                                'wtf_end_time'        => isset($wtfs->end_tme) ? $wtfs->end_tme : "00:00:00"

                            ];

                        }

                    }
                    //start preventive service
                    if($r_hs['p_service_type']=="soft_service" && $dont_create == false){
                        $work_order_number = Helper::generateUniqueWorkOrderNumber($work_order_number);

                        // $work_order_number= 'B'.$property_buildings->building_name.' :'.str_pad($last_inc++, 6, '0', STR_PAD_LEFT);
                        $sbn_data[] = [
                            'project_user_id' => $user->project_user_id,
                            'created_by'          => $user->id,
                            'service_provider_id' => isset($get_sp_admin->id)? $get_sp_admin->id : 0,
                            'supervisor_id'       => implode(',', $sup_ids),

                            'unique_id'           => $unique_id,
                            'work_order_id'       => 'PM-'.$work_order_number,
                            'work_order_type'     => "preventive",
                            'pm_title'            => $r_hs['pm_title'],
                            'service_type'        => $wrk_typ,
                            'property_id'         => $r_hs['p_property_name'],
                            'unit_id'               => isset($r_hs['unit_id'])? $r_hs['unit_id'] : 0,

                            'floor'               => $r_hs['floor'],
                            'room'                => $r_hs['room'],
                            'asset_category_id'   => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                            'asset_name_id'       => !empty($r_hs['asset_name']) ? $r_hs['asset_name'] : 0,
                            'asset_number_id'     => !empty($r_hs['asset_number']) ? $r_hs['asset_number'] : 0,
                            'contract_id'         => $r_hs['contract'],
                            'contract_type'       => $contract_type,
                            'description'         => !empty($r_hs['work_order_description']) ? $r_hs['work_order_description'] : '',
                            'checklist_id'        => !empty($r_hs['choose_asset_checklist']) ? $r_hs['choose_asset_checklist'] : 0,
                            'service_category_id' => !empty($r_hs['asset_category']) ? $r_hs['asset_category'] : 0,
                            'frequency_id'        => !empty($r_hs['frequency']) ? $r_hs['frequency'] : 0,
                            // 'start_date'=>!empty($r_hs['w_start_date'])? $r_hs['w_start_date'] : '0000-00-00',
                            // 'end_date'=>!empty($r_hs['w_end_date'])? $r_hs['w_end_date'] : '0000-00-00',
                            'start_date'          => !empty($date) ? $date : date("Y-m-d"),
                            'end_date'            => !empty($date) ? $date : date("Y-m-d"),
                            'target_date'         => !empty($date) ? $date : date("Y-m-d"),
                            'status'              => 1,
                            'is_deleted'          => "no",
                            'created_at'          => date('Y-m-d H:i:s'),
                            'wo_default_created_at'          => $wo_original_created_date,
                            'bm_approove'         => isset($r_hs['bm_approove']) ? $r_hs['bm_approove'] : 1,
                            'priority_id'         => isset($r_hs['priority_id']) ? $r_hs['priority_id'] : 0,
                            'pm_end_date'         => $pm_end_date,
                            'permanent_worker' =>isset($permanent_worker)? $permanent_worker : 0,
                            'worker_id' =>isset($permanent_worker)? $permanent_worker : 0,
                            'wtf_start_time'      => isset($wtfs->start_time) ? $wtfs->start_time : "00:00:00" ,
                            'wtf_end_time'        => isset($wtfs->end_tme) ? $wtfs->end_tme : "00:00:00"

                        ];
                    }

                    //$time = strtotime('+1 month', $time);
                    switch ($r_hs['frequency']) {
                        case 1:
                            //day
                            $time = strtotime('+1 day', $time);
                            break;
                        case 2:
                            $time = strtotime('+1 week', $time);
                            break;
                        case 3:
                            /** Bi-weekly */
                            $time = strtotime('+3 day', $time);
                            break;
                        case 4:
                            /**monthly */
                            $time = strtotime('+1 month', $time);
                            break;
                        case 5:
                            /**Quarterly */
                            $time = strtotime('+4 month', $time);
                            break;
                        case 6:
                            /**Half-annually */
                            $time = strtotime('+6 month', $time);
                            break;
                        case 7:
                            /**    Annually*/
                            $time = strtotime('+1 years', $time);
                            break;

                        default:
                            $time = strtotime('+1 day', $time);
                            break;
                    }
                }
                //dd($sbn_data);
                $latestUser = DB::table('work_orders')->select('id')->orderBy('id', 'DESC')->first();
                $rms = WorkOrders::insert($sbn_data);
            }
            //dd($rms);
            if($rms)
            {

                $rows = DB::table('work_orders')->select('id','workorder_journey','contract_id','start_date', 'wtf_start_time')->where('unique_id', '=', $unique_id)->where('status',1)->get();
                $rows = json_decode(json_encode($rows), true);
                //dd($rows);
                if (!empty($rows)) {
                    foreach ($rows as $row) {                
                        $message = 'Work Order Submitted by <strong>'.$user->name.'</strong>';
                        $message_ar = 'تم تسليم أمر العمل من قبل <strong>'.$user->name.'</strong>';
                        if(isset($row['start_date']) && isset($row['wtf_start_time']))
                        {
                            $created_at = $row['start_date'].' '.$row['wtf_start_time'];
                        }
                        else
                        {
                            $created_at = date('Y-m-d H:i:s');
                        }
                        Log::info($created_at);
                        Log::info($row['start_date'].' Edit date testing '.$row['wtf_start_time']);
                        DB::table('notifications')->insert(array('user_id' => $user->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'section_id' => $row['id'], 'created_at' => $created_at));
                    }
                }
            }

            $ppmReportStatus = WorkOrderHelper::getWorkorderPpmreportstatus($unique_id);
            $is_force_edit = 0;
            
            if($ppmReportStatus == 'approved')
            {
                if($r_hs['frequency'] != $r_hs['old_frequency'])
                {
                    //if ref diff
                    $is_force_edit = 1;
                }
                else
                {
                    //if freq same
                    if($r_hs['choose_asset_checklist'] != $r_hs['old_checklist'])
                    {
                        $is_force_edit = 1;
                    }
                    elseif($r_hs['choose_asset_checklist'] == '25')
                    {
                        $custom_dates_diff = array_diff($r_hs['w_custom_dates'], $r_hs['old_customdate']);
                        if($custom_dates_diff)
                        {
                            $is_force_edit = 1;
                        }
                    }
                    else
                    {
                        //non custom
                        if($r_hs['w_start_date'] != $r_hs['old_startdate'])
                        {
                            $is_force_edit = 1;
                        }

                        if($r_hs['w_end_date'] != $r_hs['old_customdate'])
                        {
                            $is_force_edit = 1;
                        }
                    }
                }
            }

            if($ppmReportStatus == 'approved' && $is_force_edit == 1)
            {
                $updateForceEdit = WorkOrders::where('unique_id',$unique_id)->update(['is_force_edit'=> 1,'force_edit_by'=> $user->id,'force_edit_at'=> now()]);
            }
           
            $frequency_id = $r_hs['frequency'];
            $frequencies = DB::table('frequencies_master')
                            ->select('frequencies_master.title','frequencies_master.title_ar')
                            ->where('frequencies_master.id', $frequency_id)
                            ->first();



         
        }
        

    }
     
      
}
