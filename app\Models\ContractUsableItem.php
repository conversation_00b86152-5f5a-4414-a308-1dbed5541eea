<?php

namespace App\Models;

use Akaunting\Api\Akaunting;
use Akaunting\Api\Data\InvoiceData;
use Akaunting\Api\Data\ItemData;
use Akaunting\Api\Data\WarehouseData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * class App\Models\ContractUsableItem
 * @property int $id
 * @property int $contract_id
 * @property int $company_id
 * @property int $warehouse_id
 * @property int $item_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property-read \App\Models\Contracts $contract
 */
class ContractUsableItem extends Model
{
    use HasFactory;

    protected $table = 'contract_usable_items';

    protected $fillable = [
        'contract_id',
        'company_id',
        'warehouse_id',
        'item_id',
        'low_stock',
        'open_stock',
        'mandatory',
        'approval',
        'price',
        'penalty',
        'penalty_type'
    ];

    protected $casts = [
        'contract_id' => 'int',
        'company_id' => 'int',
        'warehouse_id' => 'int',
        'item_id' => 'int',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    /**
     * @return BelongsTo
     */
    public function contract()
    {
        return $this->belongsTo(Contracts::class, 'contract_id');
    }

    /**
     * @return ItemData
     * @throws \JsonException
     */
    public function getItem()
    {
        if (empty($this->item_id)) {
            return null;
        }
        $akaunting = new Akaunting();
        $item = $akaunting->inventory()->items()->getItem($this->item_id, $this->warehouse_id, $this->company_id);
        $item = ItemData::from($item->json('data', []));
        return $item;
    }
}
