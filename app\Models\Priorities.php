<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DB;
use Auth;
use Illuminate\Database\Eloquent\SoftDeletes;
class Priorities extends Model
{
    use HasFactory; use SoftDeletes;

    protected $table = 'priorities';
    const UPDATED_AT = 'modified_at';


    public function prioritables()
    {
        return $this->morphedByMany(AssetCategory::class, 'prioritables');
    }

    public function assetCategories()
    {
        return $this->hasMany(AssetCategory::class);
    }

    public static function getPrioritiesCL($user_id, $asset_ids = []){
        $data_set=[];
        if(!empty($user_id)){
            $data_query = Priorities::select(
                'priorities.id',
                'priorities.user_id',
                'priorities.priority_level',
                'priorities.service_window',
                'priorities.response_time',
                'priorities.service_window_type',
                'priorities.response_time_type'
            )
                ->where('priorities.user_id', $user_id)
                ->whereHas('prioritables', function ($query) {
                    $query->where('prioritables.prioritables_type', AssetCategory::class);
                })
                ->groupBy(
                    'priorities.id',
                    'priorities.user_id',
                    'priorities.priority_level',
                    'priorities.service_window',
                    'priorities.response_time',
                    'priorities.service_window_type',
                    'priorities.response_time_type'
                )
                ->get();

            if(!empty($data_query)){
                $data_set['res']=['code'=>'ok','list'=>$data_query];
            }
        }
        else{
              $data_set['res'] = ['code'=>'no','list'=>"empty data set"];
        }
        return $data_set;
    }

    public static function getPrioritiesCLContract($user_id, $c_number)
    {
        //dd($c_number);
        $users = Auth::user();
        $result= DB::table('contract_priorities')
                ->select('priorities.*','priorities.id','priorities.user_id','priorities.priority_level','contract_priorities.service_window','contract_priorities.response_time','contract_priorities.service_window_type','contract_priorities.response_time_type')
                ->join('priorities','priorities.id','=','contract_priorities.priority_id')
                // ->where('priorities.is_deleted','=',"no")
                ->where('contract_priorities.contract_number', $c_number);
                // ->groupby('priorities.id')
                if(Auth()->user()->user_type != 'sp_admin' && Auth()->user()->user_type != 'supervisor') {
                    $result = $result->where('contract_priorities.user_id','=',$user_id);
                }
                //$result = $result->get();
                //->where('contract_priorities.user_id','=',$user_id);
                // ->orderby('contract_priorities.id', 'desc');
                // ->get();

        if($users['user_type']!='osool_admin')
        {
            if(Auth()->user()->user_type != 'sp_admin' && Auth()->user()->user_type != 'supervisor') {
                $data_query = $result->where(['priorities.user_id'=>$users['project_user_id']])->groupby('priorities.id')->orderby('contract_priorities.id', 'desc')->get();
            }
            else
            {
                $data_query = $result->groupby('priorities.id')->orderby('contract_priorities.id', 'desc')->get();
            }
        }
        else
        {
            $data_query = $result->groupby('priorities.id')->orderby('contract_priorities.id', 'desc')->get();
        }
        foreach( $data_query as $dq){
            if($dq->deleted_at != '' || $dq->is_deleted == 'yes'){
                $dq->priority_level = $dq->priority_level.__('general_sentence.modal.deleted');

            }
        }

        return json_decode(json_encode($data_query), true);
    }


    public static function retainLongestPriority()
    {
        AssetCategory::with('priority')->each(function ($category) {
            self::retainLongestPriorityForCategory($category, 'App\\Models\\AssetCategory');
        });

        ContractAssetCategories::with('priority')->each(function ($category) {
            self::retainLongestPriorityForCategory($category, 'App\\Models\\ContractAssetCategories');
        });
    }

    public static function retainLongestPriorityForCategory($category, $type)
    {
        $prioritiesWithTimes = $category->priority->map(function ($priority) {
            $timeInMinutes = self::convertToMinutes($priority->response_time, $priority->response_time_type);
            return ['id' => $priority->id, 'time_in_minutes' => $timeInMinutes];
        });

        $longestPriority = $prioritiesWithTimes->sortByDesc('time_in_minutes')->first();

        if($longestPriority != null) {
            \DB::table('prioritables')
                ->where('prioritables_id', $category->id)
                ->where('prioritables_type', $type)
                ->whereNotIn('priorities_id', [$longestPriority['id']])
                ->delete();
        }
    }

    public static function convertToMinutes($time, $type)
    {
        switch (strtolower($type)) {
            case 'minutes':
                return $time;
            case 'hours':
                return $time * 60;
            case 'days':
                return $time * 60 * 24;
            default:
                return 0;
        }
    }
}
