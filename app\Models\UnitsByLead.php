<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\RoomsTypeFloors;

class UnitsByLead extends Model
{
    use HasFactory;

        protected $table = 'units_by_lead';

        protected $fillable = [
        'lead_id',
        'unit_id'
        ];  

        public $timestamps = true;


          public function unit()
    {
        return $this->belongsTo(RoomsTypeFloors::class, 'unit_id');
    }

}
