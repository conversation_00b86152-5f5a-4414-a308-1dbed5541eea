<?php

namespace App\Models;

use App\Models\Country;
use App\Models\WorkerAttendances;
use App\Models\{ServiceProvider};
use Illuminate\Support\Facades\DB;
use Laravel\Passport\HasApiTokens;
use Illuminate\Support\Facades\Log;
use App\Notifications\PasswordReset;
use Illuminate\Support\Facades\Cache;
use Spatie\Permission\Traits\HasRoles;
use App\Http\Helpers\ImagesUploadHelper;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Response;
use OwenIt\Auditing\Contracts\Auditable;
use NhcPartnership\Api\Models\UserProperty;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

/**
 * @property int $id
 * @property string $email
 * @property string $password
 * @property string $name
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $phone
 * @property string|null $profile_img
 * @property string|null $emp_id
 * @property int|null $profession_id
 * @property string|null $emp_dept
 * @property string|null $building_ids
 * @property string|null $contract_ids
 * @property string|null $supervisor_id
 * @property int|null $sp_admin_id
 * @property string|null $address
 * @property int $country_id
 * @property int $city_id
 * @property string|null $role_regions
 * @property string|null $role_cities
 * @property string|null $asset_categories
 * @property string|null $keeper_warehouses
 * @property string|null $properties
 * @property string|null $contracts
 * @property string|null $beneficiary
 * @property string|null $service_provider
 * @property string $user_type
 * @property mixed|null $user_privileges
 * @property int|null $approved_max_amount
 * @property int $created_by
 * @property int|null $project_id
 * @property int $project_user_id
 * @property string|null $device_token
 * @property string $device_type
 * @property string|null $api_token
 * @property string|null $otp
 * @property string|null $apartment
 * @property string|null $unit_receival_date
 * @property string|null $unit_receival_later_clicked_at
 * @property string $langForSms
 * @property int $otp_verified
 * @property int $email_verified
 * @property int $email_attempts
 * @property string|null $last_email_attempt_at
 * @property bool $allow_akaunting
 * @property int $status
 * @property string $is_deleted
 * @property string $created_at
 * @property string|null $modified_at
 * @property string|null $save_later_date
 * @property string $favorite_language
 * @property string|null $last_ip
 * @property string|null $deleted_at
 * @property string|null $last_login_datetime
 * @property string|null $temp_password
 * @property string|null $otp_for_password
 * @property int $otp_for_password_verified
 * @property string $selected_app_language
 * @property string|null $temp_phone_number
 * @property bool $is_subcontractors_worker
 * @property bool $first_login
 * @property bool $is_unit_link
 * @property string|null $later_booking_alert
 * @property int|null $akaunting_vendor_id
 * @property int|null $akaunting_customer_id
 *
 * @property ServiceProvider|null $serviceProvider
 * @property ProjectsDetails $projectDetails
 * @property UserCompany|null $userCompany
 * @property WorkerProfession|null $profession
 */
class User extends Authenticatable implements Auditable
{
    use HasApiTokens, HasFactory, Notifiable;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;
    use HasRoles;
    protected $table = 'users';

    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'modified_at';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'allow_akaunting',
        'email',
        'password',
        'name',
        'first_name',
        'last_name',
        'apartment',
        'unit_receival_date',
        'later_booking_alert',
        'phone',
        'profile_img',
        'address',
        'country_id',
        'city_id',
        'role_regions',
        'role_cities',
        'asset_categories',
        'properties',
        'contracts',
        'beneficiary',
        'service_provider',
        'user_type',
        'project_id',
        'project_user_id',
        'created_by',
        'status',
        'user_privileges',
        'approved_max_amount',
        'emp_id',
        'profession_id',
        'emp_dept',
        'building_ids',
        'contract_ids',
        'supervisor_id',
        'sp_admin_id',
        'langForSms',
        'deleted_at',
        'otp',
        'temp_password',
        'otp_for_password',
        'otp_for_password_verified',
        'temp_phone_number',
        'favorite_language',
        'is_subcontractors_worker',
        'keeper_warehouses',
        'save_later_date',
        'first_login',
        'is_unit_link',
        'akaunting_vendor_id',
        'akaunting_customer_id',
        'crm_api_token',
        'workspace_slug',
        'is_bma_area_manager',
        'assigned_workers'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public static function boot()
    {
        parent::boot();

        static::saving(function (User $user) {
            self::updateAllowAkaunting($user);
        });
    }


    private static function updateAllowAkaunting(User $user)
    {
            $projectsDetails = null;

            if ($user->projectDetails){
                $projectsDetails = $user->projectDetails;
            }else{
                $projects = $user->projectsMany;

                if ($projects->count() > 0) {
                    $projectsDetails = $projects->first();
                }
            }

            if ($projectsDetails && $projectsDetails->use_erp_module) {
                $user->allow_akaunting = 1;
            } else {
                $user->allow_akaunting = 0;
            }
    }

    public function userCompany()
    {
        return $this->hasOne(UserCompany::class, 'user_id');
    }

    public function projectDetails()
    {
      return $this->belongsTo(ProjectsDetails::class, 'project_id');
    }

    public function commercialContracts()
    {
        return $this->hasMany(CommercialContracts::class, 'tenant_id'); // A tenant can have many contracts
    }


    public function building()
    {
        return $this->belongsTo(PropertyBuildings::class, 'building_ids'); // Assuming user can have only one building
    }

    public function userProperties() {
        return $this->hasMany(Property::class, 'user_id', 'project_user_id');
    }
    public function userSubPrivileges() {
        return $this->hasMany(UserSubPrivileges::class, 'privilage_user_id');
    }
    public function assets() {
        return $this->hasMany(Asset::class, 'user_id', 'project_user_id');
    }

    public function assetCategories() {
        return $this->hasMany(AssetCategory::class, 'user_id', 'project_user_id');
    }

    public function frequencies() {
        return $this->hasMany(Frequencies::class, 'user_id', 'project_user_id');
    }

    public function workOrders() {
        return $this->hasMany(WorkOrders::class, 'created_by');
    }

    public function isSuperAdmin()
    {
        return $this->user_type === 'super_admin';
    }

    public function isOsoolAdmin()
    {
        return $this->user_type === 'osool_admin';
    }

    public function isProjectOwner()
    {
        return $this->user_type === 'admin';
    }

    public function isProjectOwnerEmployee()
    {
        return $this->user_type === 'admin_employee';
    }

    public function isBuildingManager()
    {
        return $this->user_type === 'building_manager';
    }

    public function isBuildingManagerEmployee()
    {
        return $this->user_type === 'building_manager_employee';
    }

    public function isServiceProviderAdmin()
    {
        return $this->user_type === 'sp_admin';
    }

    public function isSupervisor()
    {
        return $this->user_type === 'supervisor';
    }

    public function isTenant()
    {
        return $this->user_type === 'tenant';
    }

    public function serviceProvider()
    {
        return $this->belongsTo(serviceProvider::class, 'service_provider');
    }

    public function buildingManagerProperties() {
        return $this->belongsTo(User::class, 'sp_admin_id')
            ->select('id', 'properties');
    }

    public function contracts()
    {
        return $this->hasMany(Contracts::class, 'service_provider_id', 'service_provider');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function approvedLeave()
    {
        return $this->hasOne(ManageWorkerAvailabilityStatus::class, 'worker_id')
            ->where('approval_status', 'approved')
            ->where('from_datetime', '<=', now())
            ->where('to_datetime', '>=', now())
            ->where('is_leave_terminated', 0)
            ->latest();
    }

    public function approvedLeaveReason()
    {
        return $this->approvedLeave()->with('reasonType');
    }
    public function contractPropertyBuildings()
    {
        return $this->hasManyThrough(
            ContractPropertyBuildings::class,
            Contracts::class,
            'service_provider_id',
            'contract_id',
            'service_provider',
            'id'
        );
    }

    public function properties()
    {
        return $this->hasManyThrough(
            Property::class,
            ContractPropertyBuildings::class,
            'property_building_id',
            'id',
            'id',
            'property_id'
        );
    }

    public function getAssociatedContracts($user)
    {
      // Eager load the necessary relationships
      $this->load('contractPropertyBuildings.propertyBuilding.property');

      // Get all the unique property_ids associated with the user's contracts
      $propertyIds = $this->contractPropertyBuildings->pluck('propertyBuilding.property.id')->unique()->toArray();

      return $propertyIds;
    }

    public function filterPropertiesByContracts($propertiesQuery, $user)
    {
        $contracts = $this->getAssociatedContracts($user);
        if (!empty($contracts)) {
            $propertiesQuery->whereIn('properties.id', $contracts);
        }
    }

    public function assetCategoryIds()
    {
        return $this->hasManyThrough(ContractAssetCategories::class, Contracts::class, 'service_provider_id', 'contract_number', 'service_provider', 'contract_number')
            ->select('asset_category_id');
    }

    public function spAdmin()
    {
        return $this->belongsTo(User::class, 'sp_admin_id');
    }

    public function supervisors()
    {
        return $this->hasMany(User::class, 'sp_admin_id');
    }

    public function projects()
    {
        return $this->hasMany(ProjectsDetails::class, 'user_id');
    }

    public function projectsMany()
    {
        return $this->belongsToMany(ProjectsDetails::class, 'user_projects', 'user_id', 'project_id');
    }

    public function userProjects()
    {
        return $this->hasMany(UserProject::class, 'user_id');
    }

    public function profession()
    {
        return $this->belongsTo(WorkerProfession::class, 'profession_id');
    }

    public function latestAttendance()
    {
        return $this->hasOne(WorkerAttendances::class, 'worker_id')
                    ->latestOfMany(); // gets the most recent attendance
    }

    /**
     * Define a one-to-many relationship with the WorkOrdersColumn model.
     *
     * This relationship represents the work order columns associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function workOrderColumns()
    {
        // Establish a one-to-many relationship with the WorkOrdersColumn model
        // where the 'user_id' column in the WorkOrdersColumn model corresponds
        // to the 'id' column in the User model.
        return $this->hasMany(WorkOrdersColumn::class, 'user_id');
    }

    // Define the relationship to TenantContactNumber
    public function tenantContactNumber() {
        return $this->hasOne(TenantContactNumber::class, 'building_id', 'building_ids');
    }
    public function user_type_details() {
        return $this->hasOne(UserTypes::class, 'slug', 'user_type');
    }
    public function tenantAttachments()
    {
        return $this->hasMany(TenantAttachment::class, 'tenant_id');
    }

    public function tenantBuildingAttachments()
    {
        return $this->hasManyThrough(TenantBuildingAttachment::class, PropertyBuildings::class, 'user_id', 'building_id');
    }

    public function chatrooms()
    {
        return $this->hasMany(Chatroom::class, 'tenant_id');
    }

    public function complaints()
    {
        return $this->belongsToMany(Complaint::class);
    }

    public function serviceProviderWorkers() {
        return $this->hasMany(User::class, 'service_provider', 'service_provider')->where('user_type', 'sp_worker');
    }

    public function supervisorsUsingServiceProvider()
    {
        return $this->hasMany(User::class, 'service_provider');
    }

    public function adminWorkers() {
        return $this->hasMany(User::class, 'project_user_id', 'project_user_id')->where('user_type', 'sp_worker');
    }

    public function getProfileImageUrl()
    {
        if ($this->profile_img) {
            return ImagesUploadHelper::displayImage($this->profile_img, 'uploads/profile_images');
        } else {
            // Return a default image URL if no profile image is set
            return asset('/uploads/profile_images/dummy_profile_image.png'); // Replace with your default image URL
        }
    }


    public function getProfileImageUrlAttribute()
    {
        if ($this->profile_img) {
            return ImagesUploadHelper::displayImage($this->profile_img, 'uploads/profile_images');
        } else {
            // Return a default image URL if no profile image is set
            return asset('/uploads/profile_images/dummy_profile_image.png'); // Replace with your default image URL
        }
    }

    public function assignedWorkOrders()
    {
        return $this->belongsToMany(WorkOrder::class, 'work_order_workers_timings', 'worker_id', 'work_order_id')
            ->using(WorkOrderWorkerTiming::class)
            ->withPivot('start_time', 'end_time');
    }

    public static function existingcompanyList($users)
    {
        $existingcompanyList=User::select('service_provider')
        ->where('project_user_id',$users->project_user_id)
        ->where('user_type','sp_admin')->where('is_deleted','no')->where('status',1)->get();
        $existingcompanyArr=array();
        if(isset($existingcompanyList)) //Check if Company exists
        {
          foreach ($existingcompanyList as $key=>$value){
            $existingcompanyArr[]=$value->service_provider;
          }
        }
        return $existingcompanyArr;
    }

    public static function getcompanyList($users)
    {
        $is_supervisors= 'yes';
        if($users->user_type == 'sp_admin') //For SP Admin
        {
          $company_list=ServiceProvider::where('user_id',$users->project_user_id)
          ->where('is_deleted','no')->where('status',1)
          ->whereNotExists(function($query)
          {
              $query->select(DB::raw(1))
                    ->from('users')
                    ->whereRaw('users.service_provider = service_providers.id');
          })
          ->get();

          $supervisors=User::where('user_type','supervisor')
          ->where('is_deleted','no')
          ->where('service_provider',$users->service_provider)
          ->where('status',1)->count();
          if($supervisors ==0){
            $is_supervisors= 'no';
          }

        } else //For other users
        {
          $company_list=ServiceProvider::where('user_id',$users->project_user_id)
          ->where('is_deleted','no')->where('status',1)
          ->get();
        }
        return [
            'company_list' => $company_list,
            'is_supervisors' => $is_supervisors
        ];
    }
    public static function getUsersWithPUIUserType($user_type, $project_user_id)
    {
        return User::where('user_type',$user_type)->where('project_user_id',$project_user_id)->where('is_deleted','no')->where('status',1)->get();
    }

    public static function getSPSupervisors($users)
    {
        return User::where(['user_type'=>'supervisor', 'service_provider' => $users->service_provider, 'is_deleted' => 'no', 'status' => 1])->get();
    }

    public static function getBuildingManagers($users)
    {
      return SELF::select('users.id','users.name')->leftJoin('service_providers','service_providers.id','=','users.service_provider')
              ->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ")
              ->whereRaw("( `users`.`user_type` = 'building_manager')")
              ->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])
              ->where('users.project_user_id', $users->project_user_id)
              ->orderBy('users.user_type')
              ->get()->toArray();
    }

    public static function getBuildingManagerEmployees($users, $ids)
    {
      return SELF::select('users.id','users.name')->leftJoin('service_providers','service_providers.id','=','users.service_provider')
              ->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ")
              ->whereRaw("(`users`.`user_type` = 'building_manager_employee')")
              ->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])
              ->where('users.project_user_id', $users->project_user_id)
              ->whereIn('users.sp_admin_id', $ids)
              ->orderBy('users.user_type')
              ->get()->toArray();
    }

    public function getBuildingManagerEmployeesList()
    {
        return User::select('users.id','users.name')->leftJoin('service_providers','service_providers.id','=','users.service_provider')
              ->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ")
              ->whereRaw("(`users`.`user_type` = 'building_manager_employee')")
              ->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])
              ->where('users.sp_admin_id', $this->id)
              ->orderBy('users.user_type')
              ->get();

    }

    public static function getSupervisors($users, $ids)
    {
      return SELF::select('users.id','users.name','users.user_type')->leftJoin('service_providers','service_providers.id','=','users.service_provider')
              ->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ")
              ->where('users.user_type', 'supervisor')
              ->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])
              ->where('users.project_user_id', $users->project_user_id)
              ->where(function ($query) use ($ids) {
                foreach ($ids as $value) {
                    $query->orWhereRaw("FIND_IN_SET('$value', contract_ids) > 0");
                }
              })
              ->orderBy('users.id','desc')
              ->get()->toArray();
    }


    public function syncWarehouses($warehousesIds)
    {
        DB::beginTransaction();
        if (!is_null($warehousesIds)) {
            $warehousesIdsAsArray = explode(",", $warehousesIds);
            DB::table('user_warehouses')
                ->where('user_id', '=', $this->id)
                ->whereNotIn('warehouse_id', $warehousesIdsAsArray)
                ->delete();
            foreach ($warehousesIdsAsArray as $warehouseId) {
                DB::table('user_warehouses')->UpdateOrinsert([
                    'user_id' => $this->id,
                    'warehouse_id' => $warehouseId,
                ], [
                    'created_at' => now()
                ]);
            }
        }

        DB::commit();
        return $this;
    }

    public function getWarehouses()
    {
        return DB::table('user_warehouses')
            ->select(['warehouse_id'])
            ->where('user_id', '=', $this->id)
            ->get()->toArray();
    }
    public function getProjectIdAttribute(){
        return $this->attributes['project_id'] ?? null;
    }


    public static function updateUserProject($userId, $projectId)
    {
        return self::where('id', $userId)
            ->update([
                'project_id' => $projectId,
                'project_user_id' => $userId,
            ]);
    }
    public function assignRole($role)
    {
        $this->syncRoles([$role]);
    }

    public function updateAuthUserProject()
    {
        $authUser = Auth::user();
        $authUser->project_user_id = $this->id;
        $authUser->save();
    }

    public function hasPermission($permissions)
        {
            try {
            if (is_array($permissions)) {
                $permissionsCount = \DB::table('model_has_permissions')
                    ->join('permissions', 'model_has_permissions.permission_id', '=', 'permissions.id')
                    ->where('model_has_permissions.model_id', $this->id)
                    ->where('model_has_permissions.model_type', User::class)
                    ->whereIn('permissions.name', $permissions)
                    ->count();

                return $permissionsCount === count($permissions);
            }

            return \DB::table('model_has_permissions')
                ->join('permissions', 'model_has_permissions.permission_id', '=', 'permissions.id')
                ->where('model_has_permissions.model_id', $this->id)
                ->where('model_has_permissions.model_type', User::class)
                ->where('permissions.name', $permissions)
                ->exists();
            } catch (\Throwable $th) {
                Log::info("hasPermission Error:" ,$th);
            }
        }

    public function permissionPa()
    {
        return $this->hasOne(PermissionPAAccess::class, 'user_id');
    }

        public static function getUserFullNameByID($user_id){
            try{
                $name = User::select('name')->where('id', $user_id)->first();
                return $name;
            } catch ( \Throwable $th ) {
                Log::info(['getUserByID Delete'=>$th]);
                return false;
            }
        }


        public function sendPasswordResetNotification($token)
{
    $this->notify(new PasswordReset($token));
}
    public function beneficiaries()
    {
        return $this->hasMany(Beneficiary::class, 'user_id', $this->project_user_id);
    }

   public function scopeBuildingManagers($query)
    {
        return $query->where('is_deleted', 'no')->where('user_type', 'building_manager');
    }

    public function isDeleted(){
        return !is_null($this->deleted_at);
    }

    public function crmUser()
    {
        return $this->hasOne(CrmUser::class, 'user_id', 'id');
    }

    public function isVendorServiceProvider()
    {

    }

    public function vendorProfile()
    {
        return $this->hasOne(VendorProfile::class,'service_provider_id','service_provider');
    }

    public function getIsCrmUserAttribute()
    {
        return ProjectsDetails::where('id', $this->project_id)->where('use_crm_module', 1)->exists() && CrmUser::where('user_id', $this->id)->exists() && !in_array($this->user_type, ['super_admin', 'osool_admin']);
    }

    public function getWorkspaceAttribute()
    {
        return $this->workspace_slug ?? Cache::get('workspace_slug_'.$this->email, 'none');
    }

    public function getCrmApiTokenAttribute($value)
    {
        return $value ?? Cache::get('crm_api_token_'.$this->email);
    }
        
    public function nhcProperty()
    {
        return $this->hasOne(UserProperty::class);
    }
    
    public function isBuildingAdminAreaManager(): bool
    {
        return $this->is_bma_area_manager === 1;
    }

    public function adminEmployee(){
        return $this->belongsTo(User::class, 'sp_admin_id');
    }

    public function adminSupervisor(){
        return $this->belongsTo(User::class, 'sp_admin_id');
    }

    public function adminWorker(){
        return $this->hasMany(User::class, 'supervisor_id');
    }
}
