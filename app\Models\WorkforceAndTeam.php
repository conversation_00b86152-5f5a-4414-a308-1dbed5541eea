<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WorkforceAndTeam extends Model
{
    use HasFactory;

    // Define the fillable fields
    protected $fillable = [
        'contract_id',
        'role',
        'proficiency',
        'quantity',
        'deduction_rate',
        'working_days',
        'localization_target',
        'working_hours',
        'attendance_mandatory',
        'minimum_wage',
        'uniform_and_tools_mandatory',
        'user_id',
    ];

    public function getAttendanceMandatoryTextAttribute()
    {
        return $this->attendance_mandatory ? 'Yes' : 'No';
    }

    public function getUniformAndToolsMandatoryTextAttribute()
{
    return $this->uniform_and_tools_mandatory ? 'Yes' : 'No';
}

}
