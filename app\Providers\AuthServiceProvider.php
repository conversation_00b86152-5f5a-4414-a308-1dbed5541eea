<?php

namespace App\Providers;
use App\Auth\CustomPassportUserProvider;
use Illuminate\Auth\RequestGuard;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Passport\ClientRepository;
use Lara<PERSON>\Passport\Guards\TokenGuard;
use Lara<PERSON>\Passport\Passport;
use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Laravel\Passport\TokenRepository;
use League\OAuth2\Server\ResourceServer;
use App\Gates\ServiceProviderGate;
use App\Models\WorkOrders;
use App\Policies\WorkOrderPolicy;


class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        'App\Models\Model' => 'App\Policies\ModelPolicy',
        WorkOrders::class  => WorkOrderPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Passport::routes();

        Auth::extend('passport', function ($app, $name, array $config) {
            return new RequestGuard(function ($request) use ($config) {
                return (new TokenGuard(
                    $this->app->make(ResourceServer::class),
                    new CustomPassportUserProvider(Auth::createUserProvider($config['provider']), $config['provider']),
                    $this->app->make(TokenRepository::class),
                    $this->app->make(ClientRepository::class),
                    $this->app->make('encrypter')
                ))->user($request);
            }, $this->app['request']);
        });

        // Implicitly grant "Super Admin" role all permissions
        // This works in the app by using gate-related functions like auth()->user->can() and @can()
        // Make sure to seed "model_has_roles" table with "super_admin" data
        // In our case : ('1', 'App\\Models\\User', '21')
        Gate::before(function ($user, $ability) {
            return $user->hasRole('super_admin') ? true : null;
        });
        //Gate::define('view-work-order-summary', [ServiceProviderGate::class, 'accesssServiceProvider']);
    }

}
