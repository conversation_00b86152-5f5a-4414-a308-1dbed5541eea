<?php
namespace App\QueryBuilders;

use App\Enums\WorkOrderStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class WorkOrderQueryBuilder extends Builder
{
    public function forServiceProvider(int $serviceProviderId): self
    {
        return $this->whereHas('contract', function ($query) use ($serviceProviderId) {
            $query->where('service_provider_id', $serviceProviderId);
        });
    }

    public function forBuildingManager(int $managerId): self
    {
        return $this->whereHas('propertyBuilding', fn($q) => $q->where('manager_id', $managerId));
    }

    public function forSupervisor(int $supervisorId): self
    {
        return $this->whereRaw("FIND_IN_SET(?, supervisor_id)", [$supervisorId]);
    }

    public function forServiceProviderUser(int $userId): self
    {
        return $this->whereHas('contract.serviceProvider.users', fn($q) => $q->where('id', $userId));
    }

    public function withCommonRelations(): self
    {
        return $this->select([
            'id',
            'description',
            'status',
            'workorder_journey',
            'contract_type',
            'work_order_id',
            'work_order_type',
            'response_time',
            'sp_approove',
            'start_date',
            'wtf_start_time',
            'job_started_at',
            'contract_type',
            'priority_id as wo_priority_id',
            'priority_id',
        ])
        ->with([
            'contract' => function ($query) {
                $query->select('id', 'service_provider_id', 'contract_number');
            },
            'slaAssetCategory' => function ($query) {
                $query->select('id', 'contract_number');
            },
            'frequencyMaster.frequencies.contractFrequencies' => function ($query) {
                $query->select('id', 'contract_number', 'response_time', 'service_window', 'response_time_type', 'service_window_type');
            }
        ]);
    }

    public function withPassFailStatus()
    {
        return $this->addSelect('pass_fail');
    }

    public function regularContract(): self
    {
        return $this->where('contract_type', 'regular');
    }

    // Status filters
    public function statusOpen(): self
    {
        return $this
            ->where('status', WorkOrderStatus::Open->value)
            ->where('workorder_journey', 'submitted');
    }

    public function statusInProgress(): self
    {
        return $this->where('workorder_journey', 'job_execution');
    }

    public function statusUnderEvaluation(): self
    {
        return $this->where(fn($innerQuery) => $innerQuery
                ->where('workorder_journey', 'job_evaluation')
                ->orWhere('workorder_journey', 'job_approval')
        );

    }

    public function statusClosed(): self
    {
        return $this->where('status', WorkOrderStatus::Closed->value);
    }

    public function statusReopened(): self
    {
        return $this->where('status', WorkOrderStatus::Reopened->value);
    }
    public function statusOnHold(): self
    {
        return $this->where('status', WorkOrderStatus::OnHold->value);
    }
    public function notDeleted(): self
    {
        return $this->where('is_deleted', '<>', 'yes');
    }
    public function forBuildingManagerPending(): self
    {
        return $this
        // ->whereIn('property_id', $buildingIds)
        //     ->whereIn('asset_category_id', $assetCategories)
        //     ->when(! is_null($serviceProviderIds) && is_array($serviceProviderIds), function ($query) use ($contracts) {
        //         $query->whereIn('contract_id', $contracts);
        //     })
            ->where(function ($query) {
                $query->where('contract_type', 'warranty')
                    ->where('status', '<>', 4)
                    ->orWhere(function ($subquery) {
                        $subquery->where('contract_type', 'regular')
                            ->where('status', 1)
                            ->whereNotNull('proposed_new_date')
                            ->where('bm_approve_issue', 0);
                    })
                    ->orWhere(function ($subquery) {
                        $subquery->where('contract_type', 'regular')
                            ->whereIn('status', [2, 6])
                            ->where('workorder_journey', 'job_approval')
                            ->whereIn('sp_approve_job', [2, 3])
                            ->where('bm_approve_job', 0);
                    })
                    ->orWhere(function ($subquery) {
                        $subquery->where('contract_type', 'regular')
                            ->where('status', 6)
                            ->where('workorder_journey', 'submitted')
                            ->where('sp_reopen_status', 1);
                    })
                    ->orWhere('status', 5); // Pending status
            });
    }
    public function forServiceProviderAdminPending(): self
    {
        return $this->whereRaw("(((contract_type = 'regular' and (status = 2 OR status = 6) and workorder_journey = 'job_evaluation') and sp_approve_job != 1))");
    }

    public function forSupervisorPending(int $userId): self
    {
        return $this->whereRaw("find_in_set($userId, supervisor_id)")
            ->whereRaw("(((contract_type = 'regular' and status = 1) and proposed_new_date IS NULL and bm_approve_issue = 0)
                    OR ((contract_type = 'regular' and status = 1) and proposed_new_date IS NOT NULL and bm_approve_issue = 1)
                    OR ((contract_type = 'regular' and (status = 6) and workorder_journey = 'job_execution'))
                    OR ((contract_type = 'regular' and (status = 6) and workorder_journey = 'job_evaluation') and sp_approve_job != 1)
                    OR ((contract_type = 'regular' and status = 6 and workorder_journey = 'submitted') and sp_reopen_status = 0))");
    }

    public function forSpAdminOrSupervisorSpareParts(): self
    {
        return $this->whereHas('workOrderItemRequests', function ($query) {
            $query->where('worker_spare_parts_requests.status', 'requested')
                ->where('sent_to_project_owner', 0);
        });
    }

    public function forAdminOrEmployeeSpareParts(): self
    {
        return $this->where(function ($query) {
            $query->whereHas('workOrderItemRequests', function ($subQuery) {
                $subQuery->where('worker_spare_parts_requests.status', 'requested')
                    ->where('sent_to_project_owner', 1);
            })
                ->orWhereHas('serviceProviderMissingItemRequests', function ($subQuery) {
                    $subQuery->where('service_provider_missing_items_requests.status', 'requested');
                });
        });
    }
    public function forSpAdminRequests(int $userId): self
    {
        return $this->where('service_provider_id', '=', $userId);
    }

    public function forSupervisorRequests(int $userId): self
    {
        return $this->whereRaw("find_in_set(?, supervisor_id)", [$userId]);
    }

    public function forRequestStatus(): self
    {
        return $this->whereRaw("(((contract_type = 'regular' and (status = 2 OR status = 6)
        and workorder_journey = 'job_evaluation') and sp_approve_job != 1))");
    }

    public function forNotAdmin($projectUserId): self
    {
        return $this->where('project_user_id', $projectUserId);
    }

    public function activeByDate(): self
    {
        return $this->where('start_date', '<=', Carbon::now());
    }

    public function forSpRequests(?array $contracts): self
    {
        return $this
            ->whereIn('contract_id', $contracts)
            ->where('contract_type', 'regular')
            ->where(fn($query) =>
                $query->where(fn($q) =>
                    $q->where('status', 1)
                        ->whereNull('proposed_new_date')
                        ->where('bm_approve_issue', 0)
                )->orWhere(fn($q) =>
                    $q->where('status', 1)
                        ->whereNotNull('proposed_new_date')
                        ->where('bm_approve_issue', 1)
                )->orWhere(fn($q) =>
                    $q->where('status', 6)
                        ->where('workorder_journey', 'job_execution')
                )->orWhere(fn($q) =>
                    $q->where('status', 6)
                        ->where('workorder_journey', 'job_evaluation')
                        ->where('sp_approve_job', '!=', 1)
                )->orWhere(fn($q) =>
                    $q->where('status', 6)
                        ->where('workorder_journey', 'submitted')
                        ->where('sp_reopen_status', 0)
                )
            );
    }
    public function forSupervisorRequest(int $userId): self
    {
        return $this
         ->whereRaw("FIND_IN_SET(?, work_orders.supervisor_id)", [$userId])
         ->where('work_orders.contract_type', 'regular')
         ->where(fn($query) =>
             $query->where(fn($q) =>
                 $q->where('work_orders.status', 1)
                   ->whereNull('work_orders.proposed_new_date')
                   ->where('work_orders.bm_approve_issue', 0)
             )->orWhere(fn($q) =>
                 $q->where('work_orders.status', 1)
                   ->whereNotNull('work_orders.proposed_new_date')
                   ->where('work_orders.bm_approve_issue', 1)
             )->orWhere(fn($q) =>
                 $q->where('work_orders.status', 6)
                   ->where('work_orders.workorder_journey', 'job_execution')
             )->orWhere(fn($q) =>
                 $q->where('work_orders.status', 6)
                   ->where('work_orders.workorder_journey', 'job_evaluation')
                   ->where('work_orders.sp_approve_job', '!=', 1)
             )->orWhere(fn($q) =>
                 $q->where('work_orders.status', 6)
                   ->where('work_orders.workorder_journey', 'submitted')
                   ->where('work_orders.sp_reopen_status', 0)
             )
         );
    }
}
