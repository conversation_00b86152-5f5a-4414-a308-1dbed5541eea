<?php

namespace App\Services;


use App\Models\City;

use App\Models\Region;

use App\Models\Priority;
use App\Models\Contracts;
use App\Models\Priorities;
use Illuminate\Support\Str;
use App\Http\Helpers\Helper;
use App\Models\AssetCategory;
use App\Models\ContractMonth;
use App\Models\WorkforceAndTeam;
use App\Models\PropertyBuildings;
use App\Models\ContractServiceKpi;
use App\Models\ContractUsableItem;
use Illuminate\Support\Facades\DB;
use App\Http\Traits\FunctionsTrait;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Illuminate\Support\Facades\Auth;
use App\Models\SmartAssigningContract;
use App\Models\ContractPropertyBuildings;
use App\Models\AdvanceContractDraftApproval;
use App\Models\contractTenantServiceCategories;
use App\Http\Traits\SmartAssigningContractTrait;

class AdvanceContractService
{
      use  FunctionsTrait, SmartAssigningContractTrait;
    // Method to get contract-related data
    public function getContractData($contractId)
    {
        // Decrypt the contract ID
        $id = base64_decode($contractId);
        
        // Fetch contract with related data
        $contract = Contracts::with([
                        'serviceProvider', 
                        'contractPriorities.exportPriority', 
                        'contractMonths', 
                        'contractServiceKpis.assetCategory', 
                        'workforceAndTeams', 
                        'advanceContractDraft'
                    ])->find($id);

        if (!$contract) {
            return null; // If contract not found
        }
        $service = new PerformanceIndicatorService();
        $overallPerformance = $service->getOverallPerformancePercentage(collect([$contract ]));

        // Fetch and group performance indicators
        $performanceIndicators = $contract->contractPerformanceIndicatorsRecord()
            ->orderBy('performance_indicator')
            ->orderBy('range_id')
            ->get();

        $groupedIndicators = [];
        foreach ($performanceIndicators as $indicator) {
            $groupedIndicators[$indicator->performance_indicator][] = $indicator;
        }
        $locale = App::getLocale(); // 'en' or 'ar'
         // Load contract properties
        $contractProperties = $this->loadContractProperties($contract->id);
        // Compile the contract data
        $contractData = [
            'id'                     =>  $contract->id,
            'contract'               => $contract,
            'groupedIndicators'      => collect($groupedIndicators),
            'buildings'              => ContractPropertyBuildings::where('contract_id', $id)->get(),
            'totalUnits'             => 0, // Initialized to 0, will be calculated later
            'totalZones'             => 0, // Same here
            'total'                  => 0, // Same here
            'installmentsCount'      => $contract->contractMonths->count(),
            'serviceProvider'        => $contract->serviceProvider,
            'startDate'              => date('d-m-Y', strtotime($contract->start_date)),
            'endDate'                => date('d-m-Y', strtotime($contract->end_date)),
            'serviceType'            => $this->getAssetCategorie($contract->asset_categories),
            'assetsName'             => $this->getAssetName($contract->asset_names),
            'percentage'             => $this->calculatePercentage($contract->start_date, $contract->end_date),
            'assetCategories'        => $this->getAssetCategories($contract, $id),
            'smartAssignedServices'  => $this->getSmartAssignedServices($contract, $id),
            'items'                  => $this->getUsableItems($contract),
            'overallPerformance'     =>  $overallPerformance,
            'cityNames'              => $this->fetchCities($locale,$contract->city_id),
            'regionNames'            => $this->fetchRegionNames($locale,$contract->city_id),
            'propertyNames'          => $contractProperties['propertyNames'],  // Added property names
            'propertyNamesLimited'   => $contractProperties['propertyNamesLimited'], // Added limited property names
          
    
        ];

        // Calculate totals based on buildings
        $contractData['totalUnits'] = $contractData['buildings']->sum('units_count');
        $contractData['totalZones'] = $contractData['buildings']->sum('zones_count');
        $contractData['total'] = $contractData['buildings']->count();
        $contractData['perMonthAmount'] = $contract->contractMonths->avg('amount');
        $contractData['startDate'] = date('d-m-Y', strtotime($contract->start_date));
        $contractData['endDate'] = date('d-m-Y', strtotime($contract->end_date));

        return $contractData;
    }

    public function transformDraftData($draft){
        
        $mainInformation = $draft->main_information_data;
        $extraData = $draft->extra_data;
        $team = $draft->workforce_team_data;
        $kpiData = $draft->data_agreement_kpi_data; // <-- KPI data
        $assetData = $draft->assets_ppm_data;
        
        $sideFeatures = $kpiData['sideFeatures'] ?? [];
        $allowSub    =  !empty($sideFeatures['allowSub']) ? 1 : 0;
        $smartAssign =  !empty($sideFeatures['smartAssign']) ? 1 : 0;
        $tenant      =  !empty($sideFeatures['tenant']) ? 1 : 0;

        $locale = App::getLocale(); // 'en' or 'ar'

        $serviceIds = implode(',', array_map(function ($serviceRow) {
                return $serviceRow['service_id'];
            }, $kpiData['servicesRows']));

        $draftMonths = $mainInformation ['months'] ?? [];
        $draftAvgAmount = collect($draftMonths)->avg('amount'); 
              
        $contractDraftData = [
            'approver_id'           => $draft->approver_id,
            'groupedIndicators'      => $kpiData['selectedKpis'],
            'contract_number'        => $mainInformation['contract_name'],
            'buildings'              => $kpiData['property_details'],
            'comment'             => $extraData['comments'] ?? null,
            'picture'             => $extraData['file_path'] ?? null,
            'totalUnits'             => 0, // Initialized to 0, will be calculated later
            'totalZones'             => 0, // Same here
            'total'                  => 0, // Same here
            'paymentInterval'        =>  $mainInformation['interval'],
            'installmentsCount'      => count($draftMonths),
            'perMonthAmount'         =>  $draftAvgAmount,
            'contractValue'          =>  $mainInformation['amount'],
            'startDate'              => date('d-m-Y', strtotime($mainInformation['start_date'])),
            'endDate'                => date('d-m-Y', strtotime($mainInformation['end_date'])),
            'serviceType'            => $this->getAssetCategorie( $serviceIds),
            'assetsName'             => $this->getAssetName( isset($assetData['assets']) ? implode(',', $assetData['assets']) : null,),
            'percentage'             => $this->calculatePercentage($mainInformation['start_date'], $mainInformation['end_date']),
            'use_smart_assigning'       =>  $smartAssign,
            'use_form_of_unit_receival' =>  $tenant,
            'assetCategories'        => $this->getAssetCategoryNames($kpiData['selectedTenantCategories']),
            'allowSub'               =>  $allowSub,
            'smartAssignedServices'  => $this->getAssetCategoryNames($kpiData['selectedSmartAssignCategories']),
            'items' => $assetData['stocks'][$assetData['ownership']] ?? [],
            'cityNames'              => $this->fetchCities($locale,(isset($kpiData['selected_cities']) ? implode(',', $kpiData['selected_cities']) : null)),
            'regionNames'            => $this->fetchRegionNames($locale,(isset($kpiData['region_id']) ? implode(',', $kpiData['region_id']) : null)),
            'workforce_team'    => $team,
            'propertyNames'          => $kpiData['property_details'],  // Added property names
            'propertyNamesLimited'   => $kpiData['property_details'], // Added limited property names
            'contractServiceKpis'   =>  collect($kpiData['servicesRows'])->map(function ($row) {
                                            $service = AssetCategory::find($row['service_id']); // or use a cached collection if performance is critical

                                            return array_merge($row, [
                                                'assetCategory' => [
                                                    'service_name' => $service->asset_category ?? 'N/A',
                                                ],
                                            ]);
                                        })->toArray(),
            'contractPriorities'     => collect($kpiData['slaRows'])->map(function ($row) {
                                            $priority = Priorities::find($row['priority_id']); // or use a cached collection if performance is critical

                                            return array_merge($row, [
                                                'exportPriority' => [
                                                    'priority_level' => $priority->priority_level ?? 'N/A',
                                                ],
                                            ]);
                                        })->toArray(),];
            
        $contractDraftData['totalUnits'] = collect($contractDraftData['buildings'])->sum('units_count');
        $contractDraftData['totalZones'] = collect($contractDraftData['buildings'])->sum('zones_count');
        $contractDraftData['total'] = collect($contractDraftData['buildings'])->count();

        return $contractDraftData;
    }

    public function applyVariationOrder($draft){
         DB::transaction(function () use ($draft) {
            $draft->status =  'approved';
            $draft->save();
            $contract = Contracts::findOrFail($draft->contract_id);
        
            $mainInformation = $draft->main_information_data;
            $extraData = $draft->extra_data;
            $team = $draft->workforce_team_data;
            $kpiData = $draft->data_agreement_kpi_data; // <-- KPI data
            $assetData = $draft->assets_ppm_data;
            $sideFeatures = $kpiData['sideFeatures'] ?? [];

            $allowSub    =  !empty($sideFeatures['allowSub']) ? 1 : 0;
            $smartAssign =  !empty($sideFeatures['smartAssign']) ? 1 : 0;
            $tenant      =  !empty($sideFeatures['tenant']) ? 1 : 0;

            $user   =   Auth::user();
            $serviceIds = implode(',', array_map(function ($serviceRow) {
                return $serviceRow['service_id'];
            }, $kpiData['servicesRows']));
            
            $contract->update([
                'reference_uuid'             => $draft->uuid,
                'contract_number'            => $mainInformation['contract_name'],
                'contract_value'             => $mainInformation['amount'],
                'payment_interval'           => $mainInformation['interval'],
                'start_date'                 => date('Y-m-d', strtotime($mainInformation['start_date'])),
                'end_date'                   => date('Y-m-d', strtotime($mainInformation['end_date'])),
                'is_subcontract'             => $mainInformation['is_subcontract'],
                'parent_contract_id'         => $mainInformation['selected_subcontract_id'] ?? null,
                'comment'                    => $extraData['comments'] ?? null,
                'picture'                    => $extraData['file_path'] ?? null,
                'region_id'                  => isset($kpiData['region_id']) ? implode(',', $kpiData['region_id']) : null,
                'city_id'                    => isset($kpiData['selected_cities']) ? implode(',', $kpiData['selected_cities']) : null,
                'properties'                 => isset($kpiData['selected_properties']) ? implode(',', $kpiData['selected_properties']) : null,
                'warehouse_owner'            => $assetData['ownership'],
                'asset_categories'           => $serviceIds,
                'asset_names'                => isset($assetData['assets']) ? implode(',', $assetData['assets']) : null,
                'use_smart_assigning'        => $smartAssign,
                'allow_subcontract'          => $allowSub,
                'use_form_of_unit_receival'  => $tenant,
            ]);

            $contract->propertyBuildings()->delete();
            foreach($kpiData['property_details'] as $property)
            {
                $data = array(
                    'contract_id' => $contract->id,
                    'property_building_id' => $property['building_id'],
                    'building_name' => $property['building_name'],
                    'complex_name' => $property['complex_name'],
                    'property_type' => $property['property_type'],
                    'units_count' => $property['units_count'],
                    'zones_count' => $property['zones_count'],
                );
                DB::table('contract_property_buildings')->insert($data);
            }

            // Insert stock data into contract_usable_items table
            if (isset($assetData['stocks'][$assetData['ownership']])) {
               $usableItems = $contract->getUsableItems();

                // Get company_id from first usable item (if available)
                $companyId = optional($usableItems->first())->company_id;

                // Log it or use as needed
                Log::info('Company ID before deletion', ['company_id' => $companyId]);

                // Now delete the items
               $contract->getUsableItems()->delete();

                foreach ($assetData['stocks'][$assetData['ownership']] as $stock) {
                    ContractUsableItem::create([
                        'contract_id'    => $contract->id,
                        'company_id'      => $companyId,
                        'item_id'         => $stock['id'],
                        'price'           => $stock['price'],
                        'penalty'         => $stock['penalty'],
                        'approval'        => $stock['approval'],
                        'low_stock'       => $stock['low_stock'],
                        'mandatory'       => $stock['mandatory'],
                        'open_stock'      => $stock['open_stock'],
                        'penalty_type'    => $stock['penalty_type'],
                    ]);
                }
            }

            if($smartAssign== 1){
                // First, delete existing entries for this contract
                SmartAssigningContract::where('contract_id', $contract->id)->delete();
                $data = $this->implodeDataFromField($kpiData['selectedSmartAssignCategories']);
                $this->saveSmartAssigningContract($contract->id, $data, $user->id);
            }

            if(isset($tenant) && $tenant == 1)
            {
                contractTenantServiceCategories::where('contract_id', $contract->id)->delete();
                $contract->contractTenantServiceCategories()->attach($kpiData['selectedTenantCategories']);
            }

            $contract->contractMonths()->delete();
            foreach ($mainInformation['months'] ?? [] as $monthData) {
                ContractMonth::create([
                    'contract_id' => $contract->id,
                    'month'       => $monthData['month'],
                    'amount'      => $monthData['amount'],
                    'is_paid'     => false,
                    'user_id'     => auth()->id(),
                ]);
            }

            $contract->workforceAndTeams()->delete();
            foreach ($team ?? [] as $member) {
                WorkforceAndTeam::create([
                    'contract_id'          => $contract->id,
                    'role'                 => $member['role'],
                    'proficiency'          => $member['proficiency'],
                    'quantity'             => $member['quantity'],
                    'deduction_rate'       => $member['deduction_rate'],
                    'working_days'         => $member['working_days'],
                    'localization_target'  => $member['localization_target'],
                    'working_hours'        => $member['working_hours'],
                    'attendance_mandatory' => $member['attendance_mandatory'],
                    'minimum_wage'         => $member['minimum_wage'],
                    'uniform_and_tools_mandatory' => $member['uniform_and_tools'],
                    'user_id'              => auth()->id(),
                ]);
            }

            $contract->contractPerformanceIndicatorsRecord()->delete();
            // Insert KPI data into contract_performance_indicators
            foreach ($kpiData['selectedKpis'] ?? [] as $kpi) {
                $performanceClass = $kpi['class'] ?? null;
                $deductionType = $kpi['percentage_type'] ?? null; // fixed or percentage
                $ranges = $kpi['ranges'] ?? [];

                foreach ($ranges as $range => $data) {
                    $penalty = $data['penalty'] ?? null;
                    $rangeId = self::getRangeIdFromLabel($range); // Map 100%-95% => 1, etc.

                    if ($rangeId && $penalty !== null) {
                        DB::table('contract_performance_indicators')->insert([
                            'contract_id'           => $contract->id,
                            'range_id'              => $rangeId,
                            'penalty'               => $penalty,
                            'performance_indicator' => $performanceClass,
                            'deduction_value'       => $data['deduction'] ?? 0,
                            'deduction_type'        => $deductionType ?? 'fixed',
                            'created_at'            => now(),
                            'updated_at'            => now(),
                        ]);
                    }
                }
            }

            $contract->contractPriorities()->delete();
            // ✅ Insert Performance Indicatoe Rows into contract_priorities
            foreach ($kpiData['slaRows'] ?? [] as $row) {
                DB::table('contract_priorities')->insert([
                    'user_id'             => auth()->id(),
                    'contract_id'         => $contract->id,
                    'priority_id'         => $row['priority_id'],
                    'service_window'      => $row['service_window_input'],
                    'response_time'       => $row['response_time_input'],
                    'service_window_type' => $row['service_window_select'],
                    'response_time_type'  => $row['response_time_select'],
                    'created_at'          => now(),
                ]);
            }

            $contract->contractServiceKpis()->delete();
            if (isset($kpiData['servicesRows']) && !empty($kpiData['servicesRows'])) {
                foreach ($kpiData['servicesRows'] as $serviceRow) {
                    
                    $serviceKpi = ContractServiceKpi::create([
                        'contract_id' => $contract->id,
                        'service_id' => $serviceRow['service_id'],
                        'performance_indicator' => json_encode($serviceRow['kpi_ids'] ?? null),
                        'price' => $serviceRow['price'] ?? 0,
                        'description' => $serviceRow['description'] ?? null,
                        'created_by' => $user->id,
                        'updated_by' => $user->id,
                    ]);
                }
            }
         });
        
    }

     private static function getRangeIdFromLabel(string $label): ?int
    {
        $map = [
            '100%-95%' => 1,
            '94%-90%'  => 2,
            '89%-85%'  => 3,
            '85%-80%' => 4,
            '80%-75%' => 5,
            '75%-70%' => 6,
            '70%-65%' => 7,
        ];
    
        return $map[$label] ?? null;
    }
    
    public function getApprovalMessage()
    {
        $user = Auth::user();

        if (!$user || !isset($user->user_type)) {
            return null;
        }

        $requiredRoles = Helper::getApprovalRolesFor($user->user_type);

        if (empty($requiredRoles)) {
            return null;
        }

        $roleLabels = [
            'building_manager' => 'Building Manager',
            'project_owner'    => 'Project Owner',
            'sp_admin'         => 'Service Provider',
            'admin'            => 'Project Owner',
            'supervisor'       => 'Supervisor',
        ];

        $roles = collect($requiredRoles)
                    ->map(function ($role) {
                        return __('advance_contracts.variation_order.roles.' . $role);
                    })
                    ->toArray();;

        // Format: A, B & C
        $formattedRoles = implode(', ', array_slice($roles, 0, -1)) .
                        (count($roles) > 1 ? ' & ' : '') .
                        end($roles);

        // Return the translated string with dynamic roles
        return __('advance_contracts.variation_order.alert_message', ['roles' => $formattedRoles]);
    }




    // Method to get asset categories based on contract data
    public function getAssetCategorie($assetIds)
    {
        $serviceIds = explode(',', $assetIds);
        return Contracts::getAssetCategorie($serviceIds);
    }

    // Method to get asset names based on contract data
    public function getAssetName($assetIds)
    {
        $assetIds = explode(',', $assetIds);
        return Contracts::getAssetName($assetIds);
    }

    // Method to fetch cities based on contract city IDs
    public function fetchCities($locale, $contractCityIds)
    {
        $cityIds = array_filter(explode(',', $contractCityIds));
        return City::select('id', 'name_en', 'name_ar', 'is_deleted')
            ->whereIn('id', $cityIds)
            ->where('status', 1)
            ->get()
            ->map(function ($city) use ($locale) {
                $name = $locale === 'ar' ? $city->name_ar : $city->name_en;
                if ($city->is_deleted !== 'no') {
                    $name .= __('general_sentence.modal.deleted');
                }
                return $name;
            })
            ->toArray();
    }

    // Method to fetch region names based on contract region IDs
    public function fetchRegionNames($locale, $regionIds)
    {
        $regionIds = array_filter(explode(',', $regionIds));
        return Region::select('id', 'name', 'name_ar', 'is_deleted')
            ->whereIn('id', $regionIds)
            ->where('status', 1)
            ->get()
            ->map(function ($region) use ($locale) {
                $name = $locale === 'ar' ? $region->name_ar : $region->name;
                if ($region->is_deleted !== 'no') {
                    $name .= __('general_sentence.modal.deleted');
                }
                return $name;
            })
            ->toArray();
    }


    // Method to calculate contract progress percentage
    public function calculatePercentage($start_date, $end_date)
    {
        $fromDate = strtotime($start_date);
        $toDate = strtotime($end_date);
        $currentDate = time();

        $datediffA = round(($toDate - $fromDate) / (60 * 60 * 24)); // Total days
        $datediffB = round(($currentDate - $fromDate) / (60 * 60 * 24)); // Days passed

        if ($datediffB < 0) {
            return 0; // No progress
        } elseif ($datediffA > 0 && $datediffA >= $datediffB) {
            return round(($datediffB * 100) / $datediffA, 2); // Calculate percentage
        } else {
            return 100; // Completed
        }
    }

    // Method to load contract properties based on contract ID
    public function loadContractProperties($contractId)
    {
        // Get property_building_ids from pivot table
        $propertyBuildingIds = DB::table('contract_property_buildings')
            ->where('contract_id', $contractId)
            ->pluck('property_building_id')
            ->toArray();

        $propertyNames = [];
        $propertyNamesLimited = [];

        if (!empty($propertyBuildingIds)) {
            // Fetch property names without limit
            $propertyNames = $this->getPropertyNames($propertyBuildingIds);

            // Fetch property names with limit (e.g., only first 6)
            $propertyNamesLimited = $this->getPropertyNames($propertyBuildingIds, true); // with limit
        }

        return [
            'propertyNames' => $propertyNames,
            'propertyNamesLimited' => $propertyNamesLimited,
        ];
    }


    // Method to get property names based on property IDs
    private function getPropertyNames(array $propertyIds, $limit = false)
    {
        $query = PropertyBuildings::with(['property:id,complex_name,property_type'])
            ->whereIn('id', $propertyIds);

        if ($limit) {
            $query->limit(6); // Limit to 6 results
        }

        return $query->get()->map(function ($building) {
            $name = $building->building_name;
            if (
                $building->property &&
                $building->property->property_type === 'complex' &&
                !empty($building->property->complex_name)
            ) {
                $name = $building->property->complex_name . ' - ' . $name;
            }

            return [
                'id' => $building->id,
                'building_tag' => $name,
                'deleted_at' => $building->deleted_at,
            ];
        })->toArray();
    }

    // Method to get asset categories based on contract data
    public function getAssetCategories($contract, $contractId)
    {
        if ($contract->use_form_of_unit_receival) {
            return AssetCategory::whereIn('id', function ($query) use ($contractId) {
                                        $query->select('asset_category_id')
                                            ->from('contract_tenant_service_categories')
                                            ->where('contract_id', $contractId);
                                    })->pluck('asset_category')->implode(', ');
        }

        return ''; // Return empty string if no categories
    }

    public function getAssetCategoryNames(array $ids): array
    {
        return AssetCategory::whereIn('id', $ids)
            ->pluck('asset_category', 'id') // returns [id => name]
            ->toArray();
    }



    // Method to get smart assigned services
    public function getSmartAssignedServices($contract, $contractId)
    {
        if ($contract->use_smart_assigning) {
            $smartAssigning = SmartAssigningContract::where('contract_id', $contractId)->first();
            $smartAssignedServices = '';

            if ($smartAssigning && !empty($smartAssigning->service_id)) {
                $serviceIds = explode(',', $smartAssigning->service_id);

                $smartAssignedServices = AssetCategory::whereIn('id', $serviceIds)
                    ->pluck('asset_category')
                    ->implode(', ');
            }

            return $smartAssignedServices;
        }

        return ''; // Return empty string if no smart assigning
    }

    // Method to fetch usable items
    public function getUsableItems($contract)
    {
        return $contract->getUsableItems->map(function (ContractUsableItem $usableItem) {
            $item = $usableItem->getItem(); // fetch Akaunting item info
            if ($item) {
                return [
                    'name'           => $item->name,
                    'open_stock'     => $usableItem->open_stock,
                    'low_stock'      => $usableItem->low_stock,
                    'mandatory'      => $usableItem->mandatory,
                    'approval'       => $usableItem->approval,
                    'price'          => $usableItem->price,
                    'penalty'        => $usableItem->penalty,
                    'penalty_type'   => $usableItem->penalty_type,
                ];
            }

            return null;
        })->filter(); // filter out nulls if getItem fails
    }

}
