<?php


namespace App\Services\CRM;

use App\Services\Contracts\DashCrmInterface;

class CRMLeadService
{
    protected $crmApiService;
    protected $workspaceSlug;

    public function __construct(DashCrmInterface $crmApiService)
    {
        $this->crmApiService = $crmApiService;
        $this->workspaceSlug = auth()->user()->workspace;
    }

    /**
     * Get all leads.
     *
     * @param string $workspaceSlug
     * @return array
     */
    public function getLeads(array $data): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/leads", $data);
    }

    public function getPageDetails(string $pageType, int $leadId): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/$pageType/show/{$leadId}");
    }

    /**
     * Get lead details by ID.
     *
     * @param string $workspaceSlug
     * @param int $leadId
     * @return array
     */
    public function getLeadDetails(int $leadId): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/lead/show/{$leadId}");
    }

    /**
     * Create a new lead.
     *
     * @param string $workspaceSlug
     * @param array $data
     * @return array
     */
    public function createLead(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/lead/create", $data);
    }

    /**
     * Update an existing lead.
     *
     * @param string $workspaceSlug
     * @param int $leadId
     * @param array $data
     * @return array
     */
    public function updateLead(int $leadId, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/lead/update/{$leadId}", $data);
    }

    /**
     * Delete a lead.
     *
     * @param string $workspaceSlug
     * @param int $leadId
     * @return array
     */
    public function deleteLead(int $leadId): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/lead/delete/{$leadId}");
    }

    /**
     * Lead Details.
     *
     * @param string $workspaceSlug
     * @param int $leadId
     * @return array
     */
    public function leadDetails(int $leadId): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/lead/show/{$leadId}");
    }

    /**
     * Change Stage.
     *
     * @param string $workspaceSlug
     * @param int $leadId
     * @param array $data
     * @return array
     */
    public function changeStage(int $leadId, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/lead/{$leadId}/change-stage", $data);
    }

    /**
     * Convert To Deal
     *
     * @param string $workspaceSlug
     * @param int $leadId
     * @return array
     */
    public function convertToDeal(int $leadId, $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/transfer-lead-to-deal/{$leadId}", $data);
    }

    public function createNote(int $leadId, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/lead-note/create/{$leadId}", $data);
    }

    public function addUserToLead(int $leadId, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/lead-user/store/{$leadId}", $data);
    }

    public function deleteLeadUser(int $leadId, int $userId)
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/lead-user/{$leadId}/delete/$userId");
    }

    /*
     *
     * Lead followup status dropdown
     */
    public function getLeadFollowUpStatuses()
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/leads/followup/status-dropdown");
    }

    public function updateLeadFollowUpStatus(int $leadId, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/leads/followup/status-update/{$leadId}", $data);
    }

    public function rescheduledLeadFollowUp(int $leadId, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/leads/followup/reschedule/{$leadId}", $data);
    }
}
