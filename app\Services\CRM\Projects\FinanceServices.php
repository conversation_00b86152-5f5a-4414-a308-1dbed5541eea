<?php


namespace App\Services\CRM\Projects;

use App\Services\Contracts\DashCrmInterface;
use GuzzleHttp\Client;
use Illuminate\Http\UploadedFile;
use App\Data\CRMProjects\CreateProjectResponseData;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
class FinanceServices
{
    protected $crmApiService;
    protected $client;
    protected $baseUrl;
    protected $workspaceSlug;

    public function __construct(DashCrmInterface $crmApiService)
    {
        $this->crmApiService = $crmApiService;
        $this->baseUrl = config('crm.base_url');
        $this->client = new Client(['base_uri' => $this->baseUrl, 'verify' => false]);
        $this->workspaceSlug = auth()->user()->workspace;
    }


    public function getDocuments(int $projectId, string $document_type, int $page = 1, int $perPage = 10, ?string $search = null, ?string $sortBy = null, ?string $sortOrder = null): array
    {
        $params = [
            'document_type' => $document_type,
            'page' => $page,
            'perPage' => $perPage,
        ] + (!empty($search) ? ['search' => $search] : []);
        if(!empty($sortBy)){
            $params['sort_by']=$sortBy;
        }
        if(!empty($sortOrder)){
            $params['sort_order']=$sortOrder;
        }
        //dd("/api/{$this->workspaceSlug}/project/{$projectId}/finance-manage",$params);
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectId}/finance-manage", $params);
    }
    

 
    public function deleteDocument($projectID, $documentType, $documentID)
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/delete");
        // switch ($documentType) {
        //     case 'proposal':
        //         return $this->deleteProposal($projectID, $documentID);
    
        //     case 'invoice':
        //         return $this->deleteInvoice($projectID, $documentID);
    
        //     case 'bill':
        //         return $this->deleteBill($projectID, $documentID);
    
        //     default:
        //         throw new \InvalidArgumentException("Invalid document type: $documentType");
        // }
    }


    public function duplicateDocument($projectID, $documentType, $documentID)
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/duplicate");
    }

    
    public function GenerateEInvoice($documentID)
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/invoice-einvoice/{$documentID}");
    }



    /* Get proposal details*/
    public function proposalDetail($projectID, $documentType, $documentID){
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/proposal/{$documentID}/show");
        } 
        
        catch (\Throwable $th) {
            Log::info("proposalDetail error: ".$th);
        }
    }

    /* Convert document to invoice*/
    public function convertProposalToInvoice($projectID, $documentType, $documentID){
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/convert-to-invoice");
        } 
        
        catch (\Throwable $th) {
            Log::info("convertProposalToInvoice error: ".$th);
        }
    }
    
  
    /* ------------------ */
     /* Get Customar list for dropdown*/
     public function getCustomarsList(): array{
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/proposal/customer-dropdown");
        } 

        catch (\Throwable $th) {
            Log::info("getCustomarList error: ".$th);
            return [];
        }
    }
     /* Get Vendor list for dropdown*/
     public function getVendorssList(): array{
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/bill/vendor/dropdown");
        } 

        catch (\Throwable $th) {
            Log::info("getCustomarList error: ".$th);
            return [];
        }
    }

    /* Get tax list for dropdown*/
    public function getTaxList(): array {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/proposal/tax-dropdown");
        } 

        catch (\Throwable $th) {
            Log::info("getTaxList error: ".$th);
            return [];
        }
    }

    /* Get project details*/
    public function getProjectDetails(int $projectId): array {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/show/{$projectId}") ?? [];
        } 

        catch (\Throwable $th) {
            Log::info("getProjectDetails error: ".$th);
            return [];
        }
    }

    /* Create proposal*/
    public function saveProposalData(int $projectId, array $data): array {
        try {
            return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$projectId}/proposal/create", $data) ?? [];
        } 

        catch (\Throwable $th) {
            Log::info("saveProposalData error: ".$th);
            return [];
        }
    }
    public function updateFinanceProposal(int $projectId, array $data,int $document_id): array {
        try {
            return $this->crmApiService->put("/api/{$this->workspaceSlug}/project/{$projectId}/proposal/{$document_id}/update", $data) ?? [];
        } 

        catch (\Throwable $th) {
            Log::info("saveProposalData error: ".$th);
            return [];
        }
    }

    /* Get tax details list*/
    public function getTaxDetailsList() : array {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/invoice/tax/list-dropdown") ?? [];
        } 

        catch (\Throwable $th) {
            Log::info("getTaxDetailsList error: ".$th);
            return [];
        }
    }




    public function uploadSingleFile($workspaceSlug, $projectId, $filePath, $fileName)
    {
        try {
            $response = $this->client->post("/api/{$workspaceSlug}/project/{$projectId}/file-upload", [
                'headers' => [
                    'Authorization' => 'Bearer ' . auth()->user()->crm_api_token,
                    'Accept' => 'application/json',
                ],
                'multipart' => [
                    [
                        'name'     => 'attachments[]',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => $fileName,
                    ]
                ]
            ]);

            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            return [
                'error' => true,
                'message' => $e->getMessage(),
            ];
        }
    }
  
    /* Get items list */ 
    public function getItemsList(int $projectId) : array {
        try {
            return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/invoice/item?project_id={$projectId}") ?? [];
        } 

        catch (\Throwable $th) {
            Log::info("getItemsList error: ".$th);
            return [];
        }
    }

    /* Get last proposal number */
    public function getLastProposalNumber() : array {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/proposal/last-number") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getLastProposalNumber error: ".$th);
            return [];
        }
    }


    /* Upload attachment by document type */
    public function uploadAttachment($projectID, $documentType, $documentID, $filePath, $fileName) {
        try {
            $response = $this->client->post("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/attechment", [
                'headers' => [
                    'Authorization' => 'Bearer ' . auth()->user()->crm_api_token,
                    'Accept' => 'application/json',
                ],
                'multipart' => [
                    [
                        'name'     => 'files[]',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => $fileName
                    ]
                ]
            ]);

            return json_decode($response->getBody(), true);
        } 
        
        catch (\Throwable $th) {
            Log::info("uploadAttachment error: ".$th);
        }
    }

    /*  Get attachments data by values */
    public function getAttachmentList($projectID, $documentType, $documentID, $perPage, $search, $page) {
        try {
            return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/attachment/list?perPage={$perPage}&page={$page}&search={$search}") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getAttachmentList error: ".$th);
        }
    }

    /*  Delete attachments data by values */
    public function deleteAttachmentByValues($projectID, $documentType, $documentID, $attachmentId) {
        try {
            return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/attechment/{$attachmentId}/destroy") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("deleteAttachmentByValues error: ".$th);
        }
    }
    /* Get last Invoice number */
    public function getLastInvoiceNumber() : array {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/invoice/last-number") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getLastProposalNumber error: ".$th);
            return [];
        }
    }

    /* Get last Bill number */
    public function getLastBillNumber() : array {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/bill/last-number") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getLastProposalNumber error: ".$th);
            return [];
        }
    }

    public function createInvoie(int $projectId, array $data) {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/invoice/{$projectId}/store", $data) ?? [];
    }

    public function updateInvoice(int $projectId, int $invoiceId, array $data) {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/project/{$projectId}/invoice/{$invoiceId}/update", $data) ?? [];
    }

    public function createBill(int $projectId, array $data) {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$projectId}/bill/store", $data) ?? [];

    }

    /* Get invoice delivery data form */
    public function invoiceDelivery($projectID, $documentType, $documentID){
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/invoice/show/{$documentID}");
        } 
        
        catch (\Throwable $th) {
            Log::info("invoiceDelivery error: ".$th);
            return [];
        }
    }
  
    /* Update document status */  
    public function updateStatusByValues($documentType, $documentID, $status) {
        try {
            return $this->crmApiService->put("/api/{$this->workspaceSlug}/project/{$documentType}/{$documentID}/update-status?status={$status}") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("updateStatusByValues error: ".$th);
            return [];
        }
    }  

     /* Send document mail */ 
    public function sendMailByValues($documentType, $documentID) {
        try {
            return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$documentType}/send/{$documentID}") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("sendMailByValues error: ".$th);
            return [];
        }
    }

    /* Invoice payment create */
    public function createPaymentInvoice($projectID, $documentType, $documentID, $filePath, $fileName, $date, $amount, $accountId, $reference, $description) {
        try {
            $response = $this->client->post("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/payment/create?date={$date}&amount={$amount}&account_id={$accountId}&reference={$reference}&description={$description}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . auth()->user()->crm_api_token,
                    'Accept' => 'application/json',
                ],
                'multipart' => [
                    [
                        'name'     => 'add_receipt',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => $fileName,
                    ]
                ]
            ]);

            return json_decode($response->getBody(), true);
        } 
        
        catch (\Throwable $th) {
            Log::info("createPaymentInvoice error: ".$th);
            return [];
        }
    }

    /* Get invoice details*/
    public function getInvoiceDetails($documentType, $documentID) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/{$documentType}/show/{$documentID}") ?? [];
        } 

        catch (\Throwable $th) {
            Log::info("getInvoiceDetails error: ".$th);
            return [];
        }
    }


    /* Get Bill details*/
    public function getBillDetails($projectID,$documentType, $documentID) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/show") ?? [];
        } 

        catch (\Throwable $th) {
            Log::info("getInvoiceDetails error: ".$th);
            return [];
        }
    }


    

    /* Invoice Download Data */
    public function getInvoiceDownloadData($projectID, $documentType, $documentID) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/download") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getInvoiceDownloadData error: ".$th);
        }
    }
  
    public function getInvoie(int $invoiceId){
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/invoice/show/{$invoiceId}") ?? [];
        }
        catch (\Throwable $th) {
            Log::info("getInvoie error: ".$th);
            return [];
        }
    }

    /* Get Receipt Summary */
    public function getReceiptSummary($projectID, $documentType, $documentID, $data) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/receipt/summary", $data) ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getReceiptSummary error: ".$th);
            return [];
        }
    }


    /* Get attachment list for invoice */
    public function getInvoiceAttachmentList($projectID, $documentType, $documentID, $data) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/attachment/list", $data) ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getInvoiceAttachmentList error: ".$th);
        }
    }

     /* Resend document by values */
    public function resendDocument($projectID, $documentType, $documentID) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/resend") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("resendDocument error: ".$th);
        }
    }

    /* Receipt reminder document by values */
    public function receiptReminderDocument($projectID, $documentType, $documentID) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/payment/reminder") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("receiptReminderDocument error: ".$th);
        }
    }

    /* Get account list for document by values */
    public function getDocumentAccountsList($documentType, $documentID) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/{$documentType}/{$documentID}/account/list") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getDocumentAccountsList error: ".$th);
        }
    }

    /* Delete row from receipt summary list by values */
    public function deleteReceiptSummaryByValues($projectID, $documentType, $documentID, $receiptID) {
        try {
            return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/receipt/{$receiptID}/summary") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("deleteReceiptSummaryByValues error: ".$th);
        }
    }

    /* Get credit not summary list by values */
    public function getCreditNotSummaryListByValues($projectID, $documentType, $documentID, $data) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/credit-note/summary", $data) ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("getCreditNotSummaryListByValues error: ".$th);
        }

        
    }
   public function getBill($projectID, $documentID) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/bill/{$documentID}/show") ?? [];
        } 

        catch (\Throwable $th) {
            Log::info("getInvoiceDetails error: ".$th);
            return [];
        }
    }


        public function updateBill(int $projectId, int $BillID, array $data) {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/project/{$projectId}/bill/{$BillID}/update", $data) ?? [];
    }



    public function deletePaymentByValues($projectID, $documentType, $documentID, $receiptID) {
        try {
            return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/payment/{$receiptID}/destroy") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("deleteReceiptSummaryByValues error: ".$th);
        }
    }

     public function deletePaymentInvoice($projectID, $documentType, $documentID, $receiptID) {
        try {
            return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$projectID}/invoice/{$documentID}/receipt/{$receiptID}/summary") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("deleteReceiptSummaryByValues error: ".$th);
        }
    }

    public function resendBill($projectID, $documentType, $documentID) {
        try {
            return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/bill-resent") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("deleteReceiptSummaryByValues error: ".$th);
        }
    }




    public function createBillPayment($projectID, $documentType, $documentID, $filePath, $fileName, $date, $amount, $accountId, $reference, $description)
    {
        try {
            $multipart = [
                [
                    'name'     => 'date',
                    'contents' => $date,
                ],
                [
                    'name'     => 'amount',
                    'contents' => $amount,
                ],
                [
                    'name'     => 'account_id',
                    'contents' => $accountId,
                ],
                [
                    'name'     => 'reference',
                    'contents' => $reference,
                ],
                [
                    'name'     => 'description',
                    'contents' => $description,
                ],
            ];

            // Add file only if it exists
            if (!empty($filePath) && file_exists($filePath)) {
                $multipart[] = [
                    'name'     => 'add_receipt',
                    'contents' => fopen($filePath, 'r'),
                    'filename' => $fileName,
                ];
            }

            $response = $this->client->post("/api/{$this->workspaceSlug}/project/{$projectID}/{$documentType}/{$documentID}/create-payment", [
                'headers' => [
                    'Authorization' => 'Bearer ' . auth()->user()->crm_api_token,
                    'Accept'        => 'application/json',
                ],
                'multipart' => $multipart,
            ]);

            return json_decode($response->getBody(), true);
        } catch (\GuzzleHttp\Exception\ServerException $e) {
            Log::error('ServerException: ' . $e->getResponse()->getBody()->getContents());
            return [];
        } catch (\Throwable $th) {
            Log::error('Exception: ' . $th->getMessage());
            return [];
        }
    }

    public function billSendMail($projectID,$documentType, $documentID) {
        try {
            return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$projectID}/bill/{$documentID}/send") ?? [];
        } 
        
        catch (\Throwable $th) {
            Log::info("billSendMail error: ".$th);
            return [];
        }
    }


}
