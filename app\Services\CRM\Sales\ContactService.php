<?php


namespace App\Services\CRM\Sales;

use App\Jobs\ProcessCrmLogin;
use App\Services\Contracts\DashCrmInterface;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Session;

class ContactService
{
    protected $crmApiService;
    protected $workspaceSlug;
    protected $client;
    protected $baseUrl;
    public function __construct(DashCrmInterface $crmApiService)
    {
        $this->crmApiService = $crmApiService;
        $this->workspaceSlug = auth()->user()->workspace;
        $this->baseUrl = config('crm.base_url');
        $this->client = new Client(['base_uri' => $this->baseUrl, 'verify' => false]);
    }


    public function getAll(int $page = 1): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales-contacts", ['page' => $page]);
    }

    public function getAllAccounts(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/account-dropdown");
    }

    public function create(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales-contact/create", $data);
    }


    public function update(int $id, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/sales-contact/update/{$id}", $data);
    }

    public function delete(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/sales-contact/delete/{$id}");
    }

    public function getComments(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/get/comments/$id");
    }

    public function getOpportunities(int $id, int $page): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/opportunity/list/$id", ['page' => $page]);
    }

    public function createOpportunity(int $id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales/contact/opportunity/create/$id", $data);
    }

    public function deleteOpportunity(int $contact_id, int $opportunity_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/sales/contact/opportunity/delete/$contact_id/$opportunity_id");
    }

    public function getCases(int $id, int $page): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/case/list/$id", ['page' => $page]);
    }

    public function createCase(int $id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales/contact/case/create/$id", $data);
    }

    public function deleteCase(int $contact_id, int $case_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/sales/contact/case/delete/$contact_id/$case_id");
    }

    public function addStreamComment(int $id, $comment_stream, $filePath, $fileName): array
    {
        // return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales/account/stream/update/{$id}", $data);
        try {

            $response = $this->client->post("/api/{$this->workspaceSlug}/sales/contact/stream/update/{$id}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . auth()->user()->crm_api_token,
                    'Accept' => 'application/json',
                ],
                'multipart' => array_filter([
                    [
                        'name'     => 'stream_comment',
                        'contents' => $comment_stream,
                    ],
                    $filePath ? [
                        'name'     => 'attachment',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => $fileName,
                    ] : null
                ])
            ]);

            if ($response->getStatusCode() == 401) {
                auth()->user()->crm_api_token = null;
                auth()->user()->workspace_slug = null;
                auth()->user()->save();
                ProcessCrmLogin::dispatch(auth()->user()->email, Session::get('plain_user_password'));
                if (request()->ajax() && (auth()->user()->workspace != 'none' || !auth()->user()->workspace_slug)) {
                    abort(401);
                }
            }

            return  $responseBody = json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            Log::error('Request failed:', ['error' => $e->getMessage()]);
            return [
                'error' => true,
                'message' => $e->getMessage(),
            ];
        }
    }
    public function getQuotes(int $id, int $page): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/quote/list/$id", ['page' => $page]);
    }

    public function createQuote(int $id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales/contact/quote/create/$id", $data);
    }

    public function deleteQuote(int $contact_id, int $quote_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/sales/contact/quote/delete/$contact_id/$quote_id");
    }

    public function getOrders(int $id, int $page): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/order/list/$id", ['page' => $page]);
    }

    public function createOrder(int $id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales/contact/order/create/$id", $data);
    }

    public function deleteOrder(int $contact_id, int $order_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/sales/contact/order/delete/$contact_id/$order_id");
    }

    public function getInvoices(int $id, int $page): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/invoice/list/$id", ['page' => $page]);
    }

    public function createInvoice(int $id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales/contact/invoice/create/$id", $data);
    }

    public function deleteInvoice(int $contact_id, int $invoice_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/sales/contact/invoice/delete/$contact_id/$invoice_id");
    }

    public function getCalls(int $id, int $page): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/calls/list/$id", ['page' => $page]);
    }

    public function createCall(int $id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales/contact/calls/create/$id", $data);
    }

    public function deleteCall(int $contact_id, int $call_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/sales/contact/calls/delete/$contact_id/$call_id");
    }

    public function getMeetings(int $id, int $page): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/meeting/list/$id", ['page' => $page]);
    }

    public function createMeeting(int $id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/sales/contact/meeting/create/$id", $data);
    }

    public function deleteMeeting(int $contact_id, int $meeting_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/sales/contact/meeting/delete/$contact_id/$meeting_id");
    }

    public function getOverview(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales/contact/overview-details/$id");
    }

    public function updateOverview(int $id, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/sales/contact/overview/update/$id", $data);
    }

    public function getContactDetails(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/sales-contact/$id/details");
    }
}
