<?php


namespace App\Services\CRM\Sales;

use App\Jobs\ProcessCrmLogin;
use App\Services\Contracts\DashCrmInterface;
use GuzzleHttp\Client;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Session;

class ProjectService
{
    protected $crmApiService;
    protected $workspaceSlug;
    protected $client;
    protected $baseUrl;
    public function __construct(DashCrmInterface $crmApiService)
    {
        $this->crmApiService = $crmApiService;
        $this->workspaceSlug = auth()->user()->workspace;
        $this->baseUrl = config('crm.base_url');
        $this->client = new Client(['base_uri' => $this->baseUrl, 'verify' => false]);
    }

    public function taskBoard(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/task-board");
    }

    public function taskBulkAction(int $id,  $data)
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/project/{$id}/tasks/bulk-action", $data);
    }

    public function taskBoardListView(int $id,  $data)
    { //int $id, int $page
        // echo '<pre>';print_r($data);die;
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/task-board-list", $data);
    }

    public function bugReport(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/bug-report/{$id}");
    }

    public function bugReportListView(int $id, int $page): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/bug-report-list", ['page' => $page]);
    }

    public function createTask($id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$id}/task/create", $data);
    }

    public function updateTaskStatusAndOrder($id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$id}/task-board/order-update", $data);
    }

    public function createBug($id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/bug-report/create/{$id}", $data);
    }

    public function updateBugStatusAndOrder($id, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$id}/bug-report/order-update", $data);
    }

    public function usersDropdown(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/assing-user-list-for-dropdown");
    }

    public function milestonesDropdown(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/milestone-list-for-dropdown");
    }

    public function priorityDropdown(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/priority-list-for-dropdown");
    }

    public function bugStatusDropdown(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/bug/status-list-for-dropdown");
    }

    public function bugPriorityDropdown(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/bug/priority-list-for-dropdown");
    }

    public function bugUsersDropdown(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/bug/{$id}/assing-user-list-for-dropdown");
    }

    public function deleteIncident(int $id, int $incident_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$id}/bug-report/delete/{$incident_id}");
    }

    public function deleteTask(int $id, int $task_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$id}/task/delete/{$task_id}");
    }

    public function incidentStages(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/stage/bug/index");
    }

    public function createIncidentStage(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/stage/bug/create", $data);
    }

    public function deleteIncidentStage(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/stage/bug/delete/{$id}");
    }

    public function taskStages(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/stage/task/index");
    }

    public function createTaskStage(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/stage/task/create", $data);
    }

    public function deleteTaskStage(int $id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/stage/task/delete/{$id}");
    }

    public function calendar(int $id, array $data): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/calendar/{$id}", $data);
    }

    public function projectList(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/project-list-dropdown");
    }

    public function getTaskStages(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/task-stages");
    }


    public function milestoneList(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/milestone-list-for-dropdown");
    }

    public function priorityList(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/priority-list-for-dropdown");
    }

    public function userList(int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/assing-user-list-for-dropdown");
    }

    public function stageList(): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/stage-dropdown");
    }

    public function taskData(int $project_id, int $id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$project_id}/get-task-data/{$id}");
    }

    public function updateTaskData(int $project_id, int $id, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/project/{$project_id}/task/update/{$id}", $data);
    }
    public function getAllUsers($id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/assing-user-list-for-dropdown");
    }
    public function getAlLMilestones($id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$id}/milestone-list-for-dropdown");
    }

    public function  createBmaAcccount(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/user/create-bma-account", $data);
    }
    public function  checkAccountEmail(array $email): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/user/email-find", $email);
    }
    public function getTaskDetails($id): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/task/show/{$id}");
    }
    public function taskboardDetail(int $taskboardId): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/task/show/{$taskboardId}");
    }
    public function saveComment(int $taskboardId, int $projectId, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$projectId}/tasks/{$taskboardId}/comments/0", $data);
    }
    public function deleteComment(int $taskboardId, int $comment_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$comment_id}/comment/{$taskboardId}");
    }
    public function saveAttachment(int $taskboardId, int $projectId, $filePath, $fileName): array
    {
        try {
            $response = $this->client->post("/api/{$this->workspaceSlug}/project/comment/{$taskboardId}/file", [
                'headers' => [
                    'Authorization' => 'Bearer ' . auth()->user()->crm_api_token,
                    'Accept' => 'application/json',
                ],
                'multipart' => [
                    [
                        'name'     => 'file',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => $fileName,
                    ]
                ]
            ]);

            if ($response->getStatusCode() == 401) {
                auth()->user()->crm_api_token = null;
                auth()->user()->workspace_slug = null;
                auth()->user()->save();
                ProcessCrmLogin::dispatch(auth()->user()->email, Session::get('plain_user_password'));
                if (request()->ajax() && (auth()->user()->workspace != 'none' || !auth()->user()->workspace_slug)) {
                    abort(401);
                }
            }
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            return [
                'error' => true,
                'message' => $e->getMessage(),
            ];
        }
    }
    public function deleteAttachment(int $taskboardId, int $file_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$file_id}/comment/{$taskboardId}/file");
    }
    public function fileDetail(int $taskboardId): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/comment/{$taskboardId}/file/list");
    }



    public function taskboardSubtask(int $taskboardId, int $projectId): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/project/{$projectId}/task/{$taskboardId}/sub-task/list");
    }

    public function saveSubtask(int $taskboardId, int $projectId, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$projectId}/task/{$taskboardId}/sub-task/create", $data);
    }
    public function deleteSubtask(int $taskboardId, int $projectId, int $sub_task_id): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/project/{$projectId}/task/{$taskboardId}/sub-task/{$sub_task_id}/delete");
    }
    public function updateSubtaskStatus(int $taskboardId, int $projectId, int $sub_task_id, array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/project/{$projectId}/task/{$taskboardId}/sub-task/{$sub_task_id}/update", $data);
    }


     public function taskboardAssignMember(int $taskboardId,int $projectId, array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/project/{$projectId}/task/{$taskboardId}/assign-member",$data);
    }
}
