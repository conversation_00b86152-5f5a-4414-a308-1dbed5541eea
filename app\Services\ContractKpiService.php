<?php

namespace App\Services;
use App\Http\Traits\ContractsTrait;
use App\Models\Complaint;
use App\Models\ContractDocument;
use App\Models\ContractInspectionReport;
use App\Models\ContractPayroll;
use App\Models\ContractPropertyBuildings;
use App\Models\Contracts;
use App\Models\ManageWorkerAvailabilityStatus;
use App\Models\User;
use App\Models\WorkerAttendances;
use App\Models\WorkerContractMapping;
use App\Models\WorkOrders;
use App\Models\WorkOrderWorkers;
use App\Models\WorkOrderWorkerTiming;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class ContractKpiService
{

    use ContractsTrait;
    /**
     * KPI -1 : Calculate the KPI for RM to PM count ratio
     * Check if RM count should not be more than 20% of PM Count
     *
     * @param \Illuminate\Support\Collection $request
     * @return float The calculated KPI percentage.
     */
    public static function rmToPmWorkordersByContract($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve work orders for the given contracts in the current month
        $baseQuery = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->where('is_deleted', '!=', 'yes');

        // Clone the base query for later use
        $currentMonthWorkOrders = clone $baseQuery;

        // Count RM and PM work orders separately
        $rmWorkOrdersCount = $currentMonthWorkOrders->where('work_order_type', 'reactive')->count();

        // Clone the base query again for preventive work orders
        $pmWorkOrdersQuery = clone $baseQuery;

        // Count PM work orders
        $pmWorkOrdersCount = $pmWorkOrdersQuery->where('work_order_type', 'preventive')->count();

        // Calculate the percentage of reactive work orders compared to total work orders
        $totalWorkOrdersCount = $rmWorkOrdersCount + $pmWorkOrdersCount;
        $reactivePercentage = ($totalWorkOrdersCount > 0) ? ($rmWorkOrdersCount / $totalWorkOrdersCount) * 100 : 0;

        $totalPercentage = 100 - $reactivePercentage;

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\ServiceRequest', $totalPercentage);
        $fetchThreshold = $totalPercentage < 80 ? $fetchThreshold : 0;
        // Calculate the allowed threshold for reactive work orders based on 20% of PM work orders
        $allowedRmThreshold = $pmWorkOrdersCount * 0.2;

        // Calculate the deduction points for exceeding the allowed threshold
        $deductionPoints = intval(max(0, $rmWorkOrdersCount - $allowedRmThreshold));

        // Result for the previous month
        $result = [
            'totalPercentage' => 100 - $reactivePercentage,
            'status' => $rmWorkOrdersCount <= $allowedRmThreshold ? 'pass' : 'fail',
            'rmWorkOrdersCount' => $rmWorkOrdersCount,
            'pmWorkOrdersCount' => $pmWorkOrdersCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '80'
        ];
        return $result;
    }

    /**
     * KPI -2 : Calculation of Response time success
     *Response time should be greater than 95%
     *
     * @param \Illuminate\Support\Collection $request
     * @return float The calculated KPI percentage.
     */
    public static function responseTimePerMonthByContract($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        $currentMonthWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->where('work_orders.contract_type', '!=', 'warranty')
            ->where('is_deleted', '!=', 'yes');

        // Clone the base query for later use
        $clonedWorkOrdersQuery = clone $currentMonthWorkOrders;

        // Clone the base query for later use
        $otherClonedWorkOrdersQuery = clone $currentMonthWorkOrders;

        // Calculate total work orders for the current month
        $totalWorkOrdersCount = $currentMonthWorkOrders->count();

        // Calculate on time work orders for the current month
        $completedWorkOrdersCount = $currentMonthWorkOrders->whereRaw("(response_time = 'On time' OR sp_approove = 0)")->count();

        // Calculate the completion percentage
        $completionPercentage = ($totalWorkOrdersCount > 0) ? ($completedWorkOrdersCount / $totalWorkOrdersCount) * 100 : 100;

        //Calculate the number of work orders responded late
        $responsedLate = $clonedWorkOrdersQuery->whereRaw("(response_time = 'Late')")->count();

        //Calculate the number of work orders not responded
        $notResponsed = $otherClonedWorkOrdersQuery->where('job_started_at', NULL)->count();

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\ResponseTime', $completionPercentage);
        $fetchThreshold = $completionPercentage < 95 ? $fetchThreshold : 0;

        // Result for the current month
        $result = [
            'completionPercentage' => $completionPercentage,
            'status' => $completionPercentage >= 95 ? 'pass' : 'fail',
            'totalWorkOrdersCount' => $totalWorkOrdersCount,
            'countResponsedOnTimeWorkOrders' => $completedWorkOrdersCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '95',
            'countResponsedLateWorkOrders' => $responsedLate,
            'countNotResponsedWorkOrders' => $notResponsed,
        ];

        return $result;
    }

    /**
     * KPI 3: Calculate the KPI for execution time based on the completion
     * of 95% of all RM work orders during the month for multiple contracts.
     *
     * @param \Illuminate\Support\Collection $contracts
     * @return float The calculated KPI percentage.
     */
    public static function calculateExecutionTimeKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        $currentMonthRmWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->where('work_order_type', 'reactive')
            ->where('is_deleted', '!=', 'yes');

        // Clone the base query for later use
        $clonedRmWorkOrdersQuery = clone $currentMonthRmWorkOrders;

        // Clone the base query for later use
        $otherClonedRmWorkOrdersQuery = clone $currentMonthRmWorkOrders;

        // Calculate total and completed RM work orders for the current month
        $totalRmWorkOrdersCount = $currentMonthRmWorkOrders->count();
        $completedRmWorkOrdersCount = $currentMonthRmWorkOrders->where('pass_fail', 'pass')->count();

        // Calculate the completion percentage
        $completionPercentage = ($totalRmWorkOrdersCount > 0) ? ($completedRmWorkOrdersCount / $totalRmWorkOrdersCount) * 100 : 100;

        // Calculate total of RM work orders executed late for the current month
        $lateRmWorkOrdersCount = $clonedRmWorkOrdersQuery->where('pass_fail', 'fail')->count();

        // Calculate total of RM work orders not executed for the current month
        $notExecutedRmWorkOrdersCount = $otherClonedRmWorkOrdersQuery->where('pass_fail', 'pending')->count();

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\ExecutionTime', $completionPercentage);
        $fetchThreshold = $completionPercentage < 95 ? $fetchThreshold : 0;
        // Result for the current month
        $result = [
            'completionPercentage' => $completionPercentage,
            'status' => $completionPercentage >= 95 ? 'pass' : 'fail',
            'totalRmWorkOrdersCount' => $totalRmWorkOrdersCount,
            'completedRmWorkOrdersCount' => $completedRmWorkOrdersCount,
            'lateRmWorkOrdersCount' => $lateRmWorkOrdersCount,
            'notExecutedRmWorkOrdersCount' => $notExecutedRmWorkOrdersCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '95'
        ];

        return $result;
    }

    /**
     * KPI 4: Calculate the KPI based on a 5-star rating from the total closed work orders.
     * The KPI must not be less than 90%.
     *
     * @param \Illuminate\Support\Collection $contracts
     * @return float The calculated KPI percentage.
     */
    public static function calculateFiveStarRatingKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        $currentMonthWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->where('status', '4')
            ->where('workorder_journey', 'finished')
            ->where('is_deleted', '!=', 'yes');

        // Clone the base query for later use
        $clonedWorkOrdersQuery = clone $currentMonthWorkOrders;

        // Clone the base query for later use
        $otherClonedWorkOrdersQuery = clone $currentMonthWorkOrders;

        // Calculate the total number of work orders and ignore auto-approved work orders
        $totalClosedWorkOrdersCount = $currentMonthWorkOrders->count();

        // Calculate the number of 5-star rated work orders
        $fiveStarRatedWorkOrdersCount = $currentMonthWorkOrders->where('rating', '5')->count();

        // Calculate the number of <5-star rated work orders
        $lessFiveStarRatedWorkOrdersCount = $clonedWorkOrdersQuery->where('rating', '<', '5')->where('rating', '<>', '0')->count();

        // Calculate the number of not rated work orders
        $notRatedWorkOrdersCount = $otherClonedWorkOrdersQuery->where('rating', '=', '0')->count();

        // Calculate the completion percentage
        $percentage = ($totalClosedWorkOrdersCount > 0) ? ($fiveStarRatedWorkOrdersCount / $totalClosedWorkOrdersCount) * 100 : 100;

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\FiveStarRating', $percentage);
        $fetchThreshold = $percentage < 90 ? $fetchThreshold : 0;
        // Result for the current month
        $result = [
            'percentage' => $percentage,
            'status' => $percentage >= 90 ? 'pass' : 'fail',
            'totalClosedWorkOrdersCount' => $totalClosedWorkOrdersCount,
            'fiveStarRatedWorkOrdersCount' => $fiveStarRatedWorkOrdersCount,
            'lessFiveStarRatedWorkOrdersCount' => $lessFiveStarRatedWorkOrdersCount,
            'notRatedWorkOrdersCount' => $notRatedWorkOrdersCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '90'
        ];

        return $result;
    }

    /**
     * KPI 5: Calculate the On-Hold Equipment Key Performance Indicator (KPI) for the given contracts.
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $contracts
     * @return array
     */
    public static function calculateOnHoldEquipmentKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Get the currently logged-in user
        $loggedInUser = Auth::user();

        $currentMonthWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->where('is_deleted', '!=', 'yes')->get();

        // Calculate the total number of work orders
        $totalClosedWorkOrdersCount = $currentMonthWorkOrders->count();

        // Calculate the number of work orders exceeding 12 hours
        $workOrdersExceeding12HoursCount = $currentMonthWorkOrders->filter(function ($workOrder) {
            // Check if the time between job_started_at and job_completion_date is more than 12 hours
            $jobStartDate = Carbon::parse($workOrder->job_started_at);
            $jobCompletionDate = Carbon::parse($workOrder->job_completion_date);
            $hoursBetween = $jobStartDate->diffInHours($jobCompletionDate);

            return $hoursBetween > 12;
        })->count();

        // Calculate the completion percentage
        $percentage = ($totalClosedWorkOrdersCount > 0) ? (($totalClosedWorkOrdersCount - $workOrdersExceeding12HoursCount) / $totalClosedWorkOrdersCount) * 100 : 100;

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\OnHoldEquipment', $percentage);
        $fetchThreshold = $percentage < 100 ? $fetchThreshold : 0;
        // Result for the current month
        $result = [
            'percentage' => $percentage,
            'status' => $percentage >= 100 ? 'pass' : 'fail',
            'totalClosedWorkOrdersCount' => $totalClosedWorkOrdersCount,
            'workOrdersExceeding12HoursCount' => $workOrdersExceeding12HoursCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100'
        ];

        return $result;
    }

    /**
     * KPI 6: Calculate the Key Performance Indicator (KPI) for Reopened Work Orders for the given contracts.
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $contracts
     * @return array
     */
    public static function calculateReopenedWorkorderKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve finished work orders for the given contracts
        $currentMonthWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->where('is_deleted', '!=', 'yes');

        // Calculate the total number of closed work orders
        $totalWorkOrdersCount = $currentMonthWorkOrders->count();

        // Calculate the number of reopened work orders
        $reopenedWorkOrdersCount = $currentMonthWorkOrders->where('reopen', 'yes')->count();

        // Calculate the completion percentage for the current month
        $percentage = ($totalWorkOrdersCount > 0) ? ($reopenedWorkOrdersCount / $totalWorkOrdersCount) * 100 : 0;

        // Calculate the maximum allowable reopened work orders count based on the percentage
        $maxAllowableReopenedWorkOrders = ceil((3 / 100) * $totalWorkOrdersCount);

        // Calculate the actual deduction points
        $deductionPoints = intval(round(max(0, $reopenedWorkOrdersCount - $maxAllowableReopenedWorkOrders)));

        // Result for the current month
        $result = [
            'percentage' => $percentage <= 3 ? 100 : 0,
            'status' => $percentage <= 3 ? 'pass' : 'fail',
            'totalWorkOrdersCount' => $totalWorkOrdersCount,
            'reopenedWorkOrdersCount' => $reopenedWorkOrdersCount,
            'deductionPoints' => $percentage > 3 ? $deductionPoints ? $deductionPoints : 1 : 0,
            'targetPercentage' => '3'
        ];

        return $result;
    }

    /**
     * KPI 7: Calculate Maintenance Request Rating Key Performance Indicator (KPI)
     *
     * @param \Illuminate\Database\Eloquent\Collection $contracts
     * @return array
     */
    public static function calculateMrRatingKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve finished work orders for the given contracts
        $currentMonthWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->where('maintanance_request_id', '!=', null)
            ->where('is_deleted', '!=', 'yes')->get();

        // Calculate the total number of closed work orders
        $totalWorkOrdersCount = $currentMonthWorkOrders->count();

        // Calculate the number of flagged work orders
        $flaggedWorkOrdersCount = $currentMonthWorkOrders->filter(function ($workOrder) {
            // Access the feedback value from the related MaintenanceRequest
            $feedbackValue = optional($workOrder->maintenanceRequest)->feedback_value;

            // Check if the feedback value indicates a flagged work order
            return $feedbackValue == 1;
        })->count();

        // Calculate the completion percentage for the current month
        $percentage = ($totalWorkOrdersCount > 0) ? ($flaggedWorkOrdersCount / $totalWorkOrdersCount) * 100 : 0;

        // Calculate the maximum allowable reopened work orders count based on the percentage
        $maxAllowableReopenedWorkOrders = ceil((3 / 100) * $totalWorkOrdersCount);

        // Calculate the actual deduction points
        $deductionPoints = intval(round(max(0, $flaggedWorkOrdersCount - $maxAllowableReopenedWorkOrders)));

        // Result for the current month
        $result = [
            'percentage' => $percentage <= 3 ? 100 : 0,
            'status' => $percentage <= 3 ? 'pass' : 'fail',
            'totalWorkOrdersCount' => $totalWorkOrdersCount,
            'flaggedWorkOrdersCount' => $flaggedWorkOrdersCount,
            'deductionPoints' => $percentage > 3 ? $deductionPoints ? $deductionPoints : 1 : 0,
            'targetPercentage' => '3'
        ];

        return $result;
    }

    /**
     * KPI 8: Calculate Preventive Maintenance (PM) Work Orders Fulfillment Key Performance Indicator (KPI)
     *
     * @param \Illuminate\Database\Eloquent\Collection $contracts
     * @return array
     */
    public static function calculatePmWorkOrdersFulfillmentKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve preventive maintenance (PM) work orders for the given contracts
        $currentMonthPmWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->where('work_order_type', 'preventive')
            ->where('is_deleted', '!=', 'yes');

        // Calculate total and completed PM work orders for the current month
        $totalPmWorkOrdersCount = $currentMonthPmWorkOrders->count();
        $completedPmWorkOrdersCount = $currentMonthPmWorkOrders->where('status', '4')->count();

        // Calculate uncompleted PM work orders for the current month
        $uncompletedPmWorkOrdersCount = $totalPmWorkOrdersCount - $completedPmWorkOrdersCount;

        // Calculate the completion percentage
        $completionPercentage = ($totalPmWorkOrdersCount > 0) ? ($completedPmWorkOrdersCount / $totalPmWorkOrdersCount) * 100 : 100;

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\PmWorkOrdersFulfillment', $completionPercentage);
        $fetchThreshold = $completionPercentage < 100 ? $fetchThreshold : 0;
        // Result for the current month
        $result = [
            'completionPercentage' => $completionPercentage,
            'status' => $completionPercentage >= 100 ? 'pass' : 'fail',
            'totalPmWorkOrdersCount' => $totalPmWorkOrdersCount,
            'completedPmWorkOrdersCount' => $completedPmWorkOrdersCount,
            'uncompletedPmWorkOrdersCount' => $uncompletedPmWorkOrdersCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100'
        ];

        return $result;
    }

    /**
     * KPI 9: Calculate Unfinished Work Orders Key Performance Indicator (KPI)
     *
     * @param \Illuminate\Database\Eloquent\Collection $contracts
     * @return array
     */
    public static function calculateUnfinishedWorkordersKPI($contracts)
    {
        $startOfPreviousMonth = now()->subMonth()->startOfMonth()->toDateString(); // First day of the previous month
        $endOfPreviousMonth = now()->subMonth()->endOfMonth()->toDateString();     // Last day of the previous month

        // Base query to retrieve work orders for the given contracts
        $previousMonthWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->whereBetween('start_date', [$startOfPreviousMonth, $endOfPreviousMonth])
            ->where('is_deleted', '!=', 'yes');

        // Calculate total and completed RM work orders for the previous month
        $totalWorkOrdersCount = $previousMonthWorkOrders->count();
        $completedWorkOrdersCount = $previousMonthWorkOrders->where('status', '4')->count();

        // Calculate uncompleted RM work orders for the previous month
        $uncompletedPmWorkOrdersCount = $totalWorkOrdersCount - $completedWorkOrdersCount;

        // Calculate the completion percentage
        $completionPercentage = ($totalWorkOrdersCount > 0) ? ($completedWorkOrdersCount / $totalWorkOrdersCount) * 100 : 100;

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\UnfinishedWorkorders', $completionPercentage);
        $fetchThreshold = $completionPercentage < 100 ? $fetchThreshold : 0;
        // Result for the previous month
        $result = [
            'completionPercentage' => $completionPercentage,
            'status' => $completionPercentage >= 100 ? 'pass' : 'fail',
            'totalRmWorkOrdersCount9' => $totalWorkOrdersCount,
            'completedRmWorkOrdersCount' => $completedWorkOrdersCount,
            'uncompletedPmWorkOrdersCount9' => $uncompletedPmWorkOrdersCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100'
        ];

        return $result;
    }

    /**
     * KPI-11 : Calculate Worker Availability Key Performance Indicator (KPI) and Deduction Points.
     *
     * This function calculates the KPI by deducting points for every 1% less than the minimum level of worker availability.
     * Deduction points also impact workers, leading to a history of PayRoll deductions based on availability.
     *
     * @param \Illuminate\Database\Eloquent\Collection $contracts The collection of contracts for which the KPI is calculated.
     *
     * @return array The result of the KPI calculation including completion percentage, status, total workers count,
     *               total assigned workers count, deduction points, and target percentage. Additionally, it provides
     *               a breakdown of deduction points for each service provider.
     */
    public static function calculateWorkerAvailabilityKPI($contracts)
    {
        // Base query to retrieve workers for the given contracts
        $totalWorkers = User::whereRaw("FIND_IN_SET(?, contract_ids) > 0", [$contracts])->where('user_type', 'sp_worker')->orderByDesc('id')->pluck('id');
        // Base query to retrieve workers for the given contracts
        $totalWorkersCount = $totalWorkers->count();
        $workersWithAbsences = collect();
        $missingClockInsCount = 0;
        $AverageAvailability = 0;

        $getContractData = Contracts::select('id','start_date','end_date')->whereIn('id',$contracts)->first();
        if(isset($getContractData))
        {
            $contract_start_date = $getContractData->start_date;
            $contract_end_date = Carbon::parse($getContractData->end_date);

            // Check if the contract is not expired
            if (Carbon::now()->lte($contract_end_date)) {

                if ($totalWorkersCount > 0) {
                    // Get start and end dates for the current month
                    //$startDate = Carbon::now()->startOfMonth();
                    //$endDate = Carbon::now()->endOfMonth()->isFuture() ? Carbon::now() : Carbon::now()->endOfMonth();

                    // Calculate start date as the later of the contract start date or the start of the current month
                    $startDate = Carbon::now()->startOfMonth()->max($contract_start_date);
                    // If the contract expires in the future, calculate till the current date or the contract end date
                    $endDate = $contract_end_date->isFuture()
                    ? Carbon::now()->min($contract_end_date)  // Calculate till the current date or the contract end date, whichever is earlier
                    : $contract_end_date; // If the contract end date is earlier, use that as the limit


                    // Generate a list of all dates in the current month
                    $dates = [];
                    $currentDate = $startDate->copy();

                    while ($currentDate->lte($endDate)) {
                        $dates[] = $currentDate->toDateString();
                        $currentDate->addDay();
                    }

                    // Fetch attendances for those worker IDs within the current month
                    $attendances = WorkerAttendances::whereIn('worker_id', $totalWorkers)
                        ->whereBetween('clock_in_datetime', [$startDate, $endDate])
                        ->get()
                        ->groupBy('worker_id');

                    // Fetch approved leave records
                    $approvedLeaves = ManageWorkerAvailabilityStatus::whereIn('worker_id', $totalWorkers)
                        ->where('approval_status', 'approved')
                        ->whereRaw("FIND_IN_SET(?, contract_id) > 0", [$contracts])
                        ->where(function ($query) use ($startDate, $endDate) {
                            $query->where(function ($q) use ($startDate,$endDate) {
                                    // Leave starts before or within the current month
                                    $q->where('from_datetime', '<=', $endDate)
                                      ->where('to_datetime', '>=', $startDate);
                                });
                        })
                        ->get()
                        ->groupBy('worker_id');

                    // Fetch worker creation dates to ignore past missing dates
                        $workerCreationDates = User::whereIn('id', $totalWorkers)
                        ->get()
                        ->pluck('created_at', 'id');

                        $workersWithAbsences = collect();

                    foreach ($totalWorkers as $workerId) 
                    {
                       // Ensure we consider worker creation date
                        $workerCreationDate = Carbon::parse($workerCreationDates->get($workerId));
                        $effectiveStartDate = $startDate->max($workerCreationDate);

                        // Generate a list of all dates from effective start date to end date
                        $workerDates = collect();
                        $currentDate = $effectiveStartDate->copy();

                        while ($currentDate->lte($endDate)) {
                            $workerDates->push($currentDate->toDateString());
                            $currentDate->addDay();
                        }

                        // Include today's date if it falls within the contract period and is not already included
                        if (Carbon::today()->between($effectiveStartDate, $endDate) && !$workerDates->contains(Carbon::today()->toDateString())) {
                            $workerDates->push(Carbon::today()->toDateString());
                        }
                        // Get the worker's attendances for the current month
                        $workerAttendances = $attendances->get($workerId, collect());
                        
                        $workerLeaveRanges = $approvedLeaves->get($workerId, collect())->map(function ($leave) {
                            return [
                                'start' => Carbon::parse($leave->from_datetime),
                                'end' => Carbon::parse($leave->to_datetime),
                            ];
                        });

                        // Collect all clock-in dates for the worker
                        $clockedInDates = $workerAttendances->pluck('clock_in_datetime')->map(function ($date) {
                            return Carbon::parse($date)->toDateString();
                        })->unique();

                        $hasAbsence = false; // Flag to check if the worker had any absence
                        // Check for missing dates by comparing all dates in the month with worker's clock-in dates
                        foreach ($workerDates as $date) 
                        {
                            $eligibleWorkers = WorkerContractMapping::where('worker_id', $workerId)
                            ->where('contract_id',$getContractData->id)
                            ->where(function ($query) use ($date) {
                                $query->where('start_date', '<=', $date)
                                      ->where(function ($q) use ($date) {
                                          $q->whereNull('end_date')->orWhere('end_date', '>=', $date);
                                      });
                            })->first();
            
                            if(isset($eligibleWorkers))
                            {
                                if (!$clockedInDates->contains($date)) 
                                {       
                                //\Log::info('Worker ID: '.$workerId.' Date: '.$date);
                                    // Check if the date falls within approved leave
                                    $isOnLeave = $workerLeaveRanges->contains(function ($range) use ($date) {
                                        $currentDate = Carbon::parse($date);
                                        return $currentDate->between($range['start'], $range['end']);
                                    });

                                    // If the worker was not on approved leave, count it as missing attendance
                                    if (!$isOnLeave) {
                                        $missingClockInsCount++; // Increment the count of missing clock-ins
                                        $hasAbsence = true; // Mark the worker as having at least one absence
                                    }
                                }
                            }
                            
                        }
//dd($hasAbsence); 
                        // If the worker had at least one absence, add to the worker count
                        if ($hasAbsence) {
                            $workersWithAbsences->push($workerId);
                        }
                    }
                    // Count of unique workers who had at least one absence
                    $totalWorkersWithAbsences = $workersWithAbsences->unique()->count();
               
                    $AverageAvailability = ($totalWorkersCount - $totalWorkersWithAbsences) / $totalWorkersCount * 100;
                    $AverageAvailability = round($AverageAvailability, 2);
                }
            }
        }


        // Base query to retrieve reactive maintenance (RM) work orders for the given contracts
        $baseQuery = WorkOrders::whereIn('contract_id', $contracts)->where('is_deleted', '!=', 'yes');

        // Clone the base query for later use
        $clonedWorkOrdersQuery = clone $baseQuery;

        // Get RM work orders for the previous month
        $previousMonthRmWorkOrders = $baseQuery->whereMonth('start_date', now()->month)->get();

        // Calculate total and assigned workers for the previous month
        $totalAssignedWorkersCount = $previousMonthRmWorkOrders->pluck('worker_id')->filter()->unique()->count();
        // Calculate the percentage of assigned workers

        $percentageAssignedWorkers = ($totalWorkersCount > 0) ? ($totalAssignedWorkersCount / $totalWorkersCount) * 100 : 100;
//dd($percentageAssignedWorkers);
        // Calculate deduction points based on the percentage of assigned workers
        $deductionPoints = max(0, 80 - $percentageAssignedWorkers);

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\HumanResources', $percentageAssignedWorkers);
        $fetchThreshold = $percentageAssignedWorkers < 80 ? $fetchThreshold : 0;
        
        // Result for the previous month
        $result = [
            'completionPercentage' => $percentageAssignedWorkers,
            'status' => $percentageAssignedWorkers >= 80 ? 'pass' : 'fail',
            'totalWorkersCount' => $totalWorkersCount,
            'totalAssignedWorkersCount' => $totalAssignedWorkersCount,
            'deductionPoints' => $missingClockInsCount > 0 ? $fetchThreshold : 0,
            'totalAbsence' => $totalWorkersCount > 0 ? $missingClockInsCount : 0,
            'averageAvailability' => $totalWorkersCount > 0 ? $AverageAvailability : 0,
            'targetPercentage' => '80'
        ];

        return $result;
    }

    /**
     * KPI-12 Calculate pending documents and payrolls statistics for the given contracts.
     *
     * @param \Illuminate\Database\Eloquent\Collection $contracts
     * @return array
     */
    public static function calculationOfPendingDocumentsAndPayrolls($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve payrolls for the given contracts
        $payrolls = ContractPayroll::whereIn('contract_id', $contracts)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);

        // Clone the base query for later use
        $clonedPayrollsQuery = clone $payrolls;

        // Calculate total number of payrolls for the month
        $totalPayrollsCount = $payrolls->count();

        // Calculate the number of completed payrolls for the month
        $completedPayrollsCount = $payrolls->where('file_status', '!=', 'Pending')->count();
        $rejectedPayrollsCount = $clonedPayrollsQuery->where('file_status', 'Rejected')->count();

        // Calculate the base percentage of closed payrolls
        $basePercentagePayrolls = ($totalPayrollsCount > 0) ? ($completedPayrollsCount / $totalPayrollsCount) * 100 : 100;

        // Calculate the deduction percentage for rejected payrolls
        if ($totalPayrollsCount > 0 && $totalPayrollsCount <= 10) {
            // Deduct 10% for each rejected payroll if total payrolls are between 1 and 10
            $deductionPercentage = $rejectedPayrollsCount * 10;
        } elseif ($totalPayrollsCount > 10 && $totalPayrollsCount <= 100) {
            // Deduct 1% for each rejected payroll if total payrolls are between 11 and 100
            $deductionPercentage = $rejectedPayrollsCount * 1;
        } else {
            $deductionPercentage = 0; // No deduction if total payrolls are outside 1-100
        }

        // Calculate the final percentage
        $percentagePayrolls = $basePercentagePayrolls - $deductionPercentage;

        // Ensure the percentage is within valid bounds
        $percentagePayrolls = min(max($percentagePayrolls, 0), 100);

        // Calculate deduction points based on the percentage of closed payrolls
        $deductionPointsPayrolls = max(0, $totalPayrollsCount - $completedPayrollsCount);

        // Result for the payrolls
        $payrollsResult = [
            'completionPercentage' => $percentagePayrolls,
            'status' => $percentagePayrolls == 100 ? 'pass' : 'fail',
            'totalPayrollsCount' => $totalPayrollsCount,
            'completedPayrollsCount' => $completedPayrollsCount,
            'deductionPoints' => $deductionPointsPayrolls,
            'warnings' => $rejectedPayrollsCount, // If there are rejected payrolls we should show them as warnings
            'targetPercentage' => '100'
        ];

        // Documents Calculation

        // Base query to retrieve documents for the given contracts
        $documents = ContractDocument::whereIn('contract_id', $contracts)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);;

        // Clone the base query for later use
        $clonedDocumentsQuery = clone $documents;

        // Calculate total number of documents for the month
        $totalDocumentsCount = $documents->count();

        // Calculate the number of completed documents for the month
        $completedDocumentsCount = $documents->where('file_status', '!=', 'Pending')->count();
        $rejectedDocumentsCount = $clonedDocumentsQuery->where('file_status', 'Rejected')->count();

        // Calculate the base percentage of closed payrolls
        $basePercentageDocuments = ($totalDocumentsCount > 0) ? ($completedDocumentsCount / $totalDocumentsCount) * 100 : 100;

        // Calculate the deduction percentage for rejected payrolls
        if ($totalDocumentsCount > 0 && $totalDocumentsCount <= 10) {
            // Deduct 10% for each rejected payroll if total payrolls are between 1 and 10
            $deductionPercentage = $rejectedDocumentsCount * 10;
        } elseif ($totalDocumentsCount > 10 && $totalDocumentsCount <= 100) {
            // Deduct 1% for each rejected payroll if total payrolls are between 11 and 100
            $deductionPercentage = $rejectedDocumentsCount * 1;
        } else {
            $deductionPercentage = 0; // No deduction if total payrolls are outside 1-100
        }

        // Calculate the final percentage
        $percentageDocuments = $basePercentageDocuments - $deductionPercentage;

        // Ensure the percentage is within valid bounds
        $percentageDocuments = min(max($percentageDocuments, 0), 100);

        // Calculate deduction points based on the percentage of closed documents
        $deductionPointsDocuments = max(0, $totalDocumentsCount - $completedDocumentsCount);

        // Result for the documents
        $documentsResult = [
            'completionPercentage' => $percentageDocuments,
            'status' => $percentageDocuments == 100 ? 'pass' : 'fail',
            'totalDocumentsCount' => $totalDocumentsCount,
            'completedDocumentsCount' => $completedDocumentsCount,
            'deductionPoints' => $deductionPointsDocuments,
            'warnings' => $rejectedDocumentsCount, // If there are rejected documents we should show them as warnings
            'targetPercentage' => '100'
        ];

        // Check if both totalDocumentsCount and totalPayrollsCount are greater than 0
        if ($totalDocumentsCount > 0 && $totalPayrollsCount > 0) {
            // Calculate total percentage
            $totalPercentage = ($percentageDocuments + $percentagePayrolls) / 2;
        } elseif ($totalDocumentsCount > 0) {
            // Only totalDocumentsCount is greater than 0, use percentageDocuments
            $totalPercentage = $percentageDocuments;
        } elseif ($totalPayrollsCount > 0) {
            // Only totalPayrollsCount is greater than 0, use percentagePayrolls
            $totalPercentage = $percentagePayrolls;
        } else {
            // Both totalDocumentsCount and totalPayrollsCount are 0, set totalPercentage to 0 or handle accordingly
            $totalPercentage = 100;
        }

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\ReportingAccuracy', $totalPercentage);
        $fetchThreshold = $totalPercentage == 100 ? 0 : $fetchThreshold;

        // Result for the documents and payrolls
        $finalResult = [
            'completionPercentage' => $totalPercentage,
            'status' => $totalPercentage == 100 ? 'pass' : 'fail',
            'totalDocumentsAndPayrollsCount' => $totalDocumentsCount + $totalPayrollsCount,
            'completedDocumentsAndPayrollsCount' => $completedDocumentsCount + $completedPayrollsCount,
            'deductionPoints' => $fetchThreshold,
            'warnings' => $rejectedDocumentsCount + $rejectedPayrollsCount,
            'targetPercentage' => '100'
        ];

        // Combine the results
        $result = [
            'payrolls' => $payrollsResult,
            'documents' => $documentsResult,
            'finalResult' => $finalResult
        ];

        return $result;
    }

    /**
     * KPI-14 : Calculate the Key Performance Indicator (KPI) for unresolved documents.
     *
     * This KPI evaluates the percentage of closed documents for a given month and provides deduction points.
     *
     * @param \Illuminate\Support\Collection $contracts
     * @return array
     */
    public static function calculateUnresolvedDocumentKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve Documents for the given contracts
        $documents = ContractDocument::whereIn('contract_id', $contracts)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);

        // Calculate total number of Documents for the month
        $totalDocumentsCount = $documents->count();

        // Calculate the number of completed Documents for the month
        $completedDocumentsCount = $documents->where('file_status', 'Approved')->count();

        // Calculate the number of uncompleted Documents for the month

        $unCompletedDocumentsCount = $totalDocumentsCount - $completedDocumentsCount;

        // Calculate the percentage of closed Documents
        $percentageDocuments = ($totalDocumentsCount > 0) ? ($completedDocumentsCount / $totalDocumentsCount) * 100 : 100;

        // Calculate deduction points based on the percentage of closed Documents
        $deductionPoints = max(0, $totalDocumentsCount - $completedDocumentsCount);

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\ControlAndArchiveDocuments', $percentageDocuments);
        $fetchThreshold = $percentageDocuments == 100 ? 0 : $fetchThreshold;

        // Result for the current month
        $result = [
            'completionPercentage' => $percentageDocuments,
            'status' => $percentageDocuments == 100 ? 'pass' : 'fail',
            'totalDocumentsCount' => $totalDocumentsCount,
            'completedDocumentsCount' => $completedDocumentsCount,
            'unCompletedDocumentsCount' => $unCompletedDocumentsCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100'
        ];

        return $result;
    }

    /**
     * KPI-15 : Calculate the Key Performance Indicator (KPI) for unclosed complaints.
     *
     * This KPI evaluates the percentage of closed complaints for a given month and provides deduction points.
     *
     * @param \Illuminate\Support\Collection $contracts
     * @return array
     */
    public static function calculateUnclosedComplaintKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve complaints for the given contracts
        $complaints = Complaint::whereIn('contract_id', $contracts)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);

        // Calculate total number of complaints for the month
        $totalComplaintsCount = $complaints->count();

        // Calculate the number of completed complaints for the month
        $completedComplaintsCount = $complaints->where('status', 'Completed')->count();

        // Calculate the number of uncompleted complaints for the month
        $uncompletedComplaintsCount = $totalComplaintsCount - $completedComplaintsCount;

        // Calculate the percentage of closed complaints
        $percentageComplaints = ($totalComplaintsCount > 0) ? ($completedComplaintsCount / $totalComplaintsCount) * 100 : 100;

        // Calculate deduction points based on the percentage of closed complaints
        $deductionPoints = max(0, $totalComplaintsCount - $completedComplaintsCount);

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\StakeholderCollaborationLevel', $percentageComplaints);
        $fetchThreshold = $percentageComplaints == 100 ? 0 : $fetchThreshold;

        // Result for the current month
        $result = [
            'completionPercentage' => $percentageComplaints,
            'status' => $percentageComplaints == 100 ? 'pass' : 'fail',
            'totalComplaintsCount' => $totalComplaintsCount,
            'completedComplaintsCount' => $completedComplaintsCount,
            'uncompletedComplaintsCount' => $uncompletedComplaintsCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100'
        ];

        return $result;
    }

    /**
     * KPI-16 : Calculate the Key Performance Indicator (KPI) for unresolved payrolls.
     *
     * This KPI evaluates the percentage of closed payrolls for a given month and provides deduction points.
     *
     * @param \Illuminate\Support\Collection $contracts
     * @return array
     */
    public static function calculateUnresolvedPayrollKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve payrolls for the given contracts
        $payrolls = ContractPayroll::whereIn('contract_id', $contracts)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);

        // Clone the base query for later use
        $clonedPayrollsQuery = clone $payrolls;

        // Clone the base query for later use
        $otherClonedPayrollsQuery = clone $payrolls;

        // Calculate total number of payrolls for the month
        $totalPayrollsCount = $payrolls->count();

        // Calculate the number of completed payrolls for the month
        $completedPayrollsCount = $payrolls->where('file_status', 'Approved')->count();

        // Calculate the number of provided payrolls for the month
        $providedPayrollsCount = $clonedPayrollsQuery->where('file_status', 'Review')->count();

        // Calculate the number of rejected payrolls for the month
        $rejectedPayrollsCount = $otherClonedPayrollsQuery->whereIn('file_status', ['Rejected', 'Pending'])->count();

        // Calculate the percentage of closed payrolls
        $percentagePayrolls = ($totalPayrollsCount > 0) ? ($completedPayrollsCount / $totalPayrollsCount) * 100 : 100;

        // Calculate deduction points based on the percentage of closed payrolls
        $deductionPoints = max(0, $totalPayrollsCount - $completedPayrollsCount);

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\FinancialObligationsFulfillment', $percentagePayrolls);
        $fetchThreshold = $percentagePayrolls == 100 ? 0 : $fetchThreshold;

        // Result for the current month
        $result = [
            'completionPercentage' => $percentagePayrolls,
            'status' => $percentagePayrolls == 100 ? 'pass' : 'fail',
            'totalPayrollsCount' => $totalPayrollsCount,
            'completedPayrollsCount' => $completedPayrollsCount,
            'providedPayrollsCount' => $providedPayrollsCount,
            'rejectedPayrollsCount' => $rejectedPayrollsCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100'
        ];

        return $result;
    }

    /**
     * KPI-18 : Calculate the localization percentage for a given contract.
     *
     * @param int $contractId
     * @return array
     */
    public static function calculationOfLocalizationPercent($contractIds)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Retrieve the contract details
        /** @var Contracts $contract */
        $contract = Contracts::find($contractIds);

        //obsolete: Get the localization percentage from the contract or default to 0
        //$localWorkforcePercentage = $contract->local_workforce_percentage ?? 0;

        // Get the localization percentage range from the contract
        // this will return an array with the min and max values
        $localWorkforcePercentageRange = Contracts::getLocalWorkforcePercentageRangeById($contract->local_workforce_percentage_id);
        $localWorkforcePercentage = intval($localWorkforcePercentageRange[0] ?? 0);

        // Base query to retrieve workers for the given contracts
        $workers = User::whereRaw("FIND_IN_SET(?, contract_ids) > 0", [$contractIds])
            ->where('user_type', 'sp_worker')
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);

        // Clone the base query for later use
        $clonedWorkersQuery = clone $workers;

        // Calculate total number of workers for the month
        $totalWorkersCount = $workers->count();

        // Calculate the number of saoudi workers for the month
        $completedWorkersCount = $workers->where('country_id', 1)->count();

        // Calculate the number of non-saoudi workers for the month
        $nonSaoudiWorkersCount = $clonedWorkersQuery->where('country_id', '<>', 1)->count();

        // Calculate the percentage of completed workers
        $percentage = ($totalWorkersCount > 0) ? ($completedWorkersCount / $totalWorkersCount) * 100 : 100;

        // Calculate deduction points based on the percentage of completed workers
        $deductionPoints = intval(round(max(0, $localWorkforcePercentage - $percentage)));
        $contracts = explode(',', $contractIds);

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\LocalWorkforce', $percentage);
        $fetchThreshold = $percentage >= $localWorkforcePercentage ? 0 : $fetchThreshold;

        // Result for the current month
        $result = [
            'completionPercentage' => $percentage,
            'status' => $percentage >= $localWorkforcePercentage ? 'pass' : 'fail',
            'totalWorkersCount' => $totalWorkersCount,
            'saoudiWorkersCount' => $completedWorkersCount,
            'nonSaoudiWorkersCount' => $nonSaoudiWorkersCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => $localWorkforcePercentage,
        ];
        return $result;
    }

    /**
     * KPI-19 : Calculate the injuries occurrences percentage for a given contract.
     *
     * @param int $contractId
     * @return array
     */
    public static function calculationOfInjuriesOccurencesPercent($contractId)
    {
        // Retrieve the contract details
        $contract = Contracts::find($contractId);

        // Annually Injury Limit
        $annuallyInjuryLimit = 4;

        // Base query to retrieve workers for the given contracts
        $baseQuery = User::select('id')->whereRaw("FIND_IN_SET(?, contract_ids) > 0", [$contractId])->where('user_type', 'sp_worker');

        // Clone the base query for later use
        $clonedWorkersQuery = clone $baseQuery;

        // Get workers for the current month
        $workers = $baseQuery->get();

        // GEt workers data array for worker id
        $workerdata = $workers->toArray();
        if (isset($workerdata) && !empty($workerdata)) {
            $workerids = array_column($workerdata, 'id');
        } else {
            $workerids = array(0);
        }

        $calculateInjury = ManageWorkerAvailabilityStatus::whereIn('worker_id', $workerids)
            ->where('approval_status', 'approved')
            ->whereRaw("FIND_IN_SET(?, contract_id) > 0", [$contractId])
            ->where('reason_type_id', 3); //3 means reason type is injury , from db

        $AnnuallyInjury = clone $calculateInjury;

        $MonthlyInjury = clone $calculateInjury;

        // Calculate Monthly Injury
        $MonthlyInjury = $MonthlyInjury->whereMonth('updated_at', '=', now()->month)
            ->count();

        // Calculate Annually Injury
        $AnnuallyInjury = $AnnuallyInjury->whereYear('updated_at', '=', now()->year)
            ->count();

        // Calculate total number of workers for the month
        $totalWorkersCount = $workers->count();

        // Calculate deduction points based on the monthly Injury occurence
        $deductionPoints = $AnnuallyInjury > $annuallyInjuryLimit ? intval(round(max(0, $AnnuallyInjury - $annuallyInjuryLimit))) : 0;

        // Calculate the number of recorded Injuries
        $calculateRecordedInjury = ManageWorkerAvailabilityStatus::whereIn('worker_id', $workerids)
            ->where('reason_type_id', 3)
            ->whereRaw("FIND_IN_SET(?, contract_id) > 0", [$contractId])
            ->count(); //3 means reason type is injury , from db

        // Result for the current month
        $result = [
            'annuallyInjuryLimit' => $annuallyInjuryLimit,
            'status' => $AnnuallyInjury > $annuallyInjuryLimit ? 'fail' : 'pass',
            'totalWorkersCount' => $totalWorkersCount,
            'deductionPoints' => $deductionPoints,
            'currentMonthlyInjury' => $MonthlyInjury,
            'currentAnnuallyInjury' => $AnnuallyInjury,
            'calculateRecordedInjury' => $calculateRecordedInjury,
            'calculateInjury' => $calculateInjury->count(),
            'targetPercentage' => '100',
            'percentage' => $AnnuallyInjury > $annuallyInjuryLimit ? '0' : '100'
        ];

        return $result;
    }

    /**
     * KPI-20 : Calculate the Key Performance Indicator (KPI) for unresolved Inspection Reports.
     *
     * This KPI evaluates the percentage of closed Inspection Reports for a given month and provides deduction points.
     *
     * @param \Illuminate\Support\Collection $contracts
     * @return array
     */
    public static function calculateUnresolvedInspectionReportsKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve Inspection Reports for the given contracts
        $baseQuery = ContractInspectionReport::whereIn('contract_id', $contracts)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);

        // Clone the base query for later use
        $clonedInspectionReportsQueryForMonth = clone $baseQuery;

        // Calculate total number of Inspection Reports for the month
        $totalInspectionReportsCount = $baseQuery->count();

        // Calculate the number of submitted Inspection Reports for the month
        $submittedInspectionReportsCount = $baseQuery->where('file_status', '!=', 'Pending')->count();

        // Calculate the number of completed Inspection Reports for the month
        $completedInspectionReportsCount = $clonedInspectionReportsQueryForMonth->where('file_status', 'Approved')->count();

        // Calculate the number of uncompleted Inspection Reports for the month
        $uncompletedInspectionReportsCount = $totalInspectionReportsCount - $completedInspectionReportsCount;

        // Calculate the percentage of closed Inspection Reports
        $percentageInspectionReports = ($totalInspectionReportsCount > 0) ? ($completedInspectionReportsCount / $totalInspectionReportsCount) * 100 : 100;

        // Calculate deduction points based on the percentage of closed Inspection Reports
        $deductionPoints = max(0, $totalInspectionReportsCount - $completedInspectionReportsCount);

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\UnresolvedInspectionReports', $percentageInspectionReports);
        $fetchThreshold = $percentageInspectionReports == 100 ? 0 : $fetchThreshold;

        // Result for the current month
        $result = [
            'percentage' => $percentageInspectionReports,
            'status' => $percentageInspectionReports == 100 ? 'pass' : 'fail',
            'totalInspectionCount' => $totalInspectionReportsCount,
            'submittedInspectionReportsCount' => $submittedInspectionReportsCount,
            'completedInspectionCount' => $completedInspectionReportsCount,
            'uncompletedInspectionReportsCount' => $uncompletedInspectionReportsCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100'
        ];

        return $result;
    }

    /**
     * KPI-21 : Calculate the Key Performance Indicator (KPI) for unclosed Safety and Environmental Violation (SEV).
     *
     * This KPI evaluates the percentage of closed complaints for a given month and provides deduction points.
     *
     * @param \Illuminate\Support\Collection $contracts
     * @return array
     */
    public static function calculateUnclosedSEVComplaintKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve complaints for the given contracts
        $complaints = Complaint::whereIn('contract_id', $contracts)
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->where('complaint_type_id', 2);

        // Calculate total number of complaints for the month
        $totalComplaintsCount = $complaints->count();

        // Calculate the number of completed complaints for the month
        $completedComplaintsCount = $complaints->where('status', 'Completed')->count();

        // Calculate the number of uncompleted complaints for the month
        $uncompletedComplaintsCount = $totalComplaintsCount - $completedComplaintsCount;

        // Calculate the percentage of closed complaints
        $percentageComplaints = ($totalComplaintsCount > 0) ? ($completedComplaintsCount / $totalComplaintsCount) * 100 : 100;

        // Calculate deduction points based on the percentage of closed complaints
        $deductionPoints = max(0, $totalComplaintsCount - $completedComplaintsCount);

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\UnclosedSEVComplaints', $percentageComplaints);
        $fetchThreshold = $percentageComplaints == 100 ? 0 : $fetchThreshold;

        // Result for the current month
        $result = [
            'percentage' => $percentageComplaints,
            'status' => $percentageComplaints == 100 ? 'pass' : 'fail',
            'totalComplaintsCount' => $totalComplaintsCount,
            'completedComplaintsCount' => $completedComplaintsCount,
            'uncompletedComplaintsCount' => $uncompletedComplaintsCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100'
        ];

        return $result;
    }

    /**
     * KPI 22: Calculate the Key Performance Indicator (KPI) for the Collaborative Work Orders for the given contracts.
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $contracts
     * @return array
     */
    public static function calculateCollaborativeWorkOrdersKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Step 1: Fetch Collaborative Work Orders for the Current Month
        $collaborativeWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->where('is_collaborative', true)
            ->whereBetween('target_date', [$startOfMonth, $endOfMonth])
            ->where('is_deleted', '!=', 'yes')
            ->get();

        // Initializing variables to store total counts
        $totalCompletionPercentage = 0;
        $totalAssignedWorkersCount = 0;
        $totalCompletedWorkersCount = 0;
        $totalWorkOrders = $collaborativeWorkOrders->count();

        // Looping through each collaborative work order
        foreach ($collaborativeWorkOrders as $workOrder) {
            // Step 2: Fetching Assigned Workers for Collaborative Work Orders
            $assignedWorkers = WorkOrderWorkers::where('work_order_id', $workOrder->id)
                ->pluck('worker_id');

            // Check if there are assigned workers
            if ($assignedWorkers->count() != 0) {
                // Step 3: Check Completion Status of Assigned Workers
                $completedWorkersCount = WorkOrderWorkerTiming::whereIn('worker_id', $assignedWorkers)
                    ->where('work_order_id', $workOrder->id)
                    ->whereNotNull('end_time')
                    ->count();

                // Calculate completion percentage for the work order
                $workOrderPercentage = ($completedWorkersCount / $assignedWorkers->count()) * 100;

                // Accumulate counts for total calculations
                $totalAssignedWorkersCount += $assignedWorkers->count();
                $totalCompletedWorkersCount += $completedWorkersCount;
                $totalCompletionPercentage += $workOrderPercentage;
            }
        }

        // Step 4: Calculate Average Percentage
        $averageCompletionPercentage = $totalWorkOrders > 0 ? $totalCompletionPercentage / $totalWorkOrders : 100;

        // Calculate the actual deduction points
        $deductionPoints = $totalWorkOrders > 0 ? intval(round(max(0, 75 - $averageCompletionPercentage))) : 0;

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\CollaborativeWorkOrders', round($averageCompletionPercentage, 2));
        $fetchThreshold = $averageCompletionPercentage < 75 ? $fetchThreshold : 0;

        // Result for the current month
        $result = [
            'percentage' => round($averageCompletionPercentage, 2),
            'status' => $averageCompletionPercentage >= 75 ? 'pass' : 'fail',
            'totalWorkOrdersCount' => $totalWorkOrders,
            'totalAssignedWorkersCount' => $totalAssignedWorkersCount,
            'totalCompletedWorkersCount' => $totalCompletedWorkersCount,
            'deductionPoints' => $averageCompletionPercentage < 75 ? $deductionPoints : 0,
            'targetPercentage' => '75'
        ];

        return $result;
    }

    /**
     * KPI 23: Calculate the Key Performance Indicator (KPI) for the Worker Housing Work Orders for the given contracts.
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $contracts
     * @return array
     */
    public static function calculateWorkerHousingWorkOrdersKPI($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        $totalWorkerHousingcount = ContractPropertyBuildings::whereIn('contract_id', $contracts)
            ->whereHas('propertyBuilding', function ($query) {
                $query->whereHas('property', function ($subQuery) {
                    $subQuery->where('worker_housing', true);
                });
            })->count();

        // Base query to retrieve work orders for the given contracts
        $currentMonthWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->where('is_deleted', '!=', 'yes')
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth])
            ->whereHas('propertyBuilding', function ($query) {
                // Add a condition to check if the property has worker_housing set to true
                $query->whereHas('property', function ($subQuery) {
                    $subQuery->where('worker_housing', true);
                });
            });

        // Calculate the total number of work orders
        $totalWorkOrdersCount = $currentMonthWorkOrders->count();

        // Calculate the number of closed work orders
        $closedWorkOrdersCount = $currentMonthWorkOrders->where('status', '4')->count();

        // Calculate the total number of unclosed work orders
        $unClosedWorkOrdersCount = $totalWorkOrdersCount - $closedWorkOrdersCount;

        // Calculate the completion percentage for the current month
        $percentage = ($totalWorkOrdersCount > 0) ? ($closedWorkOrdersCount / $totalWorkOrdersCount) * 100 : 100;

        // Calculate the maximum allowable closed work orders count based on the percentage
        $maxAllowableReopenedWorkOrders = ceil((3 / 100) * $totalWorkOrdersCount);

        // Calculate the actual deduction points
        $deductionPoints = intval(round(max(0, 90 - $percentage)));

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\WorkerHousing', $percentage);
        $fetchThreshold = $percentage < 90 ? $fetchThreshold : 0;

        // Result for the current month
        $result = [
            'percentage' => $percentage,
            'status' => $percentage >= 90 ? 'pass' : 'fail',
            'totalWorkerHousingcount' => $totalWorkerHousingcount,
            'totalHousingsWorkOrdersCount' => $totalWorkOrdersCount,
            'closedHousingsWorkOrdersCount' => $closedWorkOrdersCount,
            'unClosedWorkOrdersCount' => $unClosedWorkOrdersCount,
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '90'
        ];

        return $result;
    }

    /**
     * KPI-25 Calculate completion percentage and deduction points based on ratings for work orders.
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $contracts
     * @return array
     */
    public static function applyDeductionPointsBasedOnRatings($contracts)
    {
        $startOfMonth = now()->startOfMonth()->toDateString(); // First day of the current month
        $endOfMonth = now()->endOfMonth()->toDateString();     // Last day of the current month

        if(request()->has('q_date')) {
            try {
                $dates = self::extractDates(request()->get('q_date'));

                $startOfMonth = $dates['range_start_date'];
                $endOfMonth = $dates['range_end_date'];
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {

            }
        }

        // Base query to retrieve work orders for the given contracts
        $currentMonthWorkOrders = WorkOrders::whereIn('contract_id', $contracts)
            ->where('status', '4')
            ->where('is_deleted', '!=', 'yes')
            ->whereBetween('start_date', [$startOfMonth, $endOfMonth]);

        // Calculate total and completed work orders for the current month
        $totalWorkOrdersCount = $currentMonthWorkOrders->get();

        // Check if work orders have 1-star ratings in specified columns
        $workOrdersWithRatings = $currentMonthWorkOrders
            ->whereNotNull('uniform_specified_by_authority')
            ->whereNotNull('extent_of_cleanliness')
            ->whereNotNull('safety_procedure')
            ->get();

        // Check if there are work orders with 1-star ratings
        $has1StarRatings = $workOrdersWithRatings->filter(function ($workOrder) {
            return $workOrder->uniform_specified_by_authority == 1 &&
                $workOrder->extent_of_cleanliness == 1 &&
                $workOrder->safety_procedure == 1;
        });

        // Calculate the completion percentage
        $percentage = ($has1StarRatings->count() > 0) ? ((1 - $has1StarRatings->count() / max(1, $workOrdersWithRatings->count())) * 100) : 100;

        //Fetch Threshold
        $fetchThreshold = ContractsTrait::getContractPerformanceIndicatorThreshold($contracts, 'App\Calculations\PerformanceIndicators\WorkforceStandards', $percentage);
        $fetchThreshold = $percentage == 100 ? 0 : $fetchThreshold;

        // Result for the current month
        $result = [
            'percentage' => $percentage,
            'status' => $percentage == 100 ? 'pass' : 'fail',
            'totalWorkOrdersCount' => $workOrdersWithRatings->count(),
            'has1StarRatings' => $has1StarRatings->count(),
            'deductionPoints' => $fetchThreshold,
            'targetPercentage' => '100',
        ];

        return $result;
    }

    public static function extractDates($dateRange): array {
        list($rangeStartDate, $rangeEndDate) = explode(" - ", request()->get('q_date'));

        return [
            'range_start_date' => date('Y-m-d', strtotime($rangeStartDate)),
            'range_end_date' => date('Y-m-d', strtotime($rangeEndDate)),
        ];
    }
}
