<?php

namespace App\Services\Finance;

use App\Services\Contracts\DashCrmInterface;

class FinanceCustomerService
{
    protected $crmApiService;
    protected $workspaceSlug;

    public function __construct(DashCrmInterface $crmApiService)
    {
        $this->crmApiService = $crmApiService;
        $this->workspaceSlug = auth()->user()->workspace;
    }

    /**
     * Get all finance customers.
     *
     * @param array $data
     * @return array
     */
    public function list(array $data = []): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/accounting/customers", $data);
    }

    /**
     * Create a new finance customer.
     *
     * @param array $data
     * @return array
     */
    public function create(array $data): array
    {
        return $this->crmApiService->post("/api/{$this->workspaceSlug}/accounting/customer/create", $data);
    }

    /**
     * Get a specific finance customer.
     *
     * @param int $customerId
     * @return array
     */
    public function show(int $customerId): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/accounting/customer/{$customerId}");
    }

    /**
     * Delete a finance customer.
     *
     * @param int $customerId
     * @return array
     */
    public function delete(int $customerId): array
    {
        return $this->crmApiService->delete("/api/{$this->workspaceSlug}/accounting/customer/delete/{$customerId}");
    }

    /**
     * Edit a finance customer.
     *
     * @param int $customerId
     * @return array
     */
    public function edit(int $customerId): array
    {
        return $this->crmApiService->get("/api/{$this->workspaceSlug}/accounting/customer/edit/{$customerId}");
    }

    /**
     * Update a new finance customer.
     *
     * @param array $data
     * @return array
     */
    public function update(int $customerId,array $data): array
    {
        return $this->crmApiService->put("/api/{$this->workspaceSlug}/accounting/customer/update/{$customerId}", $data);
    }


}

