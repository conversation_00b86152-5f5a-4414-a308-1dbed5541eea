<?php

namespace App\Services;

use App\Calculations\PerformanceIndicators\CollaborativeWorkOrders;
use App\Calculations\PerformanceIndicators\ComplianceRate;
use App\Calculations\PerformanceIndicators\ControlAndArchiveDocuments;
use App\Calculations\PerformanceIndicators\ExecutionTime;
use App\Calculations\PerformanceIndicators\FinancialObligationsFulfillment;
use App\Calculations\PerformanceIndicators\FiveStarRating;
use App\Calculations\PerformanceIndicators\HumanResources;
use App\Calculations\PerformanceIndicators\Injuries;
use App\Calculations\PerformanceIndicators\LocalMaterial;
use App\Calculations\PerformanceIndicators\LocalWorkforce;
use App\Calculations\PerformanceIndicators\MrRating;
use App\Calculations\PerformanceIndicators\OnHoldEquipment;
use App\Calculations\PerformanceIndicators\PerformanceIndicatorInterface;
use App\Calculations\PerformanceIndicators\PmWorkOrdersFulfillment;
use App\Calculations\PerformanceIndicators\ReopenedWorkOrder;
use App\Calculations\PerformanceIndicators\ReportingAccuracy;
use App\Calculations\PerformanceIndicators\ResponseTime;
use App\Calculations\PerformanceIndicators\ServiceRequest;
use App\Calculations\PerformanceIndicators\SpareParts;
use App\Calculations\PerformanceIndicators\StakeholderCollaborationLevel;
use App\Calculations\PerformanceIndicators\Subcontractors;
use App\Calculations\PerformanceIndicators\UnclosedSEVComplaints;
use App\Calculations\PerformanceIndicators\UnfinishedWorkorders;
use App\Calculations\PerformanceIndicators\UnresolvedInspectionReports;
use App\Calculations\PerformanceIndicators\WorkerHousing;
use App\Calculations\PerformanceIndicators\WorkforceStandards;
use App\Jobs\ProcessPerformanceCalculationJob;
use App\Models\ContractPerformanceIndicator;
use App\Models\ContractPerformanceIndicatorData;
use App\Models\Contracts;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class PerformanceIndicatorService
{
    // KPI #1
    const SERVICE_REQUEST = ServiceRequest::class;

    // KPI #2
    const RESPONSE_TIME = ResponseTime::class;

    // KPI #3
    const EXECUTION_TIME = ExecutionTime::class;

    // KPI #4
    const FIVE_STAR_RATING = FiveStarRating::class;

    // KPI #5
    const ON_HOLD_EQUIPMENT = OnHoldEquipment::class;

    // KPI #6
    const REOPENED_WORK_ORDER = ReopenedWorkOrder::class;

    // KPI #7
    const MR_RATING = MrRating::class;

    // KPI #8
    const PM_WORK_ORDERS_FULFILLMENT = PmWorkOrdersFulfillment::class;

    // KPI #9
    const UNFINISHED_WORK_ORDERS = UnfinishedWorkOrders::class;

    // KPI #10
    const SPARE_PARTS = SpareParts::class;

    // KPI #11
    const HUMAN_RESOURCES = HumanResources::class;

    // KPI #12
    const REPORTING_ACCURACY = ReportingAccuracy::class;

    // KPI #13
    const COMPLIANCE_RATE = ComplianceRate::class;

    // KPI #14
    const CONTROL_AND_ARCHIVE_DOCUMENTS = ControlAndArchiveDocuments::class;

    // KPI #15
    const STAKEHOLDER_COLLABORATION_LEVEL = StakeholderCollaborationLevel::class;

    // KPI #16
    const FINANCIAL_OBLIGATIONS_FULFILLMENT = FinancialObligationsFulfillment::class;

    // KPI #17
    const LOCAL_MATERIAL = LocalMaterial::class;

    // KPI #18
    const LOCAL_WORKFORCE = LocalWorkforce::class;

    // KPI #19
    const INJURIES = Injuries::class;

    // KPI #20
    const UNRESOLVED_INSPECTION_REPORTS = UnresolvedInspectionReports::class;

    // KPI #21
    const UNCLOSED_SEV_COMPLAINTS = UnclosedSEVComplaints::class;

    // KPI #22
    const COLLABORATIVE_WORK_ORDERS = CollaborativeWorkOrders::class;

    // KPI #23
    const WORKER_HOUSING = WorkerHousing::class;

    // KPI #24
    const SUBCONTRACTORS = Subcontractors::class;

    // KPI #25
    const WORKFORCE_STANDARDS = WorkforceStandards::class;

    const PERFORMANCE_INDICATORS = [
        self::SERVICE_REQUEST,
        self::RESPONSE_TIME,
        self::EXECUTION_TIME,
        self::FIVE_STAR_RATING,
        self::ON_HOLD_EQUIPMENT,
        self::REOPENED_WORK_ORDER,
        self::MR_RATING,
        self::PM_WORK_ORDERS_FULFILLMENT,
        self::UNFINISHED_WORK_ORDERS,
        self::SPARE_PARTS,
        self::HUMAN_RESOURCES,
        self::REPORTING_ACCURACY,
        self::COMPLIANCE_RATE,
        self::CONTROL_AND_ARCHIVE_DOCUMENTS,
        self::STAKEHOLDER_COLLABORATION_LEVEL,
        self::FINANCIAL_OBLIGATIONS_FULFILLMENT,
        self::LOCAL_MATERIAL,
        self::LOCAL_WORKFORCE,
        self::INJURIES,
        self::UNRESOLVED_INSPECTION_REPORTS,
        self::UNCLOSED_SEV_COMPLAINTS,
        self::COLLABORATIVE_WORK_ORDERS,
        self::WORKER_HOUSING,
        self::SUBCONTRACTORS,
        self::WORKFORCE_STANDARDS,
    ];

    const SCORE_WEIGHTS = [
        self::SERVICE_REQUEST => 0.02, #1
        self::RESPONSE_TIME => 0.02, #2
        self::EXECUTION_TIME => 0.15, #3
        self::FIVE_STAR_RATING => 0.05, #4
        self::ON_HOLD_EQUIPMENT => 0.02, #5
        self::REOPENED_WORK_ORDER => 0.03, #6
        self::MR_RATING => 0.02, #7
        self::PM_WORK_ORDERS_FULFILLMENT => 0.20, #8
        self::UNFINISHED_WORK_ORDERS => 0.03, #9
        self::SPARE_PARTS => 0.02, #10
        self::HUMAN_RESOURCES => 0.10, #11
        self::REPORTING_ACCURACY => 0.02, #12
        self::COMPLIANCE_RATE => 0.05, #13
        self::CONTROL_AND_ARCHIVE_DOCUMENTS => 0.02, #14
        self::STAKEHOLDER_COLLABORATION_LEVEL => 0.02, #15
        self::FINANCIAL_OBLIGATIONS_FULFILLMENT => 0.02, #16
        self::LOCAL_MATERIAL => 0.03, #17
        self::LOCAL_WORKFORCE => 0.04, #18
        self::INJURIES => 0.02, #19-0.02
        self::UNRESOLVED_INSPECTION_REPORTS => 0.02, #20-0.02
        self::UNCLOSED_SEV_COMPLAINTS => 0.02, #21-0.02
        self::COLLABORATIVE_WORK_ORDERS => 0.02, #22-0.02
        self::WORKER_HOUSING => 0.02, #23-0.02
        self::SUBCONTRACTORS => 0.02,#24-0.05
        self::WORKFORCE_STANDARDS => 0.02, #25
    ];

    const TOTAL_ITEM_KEY = [
        self::EXECUTION_TIME => 'totalRmWorkOrdersCount',
        self::FIVE_STAR_RATING => 'totalClosedWorkOrdersCount',
        self::ON_HOLD_EQUIPMENT => 'totalClosedWorkOrdersCount',
        self::REOPENED_WORK_ORDER => 'totalWorkOrdersCount',
        self::MR_RATING => 'totalWorkOrdersCount',
        self::PM_WORK_ORDERS_FULFILLMENT => 'totalPmWorkOrdersCount',
        self::HUMAN_RESOURCES => 'targetPercentage',
        self::UNFINISHED_WORK_ORDERS => 'totalRmWorkOrdersCount',
        self::SPARE_PARTS => 'total_items',
        self::SUBCONTRACTORS => 'total_services',
        self::CONTROL_AND_ARCHIVE_DOCUMENTS => 'totalDocumentsCount',
    ];

    const ALLOW_LINKING_WITH_SERVICE = [
       
        self::SERVICE_REQUEST,
        self::RESPONSE_TIME,
        self::EXECUTION_TIME,
        self::FIVE_STAR_RATING,
        self::ON_HOLD_EQUIPMENT,
        self::REOPENED_WORK_ORDER,
        self::MR_RATING,
        self::PM_WORK_ORDERS_FULFILLMENT,
        self::UNFINISHED_WORK_ORDERS,
        self::WORKFORCE_STANDARDS,
    ];

    const AKAUNTING_EXCLUSIVE_INDICATORS = [
        self::SPARE_PARTS,
        self::LOCAL_MATERIAL,
    ];

    public static $contractPerformanceData = [];

    public function getAllContractsWithPerformanceIndicators()
    {
        return Contracts::with('contractPerformanceIndicators')->has('contractPerformanceIndicators')->get();
    }

    public static function getPerformanceIndicators()
    {
        if (AkauntingService::allow()) {
            $performanceIndicators = self::PERFORMANCE_INDICATORS;
        } else {
            $performanceIndicators = array_diff(self::PERFORMANCE_INDICATORS, self::AKAUNTING_EXCLUSIVE_INDICATORS);
        }

        return $performanceIndicators;
    }

    public function getOverallPerformancePercentage(\Illuminate\Support\Collection $contracts)
    {
        $totalScore = 0;

        $contractPerformanceIndicatorDatas = ContractPerformanceIndicatorData::query()
            ->whereIn('contract_id', $contracts->pluck('id'))
            ->latest('calculated_at')
            ->get();

        foreach ($contracts as $contract) {
            $cpid = $contractPerformanceIndicatorDatas->where('contract_id', $contract->id)->first();
            if (!$cpid){
                ProcessPerformanceCalculationJob::dispatch($contract);
            }else{
                $totalScore += $cpid->score;
            }
        }

        return number_format($totalScore / $contracts->count(), 2);
    }

    public function getOverallPerformancePercentageOld(\Illuminate\Support\Collection $contracts)
    {
        $totalScore = 0;

        foreach ($contracts as $contract) {
            $totalScore += $this->calculate($contract);
        }

        return number_format($totalScore / $contracts->count(), 2);
    }

    public function getOverallPerformanceChartDataset(Collection $contracts)
    {
        // Get the first contract from the collection
        $contracts = $contracts->take(1);
        $data = [];
        $barLabels = [];
        $labels = [];
        $classNames = [];
        $values = [];
        $backgroundColors = [];
        $points = [];

        $contractPerformanceIndicatorDatas = ContractPerformanceIndicatorData::query()
            ->whereIn('contract_id', $contracts->pluck('id'))
            ->latest('calculated_at')
            ->get();

        foreach (self::PERFORMANCE_INDICATORS as $performanceIndicatorClass) {

            /** @var PerformanceIndicatorInterface $performanceIndicator */
            $performanceIndicator= (new $performanceIndicatorClass());

            $score = 0;
            $deductionPoints = 0;

            foreach ($contracts as $contract) {
                //$score += Arr::get($performanceIndicator->calculate($contract), $performanceIndicator->getPercentageKey(), 0);
                $cpid = $contractPerformanceIndicatorDatas->where('contract_id', $contract->id)->first();
                if ($cpid){
                    $contractPerformanceIndicatorData = json_decode($cpid->data, true);
                    $score += Arr::get($contractPerformanceIndicatorData, $performanceIndicatorClass.'.'.$performanceIndicator->getPercentageKey(), 0);
                    $deductionPoints += Arr::get($contractPerformanceIndicatorData, $performanceIndicatorClass.'.'.$performanceIndicator->getDeductionPoints(), 0);
                }else{
                    ProcessPerformanceCalculationJob::dispatch($contract);
                }
            }

            $values[] = number_format($score / $contracts->count(), 2);
            $points[] = $deductionPoints;
            $labels[] = $performanceIndicator->getName();
            $barLabels[] = $performanceIndicator->getBarName();
            $classNames[] = $performanceIndicatorClass;
        }

        foreach ($values as $value) {
            $backgroundColors[] = $this->getPercentageColor($value);
        }

        $data[] = [
            'data' => $values,
            'points' => $points,
            'backgroundColor' => $backgroundColors
        ];

        return [
            'classNames' => $classNames,
            'labels' => $labels,
            'barLabels' => $barLabels,
            'datasets' => $data,
        ];
    }

    public function getOverallPerformanceChartDatasetOld(Collection $contracts)
    {
        $data = [];
        $barLabels = [];
        $labels = [];
        $classNames = [];
        $values = [];
        $backgroundColors = [];

        foreach (self::PERFORMANCE_INDICATORS as $performanceIndicatorClass) {

            /** @var PerformanceIndicatorInterface $performanceIndicator */
            $performanceIndicator= (new $performanceIndicatorClass());

            $score = 0;

            foreach ($contracts as $contract) {
//                $score += $performanceIndicator->calculate($contract)[$performanceIndicator->getPercentageKey()];
                $score += Arr::get($performanceIndicator->calculate($contract), $performanceIndicator->getPercentageKey(), 0);
            }

            $values[] = number_format($score / $contracts->count(), 2);
            $labels[] = $performanceIndicator->getName();
            $barLabels[] = $performanceIndicator->getBarName();
            $classNames[] = $performanceIndicatorClass;
        }

        foreach ($values as $value) {
            $backgroundColors[] = $this->getPercentageColor($value);
        }

        $data[] = [
            'data' => $values,
            'backgroundColor' => $backgroundColors
        ];

        return [
            'classNames' => $classNames,
            'labels' => $labels,
            'barLabels' => $barLabels,
            'datasets' => $data,
        ];
    }

    public function getPercentageColor($percentage)
    {
        if ($percentage < 30) {
            return '#e74c3c';
        } else if ($percentage < 70) {
            return '#f1c40f';
        } else if ($percentage < 100) {
            return '#27ae60';
        } else {
            return '#2980b9';
        }
    }

    public function getDataForContractOverview(Contracts $contracts)
    {
        $data = [];
        $totalWeight = 0;

        $cpid = ContractPerformanceIndicatorData::query()
            ->where('contract_id', $contracts->id)
            ->latest('calculated_at')
            ->first();

        if (!$cpid) {
            ProcessPerformanceCalculationJob::dispatch($contracts);
            return $data;
        }

        $contractPerformanceIndicatorData = json_decode($cpid->data, true);

        // Check if the decoded data is valid and not null
        if (is_array($contractPerformanceIndicatorData)) {
            foreach ($contractPerformanceIndicatorData as $class => $value) {
                $totalWeight += self::SCORE_WEIGHTS[$class];
            }

            foreach ($contractPerformanceIndicatorData as $class => $value) {
                $performanceIndicator = new $class($class);
                $dynamicWeight = self::SCORE_WEIGHTS[$class] / $totalWeight;

                $data[] = [
                    'name' => (new $class())->getName(),
                    'score' => Arr::get($value, $performanceIndicator->getPercentageKey(), 0),
                    'weight' => self::SCORE_WEIGHTS[$class],
                    'dynamicWeight' => number_format($dynamicWeight, 2),
                    'nonformattedWeight' => $dynamicWeight,
                    'kpi_class' => get_class($performanceIndicator),
                ];
            }
        }

        return $data;
    }

    public function getDataForContractOverviewOld(Contracts $contract){
        $contractPerformanceIndicators = $contract->contractPerformanceIndicators;

        $data = [];

        $totalWeight = 0;
        $dynamicWeightTotal = 0;

        foreach ($contractPerformanceIndicators as $contractPerformanceIndicator) {
            $totalWeight += self::SCORE_WEIGHTS[get_class($contractPerformanceIndicator->performanceIndicator())];
        }

        foreach ($contractPerformanceIndicators as $contractPerformanceIndicator) {

//            $currentIndicatorScore =
//                Arr::get($contractPerformanceIndicator->calculate(), $contractPerformanceIndicator->performanceIndicator()->getPercentageKey(), 0) *
//                self::SCORE_WEIGHTS[get_class($contractPerformanceIndicator->performanceIndicator())];
//
            $dynamicWeight = self::SCORE_WEIGHTS[get_class($contractPerformanceIndicator->performanceIndicator())] / $totalWeight;
            $dynamicWeightTotal += $dynamicWeight;

            $data[] = [
                'name' => $contractPerformanceIndicator->performanceIndicator()->getName(),
//                'score' => $contractPerformanceIndicator->calculate()[$contractPerformanceIndicator->performanceIndicator()->getPercentageKey()],
                'score' => Arr::get($contractPerformanceIndicator->calculate(), $contractPerformanceIndicator->performanceIndicator()->getPercentageKey(), 0),
//                'score' => 0,
                'weight' => self::SCORE_WEIGHTS[get_class($contractPerformanceIndicator->performanceIndicator())],
                'dynamicWeight' => number_format($dynamicWeight, 2),
            ];
        }

        return $data;
    }

    /**
     * @param Contracts $contract
     * @param string $indicator
     * @return mixed
     */
    public function calculateByIndicator(Contracts $contract, string $indicator)
    {
        $performanceIndicator = new $indicator($contract);
//        return $performanceIndicator->calculate($contract)[$performanceIndicator->getPercentageKey()];
        return Arr::get($performanceIndicator->calculate($contract), $performanceIndicator->getPercentageKey(), 0);
    }

    public function getDataByIndicator(Contracts $contract, string $indicator)
    {
        $performanceIndicator = new $indicator($contract);
        return $performanceIndicator->calculate($contract);
    }

    public function updateContractPerformanceIndicators($contract_id, $enabledContractPerformanceIndicators, \Illuminate\Support\Collection $contractPerformanceIndicators)
    {
        foreach ($enabledContractPerformanceIndicators as $contractPerformanceIndicatorClass)
        {
            // filter out the ranges based on the key that is present in $contractPerformanceIndicators
            $data = $contractPerformanceIndicators->filter(
                fn(ContractPerformanceIndicator $contractPerformanceIndicator, $key ) => str_contains($key, $contractPerformanceIndicatorClass)
            );

            // data->count == 0 means that there are no ranges for this performance indicator, go to default
            if (!$data->count()) {
                $data = collect([]);
                foreach (ContractPerformanceIndicator::RANGES as $key => $range) {
                    $data->push(new ContractPerformanceIndicator([
                        'contract_id' => $contract_id,
                        'range_id' => $key,
                        'penalty' => ContractPerformanceIndicator::DEFAULT_RANGE_PENALTIES[$key],
                        'performance_indicator' => $contractPerformanceIndicatorClass,
                    ]));
                }
            }else{
                // if there are ranges, we need to set the contract_id
                $data->map(fn(ContractPerformanceIndicator $contractPerformanceIndicator) => $contractPerformanceIndicator->contract_id = $contract_id);
            }

            // update the contract performance indicators
            $data->map(fn(ContractPerformanceIndicator $contractPerformanceIndicator) => $contractPerformanceIndicator->save());
        }

    }

    public function getWeightForContractAndPerformanceIndicator(Contracts $contract, PerformanceIndicatorInterface $performanceIndicator)
    {
        $contractPerformanceIndicators = $contract->contractPerformanceIndicators;

        $totalWeight = 0;

        foreach ($contractPerformanceIndicators as $contractPerformanceIndicator) {
            $totalWeight += self::SCORE_WEIGHTS[get_class($contractPerformanceIndicator->performanceIndicator())];
        }

        // weird flex but ok
        if ($totalWeight == 0) {
            return 1;
        }

        return self::SCORE_WEIGHTS[get_class($performanceIndicator)] / $totalWeight;
    }

    public function getDeductionPoints(Contracts $contract, PerformanceIndicatorInterface $performanceIndicator)
    {
        $result = $performanceIndicator->calculate($contract);

        if (array_key_exists('deductionPoints', $result)) {
            return $result['deductionPoints'];
        }

        return 'N/A';
    }

    public function getTotalItemCount(Contracts $contract, PerformanceIndicatorInterface $performanceIndicator)
    {
        $result = $performanceIndicator->calculate($contract);

        if (array_key_exists(get_class($performanceIndicator), self::TOTAL_ITEM_KEY)) {
            return $result[self::TOTAL_ITEM_KEY[get_class($performanceIndicator)]];
        }

        return 'N/A';
    }


    /**
     * @param Contracts $contract
     * @return float|int
     */
    public function calculate(Contracts $contract)
    {
        // we add together all the weights to get the total weight
        $totalWeight = 0;

        // TODO: refactor this to get the active performance indicators for the contracts
//        foreach (self::SCORE_WEIGHTS as $weight) {
//            $totalWeight += $weight;
//        }

        $performanceIndicators = [];

        /**
         * @deprecated this is deprecated because we are now using the ContractPerformanceIndicator model
         */
        // instantiate all performance indicators
//        foreach (self::PERFORMANCE_INDICATORS as $performanceIndicator) {
//            $performanceIndicators[] = new $performanceIndicator($contract);
//        }

        // get the active performance indicators for the contract
        $contractPerformanceIndicators = $contract->contractPerformanceIndicators;

        // calculate the total weight
        foreach ($contractPerformanceIndicators as $contractPerformanceIndicator) {
            $totalWeight += self::SCORE_WEIGHTS[get_class($contractPerformanceIndicator->performanceIndicator())];
        }

        // score is in percentage, each performance indicator returns a percentage
        // if we have 2 performance indicators, each with 50% score, the total score will be 50%
        // if we have 1 performance indicator with 100% score and 0.05 weight, the total score will be 5%
        $score = 0;

        foreach ($contractPerformanceIndicators as $contractPerformanceIndicator) {

            // calculate the score for each performance indicator and multiply it by the weight
//            $currentIndicatorScore =
//                $contractPerformanceIndicator->calculate()[$contractPerformanceIndicator->performanceIndicator()->getPercentageKey()] *
//                self::SCORE_WEIGHTS[get_class($contractPerformanceIndicator->performanceIndicator())];

            $currentIndicatorScore =
                Arr::get($contractPerformanceIndicator->calculate(), $contractPerformanceIndicator->performanceIndicator()->getPercentageKey(), 0) *
                self::SCORE_WEIGHTS[get_class($contractPerformanceIndicator->performanceIndicator())];

            // reweigh the score based on how many performance indicators are active
            $reweighedScore = ($currentIndicatorScore / $totalWeight);

            // add the score to the total score
            $score += $reweighedScore;
        }

        // return the score
        return $score;
    }

    public function getPenaltyPercentageByIndicator(Contracts $contract, string $indicator)
    {
        $percentage = $this->calculateByIndicator($contract, $indicator);
        $rangeId = ContractPerformanceIndicator::getRangeIdByPercentage($percentage);

        $contractPerformanceIndicator = ContractPerformanceIndicator::where('contract_id', $contract->id)
            ->where('performance_indicator', $indicator)
            ->where('range_id', $rangeId)
            ->first();

        if (!$contractPerformanceIndicator){
            return 0;
        }

        return $contractPerformanceIndicator->penalty;
    }

    public function calculatePenaltyByIndicator(Contracts $contract, string $indicator)
    {
        $penaltyPercentage = $this->getPenaltyPercentageByIndicator($contract, $indicator);

        if ($penaltyPercentage == 0) {
            return 0;
        }

        $penaltyMultiplier = $penaltyPercentage / 100;

        $contractValue = $contract->contract_value;
        $weightedAmountForPerformanceIndicator = $contractValue * $this->getWeightForContractAndPerformanceIndicator($contract, new $indicator($contract));
        $penalty = $weightedAmountForPerformanceIndicator * $penaltyMultiplier;

        return $penalty;
    }

    public function calculatePenalty(Contracts $contract)
    {
        $penalty = 0;

        foreach ($contract->contractPerformanceIndicators as $contractPerformanceIndicator) {
            $penalty += $this->calculatePenaltyByIndicator($contract, get_class($contractPerformanceIndicator->performanceIndicator()));
        }

        return $penalty;
    }

    /**
     * Return Performance Indicator By Contract
     */
    public static function getPerformanceIndicatorsByContractID($contract_id=null)
    {   
        /** @var User $user */
        $user = Auth::user();
        $user_type = $user->user_type;

        // filter between all contracts
        $contractsQuery = Contracts::with('contractPerformanceIndicators', 'serviceProvider.city')
        ->has('contractPerformanceIndicators');

        if ($user_type == 'admin') {
            $contractsQuery->where(['user_id' => $user->id]);
        }

        if ($user_type == 'building_manager') {
            $project = $user->projectDetails;
            $projectOwnerAdmin = $project->users()->where('user_type', '=', 'admin')->first();
            $contractsQuery->where(['user_id' => $projectOwnerAdmin->id]);
        }

        if ($user_type == 'sp_admin' || $user_type == 'supervisor'){
            $contractsQuery->where(['service_provider_id' => $user->serviceProvider->id]);
        }
        if($contract_id){
            $contractsQuery->where('id', $contract_id);
        }
        $contracts = $contractsQuery->get();

        $performanceIndicatorRes = [];
        foreach ($contracts->flatMap->contractPerformanceIndicators as $contractPerformanceIndicator) {
                $performanceIndicatorRes[$contractPerformanceIndicator->performanceIndicator()->getBarName()] = $contractPerformanceIndicator->performanceIndicator()->getName();
        }
        return $performanceIndicatorRes;
    }

}
