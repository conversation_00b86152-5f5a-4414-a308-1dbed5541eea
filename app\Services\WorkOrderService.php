<?php

namespace App\Services;


use App\Enums\AssignType;
use App\Http\Controllers\Api\V1\Tenant\ApiHelper;
use App\Models\Notification;
use App\Models\WorkOrders;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;


class WorkOrderService
{

    private function baseTeamLeaderWorkorderQuery()
    {
        return WorkOrders::with('workerTimings')
            ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
            ->leftjoin('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
            ->leftjoin('properties', 'properties.id', '=', 'property_buildings.property_id')
            ->leftjoin('users', 'users.id', '=', 'work_orders.created_by')
            ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
            ->where('job_status', '<>', 'active');
    }

    private function baseTeamLeaderQuery(array $workerIds)
    {
        return $this->baseTeamLeaderWorkorderQuery()
            ->where(function ($q) use ($workerIds) {
                $q->whereIn('work_orders.worker_id', $workerIds)
                    ->whereNull('worker_started_at')
                    ->orWhere(function ($inner) use ($workerIds) {
                        $inner->whereIn('work_order_workers.worker_id', $workerIds)
                            ->where('work_orders.is_collaborative', 1);
                    })
                    ->where(function ($inner) use ($workerIds) {
                        $inner->whereDoesntHave('workerTimings', function ($wt) use ($workerIds) {
                            $wt->whereIn('worker_id', $workerIds);
                        })->orWhereDoesntHave('workerTimings');
                    });
            });
    }
    public function getWorkOrdersCountForAssignedWorkers(array $filters = []): array
    {
        $workerIds = $this->resolveWorkerIds($filters['worker_id'] ?? null);
        $today = now();

        $rejected = $this->baseQuery($workerIds)
            ->whereRaw("(status = 2 AND sp_reopen_status != 2 AND 
                         (workorder_journey = 'job_execution' OR workorder_journey = 'job_evaluation') AND 
                         (bm_approve_job = 1 OR sp_approve_job = 1 OR 
                          (bm_approve_job = 0 AND sp_approve_job = 0 AND workorder_journey = 'job_execution' AND reason != '')))");

        $reopen = $this->baseQuery($workerIds)
            ->whereRaw("(status = 2 AND workorder_journey = 'job_execution' AND sp_reopen_status = 2)")
            ->whereNull('worker_started_at');

        $onHold = $this->baseQuery($workerIds)
            ->whereRaw("(status = 3 AND (workorder_journey = 'job_execution' OR workorder_journey = 'job_evaluation'))");

        $overdue = $this->baseQuery($workerIds)
            ->whereRaw("(target_date < ? AND status = 2 AND workorder_journey = 'job_execution' AND 
                         bm_approve_job != 1 AND sp_approve_job != 1 AND sp_reopen_status != 2)", [$today]);

        $completed = $this->baseQuery($workerIds)
            ->whereNotNull('job_submitted_at')
            ->whereRaw('UNIX_TIMESTAMP(job_submitted_at) > 0')
            ->whereIn('job_completed_by', ['worker', 'SP']);

        // Apply filters, search, sort
        foreach ([$rejected, $reopen, $onHold, $overdue, $completed] as $query) {
            $this->applyCommonFilters($query, $filters);
        }

        return [
            ['type' => 'completed', 'value' => $completed->count()],
            ['type' => 'overdue', 'value' => $overdue->count()],
            ['type' => 'onhold', 'value' => $onHold->count()],
            ['type' => 'reopen', 'value' => $reopen->count()],
            ['type' => 'rejected', 'value' => $rejected->count()],
        ];
    }    




    private function resolveWorkerIds($input)
    {
        if ($input) {
            return is_array($input) ? $input : explode(',', $input);
        }

        $assigned = Auth::user()->assigned_workers;
        return $assigned ? explode(',', $assigned) : [0];
    }

    private function baseQuery(array $workerIds)
    {
        return WorkOrders::whereIn('worker_id', $workerIds);
    }

    private function applyFilters($query, array $filters)
    {
        $map = [
            'property_id' => 'work_orders.property_id',
            'zone_id' => 'work_orders.floor',
            'unit_id' => 'work_orders.unit_id',
            'asset_name_id' => 'work_orders.asset_name_id',
            'service_type' => 'work_orders.service_type',
            'work_order_type' => 'work_orders.work_order_type',
        ];

        foreach ($map as $key => $column) {
            if (!empty($filters[$key])) {
                $query->whereIn($column, explode(',', $filters[$key]));
            }
        }
    }

    private function applySearch($query, ?string $search)
    {
        if (!empty($search)) {
            $query->where('work_orders.work_order_id', 'like', "%$search%");
        }
    }

    private function applySort($query, ?string $sort_by = null)
    {

        switch ($sort_by) {
            case 'desc':
                $query->orderBy('work_orders.id', 'desc');
                break;

            case 'asc':
                $query->orderBy('work_orders.id', 'asc');
                break;

            case 'deadline':
                $query->orderBy('work_orders.target_date', 'asc');
                break;

            default:
                $query->orderBy('work_orders.id', 'asc'); // default fallback
                break;
        }
    }

    private function applyDateRange($query, array $filters)
    {
        if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
            $start = Carbon::parse($filters['from_date'])->startOfDay();
            $end = Carbon::parse($filters['to_date'])->endOfDay();
            $query->whereRaw("(IF(work_orders.work_order_type != 'preventive', work_orders.created_at, work_orders.start_date) BETWEEN ? AND ?)", [$start, $end]);
        }
    }


    private function applyCommonFilters($query, array $filters)
    {
        $this->applyFilters($query, $filters);
        $this->applyDateRange($query, $filters);
        $this->applySearch($query, $filters['search'] ?? null);
        $this->applySort($query, $filters['sort_by'] ?? null);
    }


    public function getTeamLeaderActiveTaskCount(array $filters = []): int
    {
        $workerIds = $this->resolveWorkerIds($filters['worker_id'] ?? null);

        $query = WorkOrders::with('workerTimings')
            ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
            ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
            ->where('work_orders.workorder_journey', 'job_execution')
            ->whereNotNull('job_started_at')
            ->where('work_orders.status', 2)
            ->where(function ($query) use ($workerIds) {
                $query->whereIn('work_orders.worker_id', $workerIds)
                    ->whereNotNull('worker_started_at')
                    ->orWhere(function ($q) use ($workerIds) {
                        $q->whereIn('work_order_workers.worker_id', $workerIds)
                            ->where('work_orders.is_collaborative', 1);
                    })
                    ->whereHas('workerTimings', function ($q) use ($workerIds) {
                        $q->whereIn('worker_id', $workerIds)->whereNull('end_time');
                    });
            });

            $this->applyCommonFilters($query, $filters);

        return $query->count();
    }




    public function getTeamLeaderUpcomingTaskCount(array $filters = []): int
    {
        $today = now()->toDateString();
        $workerIds = $this->resolveWorkerIds($filters['worker_id'] ?? null);
        $examine = $filters['examine'] ?? null;

        $query = $this->baseTeamLeaderWorkorderQuery()
            ->where(function ($query) use ($today, $examine) {
                if (is_null($examine)) {
                    $query->where(function ($q) use ($today) {
                        $q->where(function ($inner) use ($today) {
                            $inner->where('work_orders.work_order_type', 'reactive')
                                ->where(function ($cond) use ($today) {
                                    $cond->where('work_orders.start_date', '>=', $today)
                                        ->orWhere('work_orders.start_date', '<=', $today);
                                });
                        })->orWhere(function ($inner) use ($today) {
                            $inner->where('work_orders.work_order_type', 'preventive')
                                ->where('work_orders.start_date', '<=', $today);
                        });
                    });
                } else {
                    $query->where(function ($q) use ($today) {
                        $q->where('work_orders.start_date', '>=', $today)
                        ->orWhere('work_orders.start_date', '<=', $today);
                    });
                }

                $query->orWhere(function ($sub) use ($today) {
                    $sub->where('work_orders.assign_type', AssignType::Smart->value)
                        ->where('work_orders.job_started_at', '<=', $today . ' 23:59:59');
                });
            });

        // Status & approval condition
        $query->where('workorder_journey', 'job_execution');

        if (is_null($examine)) {
            $query->whereRaw("((work_orders.status = 1 AND workorder_journey = 'job_execution') OR 
                ((work_orders.status = 2 OR work_orders.status = 3) AND 
                workorder_journey = 'job_execution' AND 
                work_orders.bm_approve_job != 1 AND 
                (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1)))");
        } else {
            $query->whereRaw("(work_orders.status = 2 OR work_orders.status = 3) AND 
                work_orders.bm_approve_job != 1 AND 
                (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1)");
        }

        // Worker assignment condition
        $query->where(function ($q) use ($workerIds) {
            $q->whereIn('work_orders.worker_id', $workerIds)->whereNull('worker_started_at')
            ->orWhere(function ($inner) use ($workerIds) {
                $inner->whereIn('work_order_workers.worker_id', $workerIds)
                        ->where('work_orders.is_collaborative', 1);
            })
            ->where(function ($inner) use ($workerIds) {
                $inner->whereDoesntHave('workerTimings', function ($wt) use ($workerIds) {
                    $wt->whereIn('worker_id', $workerIds);
                })->orWhereDoesntHave('workerTimings');
            });
        });

        // Examine check
        $query->when(is_null($examine),
            fn($q) => $q->whereNull('examine_button_clicked_at'),
            fn($q) => $q->whereNotNull('examine_button_clicked_at')
        );

        // Apply reusable filters
        $this->applyCommonFilters($query, $filters);

        return $query->count();
    }



    public function getTeamLeaderWorkOrderCount(array $filters = [])
    {
        $workerIds = $this->resolveWorkerIds($filters['worker_id'] ?? null);

        $query = $this->baseTeamLeaderQuery($workerIds);

        $result = $query->get(['work_orders.work_order_type'])
                ->groupBy('work_order_type')
                ->map(fn($items) => $items->count())
                ->toArray();

        return [
            'preventive' => $result['preventive'] ?? 0,
            'reactive' => $result['reactive'] ?? 0,
        ];

    }



public function getWorkordersListforAssignedWorkers(string $filterType, int $page, array $filters = [])
{
        $workerIds = $this->resolveWorkerIds($filters['worker_id'] ?? null);
        $tdate = now();

        $queryBuilder = function ($condition) use ($workerIds) {
            return WorkOrders::select([
                    'work_orders.id', 'work_orders.work_order_id', 'work_orders.property_id',
                    'work_orders.service_type', 'work_orders.asset_name_id', 'work_orders.description',
                    'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date',
                    'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey',
                    'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date',
                    'property_buildings.building_name', 'work_orders.asset_category_id',
                    'work_orders.work_order_type', 'work_orders.frequency_id', 'work_orders.priority_id',
                    'work_orders.response_time'
                ])
                ->whereIn('work_orders.worker_id', $workerIds)
                ->leftjoin('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
                ->whereRaw($condition);
        };

        switch ($filterType) {
            case 'rejected':
                $query = $queryBuilder("(status = 2 AND sp_reopen_status != 2 AND
                    (workorder_journey = 'job_execution' OR workorder_journey = 'job_evaluation') AND 
                    (bm_approve_job = 1 OR sp_approve_job = 1 OR 
                    (bm_approve_job = 0 AND sp_approve_job = 0 AND workorder_journey = 'job_execution' AND reason != '')))");
                break;

            case 'reopen':
                $query = $queryBuilder("(status = 2 AND workorder_journey = 'job_execution' AND sp_reopen_status = 2)")
                    ->whereNull('worker_started_at');
                break;

            case 'onhold':
                $query = $queryBuilder("(status = 3 AND (workorder_journey = 'job_execution' OR workorder_journey = 'job_evaluation'))");
                break;

            case 'overdue':
                $query = $queryBuilder("(target_date < '{$tdate}' AND status = 2 AND workorder_journey = 'job_execution' AND
                    bm_approve_job != 1 AND sp_approve_job != 1 AND sp_reopen_status != 2)");
                break;

            case 'completed':
                $query = $queryBuilder("job_submitted_at IS NOT NULL")
                    ->whereRaw('UNIX_TIMESTAMP(job_submitted_at) > 0')
                    ->whereIn('job_completed_by', ['worker', 'SP']);
                break;

            case 'active_task':
                $query = $this->getTeamleaderActiveTaskList($workerIds);
                break;

            case 'upcoming':
                $query = $this->getTeamLeaderUpcomingTaskList($workerIds);
                break;

            case 'examine':
                $query = $this->getTeamLeaderUpcomingTaskList($workerIds, 'examine');
                break;
                
            default:
                return collect([]);
        }

        $this->applyDateRange($query, $filters);
        $this->applyFilters($query, $filters);
        $this->applySearch($query, $filters['search'] ?? null);
        $this->applySort($query, $filters['sort_by'] ?? null);

        return $query->groupBy('work_orders.id')->paginate(10, ['*'], 'page', $page);
    
}




public function getTeamLeaderUpcomingTaskList($worker_id,$examine = NULL)
  {
    $tdate = date('Y-m-d');
    $workerids = Auth::user()->assigned_workers ? explode(',', Auth::user()->assigned_workers) : [0];
    
    if($worker_id != null && $worker_id != "")
                {
                    $workerids = $worker_id;
                }
    $upcoming_orders = WorkOrders::with('workerTimings')
    ->select('work_orders.id', 'work_orders.work_order_id',  'work_orders.property_id', 'work_orders.service_type', 'work_orders.asset_name_id', 'work_orders.sp_reopen_status', 'work_orders.worker_id', 'work_orders.old_worker_id', 'work_orders.description', 'contract_type', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'contracts.contract_number', 'property_buildings.building_name', 'work_orders.asset_category_id', 'work_orders.work_order_type', 'work_orders.frequency_id', 'work_orders.priority_id', 'work_orders.response_time', 'work_orders.worker_started_at', 'work_orders.sp_approve_job', 'work_orders.bm_approve_job')
    ->leftjoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                        ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->leftjoin('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                        ->leftjoin('properties', 'properties.id', '=', 'property_buildings.property_id')
                        //->leftjoin('users', 'users.id', '=', 'work_orders.created_by')
                        //->leftjoin('users as poa', 'poa.id', '=', 'contracts.user_id')
                        //->leftjoin('projects_details', 'projects_details.id', '=', 'poa.project_id')
                        ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
                        //->whereIn('work_orders.asset_category_id', explode(',', $loginuser_asset_categories))
                        ->where('job_status', '<>', 'active')
                        ->where(fn($query) =>
                        $query->where(function ($query) use ($tdate, $examine) {

                          if($examine == NULL) {
                              // If $examine is null, apply based on work_orders.type
                              $query->where(function ($q) use ($tdate) {
                                $q->where(function ($inner) use ($tdate) {
                                    $inner->where('work_orders.work_order_type', 'reactive')
                                          ->where(function ($cond) use ($tdate) {
                                              $cond->where('work_orders.start_date', '>=', $tdate)
                                                  ->orWhere('work_orders.start_date', '<=', $tdate);
                                          });
                                })
                                ->orWhere(function ($inner) use ($tdate) {
                                    $inner->where('work_orders.work_order_type', 'preventive')
                                          ->where('work_orders.start_date', '<=', $tdate);
                                });
                            });
                          }
                          else
                          {
                              // If $examine is not null, apply both conditions
                              $query->where(function ($q) use ($tdate) {
                                $q->where('work_orders.start_date', '>=', $tdate)
                                  ->orWhere('work_orders.start_date', '<=', $tdate);
                            });
                          }
                          // $q->where('work_orders.start_date', '>=', date('Y-m-d'))
                          //       ->orWhere('work_orders.start_date', '<=', date('Y-m-d'));
                        })
                                    ->orWhere(fn($subQuery) =>
                                        $subQuery->where('work_orders.assign_type', AssignType::Smart->value)
                                                ->where('work_orders.job_started_at', '<=', date('Y-m-d') . ' 23:59:59')
                                    ));
                        if($examine == NULL) {
                          $upcoming_orders = $upcoming_orders->whereRaw("( (work_orders.status = 1 and workorder_journey = 'job_execution') OR ((work_orders.status = 2 or work_orders.status = 3) and (workorder_journey = 'job_execution') and (work_orders.bm_approve_job != 1 and (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1))))");
                        }
                        else
                        {
                          $upcoming_orders = $upcoming_orders->where('workorder_journey', 'job_execution')->whereRaw("(work_orders.status = 2 or work_orders.status = 3) and (work_orders.bm_approve_job != 1 and (work_orders.sp_approve_job = 0 OR work_orders.sp_approve_job = 1))");
                        }

                        $upcoming_orders = $upcoming_orders->where(function ($query) use ($workerids) {
                          $query->where('work_orders.worker_id', $workerids)->whereNull('worker_started_at')
                                ->orWhere(function ($query) use ($workerids) {
                                    $query->whereIn('work_order_workers.worker_id', $workerids)
                                          ->where('work_orders.is_collaborative', 1);
                                })
                                ->where(function ($query) use ($workerids) {
                                    $query->whereDoesntHave('workerTimings', function ($query) use ($workerids) {
                                        $query->whereIn('worker_id', $workerids);
                                    })
                                    ->orWhereDoesntHave('workerTimings');
                                });
                        });
        if($examine == NULL) {
          $upcoming_orders = $upcoming_orders->whereNull('examine_button_clicked_at');
        } else {
          $upcoming_orders = $upcoming_orders->whereNotNull('examine_button_clicked_at');
        }
        
    return $upcoming_orders;
  }


  public function getTeamleaderActiveTaskList($worker_id)
        {
          $workerids = Auth::user()->assigned_workers ? explode(',', Auth::user()->assigned_workers) : [0];
          
            if($worker_id != null && $worker_id != "")
                {
                    $workerids = $worker_id;
                }
          $upcoming_orders = WorkOrders::with('workerTimings')
          ->select('work_orders.id', 'work_orders.work_order_id', 'work_orders.property_id', 'work_orders.service_type', 'work_orders.asset_name_id', 'work_orders.description', 'work_orders.floor', 'work_orders.room', 'work_orders.start_date', 'work_orders.end_date', 'target_date', 'work_orders.status', 'workorder_journey', 'work_orders.created_at', 'job_started_at', 'job_submitted_at', 'job_completion_date', 'property_buildings.building_name', 'work_orders.asset_category_id', 'work_orders.work_order_type')
          ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                          ->leftjoin('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                          ->leftjoin('properties', 'properties.id', '=', 'property_buildings.property_id')
                          ->leftJoin('work_order_workers', 'work_order_workers.work_order_id', '=', 'work_orders.id')
                          ->where('work_orders.workorder_journey', 'job_execution')
                          ->whereNotNull('job_started_at')
                          ->whereRaw("(work_orders.status = 2)")
                          ->where(function ($query) use ($workerids) {
                            $query->where('work_orders.worker_id', $workerids)
                            ->whereNotNull('worker_started_at')
                                  ->orWhere(function ($query) use ($workerids) {
                                      $query->whereIn('work_order_workers.worker_id', $workerids)
                                            ->where('work_orders.is_collaborative', 1);
                                  })
                                  ->whereHas('workerTimings', function ($query) use ($workerids) {
                                        $query->whereIn('worker_id', $workerids)->whereNull('end_time');
                                    });
                          });
              return $upcoming_orders;
          }




    public function assignToTeamLeader($workOrder, int $teamLeaderId): array
    {
        if ($workOrder->is_handle_by_team_leader == 1) {
            return ['status' => false, 'code' => 409, 'message' => 'Work order already assigned to a team leader.'];
        }

        WorkOrders::where('id', $workOrder->id)
            ->update(['is_handle_by_team_leader' => 1, 'team_leader_id' => $teamLeaderId]);

            $workerDetails = \Helper::userDetail($workOrder->worker_id);
            $workerName = $workerDetails->name ?? '';

       
              $message = 'Team Leader <strong>'.Auth::user()->name.'</strong> took action on behalf of the worker <strong>'.$workerName.'</strong>';
              $message_ar = 'قام مشرف الفريق <strong>'.Auth::user()->name.'</strong> باتخاذ إجراء نيابةً عن العامل <strong>'.$workerName.'</strong>';
      
      
            //Notifiaction
            Notification::insert(array('user_id' => Auth::user()->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $workOrder->building_id, 'notification_sub_type'=> 'wo_restarted_wo' , 'section_id' => $workOrder->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'no'));
            //Timeline
            Notification::insert(array('user_id' => Auth::user()->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $workOrder->building_id, 'notification_sub_type'=> 'wo_restarted_wo' , 'section_id' => $workOrder->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'yes'));


            $this->sendAssignmentNotification(
                $workOrder->worker_id,
                $workOrder,
                'team_leader_assign',
                'team_leader_assign_workorder'
            );

        return ['status' => true, 'code' => 200, 'message' => 'Work order assigned to team leader successfully!'];
    }




    public function assignToWorker($workOrder, int $workerId)
    {
        if ($workOrder->is_handle_by_team_leader == 0) {
            return ['status' => false, 'code' => 409, 'message' => 'Work order already assigned to a worker.'];
        }

        // Update using query builder
        WorkOrders::where('id', $workOrder->id)->update([
            'worker_id' => $workerId,
            'old_worker_id' => $workOrder->worker_id ?? 0,
            'is_handle_by_team_leader' => 0,
            'team_leader_id' => 0,
        ]);

        $oldworkerDetails = \Helper::userDetail($workOrder->worker_id);
            $oldworkerName = $oldworkerDetails->name ?? '';

            
        $newworkerDetails = \Helper::userDetail($workOrder->worker_id);
        $newworkerName = $newworkerDetails->name ?? '';
        if($workerId == $workOrder->worker_id)
        {
            //if assign to same worker

            $message = 'Team Leader <strong>'.Auth::user()->name.'</strong> assigned the work order to the worker <strong>'.$newworkerName.'</strong>';
            $message_ar = 'قام مشرف الفريق <strong>'.Auth::user()->name.'</strong> بإعادة تعيين أمر العمل للعامل <strong>'.$newworkerName.'</strong>';
        }
        else
        {
            // Assign to different worker

            $message = 'Team Leader <strong>'.Auth::user()->name.'</strong> assigned the work order to the worker <strong>'.$newworkerName.'</strong> in place of the worker <strong>'.$oldworkerName.'</strong>';
            $message_ar = 'قام مشرف الفريق <strong>'.Auth::user()->name.'</strong> بإعادة تعيين أمر العمل للعامل <strong>'.$newworkerName.'</strong> بدلًا من العامل <strong>'.$oldworkerName.'</strong>';
        }
        
      
      
            //Notifiaction
            Notification::insert(array('user_id' => Auth::user()->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $workOrder->building_id, 'notification_sub_type'=> 'wo_restarted_wo' , 'section_id' => $workOrder->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'no'));
            //Timeline
            Notification::insert(array('user_id' => Auth::user()->id, 'message' => $message, 'message_ar' => $message_ar, 'section_type' => 'work_order', 'building_ids' => $workOrder->building_id, 'notification_sub_type'=> 'wo_restarted_wo' , 'section_id' => $workOrder->id, 'created_at' => date('Y-m-d H:i:s'), 'is_timeline'=>'yes'));


        $this->sendAssignmentNotification(
        $workerId,
        $workOrder,
        'worker_reassign',
        'reassign_worker'
    );

        return ['status' => true, 'code' => 200, 'message' => 'Work order assigned to worker successfully!'];
    }



    public function sendAssignmentNotification(int $userId, $workOrder, string $messageKey, string $notificationType): void
{
    $registration_ids = [];
    $workerDetails = \Helper::userDetail($userId);

    if (!$workerDetails || empty($workerDetails->device_token)) {
        $registration_ids[] = $workerDetails->device_token;
    }

    $lang = $workerDetails->selected_app_langugage ?? 'en';
    $title = $this->getAssignmentNotificationMessage($messageKey, $lang);

    $message = [
        'title' => $title,
        'body' => $workOrder->description,
        'section_id' => $workOrder->id,
        'notification_type' => $notificationType,
    ];

    ApiHelper::send_notification_worker_FCM(
        $userId,
        $registration_ids,
        $message,
        $workOrder->property_id,
        $notificationType,
        $workOrder->id
    );
}

    public function getAssignmentNotificationMessage(string $type, string $lang): string
    {
        return match ($type) {
            'team_leader_assign' => match ($lang) {
                'ar' => 'قام مشرف الفريق باستلام أمر العمل ولم تعد مكلّفًا به الآن',
                'ur' => 'ٹیم لیڈر نے ورک آرڈر سنبھال لیا ہے، آپ اب اس کے لیے مقرر نہیں ہیں۔',
                default => 'Team Leader has taken over the Work Order. You are no longer assigned to it.',
            },

            'worker_reassign' => match ($lang) {
                'ar' => 'تمت إعادة تعيين أمر العمل لك من قبل مشرف الفريق',
                'ur' => 'ورک آرڈر آپ کو ٹیم لیڈر کی جانب سے دوبارہ تفویض کر دیا گیا ہے۔',
                default => 'Work Order has been reassigned to you by the Team Leader.',
            },

            default => '',
        };
    }

}
