<?php
namespace App\Services;

use App\Models\WorkOrders;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

class WorkOrderSummaryService
{
    public function getSummaryByWorker($workerId)
    {
        $today = Carbon::now();

        $baseQuery = WorkOrders::where('worker_id', $workerId);

        return [
            'completed' => (clone $baseQuery)
                ->whereNotNull('job_submitted_at')
                ->whereRaw('UNIX_TIMESTAMP(job_submitted_at) > 0')
                ->whereIn('job_completed_by', ['worker', 'SP'])
                ->count(),

            'active' => (clone $baseQuery)
                ->where('status', 2)
                ->whereNotNull('worker_started_at')
                ->where(function ($q) {
                    $q->where('bm_approve_job', 0)->orWhereNull('bm_approve_job');
                })
                ->count(),

            'examined' => (clone $baseQuery)
                ->where('status', 2)
                ->where(function ($q) {
                    $q->where('bm_approve_job', 1)->orWhere('sp_approve_job', 1);
                })
                ->count(),

            'upcoming' => (clone $baseQuery)
                ->where('status', 1)
                ->whereDate('target_date', '>', $today)
                ->count(),

            'overdue' => (clone $baseQuery)
                ->where('status', 2)
                ->whereDate('target_date', '<', $today)
                ->where('bm_approve_job', '!=', 1)
                ->where('sp_approve_job', '!=', 1)
                ->where('sp_reopen_status', '!=', 2)
                ->count(),

            'on_hold' => (clone $baseQuery)
                ->where('status', 3)
                ->whereIn('workorder_journey', ['job_execution', 'job_evaluation'])
                ->count(),

            'reopen' => (clone $baseQuery)
                ->where('status', 2)
                ->where('workorder_journey', 'job_execution')
                ->where('sp_reopen_status', 2)
                ->whereNull('worker_started_at')
                ->count(),

            'rejected' => (clone $baseQuery)
                ->where('status', 2)
                ->where('sp_reopen_status', '!=', 2)
                ->whereIn('workorder_journey', ['job_execution', 'job_evaluation'])
                ->where(function ($q) {
                    $q->where('bm_approve_job', 1)
                        ->orWhere('sp_approve_job', 1)
                        ->orWhere(function ($q2) {
                            $q2->where('bm_approve_job', 0)
                                ->where('sp_approve_job', 0)
                                ->where('workorder_journey', 'job_execution')
                                ->where('reason', '!=', '');
                        });
                })
                ->count(),
        ];
    }


    public function getWorkOrdersByStatus(int $workerId, ?string $status, ?string $search, int $perPage = 10): LengthAwarePaginator
    {
        $today = Carbon::now();

        $query = WorkOrders::query()
            ->where('worker_id', $workerId);

        // Apply status filter only if status is provided
        if (!empty($status)) {
            switch (strtolower($status)) {
                case 'completed':
                    $query->whereNotNull('job_submitted_at')
                        ->whereRaw('UNIX_TIMESTAMP(job_submitted_at) > 0')
                        ->whereIn('job_completed_by', ['worker', 'SP']);
                    break;

                case 'active':
                    $query->where('status', 2)
                        ->whereNotNull('worker_started_at')
                        ->where(function ($q) {
                            $q->where('bm_approve_job', 0)->orWhereNull('bm_approve_job');
                        });
                    break;

                case 'examined':
                    $query->where('status', 2)
                        ->where(function ($q) {
                            $q->where('bm_approve_job', 1)->orWhere('sp_approve_job', 1);
                        });
                    break;

                case 'upcoming':
                    $query->where('status', 1)
                        ->whereDate('target_date', '>', $today);
                    break;

                case 'overdue':
                    $query->where('status', 2)
                        ->whereDate('target_date', '<', $today)
                        ->where('bm_approve_job', '!=', 1)
                        ->where('sp_approve_job', '!=', 1)
                        ->where('sp_reopen_status', '!=', 2);
                    break;

                case 'on_hold':
                    $query->where('status', 3)
                        ->whereIn('workorder_journey', ['job_execution', 'job_evaluation']);
                    break;

                case 'reopen':
                    $query->where('status', 2)
                        ->where('workorder_journey', 'job_execution')
                        ->where('sp_reopen_status', 2)
                        ->whereNull('worker_started_at');
                    break;

                case 'rejected':
                    $query->where('status', 2)
                        ->where('sp_reopen_status', '!=', 2)
                        ->whereIn('workorder_journey', ['job_execution', 'job_evaluation'])
                        ->where(function ($q) {
                            $q->where('bm_approve_job', 1)
                                ->orWhere('sp_approve_job', 1)
                                ->orWhere(function ($q2) {
                                    $q2->where('bm_approve_job', 0)
                                        ->where('sp_approve_job', 0)
                                        ->where('workorder_journey', 'job_execution')
                                        ->where('reason', '!=', '');
                                });
                        });
                    break;

                default:
                    $query->whereRaw('1 = 0'); // Invalid status returns nothing
            }
        }

        // Apply search
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('id', 'like', "%$search%")
                ->orWhere('work_order_id', 'like', "%$search%");
            });
        }

        return $query->orderByDesc('created_at')->paginate($perPage);
    }
}
