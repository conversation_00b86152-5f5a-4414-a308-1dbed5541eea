<?php
// phpinfo();
$cspNonce = bin2hex(random_bytes(16));

?>
    <!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
      dir="{{ (app()->getLocale()=='ar' ? 'rtl' : 'ltr') }}">

<head>
    <meta charset="UTF-8">
    @if(App::getLocale()=='en')
        <title lang="en">Osool </title>
    @else (App::getLocale()=='ar')
        <title lang="ar">أصول </title>
    @endif
    <meta name="description" lang="en" content="Osool is your technical partner in following up on maintenance contracts and managing the operational system. Osool enables facilities and properties management teams to manage maintenance and property operations, link with operational service providers, follow up on maintenance contract work in one platform, and serve the final beneficiary of the property.">
    <meta name="description" lang="ar" content="أصول هو شريكك التقني في متابعة عقود الصيانة وإدارة المنظومة التشغيلية. يمكن أصول فرق إدارة المرافق والأملاك من إدارة عمليات الصيانة والعقار والربط مع مقدمي الخدمات التشغيلية ومتابعة أعمال عقود الصيانة في منصة واحدة وخدمة المستفيد النهائي من العقار.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta property="og:image" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:secure_url" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:type" content="image/jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <meta http-equiv="Content-Security-Policy"
          content="
                       style-src 'self' https://eu-test.oppwa.com 'unsafe-inline' ;
                       frame-src 'self' https://eu-test.oppwa.com;
                       script-src 'self' https://eu-test.oppwa.com 'nonce-{{$cspNonce}}' ;
                       connect-src 'self' https://eu-test.oppwa.com;
                       img-src 'self' https://eu-test.oppwa.com" >
    <link rel="shortcut icon" href="{{ asset('home/image/favicon/favicon-32x32.png') }}" type="image">

    <!-- Bootstrap, fonts & icons  -->
    @if(App::getLocale()=='en')
        <link rel="stylesheet" href="{{ asset('home/css/bootstrap.css') }}">
    @else (App::getLocale()=='ar')
        <link rel="stylesheet" href="{{ asset('home/css/bootstrap-rtl.css') }}">
    @endif
    <link rel="stylesheet" href="{{ asset('home/fonts/icon-font/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('home/fonts/typography-font/typo.css') }}">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Gothic+A1:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="{{ asset('home/plugins/aos/aos.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/fancybox/jquery.fancybox.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/nice-select/nice-select.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/slick/slick.min.css') }}">
    <!-- Vendor stylesheets  -->
    @if(App::getLocale()=='en')
        <link rel="stylesheet" href="{{ asset('home/css/main.css') }}">
    @else (App::getLocale()=='ar')
        <link rel="stylesheet" href="{{ asset('home/css/main-rtl.css') }}">
    @endif
    <link rel="stylesheet" href="{{ asset('css/scss/new-style.css') }}">

    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <link rel="stylesheet" href="https://iconsax.gitlab.io/i/icons.css">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css" rel="stylesheet">
    <link rel= "stylesheet" href= "https://maxst.icons8.com/vue-static/landings/line-awesome/font-awesome-line-awesome/css/all.min.css" >
    <style>
        .swal-footer {
            text-align: center !important;
        }
        .mt-10
        {
            margin-top:10px !important;
        }
        .required {
            vertical-align: top;
            color: red !important;
        }

        .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 10px 15px;
            transition: color 0.3s ease;
        }

        .nav-link.active p,
        .nav-link.active .nav-icon,
        .nav-link.active .chevron i {
            color: var(--primary) !important;
        }

        .nav-icon,
        .chevron i {
            color: #6c757d;
            transition: color 0.3s ease;
        }

        cnpBillingCheckoutWrapper {position:relative;}
        .cnpBillingCheckoutHeader {width:100%;border-bottom: 1px solid #c0c0c0;margin-bottom:10px;}
        .cnpBillingCheckoutLeft {width:240px;margin-left: 5px;margin-bottom: 10px;border: 1px solid #c0c0c0;display:inline-block;vertical-align: top;padding:10px;}
        .cnpBillingCheckoutRight {width:50%;margin-left: 5px;border: 1px solid #c0c0c0;display:inline-block;vertical-align: top;padding:10px;}
        .cnpBillingCheckoutOrange {font-size:110%;color: rgb(255, 60, 22);font-weight:bold;}
        div.wpwl-wrapper, div.wpwl-label, div.wpwl-sup-wrapper { width: 100% }
        div.wpwl-group-expiry, div.wpwl-group-brand { width: 30%; display: inline-block;}
        div.wpwl-group-cvv { width: 68%;  display: inline-block; margin-left:2%; 
            [lang="ar"] &{
                    margin-left:0% !important;
                    margin-right: 2% !important;
            } 
        }
        div.wpwl-group-cardHolder, div.wpwl-sup-wrapper-street1, div.wpwl-group-expiry { clear:both;vertical-align:top; }
        div.wpwl-sup-wrapper-street1 { padding-top: 1px }
        div.wpwl-wrapper-brand { width: auto }
        div.wpwl-sup-wrapper-postcode, div.wpwl-sup-wrapper-city { width:32%; display: inline-block;margin-left:2%;
            [lang="ar"] &{
                    margin-left:0% !important;
                    margin-right: 2% !important;
            } 
        }
        div.wpwl-sup-wrapper-state { width:32%; display: inline-block }
        div.wpwl-sup-wrapper-country { width: 66% }
        div.wpwl-wrapper-brand, div.wpwl-label-brand { display: none;}
        div.wpwl-group-cardNumber { width:100%;  display: inline-block; font-size: 20px;  }
        div.wpwl-group-brand { width:35%;  display: inline-block; margin-top:28px }
        div.wpwl-brand-card  { width: 65px }
        div.wpwl-brand-custom  { margin: 0px 5px }
        div.wpwl-group-brand {display:none;}
        input[name="card.number"] {
            width:100% !important;
        }
        .wpwl-control-givenName{
            [lang="ar"] &{
                    margin-right:0% !important;
                    margin-left: 2% !important;
            } 
        }
    </style>
    <!-- Custom stylesheet -->
    <!-- Start of  Zendesk Widget script -->
    <!-- <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=5dcd3800-b341-400a-b410-ac5c928e42f5"> </script> -->
    <!-- End of  Zendesk Widget script -->
</head>
<div class="position-absolute"> @php
        $lang_path=resource_path('lang/'.App::getLocale());
        $translations=collect(File::allFiles($lang_path))->flatMap(function ($file)use($lang_path) {
            return [
                ($translation = $file->getBasename('.php')) => trans($translation),
            ];
        })->toJson();
    @endphp
</div>
<script type="text/javascript">
    window.baseUrl="{{URL::to('/')}}";
    window.current_locale="{{App::getLocale()}}";
    window.translations = {!! $translations !!};
    //console.log(window.current_locale) ;
</script>
<body data-theme-mode-panel-active data-theme="light" class="ltr">
<div class="site-wrapper overflow-hidden position-relative min-h-100vh nhc">
    <div class="position-relative bg-subscription min-h-100vh py-5 subscription" id="menu1">
        <div class="container">
            <div class="d-flex flex-sm-row flex-column justify-content-sm-between align-items-center gap-10">
                <div class="d-flex align-items-center">
                    <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="pe-3 max-w-180 border-end">   <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="ps-3 max-w-180">
                </div>

                <div class="d-flex gap-10">
                    @if (App::getLocale()=='en')
                        <a href="{{route('changeLanguage',"ar")}}" class="btn nhc-back radius-10 aos-init btn-lg"><span
                                class="">{{__('landing_page.menu.en_ar')}}</span></a>
                    @elseif (App::getLocale()=='ar')
                        <a href="{{route('changeLanguage',"en")}}" class="btn nhc-back radius-10 aos-init btn-lg"><span
                                class=" ">{{__('landing_page.menu.en_ar')}}</span></a>
                    @endif

                    <button type="button" onclick="window.history.back()" class="btn nhc-back radius-10 aos-init btn-lg"><i class="iconsax rotate-ar-y me-2" icon-name="chevron-left"></i> {{ __('nhc-partnership::translate.back') }} </button>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-md-7 d-flex">
                    <div class="checkout-card pb-5 flex-fill">
            <h4 class="mb-3 text-nhc fw-bold">{{ __('nhc-partnership::translate.order_details') }}</h4>

                        <div class="row justify-content-center g-4 align-items-center">
                            <!-- Card 2 -->
                            <div class="col-md-7 aos-init aos-animate" data-aos="fade-up" data-aos-delay="600" data-aos-duration="1000">
                                <div class="p-2 pt-3 radius-xxl bg-package{{$subscription->card_type}}-outer mx-3 mt-0">
                                    @if($subscription->most_popular)
                                        <h6 class="text-center text-white mb-3 fw-bold">{{ __('nhc-partnership::translate.most_popular') }}</h6>
                                    @endif
                                    <div class="card text-white h-100 border-0 rounded-4 package-2 radius-xxl package aos-init aos-animate" data-aos="fade-up" data-aos-delay="900" data-aos-duration="1000">
                                        <div class="card-body d-flex flex-column justify-content-between">
                                            <div>
                                                <div class="text-end mb-4 d-flex">
                                                    <div class="wh-40 bg-white radius-10 d-flex d-center">
                                                        @if($subscription->storage_card_icon)
                                                            <img style="width:30px" src="{{$subscription->storage_card_icon}}">
                                                        @else

                                                            <i class="iconsax text-package2" icon-name="building-4"></i>

                                                        @endif
                                                    </div>
                                                </div>
                                                <h5 class="card-title fw-normal">{{$subscription->name}}</h5>
                                                <p class="mt-3 fw-bold fs-5 text-white text-center mb-0">{{$subscription->price}}  {!!  Helper::currencyWhite(); !!} {{ __('nhc-partnership::translate.sar_per_anum') }}</p>
                                                @if($subscription->pricing_model == 'per_unit')
                                                    <small class="text-white text-center d-block">{{ __('nhc-partnership::translate.per_unit') }}</small>
                                                @endif
                                                <hr />


                                                @php
                                                    $featuresArray = explode(',', $subscription->features);
                                                @endphp

                                                @if (!empty($featuresArray))
                                                    <ul class="mt-4 list-unstyled limit-list">
                                                        @foreach ($featuresArray as $feature)
                                                            <li>
                                                                <i class="fas fa-check fs-18 me-2"></i> {{ trim($feature) }}
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                             <div class="text-center toggle-wrapper">
                                                <i class="iconsax show-more-btn cursor-pointer" icon-name="chevron-down"></i>
                                            </div>
                                           {{-- <div class="mt-4">
                                                <a href="http://127.0.0.1:8000/nhc-partnership/choose-subscription/2" class="btn btn-light bg-white w-100 radius-10 btn-lg text-cyan">Learn More <i class="iconsax ms-3 rotate-ar-y" icon-name="chevron-right"></i></a>
                                            </div>--}}
                                        </div>
                                    </div>
                                </div>


                                <div class="table-responsive checkout-payment mt-4 px-sm-0 px-3">
                                    <div class="d-flex justify-content-between gap-10 mb-3 pb-3 border-bottom">
                                        <h6 class="text-nowrap me-3">{{ __('nhc-partnership::translate.category_name') }}</h6>
                                        <h6 class="text-end">{{$subscription->name}}</h6>
                                    </div>
                                    <div class="d-flex justify-content-between gap-10 pb-4 mb-4 border-bottom">
                                        @if($subscription->pricing_model == 'per_unit')
                                        <h6 class="text-nowrap me-3">{{ __('nhc-partnership::translate.per_unit') }}</h6>
                                        @endif
                                        <h6><span class="fs-18 text-nhc fw-bold">{{($subscription->price - $subscription->discount) * $unit_count}}  {{ __('nhc-partnership::translate.sar_per_anum') }}</h6>
                                    </div>
                                    <div class="d-flex justify-content-between gap-10 align-items-center">
                                        <h6 class="text-nowrap me-3">{{ __('nhc-partnership::translate.per_unit') }}</h6>
                                        <div class="number-input d-inline-flex align-items-center bg-light-blue radius-10">
                                            <button class="p-2" onclick="changeNumber(-1)">
                                                <i class="iconsax rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                            </button>
                                            <span id="numberValue" class="fw-bold text-secondary mx-3">{{$unit_count}}</span>
                                            <button class="p-2" onclick="changeNumber(1)">
                                                <i class="iconsax rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

                <div class="col-md-5 mt-md-0 mt-4">
                    <div class="card radius-10 shadow p-3 vendor-form checkout-form border-0 h-100">
{{--                        <form>--}}
{{--                            <h5 class="text-nhc mb-4">{{ __('nhc-partnership::translate.payment_information') }}</h5>--}}
{{--                            <p class="text-dark fw-bold mb-3 fs-14">{{ __('nhc-partnership::translate.payment_method') }}</p>--}}
{{--                            <img src="{{ asset('home/image/home/<USER>') }}" class="w-100" alt="Payment Options" />--}}
{{--                            <div class="form-group mb-2 aos-animate aos-init mt-4" data-aos="fade-up" data-aos-delay="400" data-aos-duration="1000">--}}
{{--                                <label for="exampleInputEmail1" class="form-label"> {{ __('nhc-partnership::translate.name') }} <small class="required">*</small></label>--}}
{{--                                <input type="text" class="form-control radius-xl" id="" aria-describedby="" autocomplete="off" placeholder="{{ __('nhc-partnership::translate.name') }}">--}}
{{--                                <!-- <div id="emailHelp" class="form-text">We'll never share your email with anyone else.</div> -->--}}
{{--                            </div>--}}
{{--                            <p class="text-dark fw-bold mb-3 fs-14">{{ __('nhc-partnership::translate.card_details') }}</p>--}}
{{--                            <div class="payment-card border radius-15 mb-3">--}}
{{--                                <div class="row">--}}
{{--                                    <div class="col-12">--}}
{{--                                        <input type="text" class="form-control focus-shadw-none" id="" aria-describedby="" autocomplete="off" placeholder="{{ __('nhc-partnership::translate.enter_card_number') }}">--}}
{{--                                    </div>--}}
{{--                                    <div class="col-6 pr-0">--}}
{{--                                        <input type="text" class="form-control focus-shadw-none border-top border-end" id="" aria-describedby="" autocomplete="off" placeholder="{{ __('nhc-partnership::translate.mm_yy_placeholder') }}">--}}
{{--                                    </div>--}}
{{--                                    <div class="col-6 ps-0">--}}
{{--                                        <input type="text" class="form-control focus-shadw-none border-top" id="" aria-describedby="" autocomplete="off" placeholder="{{ __('nhc-partnership::translate.cvv_placeholder') }}">--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            <div class="form-group mb-3">--}}
{{--                                <p class="text-dark fw-bold mb-3 fs-14">{{ __('nhc-partnership::translate.billing_address') }}</p>--}}
{{--                                <div class="row">--}}
{{--                                    <div class="col-12 col-xl-6 mb-xx-0 mb-2 mb-xl-0 mb-2">--}}
{{--                                        <label class="form-label">{{ __('nhc-partnership::translate.residence') }}</label>--}}
{{--                                        <input type="text" class="form-control radius-xl" id="" aria-describedby="" autocomplete="off" placeholder="">--}}
{{--                                    </div>--}}
{{--                                    <div class="col-12 col-xl-6 ps-xl-0">--}}
{{--                                        <label class="form-label">{{ __('nhc-partnership::translate.country') }}</label>--}}
{{--                                        <div class="mb-2 position-relative z-index-9999">--}}
{{--                                            <div class="dropdown countries">--}}
{{--                                                <button class="bg-white border btn-country fs-12 w-100 radius-xl dropdown-toggle px-3" type="button" id="countryDropdown" data-bs-toggle="dropdown" aria-expanded="false">--}}
{{--                                                    <div class="d-flex justify-content-between align-items-center">--}}
{{--                                                        <div>--}}
{{--                                                    <span id="selected-flag" class="flag-icon flag-icon-sa dropdown-item-flag me-2"></span>--}}
{{--                                                    <span id="selected-country">Kingdom of Saudi Arabia</span>--}}
{{--                                                </div>--}}
{{--                                                <i class="iconsax" icon-name="chevron-down"></i>--}}
{{--                                                </div>--}}
{{--                                                </button>--}}
{{--                                                <ul class="dropdown-menu" aria-labelledby="countryDropdown" style="max-height: 300px; overflow-y: auto;">--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="us" data-country="United States"><span class="flag-icon flag-icon-us dropdown-item-flag"></span>United States</a></li>--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="gb" data-country="United Kingdom"><span class="flag-icon flag-icon-gb dropdown-item-flag"></span>United Kingdom</a></li>--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="sa" data-country="Kingdom of Saudi Arabia"><span class="flag-icon flag-icon-sa dropdown-item-flag"></span>Kingdom of Saudi Arabia</a></li>--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="ae" data-country="United Arab Emirates"><span class="flag-icon flag-icon-ae dropdown-item-flag"></span>United Arab Emirates</a></li>--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="in" data-country="India"><span class="flag-icon flag-icon-in dropdown-item-flag"></span>India</a></li>--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="jp" data-country="Japan"><span class="flag-icon flag-icon-jp dropdown-item-flag"></span>Japan</a></li>--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="kr" data-country="South Korea"><span class="flag-icon flag-icon-kr dropdown-item-flag"></span>South Korea</a></li>--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="cn" data-country="China"><span class="flag-icon flag-icon-cn dropdown-item-flag"></span>China</a></li>--}}
{{--                                                    <li><a class="dropdown-item" href="#" data-flag="ru" data-country="Russia"><span class="flag-icon flag-icon-ru dropdown-item-flag"></span>Russia</a></li>--}}
{{--                                                </ul>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}


{{--                            <div class="radius-xl p-3 mt-3 bg-light-blue">--}}
{{--                                <div class="d-flex justify-content-between gap-10">--}}
{{--                                    <h5 class="text-nowrap me-3">{{ __('nhc-partnership::translate.total_payment') }}</h5>--}}
{{--                                    <h6>{{($subscription->price - $subscription->discount) * $unit_count}}  {{ __('nhc-partnership::translate.sar_per_anum') }}</h6>--}}
{{--                                </div>--}}
{{--                                <a href="{{route('nhc-partnership.success')}}" class="btn bg-package2-outer text-white btn-lg w-100 mt-2 radius-10">{{ __('nhc-partnership::translate.subscribe_package') }}</a>--}}
{{--                            </div>--}}
{{--                            @php--}}
{{--                                $whatsappMessage = App::getLocale() === 'ar'--}}
{{--                                    ? 'مرحباً ، عندي استفسار بخصوص عملية التسجيل على منصة أصول'--}}
{{--                                    : 'Hello, I have a question about the registration process on Osool';--}}
{{--                                $encodedMessage = urlencode($whatsappMessage);--}}
{{--                                $whatsappLink = "https://wa.me/966558501690?text={$encodedMessage}";--}}
{{--                            @endphp--}}
{{--                            <div class="d-flex justify-content-between align-items-center mt-4">--}}
{{--                                <p class="mb-0 text-dark fw-600 fs-18">{{ __('nhc-partnership::translate.for_more_information') }}</p>--}}
{{--                                <a href="{{ $whatsappLink }}" class="btn nhc-back radius-10 aos-init"--}}
{{--                                target="_blank">--}}
{{--                                {{ __('nhc-partnership::translate.contact_us') }}--}}
{{--                                </a>--}}
{{--                            </div>--}}

{{--                        </form>--}}


                        <h5 class="text-nhc mb-4">{{ __('nhc-partnership::translate.payment_information') }}</h5>
{{--                        <p class="text-dark fw-bold mb-3 fs-14">{{ __('nhc-partnership::translate.payment_method') }}</p>--}}
                        <p class="text-dark fw-bold mb-3 fs-14">{{ __('nhc-partnership::translate.card_details') }}</p>
                        <img src="{{ asset('home/image/home/<USER>') }}" class="w-100" alt="Payment Options" />
                        <div class="payment-card radius-15 mb-3 mt-3">
                            @if (session('error'))
                                <div class="alert alert-danger">
                                    {{ session::get('error') }}
                                </div>
                            @endif

                                <form action="{{route('nhc-partnership.success')}}" class="paymentWidgets" data-brands="VISA MASTER MADA APPLEPAY PAYPAL">



                                </form>
                            <div class="radius-xl p-3 mt-3 bg-light-blue">
                                <div class="d-flex justify-content-between gap-10">
                                    <h5 class="text-nowrap me-3">{{ __('nhc-partnership::translate.total_payment') }}</h5>
                                    <h6>{{($subscription->price - $subscription->discount) * $unit_count}}  {{ __('nhc-partnership::translate.sar_per_anum') }}</h6>
                                </div>
{{--                                <a href="{{route('nhc-partnership.success')}}" class="btn bg-package2-outer text-white btn-lg w-100 mt-2 radius-10">{{ __('nhc-partnership::translate.subscribe_package') }}</a>--}}
                            </div>
                        </div>
                        @php
                            $whatsappMessage = App::getLocale() === 'ar'
                                ? 'مرحباً ، عندي استفسار بخصوص عملية التسجيل على منصة أصول'
                                : 'Hello, I have a question about the registration process on Osool';
                            $encodedMessage = urlencode($whatsappMessage);
                            $whatsappLink = "https://wa.me/966558501690?text={$encodedMessage}";
                        @endphp
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <p class="mb-0 text-dark fw-600 fs-18">{{ __('nhc-partnership::translate.for_more_information') }}</p>
                            <a href="{{ $whatsappLink }}" class="btn nhc-back radius-10 aos-init"
                               target="_blank">
                                {{ __('nhc-partnership::translate.contact_us') }}
                            </a>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
</div>



<!-- Vendor Scripts -->
<script src="{{ asset('home/js/vendor.min.js') }}"></script>
<!-- Plugin's Scripts -->
<script src="{{ asset('home/plugins/fancybox/jquery.fancybox.min.js') }}"></script>
<script src="{{ asset('home/plugins/nice-select/jquery.nice-select.min.js') }}"></script>
<script src="{{ asset('home/plugins/aos/aos.min.js') }}"></script>
<script src="{{ asset('home/plugins/slick/slick.min.js') }}"></script>
<script src="https://porjoton.netlify.app/mekanic/js/waypoints.min.js"></script>
<script src="{{ asset('home/plugins/counter-up/jquery.counterup.min.js') }}"></script>
<script src="{{ asset('home/plugins/isotope/isotope.pkgd.min.js') }}"></script>
<script src="{{ asset('home/plugins/isotope/packery.pkgd.min.js') }}"></script>
<script src="{{ asset('home/plugins/isotope/image.loaded.js') }}"></script>
<script src="{{ asset('home/plugins/menu/menu.js') }}"></script>
<!-- Activation Script -->

<script src="{{asset('js/jquery.validate.js')}}"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="{{asset('js/admin/jquery-validate-method-add.js')}}"></script>

<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script src="{{asset('js/validator_additional.js')}}"></script>
<script src="{{ asset('home/js/custom.js') }}"></script>
<!-- Bootstrap JS and dependencies -->
<script
    src="https://eu-test.oppwa.com/v1/paymentWidgets.js?checkoutId={{$checkoutId}}"
    integrity="{{$integrity}}"
    crossorigin="anonymous">
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownItems = document.querySelectorAll('.dropdown-item');

        dropdownItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const flagCode = this.getAttribute('data-flag');
                const countryName = this.getAttribute('data-country');

                document.getElementById('selected-flag').className = `flag-icon flag-icon-${flagCode} dropdown-item-flag`;
                document.getElementById('selected-country').textContent = countryName;
            });
        });
    });
</script>
<script nonce="{{ $cspNonce }}">
    var wpwlOptions = {
        locale: "{{app()->getLocale()}}",
        style: "plain",
        iframeStyles: {
            'card-number-placeholder': {
                'font-family': 'monospace'
            },
        },
        billingAddress: {
            country: "",
            state: "",
            city: "",
            street1: "",
            street2: "",
            postcode: ""
        },
        mandatoryBillingFields:{
            country: true,
            state: true,
            city: true,
            postcode: true,
            street1: true,
            street2: false
        },
        forceCardHolderEqualsBillingName: true,
        showCVVHint: true,
        brandDetection: false,
        onReady: function(){
            $(".wpwl-group-cardNumber").after($(".wpwl-group-brand").detach());
            $(".wpwl-group-cvv").after( $(".wpwl-group-cardHolder").detach());
            var visa = $(".wpwl-brand:first").clone().removeAttr("class").attr("class", "wpwl-brand-card wpwl-brand-custom wpwl-brand-VISA")
            var master = $(visa).clone().removeClass("wpwl-brand-VISA").addClass("wpwl-brand-MASTER");
            $(".wpwl-brand:first").after( $(master)).after( $(visa));
            var imageUrl = "https://eu-test.oppwa.com/v1/static/" + wpwl.cacheVersion + "/img/brand.png";
            $(".wpwl-brand-custom").css("background-image", "url(" + imageUrl + ")");
        },
        onChangeBrand: function(e){
            $(".wpwl-brand-custom").css("opacity", "0.3");
            $(".wpwl-brand-" + e).css("opacity", "1");
        }
    }

</script>
<script>
 $(document).ready(function () {
    $('.card').each(function () {
        var $card = $(this);
        var $ul = $card.find('ul.limit-list');
        var $lis = $ul.find('li');
        var $button = $card.find('.show-more-btn');
        var $arrow = $card.find('.toggle-wrapper');

        if ($lis.length > 5) {
            $lis.slice(5).hide(); // Hide beyond fifth item
            $button.parent().show(); // Show the wrapper containing the button
        } else {
            $button.parent().hide(); // Hide if 5 or fewer items
        }

        $button.on('click', function () {
            var isExpanded = $ul.find('li:visible').length > 5;

            if (isExpanded) {
                $lis.slice(5).stop(true, true).slideUp(300);
                $arrow.removeClass('rotate-180');
            } else {
                $lis.slice(5).stop(true, true).slideDown(300);
                $arrow.addClass('rotate-180');
            }
        });
    });
});
$(".payment-card select").select2();
</script>

</body>

</html>
