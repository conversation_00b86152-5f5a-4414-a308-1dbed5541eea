<?php

namespace NhcPartnership\Api\Http\Controllers;

use GuzzleHttp\Client;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use NhcPartnership\Api\Models\ServiceOrder;
use NhcPartnership\Api\Models\SubscribedUser;
use NhcPartnership\Api\Models\Subscription;

class CheckoutController extends Controller
{
    protected $client;
    protected $baseUrl;
    protected $entityId;
    protected $accessToken;
    protected $subscribedUser;
    protected $subscription;
    protected $unit_count;

    public function __construct()
    {
        if (!session()->has('subscription_id') || !session()->has('sub_id')) {
            return Redirect::route('nhc-partnership.subscriptions');
        }
        $this->subscribedUser = SubscribedUser::find(Session::get('sub_id'));
        $this->subscription = Subscription::find(Session::get('subscription_id'));
        $this->unit_count = Session::get('unit_count');
        $this->client = new Client();
        $this->baseUrl = rtrim(config('services.hyperpay.base_url'), '/') . '/';
        $this->entityId = config('services.hyperpay.entity_id');
        $this->accessToken = config('services.hyperpay.access_token');
    }

    public function index(){
        $response = $this->client->request('POST', "{$this->baseUrl}v1/checkouts", [
            'headers' => [
                'Authorization' => "Bearer {$this->accessToken}"
            ],
            'form_params' => [
                'entityId' => $this->entityId,
                'amount' => number_format(($this->subscription->price * ($this->unit_count ?? 1)), 0, '.', ''),
                'currency' => 'SAR',
                'paymentType' => 'DB',
                'customer.email' => '<EMAIL>',
                'testMode' => 'EXTERNAL',
                'integrity' => 'true',
                'merchantTransactionId' => uniqid('txn_'),
                'customParameters[3DS2_enrolled]'=> true
            ]
        ]);
        $body = json_decode($response->getBody(), true);
        $checkoutId = $body['id'] ?? null;
        $integrity = $body['integrity'] ?? null;
        $subscription = $this->subscription;
        $unit_count = $this->unit_count;
        return view('nhc-partnership::checkout', compact('subscription', 'unit_count', 'checkoutId', 'integrity'));
    }

    public function successPage(){
        $resourcePath = request()->get('resourcePath');
        if (!$resourcePath) {
            return redirect()->route('nhc-partnership.subscriptions')->with('error', 'Payment verification failed.');
        }

        $response = $this->client->request('GET', $this->baseUrl . ltrim($resourcePath, '/'), [
            'headers' => [
                'Authorization' => "Bearer {$this->accessToken}"
            ],
            'query' => [
                'entityId' => $this->entityId
            ]
        ]);

        $body = json_decode($response->getBody(), true);
        $paymentStatus = $body['result']['code'] ?? null;
        $paymentSuccess = ($paymentStatus === '000.100.112');

        if ($paymentSuccess) {
            $subscription = $this->subscription;
            $subscription_id=$subscription->id;
            $discount=$subscription->discount;
            $total_price=($this->subscription->price - ($this->subscription->price * ($discount / 100))) * $this->unit_count;

            $osool_cut_amount=$total_price -($total_price - ($total_price * ($subscription->osool_commission / 100)));
            $nhc_cut_amount=$total_price -($total_price - ($total_price * ($subscription->nhc_commission / 100)));
            $third_party_cut_amount=$total_price -($total_price - ($total_price * ($subscription->third_party_commission / 100)));

            $subscribedUser = $this->subscribedUser;
            $serviceOrder = new ServiceOrder();
            $serviceOrder->order_id = rand('000000','111111');
            $serviceOrder->subscribed_user_id = $subscribedUser->id;
            $serviceOrder->subscription_id = $subscription_id;
            $serviceOrder->units_count = $subscribedUser->units_count;
            $serviceOrder->package_type = ServiceOrder::PACKAGE_CLOUD_PLATFORM;
            $serviceOrder->status = ServiceOrder::STATUS_NEW;
            $serviceOrder->payment_status = ServiceOrder::PAYMENT_STATUS_PAID;
            $serviceOrder->date_created = date('Y-m-d H:i:s');
            $serviceOrder->total_price = round($total_price,2);
            $serviceOrder->osool_cut_amount = round($osool_cut_amount,2);
            $serviceOrder->nhc_cut_amount = round($nhc_cut_amount,2);
            $serviceOrder->third_party_cut_amount = round($third_party_cut_amount,2);
            $serviceOrder->invoice_attachment = $body['merchantTransactionId'];
            $serviceOrder->save();
            return view('nhc-partnership::thank_you', compact('body'));
        } else {
            return redirect()->back()->with('error', $body['result']['description']);
        }
    }

}
