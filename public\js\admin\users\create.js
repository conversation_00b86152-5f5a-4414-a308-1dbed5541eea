//alert('test')
// @flip2@ adding validation for worker id
let inp_user_type = $("#inp_user_type").val();
let inp_field_validation_rules = {
    emp_id: {},
};
switch (inp_user_type) {
    case "sp_worker":
        inp_field_validation_rules.emp_id.required = true;
        break;

    default:
        inp_field_validation_rules.emp_id.required = true;
        break;
}
$("#user_create_form").validate({
    ignore: "input[type=hidden]",
    rules: {
        user_type: {
            required: true,
        },
        name: {
            required: true,
            minlength: 2,
            maxlength: 50,
        },
        nationality_id: {
            required: function () {
                return !$(".nationality_select").is(":hidden");
            },
        },
        profession_id: {
            required: function () {
                return !$(".profession_select").is(":hidden");
            },
        },
        emp_dept: {
            maxlength: 20,
        },
        emp_id: {
            required: {
                //@flip1@ add validation for workorder id
                depends: function (element) {
                    return $("#user_type").val() == "sp_worker";
                },
            },
            minlength: 2,
            maxlength: 10,
            alphanumeric: true,
            remote: {
                url: $("#ajax_check_employee_id_unique").val(),
                type: "post",
                data: {
                    email: function () {
                        return $("#emp_id").val();
                    },
                    project_id: $("#project_id").val(),
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        email: {
            required: true,
            email: true,
            maxlength: 50,
            remote: {
                url: $("#ajax_check_useremail_unique").val(),
                type: "post",
                data: {
                    email: function () {
                        return $("#email").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone: {
            number: true,
            minlength: 9,
            maxlength: 9,

            remote: {
                url: $("#ajax_check_userphone_unique").val(),
                type: "post",
                data: {
                    phone: function () {
                        return $("#phone").val();
                    },
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone2: {},
        service_provider: {},
        sp_admin_id: {},
        building_admin: {},
        supervisor_id: {},
        // store_keeper_name: {
        //     required: {
        //         depends: function (element) {
        //             return $("#user_type").val() == "store_keeper";
        //         },
        //     },
        // },
        // store_keeper_email: {
        //     required: {
        //         depends: function (element) {
        //             return $("#user_type").val() == "store_keeper";
        //         },
        //     },
        //     email: true,
        // },
    },
    messages: {
        user_type: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            //lettersandspace: true,
        },
        country_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        nationality_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        profession_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        emp_dept: {
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_20_characters,
        },
        emp_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            alphanumeric:
                translations.general_sentence.validation
                    .Letters_numbers_and_only_these_characters_special_are_allowed,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation
                .Employee_ID_already_exist_Enter_different_one,
        },
        city_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        email: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation
                .Email_format_not_Valid,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            remote: translations.general_sentence.validation
                .Email_already_exist_Enter_different_email,
        },
        phone: {
            // required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation
                .Please_enter_a_valid_number,
            minlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            maxlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            remote: translations.general_sentence.validation
                .Phone_number_already_exist_Enter_different_number,
        },
        service_provider: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        sp_admin_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        building_admin: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        supervisor_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        store_keeper_name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        store_keeper_email: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation
                .Email_format_not_Valid,
        },
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("id") == "sp_admin_id") {
            error.appendTo($("#sp_admin_id_error"));
        }

        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else if (element.attr("name") == "building_admin") {
            error.appendTo($("#building_admin-error"));
        } else if (element.attr("name") == "service_provider") {
            error.appendTo($("#service_provider-error"));
        } else if (element.attr("id") == "sp_admin_id") {
            error.appendTo($("#sp_admin_id-error"));
        } else if (element.attr("id") == "supervisor_id") {
            error.appendTo($("#supervisor_id-error"));
        } else if (element.attr("id") == "user_type") {
            error.appendTo($("#user-type-error"));
        } else if (element.attr("id") == "nationality_id") {
            error.appendTo($("#nationality-id-error"));
        } else if (element.attr("id") == "profession_id") {
            error.appendTo($("#profession-id-error"));
        } else if (element.attr("id") == "store_keeper_name") {
            error.appendTo($("#store_keeper_name-error"));
        } else if (element.attr("id") == "store_keeper_email") {
            error.appendTo($("#store_keeper_email-error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('ad');
        console.log($("#user_type").val())
        if($("#user_type").val() === 'building_manager' ) {
          if($('.unique_area_manager_error').is(':visible')){
              alert(`${translations.user_management_module.user_validation.unique_area_manager}`);
              return;
          }
        }
        form.submit();
    },
});

$("#user_create_form_worker").validate({
    ignore: "input[type=hidden]",
    rules: {
        user_type: {
            required: true,
        },
        name: {
            required: true,
            minlength: 2,
            maxlength: 50,
            //lettersandspace: true,
        },
        emp_dept: {
            maxlength: 20,
        },
        emp_id: {
            required: {
                //@flip1@ add validation for workorder id
                depends: function (element) {
                    return $("#user_type").val() == "sp_worker";
                },
            },
            minlength: 2,
            maxlength: 10,
            alphanumeric: true,
            remote: {
                url: $("#ajax_check_employee_id_unique").val(),
                type: "post",
                data: {
                    email: function () {
                        return $("#emp_id").val();
                    },
                    project_id: $("#project_id").val(),
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        email: {
            required: true,
            email: true,
            maxlength: 50,
            remote: {
                url: $("#ajax_check_useremail_unique").val(),
                type: "post",
                data: {
                    email: function () {
                        return $("#email").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone: {
            required: {
                depends: function (element) {
                    return $("#user_type").val() != "sp_worker"; //phone validation not required for worker user type
                },
            },
            number: true,
            minlength: 9,
            maxlength: 9,

            remote: {
                url: $("#ajax_check_userphone_unique").val(),
                type: "post",
                data: {
                    phone: function () {
                        return $("#phone").val();
                    },
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone2: {},
        service_provider: {},
        sp_admin_id: {},
        building_admin: {},
        "supervisor_id[]": {
            required: true,
        },
    },
    messages: {
        user_type: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            //lettersandspace: true,
        },
        country_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        emp_dept: {
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_20_characters,
        },
        emp_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            alphanumeric:
                translations.general_sentence.validation
                    .Letters_numbers_and_only_these_characters_special_are_allowed,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation
                .Employee_ID_already_exist_Enter_different_one,
        },
        city_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        email: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation
                .Email_format_not_Valid,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            remote: translations.general_sentence.validation
                .Email_already_exist_Enter_different_email,
        },
        phone: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation
                .Please_enter_a_valid_number,
            minlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            maxlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            remote: translations.general_sentence.validation
                .Phone_number_already_exist_Enter_different_number,
        },
        service_provider: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        sp_admin_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        building_admin: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        "supervisor_id[]": {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else if (element.attr("name") == "building_admin") {
            error.appendTo($("#building_admin-error"));
        } else if (element.attr("name") == "service_provider") {
            error.appendTo($("#service_provider-error"));
        } else if (element.attr("id") == "sp_admin_id") {
            error.appendTo($("#sp_admin_id-error"));
        } else if (element.attr("id") == "supervisor_id[]") {
            error.appendTo($("#supervisor_id_error"));
        } else if (element.attr("id") == "user_type") {
            error.appendTo($("#user-type-error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('ad');
        form.submit();
    },
});

$("#user_create_form_edit").validate({
    ignore: "input[type=hidden]",
    ignore: "select[id=supervisor_id]", // @fli1@ ignore when edit supervisor
    rules: {
        name: {
            required: true,
            minlength: 2,
            maxlength: 50,
            //lettersandspace: true,
        },
        country_id: {
            required: true,
        },
        emp_dept: {
            maxlength: 20,
        },
        emp_id: {
            required: {
                //@flip1@ add validation for workorder id
                depends: function (element) {
                    return $("#user_type").val() == "sp_worker";
                },
            },
            minlength: 2,
            maxlength: 10,
            alphanumeric: true,
            remote: {
                url: $("#ajax_check_employee_id_unique_edit").val(),
                type: "post",
                data: {
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    email: function () {
                        return $("#emp_id").val();
                    },
                    project_id: $("#project_id").val(),
                    user_id: $("#user_id").val(),

                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        city_id: {
            required: true,
        },
        email: {
            required: true,
            email: true,
            maxlength: 50,
            remote: {
                url: $("#ajax_check_useremail_unique").val(),
                type: "get",
                data: {
                    email: function () {
                        return $("#email").val();
                    },
                    user_id: $("#user_id").val(),
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone: {
            // @flip1@ eremove from require
            // required: {
            //     depends: function (element) {
            //         return $("#user_type").val() != 'sp_worker'; //phone validation not required for worker user type
            //     }
            // },
            number: true,
            minlength: 9,
            maxlength: 9,

            remote: {
                url: $("#ajax_check_userphone_unique_edit").val(),
                type: "post",
                data: {
                    phone: function () {
                        return $("#phone").val();
                    },
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    'user_id':$('#user_id').val(),
                     locale: "en",
                     _token: $('meta[name="csrf-token"]').attr("content"),
                 },
             },
        },
        phone2: {},
    },
    messages: {
        name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
        },
        country_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        emp_dept: {
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_20_characters,
        },
        emp_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            alphanumeric:
                translations.general_sentence.validation
                    .Letters_numbers_and_only_these_characters_special_are_allowed,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation
                .Employee_ID_already_exist_Enter_different_one,
        },
        city_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        email: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation
                .Email_format_not_Valid,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
            remote: translations.general_sentence.validation
                .Email_already_exist_Enter_different_email,
        },
        phone: {
            // required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation
                .Please_enter_a_valid_number,
            minlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            maxlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            remote: translations.general_sentence.validation
                .Phone_number_already_exist_Enter_different_number,
        },
    },
    errorPlacement: function (error, element) {
        console.log(error, element, 123);
        error.addClass("invalid-feedback");
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('HII');
        console.log(form);
        form.submit();
    },
});

$("#user_create_form_edit_worker").validate({
    ignore: "input[type=hidden]",
    rules: {
        name: {
            required: true,
            minlength: 2,
            maxlength: 50,
            //lettersandspace: true,
        },
        "supervisor_id[]": {
            required: true,
        },
        nationality_id: {
            required: true,
        },
        profession_id: {
            required: function () {
                return !$(".profession_select").is(":hidden");
            },
        },
        emp_dept: {
            maxlength: 20,
        },
        emp_id: {
            required: true /*ajax_check_employee_id_unique*/,
            minlength: 2,
            maxlength: 10,
            alphanumeric: true,
            remote: {
                url: $("#ajax_check_employee_id_unique_edit").val(),
                type: "post",
                data: {
                    user_type: function () {
                        return $("#user_type").val();
                    },
                    email: function () {
                        return $("#emp_id").val();
                    },
                    project_id: $("#project_id").val(),
                    user_id: $("#user_id").val(),

                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        phone: {
            required: {
                depends: function (element) {
                    return $("#user_type").val() != "sp_worker" &&  $("#user_type").val() != "team_leader"; //phone validation not required for worker user type
                },
            },
            number: true,
            minlength: 9,
            maxlength: 9,

             remote: {
                 url: $("#ajax_check_userphone_unique_edit").val(),
                 type: "post",
                 data: {
                     phone: function () {
                         return $("#phone").val();
                     },
                     user_type: function () {
                        return $("#user_type").val();
                     },
                     'user_id':$('#user_id').val(),
                     locale: "en",
                     _token: $('meta[name="csrf-token"]').attr("content"),
                 },
             },
        },
        phone2: {},
    },
    messages: {
        name: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_50_characters,
        },
        "supervisor_id[]": {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        nationality_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        profession_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
        },
        emp_dept: {
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_20_characters,
        },
        emp_id: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            alphanumeric:
                translations.general_sentence.validation
                    .Letters_numbers_and_only_these_characters_special_are_allowed,
            minlength:
                translations.general_sentence.validation
                    .Please_enter_at_least_2_characters,
            maxlength:
                translations.general_sentence.validation
                    .Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation
                .Employee_ID_already_exist_Enter_different_one,
        },
        phone: {
            required:
                translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation
                .Please_enter_a_valid_number,
            minlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            maxlength:
                translations.general_sentence.validation.Please_enter_9_numbers,
            remote: translations.general_sentence.validation
                .Phone_number_already_exist_Enter_different_number,
        },
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else if (element.attr("name") == "supervisor_id[]") {
            error.appendTo($("#supervisor_id_error"));
        } else if (element.attr("id") == "profession_id") {
            error.appendTo($("#profession-id-error"));
        } else if (element.attr("id") == "nationality_id") {
            error.appendTo($("#nationality-id-error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('HII');
        form.submit();
    },
});

$("#file_upload").on("change", function (event) {
    var files = document.getElementById("file_upload").files;

    if (files.length) {
        var file_size_error = false;
        var file_type_error = false;
        var file_size_in_kb = files[0].size / 2048;
        var file_type = files[0].type;

        if (file_size_in_kb > 2048) {
            file_size_error = true;
        }

        var supported_types = ["image/jpeg", "image/png", "image/jpg"];

        if (!supported_types.includes(file_type)) {
            file_type_error = true;
        }

        if (file_size_error == true || file_type_error == true) {
            document.getElementById("file_upload").value = "";

            var error_message = "";

            if (file_size_error == true && file_type_error == true) {
                error_message =
                    translations.general_sentence.validation
                        .Please_upload_only_jpg_jpeg_png_image_of_max_size_1mb;
            } else if (file_size_error == true && file_type_error == false) {
                error_message =
                    translations.general_sentence.validation
                        .File_size_should_not_be_more_than_1_mb;
            } else {
                error_message =
                    translations.general_sentence.validation
                        .Please_upload_only_jpg_jpeg_png_image;
            }

            swal({
                title: error_message,
                //text: "Hello",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            });
        } else {
            var reader = new FileReader();
            reader.onload = function () {
                var output = document.getElementById("output_pic");
                output.src = "";
                output.src = reader.result;
                console.log(reader.result);
            };
            reader.readAsDataURL(event.target.files[0]);
            $("[name='isImageRemove']").val(0);
            $(".remove-img").removeClass("hide");
        }
    } else {
        document.getElementById("file_upload").value = "";
        let app_url = $("#app_url").val();
        //$("#file_upload").attr('value','');

        $("#output_pic").attr("src", app_url + "/img/upload.png");

        $("[name='isImageRemove']").val(1);
        $(".remove-img").addClass("hide");
    }
});

$(".confirm_remove_photo").click(function () {
    document.getElementById("file_upload").value = "";
    let app_url = $("#app_url").val();
    //$("#file_upload").attr('value','');

    $("#output_pic").attr("src", app_url + "/img/upload.png");

    $("[name='isImageRemove']").val(1);
    $(".remove-img").addClass("hide");
});

$("#country_id").on("change", function () {
    let value_cnt = $(this).val();
    //alert(language);
    $.ajax({
        url: $(this).data("url"),
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            id: value_cnt,
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
            $("#city_id").empty();
            var language = current_locale;
            // @flip1@ add blank option
            $("#city_id").append(
                $("<option></option>")
                    .attr("selected", true)
                    .attr("disabled", true)
                    .text(
                        translations.user_management_module.user_forms
                            .place_holder.emp_city
                    )
            );
            //alert(language);
            $.each(data, function (key, value) {
                if (language == "en") {
                    $("#city_id").append(
                        $("<option></option>")
                            .attr("value", value.id)
                            .text(value.name_en)
                    );
                } else {
                    $("#city_id").append(
                        $("<option></option>")
                            .attr("value", value.id)
                            .text(value.name_ar)
                    );
                }
            });
        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 5000,
                positionClass: "toast-top-center",
                progressBar: true,
            });
        },
    });
});

/***************sp admin list and sup list by company change **********/

/***************sp admin list and sup list by company change **********/
$("#user_type").on("change", function () {
    let user_type_tag = $(this).val();
    let app_url = $("#app_url").val();
    $.ajax({
        url: app_url + "/user/ajax/get_userrole_list/",
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            user_type_tag: user_type_tag,
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
            $("#user_role").empty();
            $.each(data, function (key, value) {
                $("#user_role").append(
                    $("<option></option>")
                        .attr("value", value.slug)
                        .text(value.user_role_tag)
                );
            });
        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 5000,
                positionClass: "toast-top-center",
                progressBar: true,
            });
        },
    });
});

/**************employee admin id ****************************/
$("#building_admin").on("change", function () {
    let id = $(this).val();
    $("#employee_admin_id").val(id);
});

$("#spga_admin").on("change", function () {
    let id = $(this).val();
    $("#employee_admin_id").val(id);
});

$("#sp_admin_id").on("change", function () {
    let id = $(this).val();
    $("#employee_admin_id").val(id);
});


$(document).ready(function() {
    $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
      $('.profession_select').hide();
      $('.nationality_select').hide();
      $('.favorite_language_select').hide();
      //$('#emp_dept').prop('required', false); // Make input not required
      if(user_type_val!='')
      {
         if(user_type_val=='sp_worker')
         {
            //$('.profession').hide();
            $('.profession_select').show();
            $('.nationality_select').show();
            $('.favorite_language_select').show();
         }
         else
         {
            $('.profession_select').hide();
            $('.nationality_select').hide();
            $('.favorite_language_select').hide();
         }
      }

      $('#profession_id').on('change', function() {
         var selectedOption = $(this).val();

         if (selectedOption === '10') {
               $('.profession').show();
               $('#emp_dept').prop('required', true); // Make input required
               $('#emp_dept').attr('maxlength', 15); // Set the new max-length attribute
               $('#emp_dept').val('');
         } else {
               $('.profession').hide();
               $('#emp_dept').prop('required', false); // Make input not required
               $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
               $('#emp_dept').val('');
         }
      });
    });