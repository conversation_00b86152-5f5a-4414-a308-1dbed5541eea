document.addEventListener("DOMContentLoaded", function() {
    // Ensure event listener is not added multiple times
    if (!window.toastrEventRegistered) {
        window.toastrEventRegistered = true; // Set flag

        window.addEventListener("show-toastr", event => {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: "toast-top-right",
                timeOut: "3000"
            };

            if (event.detail.type === "success") {
                toastr.success(event.detail.message);
            } else if (event.detail.type === "error") {
                toastr.error(event.detail.message);
            }
        });
    }

    let livewireRequestCount = 0; // Track active requests

    if (!window.livewireHooksRegistered) {
        window.livewireHooksRegistered = true; // Prevent duplicate hooks

        Livewire.hook("message.sent", message => {
            const isEventTriggered = message.updateQueue.some(
                update => update.type === "callMethod"
            );

            const isPolling = message.updateQueue.some(
                update =>
                    update.type === "callMethod" &&
                    update.payload.method === "checkForNewData"
            );

            const validType = message.updateQueue.some(
                update => update.method === "$set"
            );

            if (isEventTriggered && !validType && !isPolling) {
                livewireRequestCount++;
                showLoader();
            }
        });

        Livewire.hook("message.processed", () => {
            livewireRequestCount--;

            if (livewireRequestCount <= 0) {
                livewireRequestCount = 0;
                hideLoader();
            }
        });
    }

    function showLoader() {
        if ($("#overlayer").css("display") === "none" && $("#overlayer1").css("display") === "none") {
            $("#overlayer").attr("style", "");
            $("#overlayer .loader-overlay").attr("style", "");
        }
    }

    function hideLoader() {
        $("#overlayer").attr("style", "display: none;");
        $("#overlayer .loader-overlay").attr("style", "display: none;");
    }

    $(document).on("hidden.bs.modal", ".modal", function() {
        var modalId = this.id;

        // Skip revenue modal - it handles its own state management
        if (modalId === 'createRevenueModal') {
            return;
        }

        if ($(`#${modalId} form`).length) {
            Livewire.emit(`resetForm_${modalId}`);
            $(`#${modalId} form`)[0].reset();
            $(`#${modalId} form select`)
                .val(null)
                .trigger("change");
        }
    });
});
