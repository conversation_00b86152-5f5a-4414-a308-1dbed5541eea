document.addEventListener("DOMContentLoaded", function() {
    window.addEventListener("close-modal", e => {
        let modalId = e.detail.modalId;
        $(`#${modalId}`).modal("hide");
        if ($(`#${modalId} form`).length) {
            $(`#${modalId} form`)[0].reset();
            $(`#${modalId} form select`)
                .val(null)
                .trigger("change");
            Livewire.emit(`resetForm_${modalId}`);
        }
    });
    window.addEventListener("open-modal", e => {
        let modalId = e.detail.modalId;
        $(`#${modalId}`).modal("show");
        if ($(`#${modalId} form`).length) {
            if (
                typeof initializeSelect2 === "function" &&
                $(`#${modalId} form select`).length
            ) {
                initializeSelect2($(`#${modalId} form select`));
            }
            if (
                typeof initializeDatepicker === "function" &&
                $(`#${modalId} form .datepicker`).length
            ) {
                initializeDatepicker(`#${modalId} form .datepicker`);
            }
        }
    });
});
