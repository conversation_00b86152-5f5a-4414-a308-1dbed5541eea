<?php

return [
    
    'title' => 'Projects',
    'system-setup' => 'System Setup',
    'create-task' => 'Create Task',
    'create-incident' => 'Create Incident',
    'project-details' => 'Project Details',
    'task-board' => 'Task Board',
    'show-list-view' => 'Show List View',
    'show-card-view' => 'Show Card View',
    'milestone' => 'Milestone',
    'due-date' => 'Due Date',
    'no_attachment' => 'No Attachment Uploaded',
    'task-stage' => 'Task Stage',
    'incident-report' => 'Incident Report',
    'task-stages' => 'Task Stages',
    'incident-stage' => 'Incident Stage',
    'incident-stages' => 'Incident Stages',
    'incident-stages-note' => 'System will consider the last stage as a completed / done project or incident status.',
    'task-stages-note' => 'System will consider the last stage as a completed / done project or task status.',
    'color' => 'Color',
    'delete' => 'Delete',
    'drag-tooltip' => 'Drag Stage to Change Order',
    'incident-stages-drag-note' => 'Note: You can easily change the order of Incident stages using drag & drop.',
    'task-stages-drag-note' => 'Note : You can easily change order of Task stage using drag & drop.',
    'save-changes' => 'Save Changes',
    'create-proposal' => 'Create Proposal',
'tangible_task_update_should_be_from_linked_workOrder' => 'Tangible task update must be done from the linked Work Order.',
'tangible_task_info_with_id' => 'Tangible Task with ID',
'tangible_task_info_title' => 'and Title',
'tangible_task_has_been_created' => 'has been created successfully.',
'create_invoice' => 'Create Invoice',
'invoice_number' => 'Invoice Number',
'create_bill'=>'Create Bill',
    'common' => [
  'draft' => 'Draft',
    'write_message' => 'Write message',
    'delete_title' => 'Delete ":title"?',
    'delete_warning' => 'This action cannot be undone.',
    'confirm_delete' => 'Yes, delete it!',
    'cancel' => 'Cancel',
    'sub_task_name' => 'Sub Task Name',
    'add_subtask' => 'Add Subtask',
    'assign_to' => 'Assign To',

          'bill_updated_successfully' => 'Bill updated successfully.',
    'failed_to_update_bill' => 'Failed to update the bill.',
         'invoice_updated_successfully' => 'Invoice updated successfully',
        'failed_to_update_invoice' => 'Failed to update invoice',
'update_status' => 'Update Status',
         'please_select_work_order' => 'Please select a work order.',
            'tangible_task_successfully' => 'Tangible task created successfully.',
        'failed_to_create_tangible_task' => 'Failed to create tangible task.',
'this_bma_has_access_to_create_tangible_task' => 'This Building Manager has permission to create a tangible task.',

        'bma_unassigned_successfully' => 'Building Manager unassigned successfully.',
'failed_to_unassign_bma' => 'Failed to unassign the Building Manager.',
        'are_you_sure_delete_assign_bma' => 'Are you sure you want to unassign',
        'no_bma_yet' => 'No Building Manager yet',
    'bma_list_will_appear' => 'The list of Building Managers will appear here once added',
        'email' => 'Email',
        'created_at' => 'Created At',
        'nb_project_included' => 'Projects Joined',
        'bmas' => 'Building Managers',
        'bma_assigned_successfully' => 'Building Manager assigned successfully.',
    'failed_to_assign_bma' => 'Failed to assign Building Manager.',
        'assign' => 'Assign',
        'bma' => 'Building Manager',
    'choose_bma' => 'Choose a Building Manager',
        'assign_building_manager' => 'Assign Building Manager',

        'proposal_updated_successfully' => 'Proposal updated successfully',


        'view'=>'View',
        'view_task'=>'View Task',
        'edit_task'=>'Edit Task',

        'budget_must_be_positive' => 'The budget must be greater than zero.',
        'total' => 'Total',
        'sub_total' => 'Sub Total',
        'select_vendor' => 'Select Vendor',
        'bill_number' => 'Bill Number',
'description_max_limit' => 'The description must not exceed 5000 characters.',
'invoice_created_successfully' => 'Invoice created successfully',
'bill_created_successfully' => 'Bill created successfully',
        'template_project_deleted_successfully' => 'Project Template deleted successfully.',
        'project_updated_successfully' => 'Project updated successfully.',
        'edit_project_template' => 'Edit Project Template',
        'template_project_updated_successfully' => 'Project Template updated successfully.',
        'finance' => 'Finance',
        'Invalid_File' => 'Invalid file',
        'Only_PDF_JPG_and_PNG_files_are_llowed' => 'Only PDF, JPG, and PNG files are allowed.',
        'File_Too_Large' => 'File too large',
        'The_file_size_must_not_exceed_2MB' => 'The file size must not exceed 2MB.',
        'no_tasks_yet' => 'No tasks yet',
        'tasks_completed' => ':completed out of :total tasks completed',
        'tasks' => 'Tasks',
        'project_status' => 'Project Status',
        'tasks_overview' => 'Tasks Overview',
        'total_projects' => 'Total Projects',
        'total_tasks' => 'Total Tasks',
        'total_incidents' => 'Total Incidents',
        'total_users' => 'Total Users',
        'projects' => 'Projects',
        'project' => 'Project',
        'project-template' => 'Project Template',
        'create_project' => 'Create Project',
        'manage_projects' => 'Manage Projects',
        'dashboard' => 'Dashboard',
        'all' => 'All',
        'ongoing' => 'Ongoing',
        'finished' => 'Finished',
        'onhold' => 'OnHold',
        'start_end_date' => 'Start/End Date',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'enter_start_date' => 'Enter Start Date',
        'enter_end_date' => 'Enter End Date',
        'close' => 'Close',
        'submit' => 'Submit',
        'search' => 'Search',
        'export' => 'Export',
        'name' => 'Name',
        'stage' => 'Stage',
        'assign_user' => 'Assign User',
        'action' => 'Action',
        'due_date' => 'Due Date',
        'members' => 'Members',
        'invite_users' => 'Invite Users',
        'share_clients' => 'Share Clients',
        'duplicate' => 'Duplicate',
        'save_as_template' => 'Save as Template',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'no_project_found' => 'No projects found.',
        'pagination' => [
            'entries_per_page' => 'Entries Per Page',
            'showing' => 'Showing',
            'to' => 'to',
            'of' => 'of',
            'results' => 'results',

        ],
        'URL_copied_to_clipboard!' => 'URL copied to clipboard!',
        'failed_Copie_URL_to_clipboard' => 'Failed to copy the URL to clipboard.',
        'are_you_sure' => 'Are you sure?',
        'you_want_to_delete_this_project' => ' you want to delete this project ',
        'you_want_to_delete_this_file' => ' you want to delete this File ',
        'this_action_cannot_be_undone' => 'This action cannot be undone',
        'error_message' => 'An error occurred. Please try again.',
        'project_deleted_success' => 'Project deleted successfully!',
        'file_deleted_success' => 'File deleted successfully!',
        'no_projects_yet' => 'No Projects Yet',
        'no_reports_yet' => 'No Reports Yet',
        'projects_list_will_appear' => 'The Projects list will appear here',
        'generate_with_ai' => 'Generate with AI',
        'placeholder_enter_project_name' => 'Enter the project name',
        'user' => 'User',
        'choose_user' => 'Choose User',
        'choose_vendor' => 'Choose Vendor',
        'choose_client' => 'Choose Client',
        'No_data_found' => 'No Data Found',

        'description' => 'Description',
        'placeholder_enter_description' => 'Enter your description',
        'edit_project' => 'Edit Project',
        'task_board' => 'Task Board',
        'users' => 'Users',
        'invite' => 'Invite',
        'please_select' => 'Please Select',
        'share_with_clients' => 'Share with Clients',
        'clients' => 'Clients',
        'share_to_clients' => 'Share to Clients',
        'share_with_vendors' => 'Share with Vendors',
        'vendors' => 'Vendors',
        'share_to_vendors' => 'Share to Vendors',
        'duplicate_project' => 'Duplicate Project',
        'all' => 'All',
        'task' => 'Task',
        'sub_task' => 'Sub Task',
        'comment' => 'Comment',
        'files' => 'Files',
        'bug' => 'Incident',
        'activity' => 'Activity',
        'team_member' => 'Team Member',
        'client' => 'Client',
        'milestone' => 'Milestone',
        'project_file' => 'Project File',
        'copy' => 'Copy',
        'template_name' => 'Template Name',
        'entrer_project_template_name' => 'Enter Project Template Name',
        'project_template_has_been_created_successfully' => 'The project template has been created successfully!',
        'failed_to_save_as_template' => 'Failed to save the project as a template.',
        'project_has_been_duplicated_successfully' => 'The project has been duplicated successfully!',
        'failed_to_duplicate_project' => 'Failed to duplicate the project.',
        'error_task_bug_on_project' => 'There are tasks or incidents associated with this project. Please remove them first.',
        'no_data_found' => 'No data found for the requested URL.',
        'no_projects_found_message' => 'You can visit the project list to explore available projects.',
        'view_project_list' => 'View Project List',
        'project_details' => 'Project Details',
        'total_membres' => 'Total Members',
        'days_left' => 'Days left',
        'budget' => 'Budget',
        'total_task' => 'Total Task',
        'comments' => 'Comments',
        'progress_last_week_tasks' => 'Progress (Last Week Tasks)',
        'days' => [
            'mon' => 'Monday',
            'tue' => 'Tuesday',
            'wed' => 'Wednesday',
            'thu' => 'Thursday',
            'fri' => 'Friday',
            'sat' => 'Saturday',
            'sun' => 'Sunday',
        ],
        'todo' => 'Todo',
        'inProgress' => 'In Progress',
        'review' => 'Review',
        'done' => 'Done',
        'team_members' => 'Team Members',
        'placeholder_enter_budget' => 'Enter the budget',
        'start_end_validation_date' => 'End date must be same or after start date.',
        'project_created_successfully' => 'Project has been created successfully!',
        'project_updated_successfully' => 'Project has been updated successfully!',
        'failed_to_create_project' => 'Failed to create the project!',
        'failed_to_update_project' => 'Failed to update the project!',

        //milestone
        'create_milestone' => 'Create Milestone',
        'edit_milestone' => 'Edit Milestone',
        'milestone_created_successfully' => 'Milestone has been created successfully!',
        'milestone_update_successfully' => 'Milestone has been updated successfully!',
        'document_update_successfully' => 'Document has been updated successfully!',
        'document_created_successfully' => 'Document has been created successfully!',
        'failed_to_create_milestone' => 'Failed to create the Milestone!',
        'failed_to_create_document' => 'Failed to create the Document!',
        'milestone_title' => 'Milestone Title',
        'status' => 'Status',
        'incomplete' => 'Incomplete',
        'complete' => 'Complete',
        'milestone_cost' => 'Milestone Cost',
        'summary' => 'Summary',
        'submit' => 'Submit',
        'close' => 'Close',
        'name' => 'Name',
        'status' => 'Status',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'cost' => 'Cost',
        'progress' => 'Progress',
        'actions' => 'Actions',
        'no_milestone_yet' => 'No Milestone Yet',
        'milestone_list_will_appear' => 'The Milestone list will appear here',
        'you_are_about_to_delete_milestone' => 'You are about to delete the Milestone',
        'you_are_about_to_delete_document' => 'You are about to delete the document',
        'milestone_deleted_success' => 'Milestone deleted successfully!',
        'document_deleted_success' => 'Document deleted successfully!',

        'no_activity_yet' => 'No activity yet',
        'failed_to_invite_user' => 'Failed to invite user.',
        'users_invited_successfully' => 'Users invited successfully.',

        'sharedprojectsettings' => 'Shared Project Settings',
        'project_setting_successfully' => 'Project setting has been created successfully!',
        'failed_to_project_setting' => 'Failed to create the Project setting!',
        'name' => 'Name',
        'off_on' => 'Off/On',
        'basic_details' => 'Basic details',
        'member' => 'Member',
        'client' => 'Client',
        'vendor' => 'Vendor',
        'milestone' => 'Milestone',
        'activity' => 'Activity',
        'attachment' => 'Files',
        'task' => 'Task',
        'bug_report' => 'Bug Report',
        'invoice' => 'Invoice',
        'bill' => 'Bill',
        'timesheet' => 'Timesheet',
        'documents' => 'Documents',
        'progress' => 'Progress',
        'password_protected' => 'Password Protected',
        'retainer' => 'Retainer',
        'proposal' => 'Proposal',
        'procurement' => 'Procurement',


        'calendar' => 'Calendar',
        'gantt_charts' => 'Gantt Charts',
        'gantt_chart' => 'Gantt Chart',
        'finance' => 'Finance',
        'rfx' => 'RFx',
        'milestones' => 'Milestones',
        'cost' => 'Cost',
        'status' => 'Status',
        'file_name' => 'File Name',
        'uploaded_by' => 'Uploaded By',
        'uploaded_date' => 'Upload Date',
        'download' => 'Download',
        'document' => 'Document',
        'subject' => 'Subject',
        'type' => 'Type',
        'sar' => 'SAR',
        'project_shared_with_vendor_successfully' => 'Project shared with vendor successfully.',
        'failed_to_share_project' => 'Failed to share the project.',
        'project_shared_with_users_successfully' => 'Project shared with users successfully.',
        'cancel_share_vendor'      => 'Are you sure you want to cancel the project share for the vendor',
        'cancel_share_client'      => 'Are you sure you want to cancel the project share for the client',
        'delete_invitation_user'   => 'Are you sure you want to delete the invitation for the user',
        'generic_delete_access'    => 'Are you sure you want to remove access for',
        'yes_process' => 'Yes, Proceed',
        'no' => 'No',
        'failed_to_complete_operation' => 'Failed to complete the operation.',
        'share_vendor_cancelled_successfully' => 'The vendor share has been successfully cancelled.',
        'share_client_cancelled_successfully' => 'The client share has been successfully cancelled.',
        'invitation_user_cancelled_successfully' => 'The user invitation has been successfully cancelled.',

        'quarter_day' => 'Quarter Day',
        'half_day' => 'Half Day',
        'day' => 'Day',
        'week' => 'Week',
        'month' => 'Month',
        'progress_percentage' => 'Progress Percentage',
        'task_informations' => 'Task Informations',
        'identifier' => 'Identifier',
        'regular' => 'Regular',
        'subtask' => 'Subtask',
        'overdue' => 'Overdue',
        'completed' => 'Completed',
        'priority' => 'Priority',
        'owner' => 'Task Owner',
        'select_account_type' => 'Select Account Type',
        'accounting' => 'Accounting',
        'cmms' => 'CMMS',
        'customer_client' => 'Customer/Client',
        'billing_type' => 'Billing Type',
        'template' => 'Template',
        'select_template' => 'Select Template',
        'issue_date' => 'Issue Date',
        'select_date' => 'Select Date',
        'proposal_number' => 'Proposal Number',
        'bill_number' => 'Bill Number',
        'select_category' => 'Select Category',
        'items' => 'Items',
        'add_item' => 'Add Item',
        'account_type' => 'Account Type',
        'select_billing_type' => 'Select Billing Type',
        'select_customer_client' => 'Select Customer',
        'tax' => 'Tax',
        'select_tax' => 'Select Tax',
        'cancel' => 'Cancel',
        'enter_proposal_number' => 'Enter Proposal Number',
        'item_wise' => 'Item Wise',
        'project_wise' => 'Project Wise',
        'parts_wise' => 'Parts Wise',
        'item_type' => 'Item Type',
        'qte' => 'Quantity',
        'price' => 'Price',
        'discount' => 'Disount',
        'amount' => 'Amount',
        'after_discount_tax' => 'AFTER DISCOUNT & TAX',
        'sar' => 'SAR',
        'low_text' => 'Low',
        'medium_text' => 'Medium',
        'hight_text' => 'High',
        'comments_text' => 'Comments',
        'task_text' => 'Task',

        'create' => 'Create',
        'update' => 'Update',
        'enter_milestone_title' => 'Enter Milestone Title',
        'enter_cost' => 'Enter Cost',
        'enter_description' => 'Enter Your Description',
        'progress' => 'Progress',

        'manage_project_reports' => 'Manage Project Reports',
        'dashboard' => 'Dashboard',
        'project_reports' => 'Project Reports',
        'filterBy' => 'Filter By',
        'startDate' => 'Start Date',
        'endDate' => 'End Date',
        'enterStartDate' => 'Enter Start Date',
        'enterEndDate' => 'Enter End Date',
        'users' => 'Users',
        'selectUser' => 'Select User',
        'status' => 'Status',
        'selectStatus' => 'Select Status',
        'reset' => 'Reset',
        'submit' => 'Submit',
        'projectReports' => 'Project Reports',
        'export' => 'Export',
        'no' => 'No',
        'projectName' => 'Project Name',
        'dueDate' => 'Due Date',
        'projectMembers' => 'Project Members',
        'stage' => 'Stage',
        'action' => 'Action',
        'noResultFound' => 'No Result Found',
        'viewReport' => 'View Report',
        'search' => 'Search',
        'projectReportsDetail' => 'Project Reports Detail',
        'download' => 'Download',
        'overview' => 'Overview',
        'projectStatus' => 'Project Status',
        'totalMembers' => 'Total Members',
        'milestoneProgress' => 'Milestone Progress',
        'taskPriority' => 'Task Priority',
        'taskStatus' => 'Task Status',
        'users' => 'Users',
        'name' => 'Name',
        'assignedTasks' => 'Assigned Tasks',
        'completedTasks' => 'Completed Tasks',
        'cashflow' => 'Cashflow',
        'monthly' => 'Monthly',
        'quarterly' => 'Quarterly',
        'year' => 'Year',
        'reports' => 'Reports',
        'duration' => 'Duration',
        'monthlyCashflow' => 'Monthly Cashflow',
        'income' => 'Income',
        'category' => 'Category',
        'january' => 'January',
        'february' => 'February',
        'march' => 'March',
        'april' => 'April',
        'may' => 'May',
        'june' => 'June',
        'july' => 'July',
        'august' => 'August',
        'september' => 'September',
        'october' => 'October',
        'november' => 'November',
        'december' => 'December',
        'totalIncomeInvoice' => 'Total Income (Invoice)',
        'expense' => 'Expense',
        'totalIncomeBill' => 'Total Income (Bill)',
        'netProfit' => 'Net Profit',
        'netProfitCalc' => 'Net Profit = Total Income - Total Expense',
        'totalExpenses' => 'Total Expenses',
        'allUsers' => 'All Users',
        'allMilestones' => 'All Milestones',
        'allStatus' => 'All Status',
        'inProgress' => 'In Progress',
        'review' => 'Review',
        'done' => 'Done',
        'allPriority' => 'All Priority',
        'taskName' => 'Task Name',
        'assignedTo' => 'Assigned To',
        'priority' => 'Priority',
        'file_uploading' => 'The file is',
        'uploading' => 'uploading',
        'might_take_time' => 'this might take a moment...',
        'file_uploaded_successfully' => 'File uploaded successfully.',
        'failed_to_upload_file' => 'Failed to upload the file.',
        'upload_file_global_validation_failed' => 'The uploaded file is not valid. Please check the file type and size.',

    ],
    'template' => [
        'page_title' => 'Manage Templates',
        'convert_to_project' => 'Convert to Project',
        'view' => 'View',
        'delete' => 'Delete',
        'number' => 'Number',
        'name' => 'Name',
        'assigned_user' => 'Assigned User',
        'actions' => 'Actions',
        'comments' => 'Comments',
        'tasks' => 'Tasks',
        'export' => 'Export',
    ],
    'template_details' => [
        'page_title' => 'Project Template',
        'milestone_title' => 'Milestone Title',
        'milestone_cost' => 'Milestone Cost',
        'summary' => 'Summary',
    ],
    'calendar' => [
        'upcoming_tasks' => 'Upcoming Tasks',
        'no_task' => 'There is no Task in this month',
    ],
    'dashboard_description' => 'Optimizes project management with task tracking, timelines, and real-time progress updates.',
    'account_type_required' => 'The account type is required',
    'billing_type_required' => 'The billing type is required',
    'issue_date_required' => 'The issue date is required',
    'customer_required' => 'The customer is required',
    'project_required' => 'The project is required',
    'issue_date_date' => 'The type of issue date must be date',
    'question_modal' => 'Are you sure you want to clear all inputs?',
    'cannot_undone' => 'This action cannot be undone.',
    'clear_all' => 'Clear all inputs',
    'proposal_successfully' => 'Proposal has been created successfully!',
    'failed_to_proposal' => 'Failed to create the proposal!',
    'enter_qte' => 'Enter the quantity',
    'enter_price' => 'Enter the price',
    'enter_discount' => 'Enter the discount',
    'item_type_required' => 'The item type is required',
    'items_required' => 'The item is required',
    'qte_required' => 'The quantity is required',
    'qte_integer' => 'The quantity must be a number',
    'price_required' => 'The price is required',
    'discount_required' => 'The discount is required',
    'validation_message' => 'Please make sure that you entered correctly these fields:',
    'sub_total' => 'Sub Total',
    'total' => 'Total',
    'create_tangible_task' => 'Create Tangible Task',
    'create_Untangible_task' => 'Create Task',
    'work_order_type' => 'Work Order Type',
    'select_work_order_type' => 'Select Work Order Type',
    'work_order_reactive' => 'Reactive',
    'work_order_preventive' => 'Preventive',
    'next' => 'Next',
    'task_linked_to_WorkOrder_description' => 'This task is linked to a Work Order. Please select the appropriate type to proceed.',
    'enter_account_type' => 'Enter The Account Type',
    'enter_billing_type' => 'Enter The Builling Type',
    'view_description' => 'View Description',

    'no_items' => 'The items list is empty..',
    'enter_template' => 'Enter the template',

    'enterDueDate' => 'Enter Due Date',
    'enter_description'=>'Enter Description',
    'enter_title'=>'Enter Title',
    'edit_status' => 'Edit Status',
    'select_status' => 'Select the status',
    'status_required' => 'The status is required',
    'status_updated_successfully' => 'Status updated successfully',
    'status_not_updated' => 'Cannot update the status',
    'select_task_owner' => 'Select the task owner',
    'select_priority' => 'Select the priority',
    'task_type' => 'Task Type',
    'select_task_type' => 'Select the task type',
    'regular_task' => 'Regular Task',
    'owner_text' => 'Owner',
    'show_hide_columns' => 'Show / hide columns',
    'no_data_found_gantt' => 'No data to display',
    'select_column_to_show' => 'Select the columns to show',
    'apply' => 'Apply',
    'reset_all_filters' => 'Reset all filters',


    'project_type' => 'Project Type',
    'project_type_dropdown' => [
        'new_construction' => 'New Construction',
        'renovation' => 'Renovation',
        'maintenance' => 'Maintenance',
        'expansion' => 'Expansion',
        'demolition' => 'Demolition',
        'infrastructure' => 'Infrastructure',
        'design_projects' => 'Design Projects',
        'compliance_safety' => 'Compliance & Safety',
        'other' => 'Other',
    ],
    'priority_level' => 'Priority Level',
    'priority_level_dropdown' => [
        'critical' => 'Critical',
        'high' => 'High',
        'medium' => 'Medium',
        'low' => 'Low',
    ],
];
