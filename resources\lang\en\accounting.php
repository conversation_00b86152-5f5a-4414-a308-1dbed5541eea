<?php



return [
    'no_data_found' => 'No Data found',
    'manageBankAccounts' => 'Manage Bank Accounts',
    'create' => 'Create',
    'accounts' => 'Accounts',
    'bankAccounts' => 'Bank Accounts',
    'editAccount' => 'Edit New Account',
    'export' => 'Export',
    'cancel' => 'Cancel',
    'create' => 'Create',
    'update' => 'Update',
    'bank_name' => 'Bank Name',
    'account_number' => 'Account Number',
    'current_balance' => 'Current Balance',
    'contact_number' => 'Contact Number',
    'bank_branch' => 'Bank Branch',
    'swift_code' => 'SWIFT Code',
    'bank_address' => 'Bank Address',
    'coa_current_assets' => 'COA (Current Assets)',
    'action' => 'Action',
    'createAccount' => 'Create New Account',
    'bankType' => 'Bank Type',
    'account' => 'Account',
    'bankHolderName' => 'Bank Holder Name',
    'bankName' => 'Bank Name',
    'accountNumber' => 'Account Number',
    'openingBalance' => 'Opening Balance',
    'contactNumber' => 'Contact Number',
    'bankBranch' => 'Bank Branch',
    'swiftCode' => 'SWIFT Code',
    'bankAddress' => 'Bank Address',
    'bankAccountsDetail' => 'Bank Account Details',


    'chartaccount' => 'Chart of Accounts',
    'transfer' => 'Transfer',
    'bankBalanceTransfer'=>'Bank Balance Transfer',
    'createNewTransfer' => 'Create New Transfer',
    'fromType' => 'From Type',
    'toType' => 'To Type',
    'fromAccount' => 'From Account',
    'selectType' => 'Select Type',
    'selectAccount' => 'Select Account',
    'toAccount' => 'To Account',
    'amount' => 'Amount',
    'enterAmount' => 'Enter Amount',
    'date' => 'Date',
    'reference' => 'Reference',
    'enterReference' => 'Enter Reference',
    'description' => 'Description',
    'enterDescription' => 'Enter Description',
    'action' => 'Action',
    'enterDate' => 'Enter Date',
    'apply' => 'Apply',

    'editNewTransfer' => 'Edit New Transfer',
    'bankTransferDetails' => 'Bank Transfer Details',
    'close' => 'Close',


    'create_new_account' => 'Create Charts of Accounts',
    'edit_new_account' => 'Edit Charts of Accounts',
    'accountDetail' => 'Charts of Accounts Detail',
    'name' => 'Name',
    'code' => 'Code',
    'account_type' => 'Account Type',
    'is_enable' => 'Is Enable',
    'description' => 'Description',
    'please_select' => 'Please select',
    'type' => 'Type',
    'parent_account_type' => 'Parent Account Type',
    'balance' => 'Balance',
    'status' => 'Status',
    'manage_chart_accounts' => 'Manage Charts of Accounts',

    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'enter_start_date' => 'Enter Start Date',
    'enter_end_date' => 'Enter End Date',
    'apply' => 'Apply',


    'creditnotes'=>'Credit Notes',
    'customer'=>'Customer',
    'create_new_credit_note' => 'Create New Credit Note',
    'invoice' => 'Invoice',
    'select_invoice' => 'Select Invoice',
    'amount' => 'Amount',
    'date' => 'Date',
    'status' => 'Status',
    'description' => 'Description',
    'edit_credit_note' => 'Edit Credit Note',
    'detail_credit_note' => 'Credit Note Detail',
    'manage_credit_notes' => 'Manage Credit Notes',



    'debitnotes'=>'Debit Notes',
    'vendor'=>'Vendor',
    'create_new_debit_note' => 'Create New Debit Note',
    'bill' => 'Bill',
    'select_bill' => 'Select Bill',
    'amount' => 'Amount',
    'date' => 'Date',
    'status' => 'Status',
    'description' => 'Description',
    'edit_debit_note' => 'Edit Debit Note',
    'detail_debit_note' => 'Debit Note Detail',
    'manage_debit_notes' => 'Manage Debit Notes',

    // Journal Entries
    'journal_entries' => 'Journal Entries',
    'create_journal_entry' => 'Create Journal Entry',
    'edit_journal_entry' => 'Edit Journal Entry',
    'view_journal_entry' => 'View Journal Entry',
    'back_to_list' => 'Back to List',
    'create_btn' => 'Create',
    'update_journal_entry' => 'Update',
    'journal_number' => 'Journal Number',
    'transaction_date' => 'Transaction Date',
    'account' => 'Account',
    'select_account' => 'Select Account',
    'debit' => 'Debit',
    'credit' => 'Credit',
    'total_debit' => 'Total Debit',
    'total_credit' => 'Total Credit',
    'add_entry' => 'Add Entry',
    'delete' => 'Delete',
    'updating' => 'Updating',
    'journal_entries_must_balance' => 'Journal entries must balance - total debits must equal total credits.',
    'journal_entries_cannot_be_zero' => 'Journal entries cannot be zero.',
    'journal_entry_created_successfully' => 'Journal entry created successfully.',
    'journal_updated_successfully' => 'Journal updated successfully.',
    'failed_to_save_journal_entry' => 'Failed to save journal entry.',
    'failed_to_update_journal' => 'Failed to update journal.',
    'failed_to_load_journal' => 'Failed to load journal.',
    'error_occurred_while_saving_journal_entry' => 'An error occurred while saving the journal entry.',
    'error_occurred_while_updating' => 'An error occurred while updating the journal.',
    'error_occurred_while_loading' => 'An error occurred while loading the journal.',

    'payment' => 'Payment',
    'managePayments'=>'Manage Payments',
    'create_new_payment' => 'Create New Payment',
    'edit_new_payment' => 'Edit Payment',
    'detail_payment' => 'Payment Detail',
    'select_account' => 'Select Account',
    'select_vendor' => 'Select Vendor',
    'category' => 'Category',
    'select_category' => 'Select Category',
    'paymentReceipt' => 'Payment Receipt',
    'fromDate' => 'From Date',
    'enterfromDate' => 'Enter From Date',
    'toDate' => 'To Date',
    'entertoDate' => 'Enter To Date',



    'manageBudgetPlanner'=>'Manage Budget Planner',
    'budgetPlanner'=>' Budget Planner',
    'no' => 'No',
    'name' => 'Name',
    'from' => 'From',
    'budget_period' => 'Budget Period',
    'action' => 'Action',
    'createBudgetPlanner'=>'Create Budget Planner',
    'editBudgetPlanner'=>'Edit Budget Planner',
    'year'=>'Year',
    'select_year'=>'Select Year',
    'manageBudgetPlanner'=>'Manage Budget Planner',
    'category' => 'Category',
    'total' => 'Total',
    'expense' => 'Expense',
    'income' => 'Income',


    'banking' => 'Banking',


    'budgetActual'=>'Budget Vs Actual',
    'bill'=>'Bill',
    'manageBills'=>'Manage Bills',
    'vendor' => 'Vendor',
    'account_type' => 'Account Type',
    'bill_date' => 'Bill Date',
    
    'due_date' => 'Due Date',
    'due_amount' => 'Due Amount',
    'url_copied' => 'URL copied to clipboard!',

    'create_bill' => 'Create Bill',
    'edit_bill' => 'edit Bill',
    'bill_detail' => 'Bill Detail',
    'dashboard' => 'Dashboard',
    'bill' => 'Bill',
    'account_type' => 'Account Type',
    'select_date' => 'Select Date',
    'select_account_type' => 'Select Account Type',
    'vendor' => 'Vendor',
    'select_vendor' => 'Select Vendor',
    'billing_type' => 'Billing Type',
    'select_billing_type' => 'Select Billing Type',
    'bill_date' => 'Bill Date',
    'due_date' => 'Due Date',
    'bill_number' => 'Bill Number',
    'order_number' => 'Order Number',
    'category' => 'Category',
    'select_category' => 'Select Category',
    'enter_order_number'=>'Enter Order Number',
    'enter_bill_number'=>'Enter Bill Number',
     'items' => 'Items',
    'quantity' => 'Quantity',
    'price' => 'Price',
    'discount' => 'Discount',
    'tax' => 'Tax (%)',
    'amount_after_discount_tax' => 'Amount',
    'amount_note' => 'AFTER DISCOUNT & TAX',
    'description' => 'Description',
    'sub_total' => 'Sub Total (SAR)',
    'discount_total' => 'Discount (SAR)',
    'tax_total' => 'Tax (SAR)',
    'total' => 'Total (SAR)',
    'add_item' => 'Add Item',
    'items' => 'Items',
    'quantity' => 'Quantity',
    'price' => 'Price',
    'discount' => 'Discount',
    'tax' => 'Tax (%)',
    'amount_after_discount_tax' => 'Amount',
    'amount_note' => 'AFTER DISCOUNT & TAX',
    'description' => 'Description',
    'sub_total' => 'Sub Total (SAR)',
    'discount_total' => 'Discount (SAR)',
    'tax_total' => 'Tax (SAR)',
    'total' => 'Total (SAR)',
    'add_item' => 'Add Item',
    'create' => 'Create',
    'cancel' => 'Cancel',
    'item'=>'Item',
    'net_profit' => 'NET PROFIT',






    'dashboard'     => 'Dashboard',
    'bill'          => 'Bill',
    'details_bill'  => 'Details Bill',
    'create_bill'      => 'Create Bill',
    'created_on'       => 'Created on :date',
    'edit'             => 'Edit',
    'send_bill'        => 'Send Bill',
    'send'             => 'Send',
    'loading'          => 'Loading..',
    'pay_bill'         => 'Pay Bill',
    'status'           => 'Status: :status',
    'add_payment'      => 'Add Payment',
    'bill' => 'Bill',
    'recurring_bill' => 'Recurring Bill',
    'monthly' => 'Monthly',
    'status' => 'Status',
    'draft' => 'Draft',
    'issue_date' => 'Issue Date',
    'due_date' => 'Due Date',
    'billed_to' => 'Billed To',
    'tax_number' => 'Tax Number',
    'item_summary' => 'Item Summary',
    'item_note' => 'All items here cannot be deleted.',
    'project' => 'Project',
    'quantity' => 'Quantity',
    'rate' => 'Rate',
    'discount' => 'Discount',
    'tax' => 'Tax',
    'description' => 'Description',
    'price' => 'Price',
    'price_note' => 'After discount & tax',
    'sales_tax' => 'Sales Tax',
    'total' => 'Total',
    'paid' => 'Paid',
    'credit_note_applied' => 'Credit Note Applied',
    'debit_note_issued' => 'Debit Note Issued',
    'due' => 'Due',
    'bill' => 'Bill',
    'payment_summary' => 'Payment Summary',
    'debit_note_summary' => 'Debit Note Summary',
    'attachment' => 'Attachment',
    'payment_summary'    => 'Payment Summary',
    'payment_receipt'    => 'Payment Receipt',
    'date'               => 'Date',
    'amount'             => 'Amount',
    'account'            => 'Account',
    'reference'          => 'Reference',
    'action'             => 'Action',
    'delete'             => 'Delete',
    'view'             => 'View',
    'no_data_found'      => 'Oops.. no data found!',
    'description' => 'Description',
    'debit_note_summary' => 'Debit Note Summary',
    'date' => 'Date',
    'amount' => 'Amount',
    'description' => 'Description',
    'action' => 'Action',
    'view' => 'View',
    'attachments'    => 'Attachments',
    'file_name'      => 'File Name',
    'file_size'      => 'File Size',
    'date_created'   => 'Date Created',
    'action'         => 'Action',
    'search'         => 'Search',
    'drag_drop'      => 'Drag & Drop or',
    'choose_file'    => 'Choose file',
    'no_attachments' => 'Oops.. no attachments found!',


    //invoice
    'invoice' => 'Invoice',
    'manageInvoices' => 'Manage Invoices',
    'invoice_number' => 'Invoice Number',
    'issue_date'     => 'Issue Date',
    'due_date'       => 'Due Date',
    'due_amount'     => 'Due Amount',
    'status'         => 'Status',
    'create_invoice' => 'Create Invoice',
    'edit_invoice' => 'Edit Invoice',
    'create_invoice' => 'Create Invoice',
    'customer' => 'Customer',
    'choose_customer' => 'Choose Customer',
    'billing_type' => 'Billing Type',
    'issue_date' => 'Issue Date',
    'due_date' => 'Due Date',
    'category' => 'Category',
    'commission' => 'Commission',
    'select_agent' => 'Select Agent',
    'ledger_account' => 'Sage Ledger Account',
    'item_details_section' => 'Item Details Section',
    'add_item' => 'Add Item',
    'item' => 'Item',
    'quantity' => 'Quantity',
    'price' => 'Price',
    'discount' => 'Discount',
    'tax' => 'Tax',
    'description' => 'Description',
    'sub_total' => 'Sub Total',
    'total_discount' => 'Discount',
    'total' => 'Total',
    'enter_invoice_number'=>'Enter Invoice Number',
    'invoice_template'=>'Invoice Template',
    'total_price'=>'Total Price',


    'commissionPlan'=>'Commission Plan',
    'agent'=>'Select Agent',

    



 ];


