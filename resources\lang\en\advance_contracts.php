<?php

return [
        'contract_steps' => [
            'dashboard'=>'Dashboard',
            'create_new_advance_contract' => 'Create New Advance Contract',
            'create_new_variation_order' => 'Create New Variation Order',
            'step_one' => 'Main Information',
            'step_two' => 'Data Agreements & KPIs',
            'step_three' => 'Assets, Items and Warehouse',
            'step_four' => 'Workforce and Team',
            'step_five' => 'Extras',
            'step_six' => 'Confirmation',
        ],
        'main_information' => [
            'title' => 'Main Information',
            'contract_with' => 'Contract With',
            'information' => 'Information',
            'contract_name' => 'Contract Name',
            'enter_amount' => 'Enter Amount',
            'interval_of_payments' => 'Interval of Payments',
            'duration' => 'Duration',
            'start_end_date' => 'Start - End Date',
            'monthly_payment_schedule' => 'Monthly Payment Schedule',
            'total_amount' => 'Total Amount',
            'month' => 'Month',
            'amount' => 'Amount',
            'type' => 'Type',
            'subcontract_note' => 'Subcontract (Requires Approval of Project Owner)',
            'select_parent_contract' => 'Select Primary Contract',
            'select_contract' => 'Select Contract',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
        ],
        
        'data_agreements' => [
            'title' => 'Data',
            'agreements_kpis' => 'Data Agreements & KPIs',
            'key_performance_indicators' => 'Key Performance Indicators',
            'performance_indicator' => 'Performance Indicator',
            'add_kpi' => 'Add KPI',
            'add_new_kpi' => 'Add/Edit KPI',
            'kpi_limit' => '(Select up to 25)',
            'services' => 'Services',
            'activated_kpi' => 'Activated KPI',
            'price' => 'Price',
            'description' => 'Description',
            'add_service' => 'Add Service',
            'side_features' => 'Side Features',
            'allow_sub_contract' => 'Allow Sub-Contract from this Contract',
            'enable_smart_assign' => 'Enable Smart Assign to this Contract',
            'use_unit_receival_form' => 'Use form of unit receival for tenants for this contract',
            'all' => 'All',
        ],
        'sla' => [
            'title' => 'Service Level Agreement',
            'priorities' => 'Service Level Agreement Priorities',
            'service_window' => 'Service Window',
            'response_time' => 'Response Time',
            'add_sla' => 'Add SLA',
            'actions' => 'Actions',
        ],
        'property' => [
            'building' => 'Building',
            'zone' => 'Zone',
            'unit' => 'Unit',
            'each_building' => 'Each building',
            'has_zones_units' => 'Has :zones Zones and consist of :units Units',
            'total_properties' => 'Total of all properties',
            'zones' => 'Zones',
            'units' => 'Units',
            'selected_properties' => 'Selected Properties',
            'search' => 'Search',

        ],
        'time' => [
            'days' => 'Days',
            'hours' => 'Hours',
            'minutes' => 'Minutes',
        ],
        'penalty' => [
            'ranges' => 'Penalty Ranges',
            'range' => 'Range',
            'points' => 'Penalty Points',
            'percentage' => 'Percentage',
            'fixed_number' => 'Fixed Number',
            'enter_points' => 'Enter Points',
        ],
        'assets_ppm' => [
            'assets_and_ppm' => 'Assets, Items and Warehouse',
            'assets_items_warehouse' => 'Assets, Items and Warehouse',
            'assets' => 'Assets',
            'define_assets' => 'Define Assets',
            'warehouse_ownership' => 'Warehouse Ownership',
            'warehouse_belongs_project_owner' => 'The ownership of the warehouse belongs to the Project owner',
            'warehouse_belongs_service_provider' => 'The ownership of the warehouse belongs to the Service provider',
            'no_inventory_used' => 'No inventory will be used in the contract',
            'items_consumables_used' => 'Items & consumables going to be used',
            'stock_other_values' => 'Items and Stocks',
            'stocks' => 'Item',
            'open_stock' => 'Open Stock',
            'low_stock' => 'Low Stock',
            'mandatory' => 'Mandatory',
            'approval' => 'Approval',
            'price' => 'Price',
            'penalty' => 'Penalty',
            'actions' => 'Actions',
            'no_data_found' => 'No Data Found',
            'select_assets' => 'Select Assets',
            'select_city' => 'Select City',
            'project_owner' => 'Project owner',
            'warehouse_belongs_to' => 'The ownership of the warehouse belongs to the',
            'service_provider' => 'Service provider',
            'select_items' => 'Select Items',
            'add_new_stock' => 'Add New Stock',
            'select_item' => 'Select Item',
            'items' => 'Items',
            'penalty_percentage_validation' => 'The penalty must be between 1 and 100 when penalty type is percentage.',
            'please_add_at_least_one_item' => 'Please add at least one Item.'

        ],

        'workforce' => [
            'workforce_and_team' => 'Workforce and Team',
            'team_distribute' => 'Team Distribute',
            'role' => 'Role',
            'proficiency' => 'Proficiency',
            'quantity' => 'Quantity',
            'deduction_rate' => 'Deduction Rate',
            'working_days' => 'Working Days',
            'localization_target' => 'Localization Target',
            'working_hours_weekly' => 'Working Hours (Weekly)',
            'attendance_mandatory' => 'Attendance Mandatory',
            'select_role' => 'Select Role',
            'select_proficiency' => 'Select Proficiency',
            'add_role' => 'Add Role',
        ],
        'extras' =>[        
            'optional_extra_information' => 'Extra Informations (Optional)',
            'attachments_files' => 'Attachments / Files',
            'click_to_upload' => 'Click to upload',
            'or_drag_and_drop' => 'or Drag and drop',
            'accepted_formats' => 'Accepted formats: jpg, png, webp, gif, heic (max 10MB)',
            'uploading' => 'Uploading...',
            'comments' => 'Comments',
        ],

        'confirmation' => [
            'confirmation' => 'Confirmation',
            'almost_there' => 'You’re almost there!',
            'create_new_advance_contract' => 'You are about to create a new Advance Contract',
            'back' => 'Back',
            'create' => 'Create',
            'confirm_and_create' => 'Confirm & Create',
        ],

        'general' => [
            'no'   => 'No',
            'yes'   => 'YEs',
            'please_select' => 'Please select',
            'please_choose' => 'Please choose',
            'enter_name' => 'Enter Name',
            'enter_amount' => 'Enter Amount',
            'enter_value' => 'Enter value',
            'enter_text'    => 'Enter text',
            'contract_type' => 'Contract Type',
            'regions' => 'Select Regions',
            'cities' => 'Select Cities',
            'properties' => 'Select Properties',
            'services' => 'Select Services',
            'priority' => 'Select Priority',
            'advance' => 'Advance',
            'regular' => 'Regular',
            'advance_contract' => 'Advanced Contract',
            'contract' => 'Contract',
            'enter_btn' => 'Enter',
            'enter_number' => 'Enter Number',
            'please_wait'   => 'Please wait',
            'info_saved' => 'Main information saved successfully!',
            'data_agreements_saved' => 'Data Agreements & KPIs information saved successfully!',
            'assets_ppm_saved' => 'Assets, Items and Warehouse information saved successfully!',
            'items_saved' => 'Items saved successfully!',
            'items_error' => 'Items could not be saved!',
            'workforce_team_saved' => 'Workforce & Team information saved successfully!',
            'extras_saved' => 'Extras information saved successfully!',
            'contract_created' => 'Advance Contract successfully created!',
            'variation_order_created' => 'Variation Order successfully created!',
            'contract_created_error' => 'Something went wrong while creating the advance contract!',
            'variation_order_error' => 'Something went wrong while creating the variation order!',
        ],
        'roles' => [
            'electricity' => 'Electricity',
            'mechanical' => 'Mechanical',
            'equipments' => 'Equipments',
            'civil' => 'Civil',
            'safety' => 'Safety',
            'it' => 'Information Technology',
            'public_works' => 'Public Works',
            'admin_support' => 'Administrative and Support',
        ],
        'proficiency' => [
            'administrative' => 'Administrative',
            'engineering_speciality' => 'Engineering and Speciality',
            'supervisory' => 'Supervisory',
            'technician' => 'Technician',
            'operational' => 'Operational and Craftsmanship',
            'low_skilled' => 'Low Skilled Workers',
        ],
        'buttons' => [
            'close' => 'Close',
            'submit' => 'Submit',
            'save' => 'Save',
            'previous' => 'Previous',
            'save_next' => 'Save & Next',
            'cancel' => 'Cancel',
        ],

        'details_page' => [
            'penality_points_description' => 'Calculated based on Penalty Points',
            'kpi_points_description'    => 'Calculated based on contract the agreement',
            'download'  => 'Download',
            'monthly_payment_schedule'  =>  'Monthly Payment Schedule',
            'interval_of_payments'  =>  'Interval of Payments',
            'total_amount'  => 'Total Amount',
            'service_provider_details'  => 'Service Provider Details',
            'service_provider_name' => 'Service Provider Name',
            'service_provider_id'   =>  'Service Provider ID',
            'business_field'    => 'Business field',
            'information'   => 'Information',
            'title_of_table'    => 'Title of Table',
            'contract_name' => 'Contract Name / With',
            'contract_feature'  => 'Contract Features',
            'unit_recieval' =>  'Enabled tenant form of unit receival?',
            'subcontract'   =>  'Allow Subcontract',
            'items_stock'   =>  'Items and stocks',
            'consumption'   =>  'Consumption',
            'minimum_wage'  =>  'Minimum Wage',
            'uniform_tool'  =>  'Uniform & Tools Mandatory',
            'warehouse_ownership'   =>  'Warehouse Ownership',
            'number_of_payments'    => 'Number of Payments',
            'total_amount_per_time' => 'Total Amount Per Time',
            'smart_Assign'  =>  'Smart Assign',
            'priority_name' => 'Priority Name',
            'service_time'  => 'Service Time'

        ],
        'variation_order' => [
            'confirmation' => 'Confirmation',
            'almost_there' => 'Variation Order Request',
            'create_new_advance_contract' => 'You are submitting a variation order request that requires approval.',
            'back' => 'Previous',
            'create' => 'Submit',
            'confirm_and_create' => 'Submit',
            'attention' => 'Attention',
            'dismiss'   =>  'Dismiss',
            'create_variation_order'    =>  'Create Variation Order',
            'alert_message' => 'The Variation Order needs to be approved by :roles.',
            'roles' => [
                        'building_manager' => 'Building Manager',
                        'project_owner'    => 'Project Owner',
                        'sp_admin'         => 'Service Provider',
                        'admin'            => 'Project Owner',
                        'supervisor'       => 'Supervisor',
                        ],
            'request_id'    =>  'Request ID',
            'status'    =>  'Status',
            'date_submitted'    =>  'Date Submitted',
            'approved_by'   =>  'Approved By',
            'decision_comment'  =>  'Decision Comment',
            'changes_summary'   =>  'Changes Summary',
            'view'  =>  'View',
            'pending_approval'  =>  'Pending Approval',
            'approved'  =>  'Approved',
            'rejected'  =>  'Rejected',
            'cancelled'  =>  'Cancelled',
            'attachment_files'  =>  'Attachments / Files',
            'comments'  =>  'Comments',
            'no_data_found' =>  'No Data Found',
            'no_comments_found' =>  'No Comments Found',
            'cancel'    =>  'Cancel',
            'reject'    =>  'Reject',
            'approve'   =>  'Approve',
            'total_changes' =>  'Total Changes',
            'changed_add'   =>  'Changed/Add',
            'removed'   =>  'Removed',
            'about_to' => "You're about to",
            'variation_order'  =>  'Variation Order!',
            'rejected_reason'   =>  'Rejection Reason',
            'enter_rejected_reason' =>  'Enter Rejection Reason',
            'comment'   =>  'Comment',
            'optional'  =>  '(Optional)',
            'enter_comments'    =>  'Enter Comments',
            'yes_approve_it'    =>  'Yes, Approve It',
            'cancel_reason'     =>  'Cancel Reason',
            'enter_Cancel_reason'   =>  'Enter Cancel Reason',
            'not_authorized'    =>  'You are not authorized to perform this action.',
            'variation_success_message' => 'The status of the variation order has been updated successfully.',
            'variation_create_validation'   => 'You can’t submit a Variation Order while another one is pending.',
            'variation_order_history'   => 'Variation Order History',
        ],
        'validation' => [
            'required_field' => 'The field is required',
            'region_required' => 'Region is required.',
            'region_valid' => 'Region must be a valid selection.',
            'city_required' => 'City selection is required.',
            'property_required' => 'At least one property must be selected.',
            'priority_required' => 'Priority is required for each row',
            'service_time_required' => 'Service time value is required.',
            'service_time_numeric' => 'Service time must be a numeric value.',
            'service_time_unit_required' => 'Service time unit is required.',
            'response_time_required' => 'Response time value is required.',
            'response_time_numeric' => 'Response time must be a numeric value.',
            'response_time_unit_required' => 'Response time unit is required.',
            'contract_name' => [
                'required' => 'The contract name is required.',
                'string' => 'The contract name must be a string.',
                'max' => 'The contract name may not be greater than 255 characters.',
                'unique' => 'The contract name has already been taken.',
            ],
    
            'amount' => [
                'required' => 'The amount is required.',
                'numeric' => 'The amount must be a number.',
                'min' => 'The amount must be at least 1.',
            ],
    
            'interval' => [
                'required' => 'The interval is required.',
                'in' => 'The selected interval is invalid.',
            ],
    
            'start_date' => [
                'required' => 'The start date is required.',
                'date' => 'The start date must be a valid date.',
            ],
    
            'end_date' => [
                'required' => 'The end date is required.',
                'date' => 'The end date must be a valid date.',
                'after' => 'The end date must be after the start date.',
            ],
    
            'months' => [
                'required' => 'You must specify at least one month.',
                'array' => 'Months must be an array.',
                'min' => 'At least one month must be selected.',
    
                'month_required' => 'Each month entry must include a month name.',
                'month_string' => 'Month name must be a string.',
    
                'amount_required' => 'Each month entry must include an amount.',
                'amount_numeric' => 'Amount must be a number.',
                'amount_min' => 'Amount must be at least 0.',
            ],
            'region_id' => [
                'required' => 'Region is required.',
                'array' => 'Region must be a valid selection.',
            ],
            'selected_cities' => [
                'required' => 'City selection is required.',
            ],
            'selected_properties' => [
                'required' => 'At least one property must be selected.',
            ],
            'slaRows' => [
                'slaRows_required' => 'Please add at least one SLA.',
                'slaRows_min' => 'Please add at least one SLA.',
                'priority_id_required' => 'Priority is required for each row.',
                'service_window_input_required' =>  'Service window value is required.',
                'service_window_input_numeric' =>  'Service window must be a numeric value.',
                'service_window_select_required' => 'Service window unit is required.',
                'response_time_input_required'  => 'Response time value is required.',
                'response_time_input_numeric'  => 'Response time must be a numeric value.',
                'response_time_select_required' =>  'Response time unit is required.',
            ],
            'servicesRows' => [
                'servicesRows_required' => 'Please add at least one service.',
                'servicesRows_min' => 'You must add at least one service.',
                'service_id_required' => 'Service is required for each row.',
                'service_id_numeric' => 'Service must be a valid number.',
                'price_required' => 'Price is required for each row.',
                'price_numeric' => 'Price must be a valid number.',
                'price_min' => 'Price must be at least 0.',
            ],
            'sideFeatures' => [
                'selectedTenantCategories_required' => 'Please select at least one tenant service category.',
                'selectedTenantCategories_array'    => 'Tenant service categories must be a valid list.',
                'selectedSmartAssignCategories_required' => 'Please select at least one smart assign service category.',
                'selectedSmartAssignCategories_array'    => 'Smart assign categories must be a valid list.',
            ],
        
        ],

    ];