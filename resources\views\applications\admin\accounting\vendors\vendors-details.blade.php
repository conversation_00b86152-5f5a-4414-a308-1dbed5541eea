@extends('layouts.app')
@section('styles')
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Manage vendors
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Manage vendors</a>
                    </li>
                </ul>
            </div>
        </div>


        <ul class="nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id="pills-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link rounded active" href="#"> Details </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link rounded text_black" href="#"> Bill </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link rounded text_black" href="#"> Purchase </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link rounded text_black" href="#"> Payment </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link rounded text_black" href="#"> Project </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link rounded text_black" href="#"> Statement </a>
            </li>
        </ul>



        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
               <div class="dropdown">
                    <button class="btn btn-white btn-default text-center svg-20 px-2 filter-button dropdown-toggle h-100" data-toggle="dropdown" aria-expanded="true">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="sort"></i> 
                        <div class="filter-click">
                            <span class="mr-3">Filter By</span><span class="close-filter mr-0"><i class="iconsax mr-0" icon-name="x-circle"></i></span>
                        </div>
                    </button>
    <div
        class="dropdown-menu market-dropdown filter-dropdown px-3 text-osool"
        aria-labelledby="dropdownMenuButton"
        data-auto-close="false"
        x-placement="bottom-start">
        <h5 class="fs-14 fw-500 text-osool">Filter by</h5>
        <div class="dropdown-divider my-2"></div>
            <form class="fs-14">
              <div class="form-group">
                <label for="">Start Date</label>
                <div class="position-relative">
                <input type="text" class="form-control datepicker" id="" aria-describedby="emailHelp" placeholder="Enter Start Date">
                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                </div>
                <!-- <small id="emailHelp" class="form-text text-muted">We'll never share your email with anyone else.</small> -->
              </div>
              <div class="form-group">
                <label for="">End Date</label>
                <div class="position-relative">
                <input type="text" class="form-control datepicker" id="" placeholder="Enter End Date">
                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                </div>
              </div>
              <div class="d-flex gap-10 mt-4 justify-content-end">
                <button type="submit" class="btn bg-warning btn-sm text-white radius-xl">Reset</button>
                <button type="submit" class="btn bg-new-primary btn-sm text-white radius-xl">Submit</button>
              </div>
            </form>
    </div>
</div>
                    <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="folder-add"></i> 
                    </button>

                    <button class="btn btn-white btn-default text-center svg-20 wh-45" wire:click="switchView('cards')">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="layout-3"></i>
                    </button>

                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false"><i class="las la-plus fs-16"></i>Create</button>

            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>


<div class="">
    <div class="col-lg-12 px-0">
    <div class="row mb-3">
        <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">Vendor Info</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Name</span></td>
                            <td>Abdul Rehman</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Email </span></td>
                            <td><EMAIL></td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Phone </span></td>
                            <td>+966-909876543</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">Billing Address</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Address</span></td>
                            <td>Riyad</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing City </span></td>
                            <td>riyad</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Zip Code </span></td>
                            <td>101</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Country </span></td>
                            <td>Saudi Arabia</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Contact </span></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-4">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">Shipping Address</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Address</span></td>
                            <td>Riyad</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing City </span></td>
                            <td>riyad</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Zip Code </span></td>
                            <td>101</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Country </span></td>
                            <td>Saudi Arabia</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Contact </span></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

    
        <div class="card mb-3" data-select2-id="108">
            <div class="card-header d-flex justify-content-start gap-5">
                <h6>Company Info</h6>
            </div>
            <div class="card-body" data-select2-id="107">
                <div class="row">
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Vendor ID</label>
                        <p>20050 W 12 Mile</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Date of Creation</label>
                        <p>10,0000</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Balance</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <label class="fw-600 text-dark">Overdue</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Total Sum of Bills</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Quantity of Bills</label>
                        <p>3</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Average Sales</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    
</div>



</div>

           </div>
        </div>


<div class="modal fade" id="delete-vendor" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-sm" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteUserModalLabel">Delete Vendor</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="{{ __('user_management_module.modal.close') }}">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center">
                                     <i class="iconsax icon text-loss fs-60" icon-name="warning-triangle"></i>
                                    <p class="mt-4">Are you sure you want to delete <br> the vendor <strong id="deleteUserName"> Abdul Rehman ?</strong></p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                <form id="deleteUserForm"  method="POST">
                                    <button type="submit" class="btn btn-danger">Yes, Delete It</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>




@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
    $(document).ready(function() {
      // Toggle active class and manage dropdown on button click
      $('.filter-button').on('click', function (e) {
        e.stopPropagation(); // Prevent the click from bubbling up to the document
        $(this).toggleClass('active'); // Toggle the 'active' class
        $(this).closest('.dropdown').find(".dropdown-menu").toggleClass('show'); // Toggle the Bootstrap dropdown
      });

      // Remove active class and close dropdown when clicking outside
      $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length) {
          // If the click is outside the dropdown, remove the 'active' class and close the dropdown
          $('.filter-button').removeClass('active');
          $('.dropdown-menu').removeClass('show');
          $('.filter-button').attr('aria-expanded', 'false');
        }
      });

      // Prevent dropdown from closing when clicking inside the dropdown menu
      $('.dropdown-menu').on('click', function (e) {
        e.stopPropagation();
      });
    });
</script>
@endsection