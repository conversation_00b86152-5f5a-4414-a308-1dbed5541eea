@extends('layouts.app')
@section('content')
    @switch($type)
        @case('all')
            <livewire:work-orders.list-all :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns" :workersList="$workersList"
                :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders" :serviceprovidersList="$serviceprovidersList" :checkCreateButton="$checkCreateButton"
                :buildingManagersList="$buildingManagersList" />
        @break

        @case('open')
            <livewire:work-orders.list-open :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns"
                :workersList="$workersList" :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders" :serviceprovidersList="$serviceprovidersList"
                :checkCreateButton="$checkCreateButton" :buildingManagersList="$buildingManagersList" />
        @break

        @case('in-progress')
            <livewire:work-orders.list-in-progress :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns"
                :workersList="$workersList" :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders" :serviceprovidersList="$serviceprovidersList"
                :checkCreateButton="$checkCreateButton" :buildingManagersList="$buildingManagersList" />
        @break

        @case('spare-parts')
            <livewire:work-orders.list-spare-parts :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns"
                :workersList="$workersList" :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders" :serviceprovidersList="$serviceprovidersList"
                :checkCreateButton="$checkCreateButton" :buildingManagersList="$buildingManagersList" />
        @break

        @case('under-evaluation')
            <livewire:work-orders.list-under-evaluation :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns"
                :workersList="$workersList" :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders" :serviceprovidersList="$serviceprovidersList"
                :checkCreateButton="$checkCreateButton" :buildingManagersList="$buildingManagersList" />
        @break

        @case('closed')
            <livewire:work-orders.list-closed :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns"
                :workersList="$workersList" :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders" :serviceprovidersList="$serviceprovidersList"
                :checkCreateButton="$checkCreateButton" :buildingManagersList="$buildingManagersList" />
        @break

        @case('pending')
            <livewire:work-orders.list-pending :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns"
                :workersList="$workersList" :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders" :serviceprovidersList="$serviceprovidersList"
                :checkCreateButton="$checkCreateButton" :buildingManagersList="$buildingManagersList" />
        @break

        @case('request')
            <livewire:work-orders.list-request :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns"
                :workersList="$workersList" :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders" :serviceprovidersList="$serviceprovidersList"
                :checkCreateButton="$checkCreateButton" :buildingManagersList="$buildingManagersList" />
        @break

        @default
            <livewire:work-orders.list-all :showProviderFilter="$showProviderFilter" :propertiesList="$propertiesList" :servicesList="$servicesList" :hiddenColumns="$hiddenColumns"
                :workersList="$workersList" :assets="$assets" :supervisorList="$supervisorList" :decryptedServiceProviderId="$decryptedServiceProviderId" :countServiceProviders="$countServiceProviders"
                :serviceprovidersList="$serviceprovidersList" :checkCreateButton="$checkCreateButton" :buildingManagersList="$buildingManagersList" />
        @break
    @endswitch
@endsection
@section('scripts')
    <style>
        #nav-proerties,
        #nav-services,
        #nav-workers,
        #nav-supervisor,
        #nav-bme,
        #nav-asset-number {
            position: relative;
            display: inline-flex;
            align-items: center;
        }

        .filter-indicator {
            width: 10px;
            height: 10px;
            background-color: #28a745;
            /* green */
            border-radius: 50%;
            margin-left: 8px;
            position: relative;
        }

        .filter-indicator::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #28a745;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
            z-index: -1;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }

            70% {
                transform: scale(2.5);
                opacity: 0;
            }

            100% {
                transform: scale(2.5);
                opacity: 0;
            }
        }
    </style>

    <script src="{{ asset('new_theme/js/functions.js') }}"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
    // Main checkbox group handler
    function setupCheckboxGroup(config) {
        const { 
            allCheckboxId, 
            checkboxClass, 
            clearLinkId, 
            navIndicatorId, 
            filterIndicatorId 
        } = config;

        const selectAllCheckbox = document.getElementById(allCheckboxId);
        const checkboxes = document.querySelectorAll(`.${checkboxClass}`);
        const resetLink = document.getElementById(clearLinkId);

        // Common methods
        const checkboxGroup = {
            uncheckAll() {
                checkboxes.forEach(cb => cb.checked = false);
            },

            checkSelection() {
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                const noneChecked = Array.from(checkboxes).every(cb => !cb.checked);

                if (allChecked) {
                    selectAllCheckbox.checked = true;
                    this.uncheckAll();
                } else if (noneChecked) {
                    selectAllCheckbox.checked = true;
                }
            },

            updateClearLink() {
                const clearLink = document.getElementById(clearLinkId);
                if (!clearLink) return;

                const allChecked = selectAllCheckbox.checked;
                const anyChecked = Array.from(checkboxes).some(cb => cb.checked);
                clearLink.style.display = (allChecked && !anyChecked) ? 'none' : 'block';
            },

            handleAllCheckboxClick(e) {
                if (selectAllCheckbox.checked) {
                    this.uncheckAll();
                    this.updateClearLink();
                    updateFilterIndicator(navIndicatorId, filterIndicatorId);
                } else {
                    e.preventDefault();
                }
            },

            handleIndividualChange() {
                if (selectAllCheckbox.checked) {
                    selectAllCheckbox.checked = false;
                }
                this.checkSelection();
                this.updateClearLink();
                updateFilterIndicator(navIndicatorId, filterIndicatorId);
            },

            handleResetClick(e) {
                e.preventDefault();
                selectAllCheckbox.checked = true;
                this.uncheckAll();
                this.updateClearLink();
                updateFilterIndicator(navIndicatorId, filterIndicatorId);
            },

            init() {
                // Initial setup
                this.updateClearLink();

                // Event listeners
                selectAllCheckbox.addEventListener('click', (e) => 
                    this.handleAllCheckboxClick(e));

                checkboxes.forEach(cb => 
                    cb.addEventListener("change", () => this.handleIndividualChange()));

                if (resetLink) {
                    resetLink.addEventListener("click", (e) => this.handleResetClick(e));
                }
            }
        };

        checkboxGroup.init();
        return checkboxGroup;
    }

    // Initialize all checkbox groups
    const groups = [
        {
            allCheckboxId: 'all_properties',
            checkboxClass: 'property',
            clearLinkId: 'clear-properites',
            navIndicatorId: 'nav-proerties',
            filterIndicatorId: 'all_properties'
        },
        {
            allCheckboxId: 'all_bme',
            checkboxClass: 'bme',
            clearLinkId: 'clear-bme',
            navIndicatorId: 'nav-bme',
            filterIndicatorId: 'all_bme'
        },
        {
            allCheckboxId: 'assets-ap',
            checkboxClass: 'as_num',
            clearLinkId: 'clear-assets',
            navIndicatorId: 'nav-asset-number',
            filterIndicatorId: 'assets-ap'
        },
        {
            allCheckboxId: 'service_all',
            checkboxClass: 'service',
            clearLinkId: 'clear-services',
            navIndicatorId: 'nav-services',
            filterIndicatorId: 'service_all'
        },
        {
            allCheckboxId: 'all_workers',
            checkboxClass: 'workers',
            clearLinkId: 'clear-workers',
            navIndicatorId: 'nav-workers',
            filterIndicatorId: 'all_workers'
        },
        {
            allCheckboxId: 'all_supervisors',
            checkboxClass: 'supervisor',
            clearLinkId: 'clear-supervisors',
            navIndicatorId: 'nav-supervisor',
            filterIndicatorId: 'all_supervisors'
        }
    ];

    // Initialize all groups
    groups.forEach(config => setupCheckboxGroup(config));
});
        </script>

    <script>
        function updateFilterIndicator(navLinkEl, allCheckboxEl) {
            const navLink = document.getElementById(navLinkEl);
            const allCheckbox = document.getElementById(allCheckboxEl);
            const existingIndicator = navLink.querySelector('.filter-indicator');

            if (!allCheckbox.checked) {
                if (!existingIndicator) {
                    const indicator = document.createElement('span');
                    indicator.className = 'filter-indicator';
                    navLink.appendChild(indicator);
                }
            } else {
                if (existingIndicator) {
                    navLink.removeChild(existingIndicator);
                }
            }
        }
    </script>

    <script>
        document.addEventListener('feather', (event) => {
            feather.replace();
        });

        $('.search_sp_filter').on('input', function() {
            searchServiceProviderList(this);
        });

         $('#check-all-sp-list').on('click', function() {
             selectServiceProvider(this);
         });

         $('.sp_list_check').on('click', function() {
            selectSingleServiceProvider();
         });

        $('.sortby_filter').on('click', function() {
            filterBySortType(this);
        });

        document.addEventListener('result', (event) => {
            hideModal('#export-modal');
            event.detail === 1 ? openSuccessModalForExport() : openFailedModalForExport();
        });

        $('.apply_multiple_sp').on('click', function() {
            filterByServiceProvider();
        });

        $("#ap-tab2").on("click", ".ratings", function() {
            filterByRatings(this);
        });

        $('#status-tab a').on('click', function() {
            filterByStatus(this);
        });

        $('#tab-view-by').on('click', '.pass_fail', function() {
            filterByViewBy(this);
        });

        $('#ap-tab').on('click', '.type', function() {
            filterByType(this);
        });

        // =====================================================

        $('#searchbme').on('input', function() {
            searchbmeList(this);
        });

        $('#searchbar').on('input', function() {
            searchAssetNumbersList(this);
        });

        $('#searchServices').on('input', function() {
            searchServicesList(this);
        });

        $('#searchWorkers').on('input', function() {
            searchWorkersList(this);
        });

        $('#searchSupervisor').on('input', function() {
            searchSupervisorsList(this);
        });

        $('#searchProperties').on('input', function() {
            searchPropertiesList(this);
        });

        $('#searchbme').on('input', function() {
            searchbmeList(this);
        });

        // $('#all_properties').on('click', function() {
        //     selectAllProperties(this);
        // });

        // $('#all_bme').on('click', function() {
        //     selectaallbme(this);
        // });

        $(".complex > div > .property").on("change", function() {
            let isChecked = $(this).is(":checked");
            $(this).closest("li").find("ul .property").prop("checked", isChecked);
        });

        // $('.property').on('click', function() {
        //     selectSingleProperties();
        // });

        // $('#clear-properites').on('click', function() {
        //     clearProperties();
        // });

        // $('#clear-bme').on('click', function() {
        //     clearbme();
        // });

        $('.apply-filters').on('click', function() {
            filterByAll();
        });

        $('.reset-filters').on('click', function() {
            resetAllFilters();
        });
    </script>
@endsection
