<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title></title>

<style>
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap');

/* *,p,h1,h2,h3,h4,h5,h6,div,table,td,th,tr,a,span { font-family: 'cairo'; } */ */

.inter-font {
  font-family: "Cairo", sans-serif;
  font-optical-sizing: auto;
  font-weight: normal;
  font-style: normal;
}

/* *,p,h1,h2,h3,h4,h5,h6,div,table,td,th,tr,a,span { font-family: 'cairo'; } */ */

* {
  font-family: "Cairo", sans-serif;
  font-style: normal;
  font-weight: normal;
}

@page {
    margin: 0;
}
*{
    color: #111;
}
h3{
  line-height: 20px;
}
h1, h1 b, h1 b span ,h1 div{
  line-height: 20px;
}

div {
  vertical-align: top;

}

  .contenedor{
    background-repeat: no-repeat;
    background-position: 0 0;
  }
  .background{
    position: fixed;
    top: 0;
    left: 0;
    width: 595.276pt;
    height: 841.89pt;
    z-index: 1;
  }
  .background img{
    width: 100%;
    height: auto;
  }
  .cuerpo{
    max-width: 484.725pt;
    max-height: 635.275pt;
    z-index: 10;
  }
  .low-lh td{
    border-bottom: 1px dotted rgba(0,0,0,0.04);
    line-height: 10px;
  }
  table td{
    line-height: 20px;
  }
  #duracion, #completado{ display: none; }
  .pagebreak { page-break-before: always; } /* page-break-after works, as well */

  /*****************New Design   Civil Defence PDF************************/
  :root{
    --osool-blue:#2a2a69;
  }
  body{
    font-family: "Cairo", sans-serif;
    color:#333333;
    margin:0;
    background-color: #f5f5f5;
  }
  p,a,div{
    font-family: "Cairo", sans-serif;
  }
  .d-flex{
     display: -webkit-box; /* wkhtmltopdf uses this one */
     display: flex;
  }
  .d-block{
    display: block;
  }
  .justify-content-between{
    justify-content: space-between;
  }
  .align-items-center{
    align-items: center;
  }
  .align-items-top{
    align-items: -webkit-start;
     align-items: start;
  }
    .justify-content-center{
    justify-content: center;
  }
  .pdf-header{
    border-bottom: 5px solid #ffffff;
    padding-bottom: 1rem;
  }
  .radius-xl {
    border-radius: 10px;
  }
  .bg-white{
    background: #ffffff;
  }
  .status-icon {
    border-radius: 12px;
    min-width: 40px;
    }
  .status-section{
    width: 40%;
  }
  .details-section{
    width: calc(60% - 20px);
  }
  .wo-details{
    margin-top: 2rem;
  }
  .wh-60 {
    width: 60px;
    height: 60px;
}

  .wh-50 {
    width: 50px;
    height: 50px;
    min-width: 50px;
}
.wh-40 {
    width: 40px;
    height: 40px;
    min-width: 40px;
}
.w-100{
  width: 100%;
}
.row{
  display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -10px;
    margin-left: -10px;
}
  .column{
    padding:0 10px;
  }
.rounded-pill{
  border-radius: 100px;
}
.bg-opacity-success {
    background: #bbf6e5;
    color:#20c997;
}
.p-2{
  padding:20px;
}
.p-1{
  padding:10px;
}
.status-text{
    padding: 5px 12px;
    font-size: 12px;
    line-height: 1.25;
    font-weight: 500;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 5px;
}
.text-light {
    color: #9299b8 !important;
}
  .text-right{
    text-align: left;
  }
  .flex-fill {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
}
.card-header h4{
  margin-top: .5rem;
  border-bottom: 1px solid #bfc2d1;
  padding-bottom: 1rem;
  font-size: .8rem;

}
table{
  border-collapse: collapse;
}
table td table td{
  font-size: 14px;
  padding:.9rem .5rem;
  vertical-align: top;
  text-align: right;
}
table th{
  padding:.3rem .5rem;
}
.details-section table tr td:first-child{
  border-right: 1px solid #bfc2d1;
}
.details-section table td{
  line-height: 5px;
}
.text-blue{
  color:#6063EA;
}
.lh-2{
  line-height: 20px;
}
.lh-0{
  line-height: 0px;
}
.lh-1{
  line-height: 10px;
}
.lh-p5{
  line-height: 5px;
}
.fs-12{
  font-size: 12px;
}
.fs-13{
  font-size: 13px;
}
.fs-14{
  font-size: 14px;
}
.fs-16{
  font-size: 16px;
}
.fs-18{
  font-size: 18px;
}
.fs-20{
  font-size: 20px;
}
.wh-25 {
    width: 25px;
    height: 25px;
}
.wh-20 {
    width: 20px;
    height: 20px;
}
.w-50{
  width: 50%;
}
.rounded{
  border-radius: 5px;
}
.wo-img img{
  max-height: 70px;
  margin-left: 10px;
  display: inline-block;
}
.d-grid{
  display: grid;
}
.wo-img{
  grid-template-columns: auto auto;
}
.table-data td{vertical-align: middle; padding:.5rem; text-align: center;}
.table-data td + td{
  text-align: right;
}
.table-data td img{
  margin-top: 10px;
}
.text-center{
  text-align: center;
}
.mt-15{
  margin-top:15px;
}
.mr-15{
  margin-left: 15px;
}
.mr-10{
  margin-left: 10px;
}
.mb-0{
  margin-bottom: 0;
}
.mr-0{
  margin-left: 0 !important;
}
.mt-0{
  margin-top: 0px !important;
}
.mt-05{
  margin-top: .5rem;
}
.mt-1{
  margin-top: 1rem !important;
}
.mb-05{
  margin-bottom: .5rem;
}
.mb-1{
  margin-bottom: 1rem;
}
.mt-2{
  margin-top: 2rem !important;
}
.mt-3{
  margin-top: 3rem !important;
}
.pb-0{
  padding-bottom: 0 !important;
}
.p-0{
  padding:0px !important;
}
.border-bottom{
  border-bottom: 1px solid #9399B5;
}
.pb-1{
  padding-bottom: 1rem;
}

.pb-2{
  padding-bottom: 2rem;
}
.footer{
  background: #2A2A69;
  color:#ffffff;
  padding:1.5rem 3rem;
  border-radius: 30px 30px 0px 0px;
  position: fixed;
  width: 88%;
  bottom: 0;
}
.footer p, .footer span{
  margin: 0;
  color:#ffffff;
  line-height: 10px;
}
.pl-1{
  padding-left: 1rem;
}
.multi-li{
  margin:0;
}
.multi-li li{
  padding-left: 20px;
  position: relative;
  list-style-type: none;
}
.check-mark{
  width: 20px;
  height: 20px;
  border:1px solid #2a2a69;
  text-align: center;
  display: inline-block;
  position: absolute;
  left:0;
  top:3px;
}
.check-mark img{
  /*position: absolute;
  left: 0px;*/
  display: none;
}
.h-auto{
  height: auto !important;
}
.bg-osool{
  background-color: var(--osool-blue);
}
.color-osool{
  color:#2a2a69;
}
.border-bottom-5{
  border-bottom: 5px solid #2a2a69;
}
.main-heading{
  font-size: 22px;
}
.blue-th th{
  background: #2a2a69;
  color: #fff;
  text-align: right;
}
.border-blue{
  border:1px solid #2a2a69;
}
.num-span{
  line-height: 0px;
  display: block;
  width: auto;
  float: left;
  padding:1.2rem 1.5rem .8rem 1.5rem;
}
.position-relative{
  position: relative;
}
.checkmark label{
  margin-left: 1.7rem;
}
.check-mark.active:before{
  content: "";
  height: 2px;
  width: 5px;
  background: #2a2a69;
  position: absolute;
  left: 4px;
    top: 9px;
  transform: rotate(45deg);
}
.check-mark.active:after{
  content: "";
  height: 2px;
  width: 10px;
  background: #2a2a69;
  position: absolute;
  left: 6px;
    top: 8px;
  border-radius: 5px;
  transform: rotate(-45deg);
}
table.border-blue td{
  border:1px solid #2a2a69;
  padding: .2rem .5rem;
}
.image-section{
    max-width: 350px;
}
.image-section .image-block-1{
  width: 120px;
  display: inline-block;
    margin-left: 5px;
    margin-bottom: 10px;
}
.image-section .image-block{
    display:inline-block;
    padding-bottom: 0;   
/*    page-break-before: avoid; */
    text-align: center;
    height: 120px;
    width: 120px;
    background-size: cover; 
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    background-repeat: no-repeat;
    margin-bottom: 0px;
   }
   .image-section .image-block img{
   position: absolute;
    top: 0;
    left: 0;
    max-height: 120px;
    min-width: 100%;
   }
   .image-section img{
    border-radius: 5px;
   }
  .image-section .image-block.pdf{
    border:1px solid #ccc;
    height: 120px;
    width: 120px;
    background-size: auto;
    background-position: center;
    background-repeat: no-repeat;
   }
   .image-section .image-block.pdf img{
    margin-left: calc(50% - 25px);
    position: relative;
    margin-top: 35px;
    min-width: 0px;
    width: 50px;
    height: 50px;
    border-radius: 0px;
}

/**************New Design Css******************/
table.tb-0 td{
    border:none;
  }
  table.tp-0 td{
    padding: 0;
  }
  table.tp-5 td{
    padding: 5px;
  }
  table.tp-10 td{
    padding: 10px;
  }
.nowrap{
  white-space: nowrap;
}
.text-osool{
  color:#152B70;
}
.text-white{
  color:#ffffff;
}
.p-1{
  padding:.5rem;
}
.pt-1{
  padding-top:.5rem;
  padding-bottom:.5rem;
}
.p-2{
  padding:1rem;
}
.px-2{
  padding-left:1rem;
  padding-right:1rem;
}
.px-3{
  padding-right:3rem;
  padding-left:3rem;
}
.br-t{
  border-radius: 10px 10px 0 0;
}
.bg-osool{
  background-color: #2a2a69;
}
.main-table{
  border-radius: 10px;
  font-size: 14px;
  max-width: 800px;
}
.main-table th{
  padding:1.5rem 1rem;
  color:#1E1E1E;
  font-weight: 700;
  background: #F1F2F6;
  text-align: right;
  border-bottom: 1px solid #F1F2F6;
  border-collapse: collapse;
   word-wrap: break-word;
}
.main-table tr th:not(.br-0):last-child{
  border-top-left-radius: 10px;
}
.main-table tr th:not(.br-0):first-child{
  border-top-right-radius:10px;
}
.main-table tbody tr td:first-child, .main-table tr th:first-child{
  border-right: 1px solid #F1F2F6;
}
.main-table tbody tr td:last-child,  .main-table tr th:last-child{
  border-left: 1px solid #F1F2F6;
}
.main-table tbody tr td table tbody tr td:first-child{
  border-right: none;
}
.main-table tbody tr td table tbody tr td:last-child{
  
  border-left: none;
}
/*.main-table thead tr th:first-child{
  border-left: 1px solid #F1F2F6;
}
.main-table thead tr th:last-child{
  border-right: 1px solid #F1F2F6;
}*/
.main-table tr td{
  padding:1.3rem 1rem;
  color:#2a2a69;
  border-bottom: 1px solid #F1F2F6;
  border-collapse: collapse;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.main-table tr td table tr:last-child td{
  border-bottom: none;
}
 .main-table tr td span{
    color:#2a2a69;
    word-wrap: break-word;
  word-break: break-all;
  }
.main-table > tr td:last-child{
  border-right: 2px solid #F1F2F6;
}
.main-table > tr td:first-child{
  border-left: 2px solid #F1F2F6;
}
.main-table > tr:last-child td{
  border-bottom: 2px solid #F1F2F6;
}
.py-10px{
  padding-top:10px;
  padding-bottom:10px;
}
.py-5px{
  padding-top:5px;
  padding-bottom:5px;
}
.py-2px{
  padding-top:2px;
  padding-bottom:2px;
}
.py-1px{
  padding-top:1px;
  padding-bottom:1px;
}
.py-3px{
  padding-top:3px;
  padding-bottom:3px;
}
.py-4px{
  padding-top:4px;
  padding-bottom:4px;
}
.fw-100{
  font-weight: 100;
}
.fw-200{
  font-weight: 200;
}
.fw-300{
  font-weight: 300;
}
.fw-400{
  font-weight: 400;
}
.fw-500{
  font-weight: 500;
}
.fw-600{
  font-weight: 600;
}
.fw-700{
  font-weight: 700;
}
.page-footer{
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding:10px 50px;
  background: #fff;
  border-radius: 10px 10px 0 0;
  margin-top: 10px;
}
 @page {
        header: html_MyCustomHeader; /* display <htmlpageheader name="MyCustomHeader"> on all pages */
        footer: html_MyCustomFooter; /* display <htmlpagefooter name="MyCustomFooter"> on all pages */
    }
}
}
.main-table.blue-head th, .main-table.blue-head td{
  border-left:none;
  border-right:none;
}
.main-table.blue-head th, .main-table.blue-head tfoot th{
  background: #2a2a69;
  color:#ffffff;
}
.main-table.blue-head thead th{
  padding-top:1.3rem;
  padding-bottom:1.3rem;
}
.main-table.blue-head tfoot th{
  padding-top:1rem;
  padding-bottom:1rem;
}
.mb-10{
  margin-bottom: 10px;
}
table.td-p-0 td{
  padding:0 !important;
}
table.main-table table.no-border td{
    border:none !important;
  }
  .d-inline-block{
    display: inline-block;
  }
.main-table tr:last-child td.border-bottom-none{
  border-bottom: none;
}
.border-right-none{
  border-right:none !important;
}
.border-left-none{
  border-left:none !important;
}
.border-width-1{
  border-width: 1px !important;
}
.main-table td.border-right{
  border-left: 1px solid #F1F2F6 !important;
}
.main-table td.border-left{
  border-right: 1px solid #F1F2F6 !important;
}
.main-table td span.text-dark,.main-table td div.text-dark{
  color:#1E1E1E;
}
.d-none{
  display: none;
}
.top-table tr td{
  padding: 5px;
}
.main-table td.pl-0{
  padding-right: 0;
}
.main-table td.pr-0{
  padding-left: 0;
}
    .bar-chart {
      align-items: flex-end;
      height: 300px;
      width: 90%;
      max-width: 700px;
      border: 2px solid #ddd;
      background-color: #f0f0f0;
      padding: 10px;
      box-sizing: border-box;
    }
    .bar-chart div{
      vertical-align: bottom;
    }
    .bar {
        width: 14%;
        background-color: #4caf50;
        display: inline-block;
        text-align: center;
        position: relative;
        margin-left: 12%;
        background-color: #BBF6E5; border:1px solid #1ACB97
    }
    .bar.bar-last{
      margin-left: 0 !important;
    }

    .bar span {
      position: absolute;
      left: 0;
      width: 100%;
      text-align: center;
      font-weight: bold;
      color: #333;
    }

    /* Bar Heights - Data representation */
    .bar1 { height: 80%; }
    .bar2 { height: 60%; }
    .bar3 { height: 100%; }
    .bar4 { height: 40%; }
    .bar5 { height: 70%; }

    /* Different Colors for each Bar */
    /*.bar1 { background-color: #BBF6E5; border:1px solid #1ACB97 }
    .bar2 { background-color: #33b5ff; }
    .bar3 { background-color: #ffeb3b; }
    .bar4 { background-color: #ff6f61; }
    .bar5 { background-color: #4caf50; }*/

    /* Text below each bar */
    .readings {
      display: flex;
      justify-content: space-around;
      width: 100%;
      margin-top: 10px;
    }
    .reading {
      text-align: center;
      font-size: 14px;
      color: #333;
    }
    .bar-dot{
      height: 20px;
      width: 20px;
      border-radius: 50%;
      position: absolute;
      left: 10px;
      top: 0px;
      background: #000;
      z-index: 9;
    }
    .image-div{
      height: 80px;width: 80px;margin-bottom: 5px;border:1px solid #ddd; border-radius: 5px; 
    }
    .pie-chart{
      height: 300px;
      width: 300px;
      border-radius: 50%;
      background: conic-gradient(
        #ff5733 0% 30%,  /* Red: 30% */
        #33b5ff 30% 70%, /* Blue: 40% */
        #ffeb3b 70% 100% /* Yellow: 30% */
      );
      position: relative;
      margin:auto 0;
    }
    .pie-dot{
      height: 10px;
      width: 10px;
      border-radius: 50%;
      background: #000;
      display: inline-block;
      margin-left: 4px;
    }
    .main-table.pie-dot td{
      padding-bottom: 0;
      vertical-align: middle;
    }
    .main-table.pie-table{
      border:none;
    }
    .table-rounded{
      border-radius: 10px;overflow: hidden;
    }
    .top-table td{
      padding: 5px;
    }
    .container{
      margin:0 auto;width: 800px;
    }
    .powered-by{
    }
    .osool-logo-p{
      display: -webkit-box; /* wkhtmltopdf uses this one */
      display: flex;
      -webkit-box-pack: end; /* wkhtmltopdf uses this one */
      justify-content: end;
      -webkit-box-align: center; /* wkhtmltopdf uses this one */
      align-items: center;
      gap: 10px;
    }
    .reports-module{
        position: relative;
        height: 1370px; /* Match A4 height at 96 DPI */
        background: #F4F5F7;
        padding-top: 2rem;
        page-break-after: always;
    }

    .donut-chart {
      width: 200px;
      height: 200px;
      border-radius: 50%;
      background: conic-gradient(#4caf50 0% 75%, #ddd 75% 100%);
      position: relative;
    }

    .donut-chart::before {
      content: "";
      width: 120px;
      height: 120px;
      background-color: white;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .donut-label {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 1.5rem;
      font-weight: bold;
      color: #333;
    }
    .flex-sample{
        display: -webkit-box; /* wkhtmltopdf uses this one */
    display: flex;
    -webkit-box-pack: end; /* wkhtmltopdf uses this one */
    justify-content: end;
    -webkit-box-align: center; /* wkhtmltopdf uses this one */
    align-items: center;
    }
.color-secondary{
color: #FF69A5;
}
.color-success{
color: #20C997;
}
.color-warning{
color: #FA8B0C;
}
.color-danger{
color: #FF4D4F;
}
.color-dark{
color: #272B41;
}
.color-primary{
color: #5F63F2;
}
.color-info{
 color: #2C99FF;
}
.bg-opacity-dark {
    background: #DFDFE3;
}
.bg-opacity-danger {
    background: #FFE4E5;
}
.bg-opacity-primary {
    background: #E7E8FD;
}
.bg-opacity-warning{
    background: #FEEEDB;
}
.bg-opacity-info{
background:#e0f0ff;
}
.bg-opacity-secondary{
background:#ffe9f2;
}
.bar-chart-td{
        vertical-align:middle;
        text-align: center;
        min-height: 300px;
    }
    .bar-chart-td img{
        width: 100%;
        margin:0 auto;
        max-height: 250px;
    }
    /* .pie-chart-td{
        width: 50%;
    } */
    .pie-chart-td img{
        max-width: 90%;
        margin:0 auto;
    }
</style>

</head>

<body style="">
<section class="reports-module" style="page-break-after:always;">
<!-- <div class=""> -->
    <div class="container">
          <div class="text-center">
          @if(ImagesUploadHelper::base64EncodeImage($project_image_link) != "")
            @php
            $project_image_link = ImagesUploadHelper::base64EncodeImage($project_image_link);
            @endphp
            @endif
              <img src="{{ $project_image_link }}" class="fourth_img mb-05" style="max-width:150px; max-height:100px;" alt="">
          </div>
          <div class="mb-1">
          <table class="w-100 top-table">
              <tr>
                <td>
                  <div>
                    <h5 class="text-osool mb-0 mt-0">تقرير لمشروع</h5>
                    <h3 class="">{{$project_name}}</h3>
                    @if($assigned_to_users != "")
                      <h5 class="mb-0 mt-0"><span class="text-osool">معيّن إلى: </span> <span class="text-dark">{{$assigned_to_users}}</span></h5>
                    @endif
                  </div>
                </td>
                <td style="width:250px"></td>
                <td>
                    <div class="text-right">
                    <table class="tb-0 top-table">
                        <tr>
                          <td><span class="nowrap text-light">{{__('reports.labels.generated_at')}}</span></td>
                          <td><span class="nowrap text-osool fw-700">{{$generated_at}}</span></td>
                        </tr>
                        <tr>
                          <td><span class="nowrap text-light">من تاريخ</span></td>
                          <td><span class="nowrap text-osool fw-700">{{$start_date}}</span></td>
                        </tr>
                        <tr>
                          <td><span class="nowrap text-light">إلى تاريخ</span></td>
                          <td><span class="nowrap text-osool fw-700">  {{$end_date}} </span></td>
                        </tr>
                        <tr>
                          <td><span class="nowrap text-light">{{__('reports.labels.report_id')}}</span></td>
                          <td> <span class="nowrap text-osool fw-700">#{{ $report_no}}</span></td>
                        </tr>
                      </table>
                    </div>
                </td>
              </tr>
            </table>
          </div>
    </div>
    <div class="container">
    <div class="mb-5 table-rounded">
          <div class="table-header bg-osool text-white py-5px px-2 br-t">
            <h3 class="text-white lh-2 fw-700">تقرير</h3>
          </div>
          <div class="table-container p-2 bg-white">
          <div class="table-rounded">
            <table class="main-table table-border w-100">
              <thead>
                <tr>
                  <th>{{__('reports.labels.total_wo')}}</th>
                  <th>{{__('reports.labels.reactive')}}</th>
                  <th>{{__('reports.labels.preventive')}}</th>
                  <th>{{__('work_order.bread_crumbs.total')}}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>جميع أوامر العمل</td>
                  <td>{{$reactive_work_orders_count}}</td>
                  <td>{{$preventive_work_orders_count}}</td>
                  <td>{{$preventive_work_orders_count + $reactive_work_orders_count}}</td>
                </tr>
                <tr>
                  <td>{{__('work_order.bread_crumbs.open')}}</td>
                  <td>{{$final_work_order_counts_by_status['reactive_open']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_open']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_open']+$final_work_order_counts_by_status['reactive_open']}}</td>
                </tr>
                <tr>
                  <td>{{__('work_order.bread_crumbs.in_progress')}}</td>
                  <td>{{$final_work_order_counts_by_status['reactive_in_progress']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_in_progress']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_in_progress']+$final_work_order_counts_by_status['reactive_in_progress']}}</td>
                </tr>
                <tr>
                  <td>{{__('work_order.bread_crumbs.re_open')}}</td>
                  <td>{{$final_work_order_counts_by_status['reactive_reopen']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_reopen']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_reopen']+$final_work_order_counts_by_status['reactive_reopen']}}</td>
                </tr>
                  <td>متوقف مؤقتًا</td>
                  <td>{{$final_work_order_counts_by_status['reactive_pause']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_pause']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_pause']+$final_work_order_counts_by_status['reactive_pause']}}</td>
                </tr>
                <tr>
                  <td>مغلق</td>
                  <td>{{$final_work_order_counts_by_status['reactive_closed']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_closed']}}</td>
                  <td>{{$final_work_order_counts_by_status['preventive_closed']+$final_work_order_counts_by_status['reactive_closed']}}</td>
                </tr>
                <tr>
                  <th colspan="3" class="br-0">أوامر العمل المغلقة</th>
                  <th colspan="" class="br-0">{{__('work_order.task_number_popup.result')}}</th>
                </tr>
                <tr>
                  <td colspan="3">تقييم أوامر العمل</td>
                  <td>
                    <span> {{$job_rating}}</span>/<span class="" lang="en">5</span>
                  </td>
                </tr>
                <tr>
                  <td colspan="3">نتيجة متوسط وقت التنفيذ</td>
                  <td>{{$wo_completed_on_time}}%</td>
                </tr>
                <tr>
                  <td colspan="3">نتيجة متوسط وقت الاستجابة</td>
                  <td>{{$response_time}}%</td>
                </tr>
              </tbody>
            </table>
          </div>
          </div>
        </div>
    </div>

    <div class="page-footer">
    <div class="container">
          <table class="no-border" style="width: 100%;">
            <tr>
              <td>
                <p class="" style="font-size: 1.2rem;">{{$user->name}}</p>
              </td>
              <td class="text-right">
                    <div class="osool-logo-p">
                        <div class="powered-by mr-15">{{__('reports.labels.powered_by')}}</div>
                        <div class=""><img src="{{ asset('img/OSOOL_logo_svg.svg') }}" style="width: 150px;"> </div>
                    </div>
              </td>
            </tr>
          </table>
        </div>
    </div>
</section>

@php
              $chunks_chart = array_chunk($merged_workorders_servicetype, 6, true);
              $loop_itr_chart = 0;
            @endphp
            @foreach ($chunks_chart as $index => $chunk)
<section class="reports-module" style="page-break-after:always;">
    <div class=" container">
    <div>
          <div class="text-center">
            <img src="{{ $project_image_link }}" style="max-width:150px; max-height:100px;" class="mb-05">
          </div>
    </div>
    <div class="mb-5">
          <table class="w-100 main-table">
          @php
              // Divide the data into chunks of two items
              $chunks = array_chunk($chunk, 2, true);
              
          @endphp
          @foreach ($chunks as $key => $pair)
            <tr>
              @foreach ($pair as $key => $val)
              <td class="{{ $loop->first ? 'pl-0' : 'pr-0' }}" style="width:50%;">
                <div class="table-container bg-white table-rounded">
                   <div class="table-header bg-osool text-white py-5px px-2 br-t">
                      <h3 class="text-white lh-2 fw-700 position-relative">{{$key}} 
                        <!-- <span class="bar-dot" style="background: #1ACB97;"></span> -->
                      </h3>
                    </div>
                <table class="main-table table-border w-100 blue-head">
                  <tbody>
                    <tr>
                      <td class="bar-chart-td">
                        <div class="bar-chart w-100">
                          <img src="https://quickchart.io/chart?bkg=white&c={ type: 'bar', data: { labels: ['{{__('work_order.bread_crumbs.open')}}', 'متوقف مؤقتًا', '{{__('work_order.bread_crumbs.in_progress')}}', '{{__('work_order.bread_crumbs.closed')}}'], datasets: [{ label: '{{$key}}', data: [{{$val['preventive_open']+$val['reactive_open']}}, {{$val['preventive_pause']+$val['reactive_pause']}}, {{$val['preventive_in_progress']+$val['reactive_in_progress']}}, {{$val['preventive_closed']+$val['reactive_closed']}}] }] }}">
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              </td>
              @endforeach

              @if (count($pair) < 2)
                <td style="width:50%;"></td>
              @endif
            </tr>
          @endforeach
          </table>
  </div>

    <div class="page-footer">
    <div class="container">
          <table class="no-border" style="width: 100%;">
            <tr>
              <td>
                <p class="" style="font-size: 1.2rem;">{{$user->name}}</p>
              </td>
              <td class="text-right">
                    <div class="osool-logo-p">
                        <div class="powered-by mr-15">{{__('reports.labels.powered_by')}}</div>
                        <div class=""><img src="{{ asset('img/OSOOL_logo_svg.svg') }}" style="width: 150px;"> </div>
                    </div>
              </td>
            </tr>
          </table>
    </div>
    </div>
    </div>
</section>
@php
    $loop_itr_chart++;
  @endphp
  @endforeach
@php
              $chunks = array_chunk($merged_workorders_servicetype, 15, true);
              $loop_itr = 0;
            @endphp
            
            @foreach ($chunks as $index => $chunk)
  <section class="reports-module" style="page-break-after:always;">
    <div class="container">
        <div>
              <div class="text-center mb-1">
                  <img src="{{ $project_image_link }}" style="max-width:150px; max-height:100px;" class="mb-05">
              </div>
        </div>
        <div class="mb-5">
          <div class="table-container bg-white table-rounded">
            <div>
            <table class="main-table table-border w-100 blue-head no-border">
              <thead>
                <tr>
                  <th>نوع الخدمة</th>
                  <th>مفتوح</th>
                  <th>توقف مؤقتا</th>
                  <th>قيد التنفيذ</th>
                  <th>مغلق</th>
                </tr>
              </thead>
              <tbody>
              @foreach ($chunk as $key => $val)
                <tr>
                  <td>{{$key}}</td>
                  <td>{{$val['preventive_open']+$val['reactive_open']}}</td>
                  <td>{{$val['preventive_pause']+$val['reactive_pause']}}</td>
                  <td>{{$val['preventive_in_progress']+$val['reactive_in_progress']}}</td>
                  <td>{{$val['preventive_closed']+$val['reactive_closed']}}</td>
                </tr>
                @endforeach
              </tbody>
              <tfoot>
                <tr>
                  <th>الإجمالي</th>
                  <th>{{$workorders_servicetype_total['total_open']}}</th>
                  <th>{{$workorders_servicetype_total['total_pause']}}</th>
                  <th>{{$workorders_servicetype_total['total_in_progress']}}</th>
                  <th>{{$workorders_servicetype_total['total_closed']}}</th>
                </tr>
              </tfoot>
            </table>
          </div>
          </div>
        </div>

          <div class="page-footer">
            <div class="container">
                <table class="no-border" style="width: 100%;">
                  <tr>
                    <td>
                      <p class="" style="font-size: 1.2rem;">{{$user->name}}</p>
                    </td>
                    <td class="text-right">
                        <div class="osool-logo-p">
                            <div class="powered-by mr-15">{{__('reports.labels.powered_by')}}</div>
                            <div class=""><img src="{{ asset('img/OSOOL_logo_svg.svg') }}" style="width: 150px;"> </div>
                        </div>
                    </td>
                  </tr>
                </table>
            </div>
          </div>
    </div>
  </section>
  @php
    $loop_itr++;
  @endphp
  @endforeach


  <section class="reports-module">
    <div class="container">
    <div style="margin:0 auto;width: 800px;" class="">
          <div class="text-center">
            
            <img src="{{ $project_image_link }}" style="max-width:150px; max-height:100px;" class="mb-05">
          </div>
    </div>
    <div class="mb-5">
      
      
          <table class="w-100 main-table">
            <tr>
              <td class="pl-0" style="width:50%;">
                <div class="table-container bg-white table-rounded">
                   <div class="table-header bg-osool text-white py-5px px-2 br-t">
                      <h3 class="text-white lh-2 fw-700 position-relative">تحليل</h3>
                    </div>
                <table class="main-table table-border w-100 blue-head" style="ver">
                  <tbody>
                    @php

                    $analysis_key = [];
                    $analysis_val = [];

                    foreach ($merged_workorders_servicetype as $key => $record) {
                        // Get keys as a comma-separated string
                        //$keys = implode(',', array_keys($key));
                        $analysis_key[] = "'" . $key .' ('.$record['percentage'].'%)'. "'";
                        
                        // Get total_reopen value from each record
                        $analysis_val[] = $record['percentage'] ?? null;
                    }

                    
                    //Chart Configuration

                    $piecharurl = "{ type: 'pie', data: { labels: [".implode(',',$analysis_key)."], datasets: [{ data: [".implode(',',$analysis_val)."] }] }, options: { legend: { display: true, position: 'right', align: 'center' },plugins: {datalabels: {display: false}} }}";

                    $encodedpieURL = 'https://quickchart.io/chart?bkg=white&c='.urlencode($piecharurl);
                      
                    @endphp
                    <tr>
                      <td style="vertical-align: middle;" class="pie-chart-td">
                        <img src="{{$encodedpieURL}}" alt="Pie chart">
                      </td>
                      <!-- <td>
                        <table class="main-table no-border w-100 pie-table">
                          <tr>
                            <td><span class="pie-dot" style="background:#1ACB97"></span> <span>Carpentry</span></td>
                            <td><span class="text-dark fs-16">60%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#2D99FF"></span> <span>Painting</span></td>
                            <td><span class="text-dark fs-16">50%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#FA8B0A"></span> <span>Electrical</span></td>
                            <td><span class="text-dark fs-16">40%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#7B57A8"></span> <span>Controls / Fire Alarm</span></td>
                            <td><span class="text-dark fs-16">30%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#B2F68E"></span> <span>HVAC</span></td>
                            <td><span class="text-dark fs-16">90%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#9B3121"></span> <span>Plumbing</span></td>
                            <td><span class="text-dark fs-16">60%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#1ACB97"></span> <span>Fire Fighting</span></td>
                            <td><span class="text-dark fs-16">70%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#979797"></span> <span>Landscape</span></td>
                            <td><span class="text-dark fs-16">10%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#DF9D50"></span> <span>Cleaning</span></td>
                            <td><span class="text-dark fs-16">60%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#73C23B"></span> <span>Celebration</span></td>
                            <td><span class="text-dark fs-16">50%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#91D3E8"></span> <span>Pest Control CLO</span></td>
                            <td><span class="text-dark fs-16">30%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#979797"></span> <span>Transfer Materials</span></td>
                            <td><span class="text-dark fs-16">20%</span></td>
                          </tr>
                          <tr>
                            <td><span class="pie-dot" style="background:#A277D8"></span> <span>Mason</span></td>
                            <td><span class="text-dark fs-16">40%</span></td>
                          </tr>
                        </table>
                      </td> -->
                    </tr>
                  </tbody>
                </table>
              </div>
              </td>
            </tr>
          </table>
      
  </div>

<div class="page-footer">
<div class="container">
      <table class="no-border" style="width: 100%;">
        <tr>
          <td>
            <p class="" style="font-size: 1.2rem;">{{$user->name}}</p>
          </td>
          <td class="text-right">
                    <div class="osool-logo-p">
                        <div class="powered-by mr-15">{{__('reports.labels.powered_by')}}</div>
                        <div class=""><img src="{{ asset('img/OSOOL_logo_svg.svg') }}" style="width: 150px;"> </div>
                    </div>
          </td>
        </tr>
      </table>
</div>
</div>
</div>
</section>


@if(in_array('reactive', $maintenance_type))

    @php $i = 1; @endphp
            @foreach($data_reactive as $wo)
            <section class="reports-module" style="page-break-after:always;">
            <div class="container">
              <div class="text-center">
                  <img src="{{ $project_image_link }}" class="fourth_img mb-05" style="max-width:150px; max-height:100px;" alt="">
              </div>
                        <div class="mb-5">
                            <table class="w-100 mt-2 mb-1">
                              <tr>
                                <td><h5 class="text-osool mb-0 mt-0 mr-10">نوع الخدمة</h5> <h4 class="mb-0 mt-0">{{$wo['asset_category']}}</h4></td>
                                <td class="text-right"><h5 class="text-osool mb-0 mt-0 mr-10">معرف أمر العمل</h5> <h4 class="mb-0 d-inline-block mt-0"># {{$wo['work_order_id']}}</h4></td>
                              </tr>
                            </table>
                          
                        <div class="table-container bg-white mt-15" style="border-radius: 10px;overflow:hidden;">
                            <div class="table-header bg-osool text-white py-5px px-2 br-t">
                              <table class="w-100">
                                <tr>
                                  <td><h3 class="text-white lh-2 fw-700">{{$i}}. {{$wo['description']}}</h3></td>
                                  <td class="text-right">
                                    @if($wo['status'] == __('work_order.bread_crumbs.open'))
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-success color-success fs-16">{{$wo['status']}}</span>
                                    @elseif($wo['status'] == __('work_order.bread_crumbs.in_progress'))
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-info color-info fs-16">{{$wo['status']}}</span>
                                    @elseif($wo['status'] == __('work_order.bread_crumbs.on_hold'))
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-warning color-warning fs-16">{{$wo['status']}}</span>
                                    @elseif($wo['status'] == __('work_order.bread_crumbs.closed'))
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-dark color-light fs-16">{{$wo['status']}}</span>
                                    @elseif($wo['status'] == __('work_order.bread_crumbs.deleted'))
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-danger color-danger fs-16">{{$wo['status']}}</span>
                                    @elseif($wo['status'] == __('work_order.bread_crumbs.re_open'))
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-primary color-primary fs-16">{{$wo['status']}}</span>
                                    @elseif($wo['status'] == __('work_order.bread_crumbs.scheduled'))
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-dark color-dark fs-16">{{$wo['status']}}</span>
                                    @elseif($wo['status'] == __('work_order.bread_crumbs.warrenty'))
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-secondary color-secondary fs-16">{{$wo['status']}}</span>
                                    @else
                                    <span class="d-inline-block py-10px px-2 rounded-pill bg-opacity-success color-success fs-16">{{$wo['status']}}</span>
                                    @endif
                                  </td>
                                </tr>
                              </table>
                            </div>
                            <div class="p-2">
                                  <table class="main-table text-dark w-100 tb-0 w-100">
                                  <tr>
                                              <td width="20%"><span class="nowrap d-inline-block mr-10 text-dark">اسم العقد</span></td>
                                              <td width="30%" class="border-right"><span class="text-osool fw-700 d-inline-block">{{$wo['contract_number']}}</span></td>
                                              <td width="20%"><span class="nowrap d-inline-block mr-10 text-dark">اسم المبنى</span></td>
                                              <td width="30%"><span class="text-osool fw-700 d-inline-block">{{$wo['building_name']}}</span></td>
                                            </tr>
                                            <tr>
                                              <td width="20%"><span class="nowrap d-inline-block mr-10 text-dark">نوع الصيانة</span></td>
                                              <td width="30%" class="border-right"><span class="text-osool fw-700 d-inline-block">تصحيحي</span></td>
                                              <td width="20%"><span class="nowrap d-inline-block mr-10 text-dark">المنطقة</span></td>
                                              <td width="30%"><span class="text-osool fw-700 d-inline-block">{{$wo['floor']}} </span></td>
                                            </tr>
                                            <tr>
                                              <td width="20%" class=""><span class="nowrap d-inline-block mr-10 text-dark">الأولوية</span></td>
                                              <td width="30%" class="border-right"><span class="text-osool fw-700 d-inline-block"> {{$wo['priority_level']}} </span></td>
                                              <td width="20%" class=""><span class="nowrap d-inline-block mr-10 text-dark">الوحدة</span></td>
                                              <td width="30%" class=""><span class="text-osool fw-700 d-inline-block"> {{$wo['room']}}  </span></td>
                                            </tr>
                                  <tr>
                                    <td><span class="text-dark"> وقت الرفع </span></td>
                                    <td><span class="text-dark"> وقت الانتهاء </span></td>
                                    <td><span class="text-dark"> وقت الإستجابة </span></td>
                                    <td><span class="text-dark"> الوقت المتوقع لإنجاز امر العمل </span></td>
                                  </tr>
                                  <tr>
                                    <td><span class="text-osool fw-700 nowrap">{{ date('d/m/Y h:i A', strtotime($wo['created_at'])) }}</span></td>
                                    @if($wo['job_started_at'] !='')
                                      <td><span class="text-osool fw-700 nowrap">{{ date('d/m/Y h:i A', strtotime($wo['job_started_at'])) }}</span></td>
                                    @else
                                      <td><span class="text-osool fw-700 nowrap">-</span></td>
                                    @endif
                                    
                                    @if($wo['job_completion_date'] !='')
                                      <td><span class="text-osool fw-700 nowrap">{{ date('d/m/Y h:i A', strtotime($wo['job_completion_date'])) }}</span></td>
                                    @else
                                      <td><span class="text-osool fw-700 nowrap">-</span></td>
                                    @endif
                                    <td><span class="text-osool fw-700 nowrap"> {{ date('d/m/Y h:i A', strtotime($wo['target_date'])) }}</span></td>
                                  </tr>
                                  <tr>
                                    <td class="">
                                      <div class="text-dark">إسم الأصل</div>
                                    </td>
                                    <td class="border-right">
                                      <div class="text-osool">{{$wo['asset_name'] ?? '-'}}</div>
                                    </td>
                                    <td class="">
                                      <div class="text-dark">رقم الأصل</div>
                                    </td>
                                    <td class="">
                                      <div class="text-osool">{{$wo['asset_number'] ?? '-'}}</div>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td class="">
                                      <div class="text-dark"><span class="nowrap d-inline-block mr-10 text-dark">رفع بواسطة</span></div>
                                    </td>
                                    <td class="border-right">
                                    <span class="nowrap text-osool fw-700 d-inline-block">{{$wo->bm_name_created_by}}</span>
                                    </td>
                                    <td class="">
                                      <span class="nowrap d-inline-block mr-10 text-dark">تكلفة أمر العمل</span>
                                    </td>
                                    <td class="">
                                      <span class="text-osool fw-700 d-inline-block">{{$wo['cost'] == '' ? 0 : $wo['cost']}} ر س</span>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td class="">
                                      <span class="nowrap d-inline-block mr-10 text-dark">اسم المشرف</span>
                                    </td>
                                    <td class="border-right">
                                      <span class="nowrap text-osool fw-700 d-inline-block">
                                                  <?php if($wo->worker_for_responsible != null){
                                                          $supervisors = Helper::get_supervisors($wo->worker_for_responsible);
                                                      }else if($wo->workorder_journey == 'submitted'){
                                                          $supervisors = '-';
                                                      }else{
                                                          if($wo->assigned_to == "sp_admin" || $wo->assigned_to == "supervisor"){ // @flip1@ spa , sps assign them self
                                                              $supervisors = Helper::get_supervisors($wo->worker_id);
                                                          }elseif ($wo->assigned_to == "sp_worker"){ // @flip1@ worker assigned
                                                              $assigned_worker_responsible_supervisor =  DB::table('users')->select('supervisor_id')->where('id', $wo->worker_id)->first();
                                                              if($assigned_worker_responsible_supervisor){
                                                                  $supervisors = Helper::get_supervisors($assigned_worker_responsible_supervisor->supervisor_id);
                                                              }else{
                                                                  $supervisors = '-';
                                                              }
                                                          }else{ // @flip1@ default supervisor.
                                                              $supervisors = '-';
                                                          }
                                          
                                                      }
                                                          echo ($supervisors != '') ? $supervisors :  '-';
                                                            ?>
                                                  </span>
                                    </td>
                                    <td class="">
                                    <span class="nowrap d-inline-block mr-10 text-dark">اسم العامل</span>
                                    </td>
                                    <td class="">
                                    @if($wo->worker_name == 'sp_started_on_behlf_of_worker')
                                    <span class="text-osool fw-700 d-inline-block">مقدم الخدمة بدأ العمل <span class="nowrap">بالنيابة</span> عن العامل</span>
                                    @elseif($wo->worker_name == 'not_assigned')
                                    <span class="text-osool fw-700 d-inline-block">لم يتم التعيين</span>
                                    @else
                                    <span class="text-osool fw-700 d-inline-block">{{$wo->worker_name}}</span>
                                    @endif
                                    </td>
                                  </tr>

                                  <tr>
                                    <td class="">
                                      <span class="nowrap d-inline-block mr-10 text-dark">مدير المبنى المعتمد</span>
                                    </td>
                                    @if(isset($wo->bm_name_closed_by))
                                                  <td class="border-right" colspan="3"><span class="nowrap text-osool fw-700 d-inline-block">{{$wo->bm_name_closed_by}}</span></td>
                                              @else
                                                  <td class="border-right" colspan="3"><span class="nowrap text-osool fw-700 d-inline-block">-</span></td>
                                              @endif
                                  </tr>
                                  <tr>
                                    <td class="border-right"><span class="text-dark">مرفقات أمر العمل</span></td>
                                    @if(trim($wo['wo_images']) != "")
                                    <td colspan="3">
                                      @foreach(explode(',',trim($wo['wo_images'])) as $k=>$v)
                                      @php
                                        $extension = pathinfo($v, PATHINFO_EXTENSION);
                                        $mime = '';
                                        if($extension != 'pdf') {
                                            $filePath = ImagesUploadHelper::displayImage($v, 'uploads/workorder', 0, false, true);
                                            $mime = 'image/jpeg';
                                        } else {
                                            $filePath = public_path('uploads/workorder/' . $v);
                                            $mime = mime_content_type($filePath);
                                        } 
                                    @endphp
                                    @if ($mime === 'application/pdf')
                                    <div class="d-inline-block image-block">
                                        <div class="image-div" style="background:url({{ asset('img/pdf.png')}});background-size: cover; background-position: center; ">
                                        </div>
                                      </div>
                                    @else
                                      <div class="d-inline-block image-block">
                                        <div class="image-div" style="background:url({{$filePath}});background-size: cover; background-position: center; ">
                                        </div>
                                      </div>
                                    @endif
                                      @endforeach
                                    </td>
                                    @else
                                    <td colspan="3"><p class="mb-0 text-dark">لم يتم رفع مرفقات</p></td>
                                    @endif
                                  </tr>
                                  <tr>
                                    <td class="border-right"><span class="text-dark">مرفقات مزود الخدمة</span></td>
                                    <td>
                                        @forelse($wo->pictures_by_worker_id as $wid)
                                            <div class="text-osool mb-10">{{$wid}}</div>
                                        @empty
                                            <div class="text-osool mb-10 text-center">-</div>
                                        @endforelse
                                    </td>
                                      @php $x =1; @endphp
                                      @php $d =1; @endphp
                                    <td colspan="2">
                                    @forelse($wo->pictures as $picture)
                                        @if($x <= 3)
                                            @if(!empty( $picture['photos']) && !empty(json_decode($picture['photos'])) ) 
                                                  @for($n = 0 ; $n<=2; $n++ )
                                                        @if(isset(json_decode($picture['photos'])[$n]))
                                                        <?php
                                                              $filename = explode('.', json_decode($picture['photos'])[$n]);
                                                              $class = '';
                                                              if(in_array('pdf',$filename)){
                                                                $url = public_path('img/pdf.png');
                                                                $class = "pdf";
                                                              }
                                                              else{
                                                                $url = ImagesUploadHelper::displayImage(json_decode($picture->photos)[$n], 'actions', 1);
                                                                $class = "";
                                                              }
                                                        ?>
                                                              @if($class == 'pdf')
                                                                <div class="d-inline-block image-block">
                                                                  <div class="image-div" style="background:url({{Helper::base64EncodeImage($url)}});background-size: cover; background-position: center; "></div>
                                                              </div>
                                                              @else
                                                              <div class="d-inline-block image-block">
                                                                  <div class="image-div" style="background:url({{ImagesUploadHelper::base64EncodeImage($url)}});background-size: cover; background-position: center; "></div>
                                                              </div>
                                                              @endif
                                                        @endif
                                                  @endfor
                                            @endif
                                        @endif
                                        @php $x = $x+1; @endphp
                                        @php $d = $d+1; @endphp
                                    @empty
                                        <p class="mb-0 text-dark">لم يتم رفع مرفقات</p>
                                    @endforelse
                                    </td>
                                  </tr>
                                  
                                  
                                  <tr>
                                    <td class="border-right"><span class="text-dark">تفاصيل طلب الصيانة</span></td>
                                    <td width="250">
                                      @if(isset($wo['maintenance_request_details']) && $wo['maintenance_request_details']!=null)
                                        <table class="no-border td-p-0">
                                        <?php $is_tenant = Helper::checkIfTenant($wo['maintenance_request_details']->phone);?>
                                          <tr>
                                                <td><span class="text-dark nowrap mr-10">إنشاء من</span></td>
                                                @if($wo['maintenance_request_details']->generated_from == 'app' && $is_tenant)
                                                <td><span class="text-osool">{{ Helper::get_appartment_from_id($wo['maintenance_request_details']->user_id) }}</span></td>
                                                @endif

                                                @if($wo['maintenance_request_details']->generated_from == 'app' && !$is_tenant)
                                                  @if($wo['maintenance_request_details']->user_id != 0 && $wo['maintenance_request_details']->user_id != '')
                                                      <td><span class="text-osool"></span>{{ Helper::getUserNameById($wo['maintenance_request_details']->user_id) }}</span></td>
                                                  @else
                                                      <td><span></span></td>
                                                  @endif
                                                @endif

                                                @if($wo['maintenance_request_details']->generated_from == 'web')
                                                    <td><span class="text-osool">{{$wo['maintenance_request_details']->name}}</span></td>
                                                @endif
                                          </tr>
                                          <tr>
                                            <td><span class="text-dark nowrap mr-10">إنشاء عبر</span></td>
                                            @if($wo['maintenance_request_details']->generated_from == 'app' && $is_tenant)
                                            <td><span class="text-osool">تطبيق المستفيد</span></td>
                                            @endif

                                            @if($wo['maintenance_request_details']->generated_from == 'app' && !$is_tenant)
                                            <td><span class="text-osool">تطبيق الفني</span></td>
                                            @endif

                                            @if($wo['maintenance_request_details']->generated_from == 'web')
                                            <td><span class="text-osool">بوابة الصيانة</span></td>
                                            @endif
                                          </tr>
                                          <tr>
                                            <td><span class="text-dark nowrap mr-10">رقم الجوال</span></td>
                                            <td><span class="text-osool">{{$wo['maintenance_request_details']->phone}}</span></td>
                                          </tr>
                                        </table>
                                        @else
                                        <span class="nowrap text-osool fw-700 d-inline-block">-</span>
                                      @endif
                                    </td>
                                    <td colspan="2">
                                    @if(isset($wo['maintenance_request_details']) && $wo['maintenance_request_details']!=null)
                                      @if($wo['maintenance_request_details']->image1 != null)
                                            @php
                                              $m_image1 = ImagesUploadHelper::displayImage($wo['maintenance_request_details']->image1, 'uploads/maintanance_request', 1);
                                            @endphp

                                            <div class="d-inline-block image-block">
                                                <div class="image-div" style="background:url({{ImagesUploadHelper::base64EncodeImage($m_image1)}});background-size: cover; background-position: center; ">
                                              </div>
                                            </div>
                                      @endif

                                      @if($wo['maintenance_request_details']->image2 != null)
                                            @php
                                              $m_image2 = ImagesUploadHelper::displayImage($wo['maintenance_request_details']->image2, 'uploads/maintanance_request', 1);
                                            @endphp

                                            <div class="d-inline-block image-block">
                                                <div class="image-div" style="background:url({{ImagesUploadHelper::base64EncodeImage($m_image2)}});background-size: cover; background-position: center; ">
                                              </div>
                                            </div>
                                      @endif

                                      @if($wo['maintenance_request_details']->image3 != null)
                                            @php
                                              $m_image3 = ImagesUploadHelper::displayImage($wo['maintenance_request_details']->image3, 'uploads/maintanance_request', 1);
                                            @endphp

                                            <div class="d-inline-block image-block">
                                                <div class="image-div" style="background:url({{ImagesUploadHelper::base64EncodeImage($m_image3)}});background-size: cover; background-position: center; ">
                                              </div>
                                            </div>
                                      @endif

                                      @if($wo['maintenance_request_details']->image1 == null && $wo['maintenance_request_details']->image2 == null && $wo['maintenance_request_details']->image3 == null)
                                            <p class="mb-0 text-dark">No Attachments uploaded</p>
                                          @endif
                                      @endif
                                    </td>
                                  </tr>
                                </table>
                          </div>
                          </div>
                          </div>
                      </div>
            </section>         
                      @php
                      $i=$i+1;
                      @endphp
            @endforeach
@endif

@if(in_array('preventive', $maintenance_type))
        @php
                  $i = 1;
                  @endphp
                @foreach($data_preventive as $work_order)
                @php
                  // Pagination setup for work order history
                  $recordsPerPage = 13; // Limit for the first page
                  $recordsPerSubsequentPage = 17; // Limit for subsequent pages
                  $historyCount = count($work_order->work_order_history);

                  // Calculate total pages for this work order
                  $totalPages = $historyCount > $recordsPerPage
                                ? ceil(($historyCount - $recordsPerPage) / $recordsPerSubsequentPage) + 1
                                : 1;

                  $startIndex = 0; // Initialize start index
                @endphp
        {{-- Loop through each page for the current work order --}}
        @for($page = 1; $page <= $totalPages; $page++)
        <section class="reports-module" style="page-break-after:always;">
        <div class="container">
        <div class="text-center">
              <img src="{{ $project_image_link }}" class="fourth_img mb-05" style="max-width:150px; max-height:100px;" alt="">
          </div>
        <div style="" class="mb-5">
                  @if($page === 1)
                    <table class="w-100 mt-2 mb-1">
                      <tr>
                        <td class=""><h5 class="text-light mb-0 d-inline-block mt-0 mr-10">نوع الخدمة</h5> <h5 class="text-osool mb-0 d-inline-block mt-0">{{$work_order->asset_category}}</h5></td>
                        <td class="text-right"><h5 class="text-light mb-0 d-inline-block mt-0 mr-10">العقد</h5> <h5 class="text-osool mb-0 d-inline-block mt-0">{{$work_order->contract_number}}</h5></td>
                      </tr>
                    </table>
                  @endif
                <div class="table-container bg-white" style="border-radius: 10px;overflow:hidden;page-break-after:always;">
                        @if($page === 1)
                          <div class="table-header bg-osool text-white py-5px px-2 br-t">
                              <h3 class="text-white lh-2 fw-700">{{$i}}. {{ $work_order->pm_title}} </h3> 
                          </div>
                        @endif
                        <div class="p-2">
                                    @if($page === 1)
                                    <table class="w-100 no-border td-p-0 mb-1">
                                        <tr>
                                              <td>
                                                <div>
                                                  <table class="tb-0 tp-10">
                                                    <tr>
                                                        <td><span class="nowrap text-light mb-10 d-inline-block mr-10">وقائي</span></td>
                                                        <td><span class="nowrap text-osool fw-700 mb-10 d-inline-block">اعمال صيانة</span></td>
                                                      </tr>
                                                      <tr>
                                                        <td><span class="nowrap text-light mb-10 d-inline-block mr-10">التكرار</span></td>
                                                        <td><span class="nowrap text-osool fw-700 mb-10 d-inline-block">
                                                          @if(isset($work_order->title_ar))
                                                            {{$work_order->title_ar}}
                                                            @endif
                                                          </span>
                                                        </td>
                                                      </tr>
                                                      <tr>
                                                        <td><span class="nowrap text-light d-inline-block mr-10">الأولوية</span></td>
                                                        <td><span class="nowrap text-osool fw-700 d-inline-block"> {{ $work_order->priority_level}} </span></td>  
                                                      </tr>
                                                    </table>
                                                </div>
                                              </td>
                                              <td></td>
                                              <td>
                                                  <div class="text-right">
                                                    <table class="tb-0 tp-10">
                                                      <tr>
                                                        <td><span class="nowrap text-light d-inline-block mr-10 mb-10">إسم الأصل</span></td>
                                                        <td><span class="nowrap text-osool fw-700 d-inline-block mr-10 mb-10">{{$work_order->asset_name ?? '-'}}</span></td>
                                                      </tr>
                                                      <tr>
                                                        <td><span class="nowrap text-light d-inline-block mr-10 mb-10">رقم الأصل</span></td>
                                                        <td><span class="nowrap text-osool fw-700 d-inline-block mr-10 mb-10">{{$work_order->asset_number ?? '-'}}</span></td>
                                                      </tr>
                                                      <tr>
                                                        <td><span class="nowrap text-light d-inline-block mr-10">رمز الأصل</span></td>
                                                        <td><span class="nowrap text-osool fw-700 d-inline-block mr-10">{{$work_order->asset_tag ?? '-'}}</span></td>
                                                      </tr>
                                                    </table>
                                                  </div>
                                              </td>
                                        </tr>
                                      </table>
                                    @endif

                                    {{-- Work Order History Table --}}
                                      <table class="main-table table-border w-100">
                                            <thead>
                                              <tr>
                                                <th>#معرف أمر العمل</th>
                                                <th>وقت الاستلام</th>
                                                <th>وقت الانتهاء</th>
                                                <th>الحالة</th>
                                                <th>التكلفة</th>
                                              </tr>
                                            </thead>
                                            <tbody>
                                              @php
                                                // Determine record limits for the current page
                                                $currentLimit = $page === 1 ? $recordsPerPage : $recordsPerSubsequentPage;
                                                $endIndex = min($startIndex + $currentLimit, $historyCount);
                                              @endphp

                                              {{-- Display records for the current page --}}
                                              @for($j = $startIndex; $j < $endIndex; $j++)
    <tr>
                                                  <td>{{$work_order->work_order_history[$j]['work_order_id']}}</td>
                                                  <td>{{ date('d-m-Y - h:i A', strtotime($work_order->work_order_history[$j]['created_at'])) }}</td>
                                                  @if($work_order->work_order_history[$j]['job_completion_date'] != '')
                                                    <td>{{ date('d-m-Y - h:i A', strtotime($work_order->work_order_history[$j]['job_completion_date'])) }}</td>
                                                  @else
                                                    <td>-</td>
                                                  @endif
                                                  <td>{{$work_order->work_order_history[$j]['status']}}</td>
                                                  <td>{{$work_order->work_order_history[$j]['cost']}} ر س</td>
                                                </tr>
                                              @endfor

                                              @php
                                                $startIndex = $endIndex; // Update start index for the next page
                                              @endphp
                                               @if($page == $totalPages)
                                                  <tr>
                                                    <th colspan="5" class="br-0">تم رفعها من مقدم الخدمة</th>
                                                  </tr>
                                                  <tr>
                                                    <td colspan="5">
                                                      @php $z =1; @endphp

                                                      @if(!empty($work_order->pictures))

                                                            @foreach($work_order->pictures as $picture)
                                                                @if(!empty( $picture['photos'] )  && !empty(json_decode($picture['photos']))  && isset(json_decode($picture['photos'])[0]) )
                                                                    @for($n = 0 ; $n < 1; $n++)
                                                                      @if(isset(json_decode($picture['photos'])[$n]))
                                                                      <?php
                                                                              $filename = explode('.', json_decode($picture['photos'])[$n]);
                                                                              $class = '';
                                                                              if(in_array('pdf',$filename)){
                                                                                $url = public_path('img/pdf.png');
                                                                                $class = "pdf";
                                                                              }
                                                                              else{
                                                                                $url = ImagesUploadHelper::displayImage(json_decode($picture->photos)[$n], 'actions', 1);
                                                                              }
                                                                          ?>
                                                                          <div class="d-inline-block image-block">
                                                                            @if($class == 'pdf')
                                                                              <div class="image-div" style="background:url({{Helper::base64EncodeImage($url)}});background-size: cover; background-position: center; "></div>
                                                                            @else
                                                                                <div class="image-div" style="background:url({{ImagesUploadHelper::base64EncodeImage($url)}});background-size: cover; background-position: center; "></div>
                                                                            @endif
                                                                            <span class="text-osool">{{$picture->wo_id}}</span>
                                                                          </div>
                                                                      @endif
                                                                    @endfor
                                                                @endif
                                                                @php $z = $z+1; @endphp
                                                            @endforeach
                                                      @else
                                                      <p class="mb-0 text-dark">لم يتم رفع مرفقات</p>
                                                      @endif
                                                    </td>
                                                  </tr>
                                                @endif
                                                  @php
                                                  $i=$i+1;
                                                  @endphp
                                            </tbody>
                                      </table>
                        </div>
                </div>
        </div>

            <div class="page-footer">
        <div class="container">
          <table class="no-border" style="width: 100%;">
            <tr>
              <td>
                <p class="" style="font-size: 1.2rem;">{{$user->name}}</p>
              </td>
              <td class="text-right">
                <div class="osool-logo-p">
                  <div class="powered-by mr-15">{{__('reports.labels.powered_by')}}</div>
                  <div class=""><img src="{{ asset('img/OSOOL_logo_svg.svg') }}" style="width: 150px;"> </div>
                </div>
              </td>
            </tr>
          </table>
        </div>
      </div>
            </div>
            </section>
            @endfor
            @endforeach
@endif
</body>
</html>