@extends('layouts.app')
@section('styles')
@endsection
@section('content')
<div class="contents">
   <div class="container-fluid">
      <div class="row">
         <div class="col-lg-12">
            <div class="page-title-wrap">
                <div class="page-title d-flex justify-content-between">
                    <div class="page-title__left">
                        <div class="d-flex align-items-center user-member__title justify-content-center ">
                            <h4 class="text-capitalize fw-500 breadcrumb-title">
                        <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a> {{__('user_management_module.user_button.add_new_user')}}</h4>
                         </div>
                    </div>
                </div>
                <!-- {{ Breadcrumbs::render('add-user') }} -->
            </div>
         </div>
      </div>
   </div>
   <div class="container-fluid">
      <div class=" checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
         <div class="row justify-content-center">
            <div class="col-xl-8">
               <div class="checkout-progress-indicator content-center">
                  <div class="checkout-progress">
                     <div class="step current" id="1">
                        <span>1</span>
                        <span>{{__('user_management_module.common.user_info')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="2">
                        <span>2</span>
                        <span>{{__('user_management_module.common.user_role')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="3">
                        <span>3</span>
                        <span>{{__('user_management_module.common.user_previleges')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="4">
                        <span>4</span>
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>
                  </div>
               </div>
               <!-- checkout -->
               <div class="row justify-content-center">
                  <div class="col-xl-7 col-lg-8 col-sm-10">
                     <div class="card checkout-shipping-form px-30 pt-2 pb-30 border-0">
                        <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0">
                           <h4 class="fw-400">{{__('user_management_module.common.user_info')}}</h4>
                        </div>
                        <div class="card-body px-0 pb-0">
                             <div class="edit-profile__body">
                                 <?php
                                 $form_id = 'user_create_form';
                                 ?>
                                 @if($data['user_type'] == 'sp_worker')
                                 <?php
                                 $form_id = 'user_create_form_edit_worker';
                                 ?>
                                 @endif
                                <form method="post" id="user_create_form" action="{{ route('users.create.role')}}" enctype="multipart/form-data" autocomplete="off">
                                @csrf

                                <div class="account-profile d-flex align-items-center mb-4 ">
                                    <div class="pro_img_wrapper">
                                       <input id="file_upload" type="file" name="profile_img" class="d-none" accept="image/*">
                                       <!-- Profile picture image-->
                                       <label for="file_upload">
                                       <img class="ap-img__main rounded-circle wh-120 bg-lighter d-flex" src="/img/upload.png" alt="profile" id="output_pic">
                                       <span class="cross" id="remove_pro_pic" >
                                       <span data-feather="camera" ></span>
                                       </span>
                                       </label>
                                       <?php
                                          if(isset($data['u_data']->profile_img) && trim($data['u_data']->profile_img) != "")
                                          {
                                             $hideclass = '';
                                          }
                                          else
                                          {
                                             $hideclass = 'hide';
                                          }
                                       ?>
                                       <span class="remove-img text-white btn-danger rounded-circle <?=$hideclass;?>" data-toggle="modal" data-target="#confirmDeletePhoto">
                                         <span data-feather="x"></span>
                                       </span>
                                    </div>
                                    <div class="account-profile__title">
                                       <h6 class="fs-15 ml-20 fw-500 text-capitalize">{{__('user_management_module.user_forms.label.photo')}}</h6>
                                    </div>
                                 </div>
                                 <div class="form-group mb-20">
                                      <div class="usertype_option">
                                         <label for="user_type">
                                         {{__('user_management_module.user_forms.label.user_type')}} <small class="required">*</small>
                                         </label>

                                         <select data-blade="{{$data['is_supervisors']}}" class="form-control" id="user_type" name="user_type" required>
                                            <option  value="" selected disabled>{{__('user_management_module.user_forms.label.user_type')}}</option>
                                            @foreach($data['usertypeList'] as $user_type)
                                              @if($user_type->slug=="supervisor" || $user_type->slug=="sp_worker" || $user_type->slug=="team_leader" || $user_type->slug=="store_keeper")
                                                @if(App::getLocale()=='en')
                                                   <option @if($user_type->slug == 'sp_worker' && $data['is_supervisors'] == 'no') disabled @endif value="{{$user_type->slug}}">{{$user_type->name}}</option>
                                                @else
                                                <option @if($user_type->slug == 'sp_worker' && $data['is_supervisors'] == 'no') disabled @endif value="{{$user_type->slug}}">{{$user_type->name_ar}}</option>

                                                @endif
                                              @endif
                                            @endforeach
                                         </select>
                                      </div>
                                      <div id="user-type-error"></div>
                                   </div>
                                   <div class="form-group mb-20 company_info">
                                      <div class="service_provider">
                                         <label for="service_provider">
                                         {{__('user_management_module.user_forms.label.company_name')}}<small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="service_provider" name="service_provider">
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.company_name')}}</option>
                                            @foreach($data['companyList'] as $company)
                                              <option value="{{$company->id}}">{{$company->name}} - {{$company->service_provider_id}}</option>
                                            @endforeach
                                         </select>
                                      </div>
                                   </div>
                                   <div class="form-group mb-20 sup_info">
                                      <div class="usertype_option">
                                         <label for="sp_admin_id">
                                         {{__('user_management_module.user_forms.label.employee_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="sp_admin_id" >
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.employee_admin')}}</option>

                                         </select>
                                      </div>
                                   </div>
                                   <div class="form-group mb-20 worker_info">
                                      <div class="usertype_option">
                                         <div id="supervisor_id_label">
                                          <label for="supervisor_id">
                                         {{__('user_management_module.user_forms.label.worker_admin')}} <small class="required">*</small>
                                         </label>
                                          </div>
                                         <select class="form-control" id="supervisor_id" name="supervisor_id[]" multiple="multiple" required="required">
                                            <option disabled>{{__('user_management_module.user_forms.label.worker_admin')}}</option>
                                            @foreach($data['sp_supervisor_list'] as $supervisor)
                                              <option value="{{$supervisor->id}}">{{$supervisor->name}}</option>
                                            @endforeach
                                         </select>
                                      </div>
                                      <div id="supervisor_id-error"></div>
                                   </div>
                                   <div class="form-group mb-20 user_info">
                                      <div id="employee_name_label">
                                      <label for="emp_name">{{__('user_management_module.user_forms.label.emp_name')}}<small class="required">*</small></label>
                                      </div>
                                      <input type="text" class="form-control" name="name" id="name" placeholder="{{__('user_management_module.user_forms.place_holder.emp_name')}}">
                                   </div>
                                   <div class="form-group mb-20 user_info">
                                      <div class="employee_label">
                                      <label for="emp_id">{{__('user_management_module.user_forms.label.emp_id')}}
                                    </label>
                                    </div>
                                      <input type="text" class="form-control" maxlength="10" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{__('user_management_module.user_forms.place_holder.emp_id')}}">
                                   </div>
                                   <div class="form-group mb-20 user_info email_box">
                                    <div class="employee_email_various">
                                      <label for="emp_email">{{__('user_management_module.user_forms.label.emp_email')}} <small class="required">*</small></label>
                                    </div>
                                      <input type="email" class="form-control" id="email" name="email" placeholder="{{__('user_management_module.user_forms.place_holder.emp_email')}}">
                                   </div>

                                    <div class="form- group mb-20 user_info nationality_select">
                                       <div class="">
                                          <label for="nationality_id">
                                          {{__('user_management_module.user_forms.label.nationality')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="nationality_id" name="nationality_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_nationality')}}</option>

                                             @foreach($data['nationalities'] as $nationality)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$nationality->id}}">{{$nationality->name_en}}</option>
                                                @else
                                                <option value="{{$nationality->id}}">{{$nationality->name_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="nationality-id-error"></div>
                                    </div>


                                    <div class="form- group mb-20 user_info favorite_language_select">
                                       <div class="">
                                          <label for="favorite_language">
                                          {{__('user_management_module.user_forms.label.favorite_language')}}
                                          </label>
                                          <select class="form-control" id="favorite_language" name="favorite_language">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_favorite_language')}}</option>
                                                <option value="en">{{__('user_management_module.user_forms.label.english')}}</option>
                                                <option value="ar">{{__('user_management_module.user_forms.label.arabic')}}</option>
                                                <option value="ur">{{__('user_management_module.user_forms.label.urdu')}}</option>
                                          </select>
                                       </div>
                                       <div id="favorite-language-error"></div>
                                    </div>

                                    <div class="form- group mb-20 user_info profession_select">
                                       <div class="">
                                          <label for="profession_id">
                                          {{__('user_management_module.user_forms.label.select_profession_heading')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="profession_id" name="profession_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_profession')}}</option>

                                             @foreach($data['workerProfessions'] as $profession)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$profession->id}}">{{$profession->profession_en}}</option>
                                                @else
                                                <option value="{{$profession->id}}">{{$profession->profession_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="profession-id-error"></div>
                                    </div>

                                   <div class="form-group mb-20 user_info profession">
                                      <label for="emp_dept">
                                        <span class="emp_dept_label">{{__('user_management_module.user_forms.label.emp_dept')}}</span>
                                        <span class="worker_info">{{__('user_management_module.user_forms.label.profession')}} <small class="required">*</small></span>
                                      </label>
                                      <input type="text" class="form-control" name="emp_dept" id="emp_dept" placeholder="{{__('user_management_module.user_forms.place_holder.emp_dept')}}">
                                   </div>
                                   <div class="form-group mb-20 user_info">
                                      <label for="emp_phone_number">{{__('user_management_module.user_forms.label.emp_phone')}}
                                       {{-- <small class="required phone_required_mark">*</small> --}}
                                    </label>
                                      <div class="input-group mb-3 phone-ltr">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="basic-addon1">+966</span>
                                        </div>
                                        <input type="tel" class="form-control" id="phone" name="phone" placeholder="576428964">
                                        </div>

                                   </div>

                                    <div class="form-group mb-20 d-none">
                                        <input type="hidden" name="service_provider" value="{{ auth()->user()->service_provider }}">
                                    </div>


                                    <input type="hidden" name="country_id" id="country_id1" value="1">

                                   <input type="hidden" name="city_id" id="city_id1" value="1">

                                   <input type="hidden" id="ajax_check_useremail_unique" value="{{route('users.ajax_check_unique_useremail')}}">
                                   <input type="hidden" id="ajax_check_userphone_unique" value="{{route('users.ajax_check_unique_usernumber')}}">
                                   <input type="hidden" id="ajax_check_employee_id_unique" value="{{route('users.ajax_check_unique_emp_id')}}">

                                   <div class="button-group d-flex pt-25 justify-content-end">
                                     <a href="{{ route('users.list') }}" class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md">{{__('user_management_module.user_button.cancel')}}</a>
                                      <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('user_management_module.user_button.save_next')}}
                                      </button>
                                   </div>
                                </form>
                             </div>
                        </div>
                     </div>
                     <!-- ends: card -->
                  </div>
                  <!-- ends: col -->
               </div>
            </div>
            <!-- ends: col -->
         </div>
      </div>
      <!-- End: .global-shadow-->
   </div>
</div>

<!-- CONFIRM DELETE Photo MODAL START -->

<div class="modal new-member  bouncein-new" id="confirmDeletePhoto" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content  radius-xl  bouncein-new">

                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-3 fs-20"><i class="fa fa-exclamation-circle mr-1 text-warning"
                                aria-hidden="true"></i>
                            {{ __('data_properties.property_forms.label.sure_remove_photo') }} </h2>
                    </div>

                        <div class="button-group d-flex justify-content-end pt-25">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" class="btn btn-light   btn-squared text-capitalize"
                                    data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.cancel') }}

                                </button>
                                <button type="button" class="btn btn-danger btn-default btn-squared text-capitalize confirm_remove_photo" data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.remove') }}
                                </button>

                            </div>

                        </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- CONFIRM DELETE Photo MODAL ENDS -->

@endsection

@section('scripts')
<script type="text/javascript" src="{{asset('js/admin/users/create.js')}}"></script>
<script type="text/javascript">

$('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
      $('.profession_select').hide();
      $('.profession').hide();
      $('#emp_dept').prop('required', false); // Make input not required
      $('.nationality_select').hide();
      $('.favorite_language_select').hide();
 
 sessionStorage.setItem('end_creation', 'false');
    function removeServiceProvider() {
        $("#store_keeper_service_provider").val('').change();
        sessionStorage.removeItem('createuser_service_provider')
    }
        
$('.user_info').hide();
$('.company_info').hide();
$('.sup_info').hide();
$('.worker_info').hide();
$('.profession_select').hide();
$('.nationality_select').hide();
$('.favorite_language_select').hide();
$(".store_keeper_info").hide();



$("#user_type").on("change", function () {
  var user_type_val = $(this).val();
  sessionStorage.setItem('createuser_user_type', JSON.stringify(user_type_val));
  if(user_type_val == 'sp_worker')
  {
      $("form#user_create_form").prop('id','user_create_form_edit_worker');
      $("form#user_create_form").select2('data', {id: 'user_create_form', text: 'primary_email'});
      $('#country_id').prop('required',false);
      $('#city_id').prop('required',false);
      $(".phone_required_mark").hide();
      $('.profession').hide();
      $('.profession_select').show();
      $('.nationality_select').show();
      $('.favorite_language_select').show();
      $('#employee_name_label').html(`  <label for="emp_name">{{__('user_management_module.user_forms.label.worker_name')}}<small class="required">*</small></label>`);
      document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.worker_name')}}";
      
      $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.user_forms.label.worker_admin')}}<small class="required">*</small></label>`);
      
      $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.worker_admin);
   }
   else if(user_type_val == 'team_leader')
   {
         $("form#user_create_form").prop('id','user_create_form_edit_worker');
         $("form#user_create_form").select2('data', {id: 'user_create_form', text: 'primary_email'});
         $('#country_id').prop('required',false);
         $('#city_id').prop('required',false);
         $(".phone_required_mark").hide();
         $('.profession').hide();
         $('.profession_select').hide();
         $('.nationality_select').show();
         $('.favorite_language_select').show();

         $('#employee_name_label').html(`  <label for="emp_name">{{__('user_management_module.team_leader_module.team_leader_name')}}<small class="required">*</small></label>`);
         document.getElementById("name").placeholder = "{{__('user_management_module.team_leader_module.team_leader_name')}}";

         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.team_leader_module.team_leader_supervisor')}}<small class="required">*</small></label>`);
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.team_leader_supervisor);
      }
  else
  {
      $("form#user_create_form").prop('id','user_create_form');
      $("form#user_create_form_edit_worker").prop('id','user_create_form');
      $("form#addTags").prop('id','editTags');
      $(".phone_required_mark").show();
      $('.profession').show();
      $('.profession_select').hide();
      $('.nationality_select').hide();
      $('.favorite_language_select').hide();

      $('#employee_name_label').html(`<label for="emp_name">{{__('user_management_module.user_forms.label.supervisor_name')}}<small class="required">*</small></label>`);
            document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.supervisor_name')}}";
  }
  //alert(user_type_val);
  if(user_type_val!='')
  {
    $('.user_info').show();
    if(user_type_val=='supervisor')
    {
      $('#supervisor_id').prop('required',false);

      $('#email').attr("type","email");
      $('.emp_dept_label').show();
      $('.employee_label').html(`<label for="emp_id">{{__('user_management_module.user_forms.label.emp_id')}}</label>`);

      $('#employee_name_label').html(`<label for="emp_name">{{__('user_management_module.user_forms.label.supervisor_name')}}<small class="required">*</small></label>`);
      document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.supervisor_name')}}";

      $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.user_forms.label.supervisor_id')}}</label>`);
      document.getElementById("emp_id").placeholder = "{{__('user_management_module.user_forms.label.supervisor_id')}}";

      $('.employee_email_various').html(`<label for="emp_email">{{__('user_management_module.user_forms.label.supervisor_email')}}<small class="required">*</small></label>`);
      document.getElementById("email").placeholder = "{{__('user_management_module.user_forms.label.supervisor_email')}}";

      $('.emp_dept_label').html(`{{__('user_management_module.user_forms.label.supervisor_department')}}</label>`);
      document.getElementById("emp_dept").placeholder = "{{__('user_management_module.user_forms.label.supervisor_department')}}";

      //$('.company_info').show();
      //$('.sup_info').show();
      //$('#service_provider').prop("required", true);
      //$('#sp_admin_id').prop("required", true);
      $('.worker_info').hide();
      //$('#email').show();
    }
    else
    {
      //$('.company_info').hide();
      //$('.sup_info').hide();
      //$('#service_provider').prop("required", false);
      //$('#sp_admin_id').prop("required", false);
    }
    if(user_type_val=='sp_worker')
    {
      $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.user_forms.label.worker_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}" id=""></i><small class="required">*</small></label>
`     );
      $('[data-toggle="tooltip"]').tooltip();

      $('#employee_name_label').html(`  <label for="emp_name">{{__('user_management_module.user_forms.label.worker_name')}}<small class="required">*</small></label>
`     );
      document.getElementById("emp_id").placeholder = "{{__('user_management_module.user_forms.label.worker_id')}}";
      $('.worker_info').show();
      $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
      $('.emp_dept_label').hide();
      //$('#service_provider').prop("required", true);
      $('#supervisor_id').prop("required", true);
      $('.email_box').hide();
      $('#email').attr("type","hidden");
      $('#country_div').hide();
      $('#city_div').hide();
      $('.profession').hide();
      $('.profession_select').show();
      $('.nationality_select').show();
      $('.favorite_language_select').show();
    }
    else if(user_type_val=='team_leader')
    {
      $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.team_leader_module.team_leader_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}" id=""></i><small class="required">*</small></label>
`     );
      $('[data-toggle="tooltip"]').tooltip();

      $('#employee_name_label').html(`  <label for="emp_name">{{__('user_management_module.team_leader_module.team_leader_name')}}<small class="required">*</small></label>
`     );
      document.getElementById("emp_id").placeholder = "{{__('user_management_module.team_leader_module.team_leader_id')}}";
      $('.worker_info').show();
      $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
      $('.emp_dept_label').hide();
      //$('#service_provider').prop("required", true);
      $('#supervisor_id').prop("required", true);
      $('.email_box').hide();
      $('#email').attr("type","hidden");
      $('#country_div').hide();
      $('#city_div').hide();
      $('.profession').hide();
      $('.profession_select').hide();
      $('.nationality_select').show();
      $('.favorite_language_select').show();
    }
    else if(user_type_val=='supervisor'){
      $('#employee_name_label').html(`<label for="emp_name">{{__('user_management_module.user_forms.label.supervisor_name')}}<small class="required">*</small></label>`);
      document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.supervisor_name')}}";

      $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.user_forms.label.supervisor_id')}}</label>`);
      document.getElementById("emp_id").placeholder = "{{__('user_management_module.user_forms.label.supervisor_id')}}";

      $('.employee_email_various').html(`<label for="emp_email">{{__('user_management_module.user_forms.label.supervisor_email')}}<small class="required">*</small></label>`);
      document.getElementById("email").placeholder = "{{__('user_management_module.user_forms.label.supervisor_email')}}";

      $('.emp_dept_label').html(`{{__('user_management_module.user_forms.label.supervisor_department')}}</label>`);
      document.getElementById("emp_dept").placeholder = "{{__('user_management_module.user_forms.label.supervisor_department')}}";
      $('.profession_select').hide();
      $('.nationality_select').hide();
      $('.favorite_language_select').hide();
    }
    else if(user_type_val=='store_keeper'){

      $('#supervisor_id').prop("required", false);
        $(".store_keeper_info").show();

        $('#employee_name_label').html(`<label for="emp_name">{{__('user_management_module.user_forms.label.store_keeper_name')}}<small class="required">*</small></label>`);
        document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.store_keeper_name')}}";

        $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.user_forms.label.store_keeper_id')}}</label>`);
        document.getElementById("emp_id").placeholder = "{{__('user_management_module.user_forms.label.store_keeper_id')}}";

        $('.employee_email_various').html(`<label for="emp_email">{{__('user_management_module.user_forms.label.store_keeper_email')}}<small class="required">*</small></label>`);
        document.getElementById("email").placeholder = "{{__('user_management_module.user_forms.label.store_keeper_email')}}";

        $('.emp_dept_label').html(`{{__('user_management_module.user_forms.label.store_keeper_department')}}</label>`);
        document.getElementById("emp_dept").placeholder = "{{__('user_management_module.user_forms.label.store_keeper_department')}}";
        
      $('.profession_select').hide();
      $('.nationality_select').hide();
      $('.favorite_language_select').hide();
    }
    else
    {
      $('#employee_name_label').html(` <label for="emp_name">{{__('user_management_module.user_forms.label.emp_name')}}<small class="required">*</small></label>`);
      //$('.company_info').hide();
      $('.worker_info').hide();
      $('.emp_dept_label').show();
      //$('#service_provider').prop("required", false);
      $('#supervisor_id').prop("required", false);
      $('#email').attr("type","email");
      $('.email_box').show();
      $('#country_div').show();
      $('#city_div').show();
      $('.profession_select').hide();
      $('.nationality_select').hide();
      $('.favorite_language_select').hide();
    }
    // if(user_type_val=='supervisor'||user_type_val=='sp_worker')
    // {
    //   $('.company_info').show();
    //   $('#service_provider').prop("required", true);
    // }
    // else
    // {
    //   $('.company_info').hide();
    //   $('#service_provider').prop("required", false);
    // }

  }
});

$('#supervisor_id').on('select2:select', function (e) {
    sessionStorage.setItem('createuser_supervisor_id', JSON.stringify($(this).val()));
   });

$('#supervisor_id').on('select2:unselecting', function (e) {
      let value_cnt1 = $(this).val();
         value_cnt1 = jQuery.grep(value_cnt1, function(value) {
               return value != e.params.args.data.id;
         });
         sessionStorage.setItem('createuser_supervisor_id', JSON.stringify(value_cnt1));
});

var storedusertypeValues = sessionStorage.getItem('createuser_user_type');
   if(storedusertypeValues)
   {
         var selecteddusertypeValues = JSON.parse(storedusertypeValues);
         // Set the selected values in the Select2 dropdown
         $('#user_type').val(selecteddusertypeValues).trigger('change');
         var user_type_val = selecteddusertypeValues


         if(user_type_val == 'sp_worker')
         {
               $("form#user_create_form").prop('id','user_create_form_edit_worker');
               $("form#user_create_form").select2('data', {id: 'user_create_form', text: 'primary_email'});
               $('#country_id').prop('required',false);
               $('#city_id').prop('required',false);
               $(".phone_required_mark").hide();
               $('.profession').hide();
               $('.profession_select').show();
               $('.nationality_select').show();
               $('.favorite_language_select').show();

               $('#employee_name_label').html(`  <label for="emp_name">{{__('user_management_module.user_forms.label.worker_name')}}<small class="required">*</small></label>`);
               document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.worker_name')}}";
               $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.user_forms.label.worker_admin')}}<small class="required">*</small></label>`);
      
               $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.worker_admin);

         }
         else if(user_type_val == 'team_leader')
   {
         $("form#user_create_form").prop('id','user_create_form_edit_worker');
         $("form#user_create_form").select2('data', {id: 'user_create_form', text: 'primary_email'});
         $('#country_id').prop('required',false);
         $('#city_id').prop('required',false);
         $(".phone_required_mark").hide();
         $('.profession').hide();
         $('.profession_select').hide();
         $('.nationality_select').show();
         $('.favorite_language_select').show();

         $('#employee_name_label').html(`  <label for="emp_name">{{__('user_management_module.team_leader_module.team_leader_name')}}<small class="required">*</small></label>`);
         document.getElementById("name").placeholder = "{{__('user_management_module.team_leader_module.team_leader_name')}}";

       
         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.team_leader_module.team_leader_supervisor')}}<small class="required">*</small></label>`);
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.team_leader_supervisor);
      }
         else
         {
               $("form#user_create_form").prop('id','user_create_form');
               $("form#user_create_form_edit_worker").prop('id','user_create_form');
               $("form#addTags").prop('id','editTags');
               $(".phone_required_mark").show();
         }
      if(user_type_val!='')
      {
         $('.user_info').show();
         if(user_type_val=='supervisor')
         {
            $('#supervisor_id').prop('required',false);

            $('#email').attr("type","email");
            $('.emp_dept_label').show();
            $('.employee_label').html(`<label for="emp_id">{{__('user_management_module.user_forms.label.emp_id')}}</label>`);

            $('#employee_name_label').html(`<label for="emp_name">{{__('user_management_module.user_forms.label.supervisor_name')}}<small class="required">*</small></label>`);
            document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.supervisor_name')}}";

            $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.user_forms.label.supervisor_id')}}</label>`);
            document.getElementById("emp_id").placeholder = "{{__('user_management_module.user_forms.label.supervisor_id')}}";

            $('.employee_email_various').html(`<label for="emp_email">{{__('user_management_module.user_forms.label.supervisor_email')}}<small class="required">*</small></label>`);
            document.getElementById("email").placeholder = "{{__('user_management_module.user_forms.label.supervisor_email')}}";

            $('.emp_dept_label').html(`{{__('user_management_module.user_forms.label.supervisor_department')}}</label>`);
            document.getElementById("emp_dept").placeholder = "{{__('user_management_module.user_forms.label.supervisor_department')}}";

            //$('.company_info').show();
            //$('.sup_info').show();
            //$('#service_provider').prop("required", true);
            //$('#sp_admin_id').prop("required", true);
            $('.worker_info').hide();
            //$('#email').show();
         }
         else
         {
            //$('.company_info').hide();
            //$('.sup_info').hide();
            //$('#service_provider').prop("required", false);
            //$('#sp_admin_id').prop("required", false);
         }
         if(user_type_val=='sp_worker')
         {
            $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.user_forms.label.worker_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}" id=""></i><small class="required">*</small></label>
      `     );
            $('[data-toggle="tooltip"]').tooltip();

            $('#employee_name_label').html(`  <label for="emp_name">{{__('user_management_module.user_forms.label.worker_name')}}<small class="required">*</small></label>
      `     );
            //$('.company_info').show();
            $('.worker_info').show();
            $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
            $('.emp_dept_label').hide();
            //$('#service_provider').prop("required", true);
            $('#supervisor_id').prop("required", true);
            $('.email_box').hide();
            $('#email').attr("type","hidden");
            $('#country_div').hide();
            $('#city_div').hide();
         }
         else if(user_type_val=='team_leader')
    {
      $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.team_leader_module.team_leader_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}" id=""></i><small class="required">*</small></label>
`     );
      $('[data-toggle="tooltip"]').tooltip();

      $('#employee_name_label').html(`  <label for="emp_name">{{__('user_management_module.team_leader_module.team_leader_name')}}<small class="required">*</small></label>
`     );
      document.getElementById("emp_id").placeholder = "{{__('user_management_module.team_leader_module.team_leader_id')}}";
      $('.worker_info').show();
      $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
      $('.emp_dept_label').hide();
      //$('#service_provider').prop("required", true);
      $('#supervisor_id').prop("required", true);
      $('.email_box').hide();
      $('#email').attr("type","hidden");
      $('#country_div').hide();
      $('#city_div').hide();
      $('.profession').hide();
      $('.profession_select').hide();
      $('.nationality_select').show();
      $('.favorite_language_select').show();
    }
         else if(user_type_val=='supervisor'){
            $('#employee_name_label').html(`<label for="emp_name">{{__('user_management_module.user_forms.label.supervisor_name')}}<small class="required">*</small></label>`);
            document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.supervisor_name')}}";

            $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.user_forms.label.supervisor_id')}}</label>`);
            document.getElementById("emp_id").placeholder = "{{__('user_management_module.user_forms.label.supervisor_id')}}";

            $('.employee_email_various').html(`<label for="emp_email">{{__('user_management_module.user_forms.label.supervisor_email')}}<small class="required">*</small></label>`);
            document.getElementById("email").placeholder = "{{__('user_management_module.user_forms.label.supervisor_email')}}";

            $('.emp_dept_label').html(`{{__('user_management_module.user_forms.label.supervisor_department')}}</label>`);
            document.getElementById("emp_dept").placeholder = "{{__('user_management_module.user_forms.label.supervisor_department')}}";
         }
         else if(user_type_val=='store_keeper'){
      $('#supervisor_id').prop("required", false);
             $('#employee_name_label').html(`<label for="emp_name">{{__('user_management_module.user_forms.label.store_keeper_name')}}<small class="required">*</small></label>`);
             document.getElementById("name").placeholder = "{{__('user_management_module.user_forms.label.store_keeper_name')}}";

             $('.employee_label').html(`<label for="worker_id">{{__('user_management_module.user_forms.label.store_keeper_id')}}</label>`);
             document.getElementById("emp_id").placeholder = "{{__('user_management_module.user_forms.label.store_keeper_id')}}";

             $('.employee_email_various').html(`<label for="emp_email">{{__('user_management_module.user_forms.label.store_keeper_email')}}<small class="required">*</small></label>`);
             document.getElementById("email").placeholder = "{{__('user_management_module.user_forms.label.store_keeper_email')}}";

             $('.emp_dept_label').html(`{{__('user_management_module.user_forms.label.store_keeper_department')}}</label>`);
             document.getElementById("emp_dept").placeholder = "{{__('user_management_module.user_forms.label.store_keeper_department')}}";
         }
         else
         {
            $('#employee_name_label').html(` <label for="emp_name">{{__('user_management_module.user_forms.label.emp_name')}}<small class="required">*</small></label>`);
            //$('.company_info').hide();
            $('.worker_info').hide();
            $('.emp_dept_label').show();
            //$('#service_provider').prop("required", false);
            $('#supervisor_id').prop("required", false);
            $('#email').attr("type","email");
            $('.email_box').show();
            $('#country_div').show();
            $('#city_div').show();
         }
         }
   }


   var storedusernameValues = sessionStorage.getItem('createuser_name');
         if(storedusernameValues)
         {
            $("#name").val(JSON.parse(storedusernameValues));
         }
         var storedusebuildingadminValues = sessionStorage.getItem('createuser_building_admin');
         if(storedusebuildingadminValues)
         {
            $("#building_admin").val(JSON.parse(storedusebuildingadminValues)).trigger('change');
         }
         var storeduserempidValues = sessionStorage.getItem('createuser_emp_id');
         if(storeduserempidValues)
         {
            $("#emp_id").val(JSON.parse(storeduserempidValues));
         }
         var storeduseremailValues = sessionStorage.getItem('createuser_email');
         if(storeduseremailValues)
         {
            $("#email").val(JSON.parse(storeduseremailValues));
         }
         var storeduserempdeptValues = sessionStorage.getItem('createuser_emp_dept');
         if(storeduserempdeptValues)
         {
            $("#emp_dept").val(JSON.parse(storeduserempdeptValues));
         }
         var storeduserphoneValues = sessionStorage.getItem('createuser_phone');
         if(storeduserphoneValues)
         {
            $("#phone").val(JSON.parse(storeduserphoneValues));
         }
         var storedusersupervisoridValues = sessionStorage.getItem('createuser_supervisor_id');
         if(storedusersupervisoridValues)
         {
            $('#supervisor_id').val(JSON.parse(storedusersupervisoridValues)).trigger('change');
         }


         $('#name').keyup(function() {
      sessionStorage.setItem('createuser_name', JSON.stringify($(this).val()));
   });

   function getEmpiddata(){
   sessionStorage.setItem('createuser_emp_id', JSON.stringify($('#emp_id').val()));
}

   $('#emp_id').keyup(function() {
      sessionStorage.setItem('createuser_emp_id', JSON.stringify($(this).val()));
   });

   $('#email').keyup(function() {
      sessionStorage.setItem('createuser_email', JSON.stringify($(this).val()));
   });

   $('#emp_dept').keyup(function() {
      sessionStorage.setItem('createuser_emp_dept', JSON.stringify($(this).val()));
   });

   $('#phone').keyup(function() {
      sessionStorage.setItem('createuser_phone', JSON.stringify($(this).val()));
   });
</script>

<script type="text/javascript">
$("#user_type,#service_provider,#supervisor_id,#country_id,#city_id").select2({
   placeholder:translations.data_contract.contract_forms.place_holder.please_choose,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
});
$("#profession_id").select2({
   placeholder:translations.user_management_module.user_forms.label.choose_a_profession,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
});
$("#nationality_id").select2({
   placeholder:translations.user_management_module.user_forms.label.choose_a_nationality,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
});
$("#favorite_language").select2({
   placeholder:translations.user_management_module.user_forms.label.choose_a_favorite_language,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
});
$('.search-select-multiple').select2({
    dropdownAutoWidth: true,
    multiple: true,
    width: '100%',
    height: '30px',
    placeholder: "Select",
    allowClear: true
});

   $(document).ready(function() {
      // $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
      // $('.profession_select').hide();
       $('.profession').hide();
      // $('#emp_dept').prop('required', false); // Make input not required
      // $('.nationality_select').hide();
      // $('.favorite_language_select').hide();

      $('#profession_id').on('change', function() {
         var selectedOption = $(this).val();

         if (selectedOption === '10') {
               $('.profession').show();
               $('#emp_dept').prop('required', true); // Make input required
               $('#emp_dept').attr('maxlength', 15); // Set the new max-length attribute
         } else {
               $('.profession').hide();
               $('#emp_dept').prop('required', false); // Make input not required
               $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
         }
      });
   });

$('.select2-search__field').css('width', '100%');

document
  .getElementById("tt")
  .setAttribute("data-title", "New Tooltip Title");
new bootstrap.Tooltip(jQuery('[data-toggle="tooltip"]'));
</script>
@endsection
