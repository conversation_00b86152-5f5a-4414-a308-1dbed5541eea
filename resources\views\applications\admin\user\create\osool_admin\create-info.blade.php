@extends('layouts.app')
@section('styles')
@endsection
@section('content')
    <div class="contents">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="page-title-wrap">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left">
                                <div class="d-flex align-items-center user-member__title justify-content-center">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title"><a href="Javascript:history.back()"><i
                                                class="las la-arrow-left"></i></a>
                                        {{ __('user_management_module.user_button.add_new_user') }}</h4>
                                </div>
                            </div>
                        </div>
                        <!-- {{ Breadcrumbs::render('add-user') }} -->
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div
                class=" checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
                <div class="row justify-content-center">
                    <div class="col-xl-8">
                        <div class="checkout-progress-indicator content-center">
                            <div class="checkout-progress">
                                <div class="step current" id="1">
                                    <span>1</span>
                                    <span>{{ __('user_management_module.common.user_info') }}</span>
                                </div>
                                <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img"
                                        class="svg"></div>
                                <div class="step" id="2">
                                    <span>2</span>
                                    <span>{{ __('user_management_module.common.user_role') }}</span>
                                </div>
                                <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img"
                                        class="svg"></div>
                                <div class="step" id="3">
                                    <span>3</span>
                                    <span id="privi">{{ __('user_management_module.common.user_previleges') }}</span>
                                </div>
                                <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img"
                                        class="svg"></div>
                                <div class="step" id="4">
                                    <span>4</span>
                                    <span>{{ __('user_management_module.common.confirm') }}</span>
                                </div>
                            </div>
                        </div>
                        <!-- checkout -->
                        <div class="row justify-content-center">
                            <div class="col-xl-7 col-lg-8 col-sm-10">
                                <div class="card checkout-shipping-form pt-2 pb-30 border-0">
                                    <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0">
                                        <h4 class="fw-400">{{ __('user_management_module.common.user_info') }}</h4>
                                    </div>
                                    <div class="card-body px-0 pb-0">
                                        <div class="edit-profile__body">
                                            <form method="post" id="user_create_form"
                                                action="{{ route('users.create.role') }}" enctype="multipart/form-data"
                                                autocomplete="off">
                                                @csrf
                                                <div class="account-profile d-flex align-items-center mb-4 ">
                                                    <div class="pro_img_wrapper">
                                                        <input id="file_upload" type="file" name="profile_img"
                                                            class="d-none" accept="image/*">
                                                        <!-- Profile picture image-->
                                                        <label for="file_upload">
                                                            <img class="ap-img__main rounded-circle wh-120 bg-lighter d-flex"
                                                                src="/img/upload.png" alt="profile" id="output_pic">
                                                            <span class="cross" id="remove_pro_pic">
                                                                <span data-feather="camera"></span>
                                                            </span>
                                                        </label>
                                                        <span class="remove-img text-white btn-danger rounded-circle hide"
                                                            data-toggle="modal" data-target="#confirmDeletePhoto">
                                                            <span data-feather="x"></span>
                                                        </span>
                                                    </div>
                                                    <div class="account-profile__title">
                                                        <h6 class="fs-15 ml-20 fw-500 text-capitalize">
                                                            {{ __('user_management_module.user_forms.label.photo') }}</h6>
                                                    </div>
                                                </div>
                                                <div class="form- group mb-20">
                                                    <div class="usertype_option">
                                                        <label for="user_type">
                                                            {{ __('user_management_module.user_forms.label.user_type') }}
                                                            <small class="required">*</small>
                                                        </label>
                                                        <select class="form-control" id="user_type" name="user_type"
                                                            required>
                                                            <option value="" selected disabled>
                                                                {{ __('user_management_module.user_forms.label.user_type') }}
                                                            </option>
                                                            @foreach ($data['usertypeList'] as $user_type)
                                                                @if(auth()->user()->user_type == "admin" || auth()->user()->user_type == "sp_admin" || auth()->user()->user_type == "admin_employee" )
                                                                
                                                                @if(auth()->user()->user_type == "admin" || auth()->user()->user_type == "sp_admin" || auth()->user()->user_type == "admin_employee" )
                                                                @if($user_type->slug == 'procurement_admin' && ! \App\Services\AkauntingService::allow())
                                                                    @continue
                                                                @endif
                                                        
                                                                @if (App::getLocale() == 'en')
                                                                    <option value="{{ $user_type->slug }}">
                                                                        {{ $user_type->name }}
                                                                    </option>
                                                                @else
                                                                    <option value="{{ $user_type->slug }}">
                                                                        {{ $user_type->name_ar }}
                                                                    </option>
                                                                @endif
                                                            @endif
                                                                @else
                                                                    @if($user_type->slug != "procurement_admin")
                                                                        @if (App::getLocale() == 'en')
                                                                            <option value="{{ $user_type->slug }}">
                                                                            {{ $user_type->name }}</option>
                                                                        @else
                                                                            <option value="{{ $user_type->slug }}">
                                                                                {{ $user_type->name_ar }}</option>
                                                                        @endif
                                                                    @endif
                                                                @endif    
                                                            @endforeach
                                                        </select>
                                                        <input type="hidden" name="inp_user_type" id="inp_user_type">
                                                    </div>
                                                    <div id="user-type-error"></div>
                                                </div>
                                                <?php
                                    $user =  Auth::user();
                                    if(Session::has('entered_project_id')) {
                                       ?>
                                                <div>
                                                    <input type="hidden" id="project_id" name="project_id"
                                                        value="{{ Session::get('entered_project_id') }}">
                                                </div>
                                                <?php
                                    } else {
                                       ?>
                                                <div class="form-group mb-20"
                                                    style="{{ auth()->user()->user_type == 'admin' ? 'display: none' : '' }}">
                                                </div>
                                                <?php
                                    }
                                    ?>


{{--                                                <div class="form-group mb-20" id="store_keeper_info">--}}
{{--                                                    <div class="store_keeper mb-20">--}}
{{--                                                        <label for="store_keeper_name">--}}
{{--                                                            {{ __('user_management_module.user_forms.label.store_keeper_name') }}<small--}}
{{--                                                                class="required">*</small>--}}
{{--                                                        </label>--}}
{{--                                                        <input type="text" class="form-control" id="store_keeper_name"--}}
{{--                                                            name="store_keeper_name"--}}
{{--                                                            placeholder="{{ __('user_management_module.user_forms.label.store_keeper_name') }}">--}}
{{--                                                        <div id="store_keeper-error"></div>--}}
{{--                                                    </div>--}}
{{--                                                    <div class="store_keeper mb-20">--}}
{{--                                                        <label for="store_keeper_id">--}}
{{--                                                            {{ __('user_management_module.user_forms.label.store_keeper_id') }}--}}
{{--                                                        </label>--}}
{{--                                                        <input type="text" class="form-control" id="store_keeper_id"--}}
{{--                                                            name="store_keeper_id"--}}
{{--                                                            placeholder="{{ __('user_management_module.user_forms.label.store_keeper_id') }}">--}}
{{--                                                    </div>--}}
{{--                                                    <div class="store_keeper mb-20">--}}
{{--                                                        <label for="store_keeper_email">--}}
{{--                                                            {{ __('user_management_module.user_forms.label.store_keeper_email') }}<small--}}
{{--                                                                class="required">*</small>--}}
{{--                                                        </label>--}}
{{--                                                        <input type="text" class="form-control"--}}
{{--                                                            id="store_keeper_email" name="store_keeper_email"--}}
{{--                                                            placeholder="{{ __('user_management_module.user_forms.label.store_keeper_email') }}">--}}
{{--                                                        <div id="store_keeper-error"></div>--}}
{{--                                                    </div>--}}
{{--                                                    <div class="store_keeper mb-20">--}}
{{--                                                        <label for="store_keeper_department">--}}
{{--                                                            {{ __('user_management_module.user_forms.label.store_keeper_department') }}--}}
{{--                                                        </label>--}}
{{--                                                        <input type="text" class="form-control"--}}
{{--                                                            id="store_keeper_department" name="store_keeper_department"--}}
{{--                                                            placeholder="{{ __('user_management_module.user_forms.label.store_keeper_department') }}">--}}
{{--                                                    </div>--}}
{{--                                                    <div class="store_keeper mb-20">--}}
{{--                                                        <label for="store_keeper_service_provider">--}}
{{--                                                            {{ __('user_management_module.user_forms.label.store_keeper_service_provider') }}--}}
{{--                                                        </label>--}}
{{--                                                        <input type="text" class="form-control"--}}
{{--                                                            id="store_keeper_service_provider"--}}
{{--                                                            name="store_keeper_service_provider"--}}
{{--                                                            placeholder="{{ __('user_management_module.user_forms.label.store_keeper_service_provider') }}">--}}
{{--                                                        <div class="d-flex mt-2">--}}
{{--                                                            <img src="{{ asset('img/performance_indicator/info.svg') }}"--}}
{{--                                                                class="mr-2" alt="info">--}}
{{--                                                            <p class="mb-0">--}}
{{--                                                                {{ __('user_management_module.user_forms.label.store_keeper_service_provider_help') }}--}}
{{--                                                            </p>--}}
{{--                                                        </div>--}}
{{--                                                    </div>--}}
{{--                                                    <div class="form-group mb-20 store_keeper">--}}
{{--                                                        <label--}}
{{--                                                            for="phone2">{{ __('user_management_module.user_forms.label.emp_phone') }}--}}
{{--                                                        </label>--}}
{{--                                                        <div class="input-group mb-3 phone-ltr">--}}
{{--                                                            <div class="input-group-prepend">--}}
{{--                                                                <span class="input-group-text"--}}
{{--                                                                    id="basic-addon1">+966</span>--}}
{{--                                                            </div>--}}
{{--                                                            <input type="tel" class="form-control" id="phone2"--}}
{{--                                                                name="phone2" placeholder="*********">--}}
{{--                                                        </div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}




                                                <div class="form-group mb-20 company_info" id="fg-company-info">
                                                    <div class="service_provider">
                                                        <label for="service_provider">
                                                            {{ __('user_management_module.user_forms.label.company_name') }}<small
                                                                class="required">*</small>
                                                        </label>
                                                        <select class="form-control" id="service_provider"
                                                            name="service_provider">
                                                            <option value="" selected disabled>
                                                                {{ __('user_management_module.user_forms.label.company_name') }}
                                                            </option>
                                                            @foreach ($data['companyList'] as $company)
                                                                <option value="{{ $company->id }}">{{ $company->name }}
                                                                    - {{ $company->service_provider_id }}</option>
                                                            @endforeach
                                                        </select>
                                                        <div id="service_provider-error"></div>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-20 worker_info">
                                                    <div class="usertype_option">
                                                        <div id="supervisor_id_label">
                                                        <label for="supervisor_id">
                                                            {{ __('user_management_module.user_forms.label.worker_admin') }}
                                                            <small class="required">*</small>
                                                        </label>
                                                        </div>
                                                        <select class="form-control" id="supervisor_id"
                                                            name="supervisor_id[]" multiple>
                                                            <option value="" disabled>
                                                                {{ __('user_management_module.user_forms.label.worker_admin') }}
                                                            </option>
                                                        </select>
                                                        <div id="supervisor_id-error"></div>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-20 sup_info">
                                                    <!-- <input type="hidden" id="sp_admin_id" name="sp_admin_id" > -->
                                                    <div class="usertype_option">
                                                        <label for="sp_admin_id">
                                                            {{ __('user_management_module.user_forms.label.employee_admin') }}
                                                            <small class="required">*</small>
                                                        </label>
                                                        <select class="form-control" id="sp_admin_id" name="sp_admin_id">
                                                            <option value="" selected disabled>
                                                                {{ __('user_management_module.user_forms.label.employee_admin') }}
                                                            </option>
                                                        </select>
                                                        <div id="sp_admin_id-error"></div>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-20 building_admin_info">
                                                    <div class="usertype_option">
                                                        <label for="building_admin">
                                                            {{ __('user_management_module.user_forms.label.employee_admin') }}
                                                            <small class="required">*</small>
                                                        </label>
                                                        <select class="form-control" name="building_admin"
                                                            id="building_admin">
                                                            <option value="" selected disabled>
                                                                {{ __('user_management_module.user_forms.label.employee_admin') }}
                                                            </option>
                                                            @if (isset($data['building_manager_list']))
                                                                @foreach ($data['building_manager_list'] as $building_manager)
                                                                    <option value="{{ $building_manager->id }}">
                                                                        {{ $building_manager->name }}</option>
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                        <div id="building_admin-error"></div>
                                                    </div>
                                                </div>
                                                <input type="hidden" name="employee_admin_id" id="employee_admin_id"
                                                    value="">
                                                <div class="form-group mb-20 user_info" id="user_type_block">
                                                    <div id="interchangelabelwrk">
                                                        <label for="emp_name"
                                                            id="user_type_name">{{ __('user_management_module.user_forms.label.worker_name') }}<small
                                                                class="required">*</small></label>
                                                    </div>
                                                    <div id="interchangelabelemp">
                                                        <label for="emp_name" id="user_type_name">
                                                            <span
                                                                id="user_type_name_label">{{ __('user_management_module.user_forms.label.emp_name') }}</span>
                                                            <small class="required">*</small></label>
                                                    </div>
                                                    <input type="text" class="form-control" name="name"
                                                        id="name"
                                                        placeholder="{{ __('user_management_module.user_forms.place_holder.emp_name') }}">
                                                </div>
                                                <div class="form-group mb-20 user_info employee_id">
                                                    <div class="employee_label">
                                                        <label
                                                            for="emp_id">{{ __('user_management_module.user_forms.label.emp_id') }}</label>
                                                    </div>
                                                    <input type="text" maxlength="10" class="form-control"
                                                        name="emp_id" onkeyup="getEmpiddata()" id="emp_id"
                                                        placeholder="{{ __('user_management_module.user_forms.place_holder.emp_id') }}">
                                                </div>
                                                <div class="form-group mb-20 user_info email_box">
                                                    <div class="employee_email_various">
                                                        <label
                                                            for="emp_email">{{ __('user_management_module.user_forms.label.emp_email') }}
                                                            <small class="required">*</small></label>
                                                    </div>
                                                    <input type="email" class="form-control" id="email"
                                                        name="email" placeholder="<EMAIL>">
                                                </div>

                                                <div class="form- group mb-20 user_info nationality_select">
                                                    <div class="">
                                                        <label for="nationality_id">
                                                            {{ __('user_management_module.user_forms.label.nationality') }}
                                                            <small class="required">*</small>
                                                        </label>
                                                        <select class="form-control" id="nationality_id"
                                                            name="nationality_id">
                                                            <option value="" selected disabled>
                                                                {{ __('user_management_module.user_forms.label.choose_a_nationality') }}
                                                            </option>

                                                            @foreach ($data['nationalities'] as $nationality)
                                                                @if (App::getLocale() == 'en')
                                                                    <option value="{{ $nationality->id }}">
                                                                        {{ $nationality->name_en }}</option>
                                                                @else
                                                                    <option value="{{ $nationality->id }}">
                                                                        {{ $nationality->name_ar }}</option>
                                                                @endif
                                                            @endforeach

                                                        </select>
                                                    </div>
                                                    <div id="nationality-id-error"></div>
                                                </div>

                                                <div class="form- group mb-20 user_info favorite_language_select">
                                                    <div class="">
                                                        <label for="favorite_language">
                                                            {{ __('user_management_module.user_forms.label.favorite_language') }}
                                                        </label>
                                                        <select class="form-control" id="favorite_language"
                                                            name="favorite_language">
                                                            <option value="" selected disabled>
                                                                {{ __('user_management_module.user_forms.label.choose_a_favorite_language') }}
                                                            </option>

                                                            <option value="en">
                                                                {{ __('user_management_module.user_forms.label.english') }}
                                                            </option>
                                                            <option value="ar">
                                                                {{ __('user_management_module.user_forms.label.arabic') }}
                                                            </option>
                                                            <option value="ur">
                                                                {{ __('user_management_module.user_forms.label.urdu') }}
                                                            </option>

                                                        </select>
                                                    </div>
                                                    <div id="favorite-language-error"></div>
                                                </div>

                                                <div class="form- group mb-20 user_info profession_select">
                                                    <div class="">
                                                        <label for="profession_id">
                                                            {{ __('user_management_module.user_forms.label.select_profession_heading') }}
                                                            <small class="required">*</small>
                                                        </label>
                                                        <select class="form-control" id="profession_id"
                                                            name="profession_id">
                                                            <option value="" selected disabled>
                                                                {{ __('user_management_module.user_forms.label.choose_a_profession') }}
                                                            </option>

                                                            @foreach ($data['workerProfessions'] as $profession)
                                                                @if (App::getLocale() == 'en')
                                                                    <option value="{{ $profession->id }}">
                                                                        {{ $profession->profession_en }}</option>
                                                                @else
                                                                    <option value="{{ $profession->id }}">
                                                                        {{ $profession->profession_ar }}</option>
                                                                @endif
                                                            @endforeach

                                                        </select>
                                                    </div>
                                                    <div id="profession-id-error"></div>
                                                </div>
                                                
                                        
                                
                                       
                                             
                                                <div class="form-group mb-20 user_info profession">
                                                    <div class="emp_dept_label-div">
                                                        <label for="emp_dept">
                                                            <span
                                                                class="emp_dept_label">{{ __('user_management_module.user_forms.label.emp_dept') }}</span>
                                                            <span
                                                                class="worker_info">{{ __('user_management_module.user_forms.label.profession') }}</span>
                                                        </label>
                                                    </div>
                                                    <input type="text" class="form-control" name="emp_dept"
                                                        id="emp_dept"
                                                        placeholder="{{ __('user_management_module.user_forms.place_holder.emp_dept') }}">
                                                </div>

                                                <div class="form-group mb-20 user_info">
                                                    <label
                                                        for="emp_phone_number">{{ __('user_management_module.user_forms.label.emp_phone') }}
                                                    </label>
                                                    <div class="input-group mb-3 phone-ltr">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text" id="basic-addon1">+966</span>
                                                        </div>
                                                        <input type="tel" class="form-control" id="phone"
                                                            name="phone" placeholder="*********">
                                                    </div>
                                                </div>

                                                @if(in_array(auth()->user()->user_type,['super_admin','osool_admin','admin','admin_employee']))
                                                    <div class="form-group mb-20 area-bma-manager" id="area-bma-manager">
                                                        <div class="mt-4 d-flex ">
                                                            <div>
                                                                <div class="checkbox-theme-default custom-checkbox ">
                                                                    <input class="checkbox" type="checkbox"
                                                                           id="bma_area_manager" value="1"
                                                                           name="bma_area_manager"
                                                                    >
                                                                    <label for="bma_area_manager"
                                                                           onfocus="theFocus(this);"
                                                                           data-toggle="tooltip" data-placement="auto"
                                                                           title="">
                                                                    <span class="checkbox-text text-dark">
                                                                        &nbsp;
                                                                    </span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <p class="mb-2">
                                                                    {{__('user_management_module.user_previleges.assign_bma_area_manager')}}
                                                                </p>
                                                                <p class="mb-2 text-danger unique_area_manager_error"
                                                                   id="unique_area_manager_error">
                                                                    {{__('user_management_module.user_validation.unique_area_manager')}}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif

                                                @if(\App\Services\AkauntingService::allow() && auth()->user()->user_type == 'admin')
                                                <div class="form-group mb-20 vendor_akaunting " id="vendor_akaunting">
                                                    <div class="mt-4 d-flex " >
            
                                                        <div >
                                                            <div class="checkbox-theme-default custom-checkbox ">
                                                                <input class="checkbox" type="checkbox"
                                                                    id="isAkaunting_Vendor" value="1"
                                                                    name="isAkaunting_Vendor"
                                                                   >
                                                                <label for="isAkaunting_Vendor" onfocus="theFocus(this);" data-toggle="tooltip" data-placement="auto" title="{{ __('purchase_request.common.message_option_decription_create') }}">
                                                                    <span class="checkbox-text text-dark">
                                                                        &nbsp;
                                                                    </span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <h5 class="mb-2">
                                                                {{ __('purchase_request.common.mark_sp_admin_as_vendor') }}
                                                            </h5>
                                                            <span>{{ __('purchase_request.common.message_once_this_option_as_enabled_create') }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                @endif
                                                
                                                @if(auth()->user()->user_type == 'admin' || 
                                                    auth()->user()->user_type == 'admin_employee' ||
                                                    auth()->user()->user_type == 'sp_admin' ||
                                                    auth()->user()->user_type == 'super_admin' ||
                                                    auth()->user()->user_type == 'sp_worker'
                                                )
                                                    <div class="form-group mb-20 store_keeper_info 2024-0-create" id="fg-service-provider">
                                                        <div class="service_provider">
                                                            <label for="service_provider">
                                                                {{ __('user_management_module.user_forms.label.store_keeper_service_provider') }}
                                                            </label>
                                                            <select class="form-control" id="store_keeper_service_provider"
                                                                    name="service_provider">
                                                                <option value="" selected disabled>
                                                                    {{ __('user_management_module.user_forms.label.store_keeper_service_provider') }}
                                                                </option>
                                                                @foreach ($data['companyList'] as $company)
                                                                    <option value="{{ $company->id }}">{{ $company->name }}
                                                                        - {{ $company->service_provider_id }}</option>
                                                                @endforeach
                                                            </select>
                                                            <div class="d-flex mt-2">
                                                                <img src="{{ asset('img/performance_indicator/info.svg') }}"
                                                                    class="mr-2" alt="info">
                                                                <p class="mb-0">
                                                                    {{ __('user_management_module.user_forms.label.store_keeper_service_provider_help') }}
                                                                </p>
                                                            </div>
                                                            <div id="store_keeper_deselect">
                                                                <p>
                                                                    <a href="javascript:void(0)"
                                                                    onclick="removeServiceProvider()">
                                                                        {{ __('user_management_module.user_forms.label.store_keeper_service_provider_deselect') }}
                                                                    </a>
                                                                </p>
                                                            </div>
                                                            <div id="store_keeper_service_provider-error"></div>
                                                        </div>
                                                    </div>
                                                @endif
                                                <input type="hidden" name="country_id" id="country_id1" value="1">
                                                <input type="hidden" name="city_id" id="city_id1" value="1">
                                                <input type="hidden" id="ajax_check_useremail_unique" value="{{ route('users.ajax_check_unique_useremail') }}">
                                                <input type="hidden" id="ajax_check_userphone_unique" value="{{ route('users.ajax_check_unique_usernumber') }}">
                                                {{--<input type="hidden" id="ajax_check_userphone_unique" value="{{ route('users.ajax_check_unique_usernumber') }}">--}}
                                                <input type="hidden" id="ajax_check_employee_id_unique" value="{{ route('users.ajax_check_unique_emp_id') }}">
                                                <input type="hidden" id="get_sp_admin_route_url" value="{{ route('users.ajax-spadmin-list') }}">
                                                <div class="button-group d-flex pt-25 justify-content-end">
                                                    <a href="{{ route('users.list') }}"
                                                        class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md">{{ __('user_management_module.user_button.cancel') }}</a>
                                                    <button type="submit"
                                                        class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{ __('user_management_module.user_button.save_next') }}
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <!-- ends: card -->
                            </div>
                            <!-- ends: col -->
                        </div>
                    </div>
                    <!-- ends: col -->
                </div>
            </div>
            <!-- End: .global-shadow-->
        </div>
    </div>
    <!-- CONFIRM DELETE Photo MODAL START -->
    <div class="modal new-member  bouncein-new" id="confirmDeletePhoto" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content  radius-xl  bouncein-new">
                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-3 fs-20"><i class="fa fa-exclamation-circle mr-1 color-warning"
                                aria-hidden="true"></i>
                            {{ __('data_properties.property_forms.label.sure_remove_photo') }}
                        </h2>
                    </div>
                    <div class="button-group d-flex justify-content-end pt-25">
                        <div class="button-group d-flex justify-content-end pt-25">
                            <button type="button" class="btn btn-light   btn-squared text-capitalize"
                                data-dismiss="modal" aria-label="Close">
                                {{ __('data_properties.property_button.cancel') }}
                            </button>
                            <button type="button"
                                class="btn btn-danger btn-default btn-squared text-capitalize confirm_remove_photo"
                                data-dismiss="modal" aria-label="Close">
                                {{ __('data_properties.property_button.remove') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <!-- CONFIRM DELETE Photo MODAL ENDS -->
@endsection
@section('scripts')

    <script type="text/javascript" src="{{ asset('js/admin/users/create.js') }}"></script>
    <script type="text/javascript">
     
     sessionStorage.setItem('end_creation', 'false');
        $("#user_type,#service_provider,#sp_admin_company_list,#supervisor_id,#sp_admin_id,#building_admin,#spga_admin,#country_id,#city_id,#store_keeper_service_provider")
            .select2({
                placeholder: translations.data_contract.contract_forms.place_holder.please_choose,
                dropdownCssClass: "tag",
                allowClear: true,
                language: {
                    noResults: () => translations.general_sentence.validation.No_results_found,
                }
            });

        $("#profession_id").select2({
            placeholder: translations.user_management_module.user_forms.label.choose_a_profession,
            dropdownCssClass: "tag",
            language: {
                noResults: () => translations.general_sentence.validation.No_results_found,
            }
        });

        $("#nationality_id").select2({
            placeholder: translations.user_management_module.user_forms.label.choose_a_nationality,
            dropdownCssClass: "tag",
            language: {
                noResults: () => translations.general_sentence.validation.No_results_found,
            }
        });

        $("#favorite_language").select2({
            placeholder: translations.user_management_module.user_forms.label.choose_a_favorite_language,
            dropdownCssClass: "tag",
            language: {
                noResults: () => translations.general_sentence.validation.No_results_found,
            }
        });
    </script>
    <script type="text/javascript">
        $(".unique_area_manager_error").hide()
        $('#store_keeper_info').hide();
        $(".store_keeper_info").hide();
        $('.user_info').hide();
        
        $('.sup_info').hide();
        $('.worker_info').hide();
        $('.company_info').hide();
        $('.building_admin_info').hide();
        $('.area-bma-manager').hide();
        $('.spga_admin_info').hide();
        $('.profession_select').hide();
        $('.nationality_select').hide();
/*         $('.vendor_akaunting').hide(); */
        $('.favorite_language_select').hide();
        $("#user_type").on("change", function() {
        var user_type = $(this).val();
       var vendorAkaunting = $("#vendor_akaunting");

    if (user_type === 'sp_admin') {
        vendorAkaunting.removeAttr('hidden'); 
    } else {
        vendorAkaunting.attr('hidden', true); 
    }
     }).trigger("change"); 

        if( $("#user_type").length > 0){
            if ($("#user_type").val() == 'sp_worker' || $("#user_type").val() == 'team_leader') {
                $("#service_provider").show();
            }else{
                $("#service_provider").hide();
            }
        }
        $("#service_provider").on("change", function() {
            let id = $(this).val();
            var app_url = $('#app_url').val();
            sessionStorage.setItem('createuser_service_provider', JSON.stringify($(this).val()));
            $.ajax({
                url: app_url + "/user/ajax/get_sp_list/",
                method: "GET",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    id: id,
                },
                dataType: "json",
                beforeSend: function() {},
                success: function(data) {

                    var user_type = $("#user_type").val();

                    $("#supervisor_id").empty();

                    $("#supervisor_id").html('<option value="" disabled>' + translations
                        .user_management_module.user_forms.label.choose_supervisor + '</option>');
                    $.each(data.supervisor_list, function(key, value) {
                        $("#supervisor_id").append(
                            $("<option></option>")
                            .attr("value", value.id)
                            .text(value.name)
                        );
                    });

                    $("#sp_admin_id").empty();
                    $("#sp_admin_id").html('<option value="" selected disabled>' + translations
                        .user_management_module.user_forms.label.employee_admin + '</option>');
                    var selected = "";
                    if (data.sp_admin_list.length == 1) {
                        var selected = "selected";
                    }
                    $.each(data.sp_admin_list, function(key, value) {
                        $("#sp_admin_id").append(
                            $("<option " + selected + "></option>")
                            .attr("value", value.id)
                            .text(value.name)
                        );
                        // On request of Mr.Haitham I have changed this.
                        //$("#sp_admin_id").val(value.id);
                    });
                },
                error: function(data) {
                    var errors = $.parseJSON(data.responseText);
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 5000,
                        positionClass: "toast-top-center",
                        progressBar: true,
                    });
                },
            });
        });

        $("#store_keeper_service_provider").on("change", function () {
            sessionStorage.setItem('createuser_service_provider', JSON.stringify($(this).val()));
        });
                
        $('#supervisor_id').on('select2:select', function(e) {
            sessionStorage.setItem('createuser_supervisor_id', JSON.stringify($(this).val()));
        });

        $('#supervisor_id').on('select2:unselecting', function(e) {
            let value_cnt1 = $(this).val();
            value_cnt1 = jQuery.grep(value_cnt1, function(value) {
                return value != e.params.args.data.id;
            });
            sessionStorage.setItem('createuser_supervisor_id', JSON.stringify(value_cnt1));
        });

        var storedusertypeValues = sessionStorage.getItem('createuser_user_type');
        if (storedusertypeValues) {
            var selecteddusertypeValues = JSON.parse(storedusertypeValues);
            // Set the selected values in the Select2 dropdown
            $('#user_type').val(selecteddusertypeValues).trigger('change');
            var user_type_val = selecteddusertypeValues;

            $('#inp_user_type').val(user_type_val) //@flip2@ to get the user_type
            if (user_type_val == 'sp_worker') {
                $('#country_id').prop('required', false);
                $('#city_id').prop('required', false);
                $(".phone_required_mark").hide();
                $('#privi').text('{{ __('user_management_module.common.password') }}');
            } else {
                $(".phone_required_mark").show();
                $('#privi').text('{{ __('user_management_module.common.user_previleges') }}');
            }

            $.ajax({
                url: $('#get_sp_admin_route_url').val(),
                method: "GET",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    user_type_val: user_type_val
                },
                dataType: "json",
                beforeSend: function() {},
                success: function(data) {
                    $('#service_provider').html('');
                    if (data.status == 1) {
                        var companyList = data.companyList;
                        var optHtml = '';
                        optHtml += '<option value="" selected disabled>' + translations.user_management_module
                            .user_forms.label.company_name + '</option>';
                        if (companyList.length > 0) {
                            for (var i = 0; i < companyList.length; i++) {
                                optHtml += '<option value="' + companyList[i].id + '">' + companyList[i].name +
                                    '-' + companyList[i].service_provider_id + '</option>';
                            }
                        } else {
                            optHtml = '<option selected disabled value=""> ' + translations
                                .configration_checklist.checklist_forms.place_holder
                                .no_service_provider_avaliable + ' </option>';
                        }

                        $('#service_provider').html(optHtml);
                        $('#store_keeper_service_provider').html(optHtml);
                        var storeduserserviceproviderValues = sessionStorage.getItem(
                            'createuser_service_provider');
                        if (storeduserserviceproviderValues) {
                            if (user_type_val !== 'store_keeper')
                                $("#service_provider").val(JSON.parse(storeduserserviceproviderValues)).trigger('change');
                            else
                                $("#store_keeper_service_provider").val(JSON.parse(storeduserserviceproviderValues)).trigger('change');
                            
                            var app_url = $('#app_url').val();
                            $.ajax({
                                url: app_url + "/user/ajax/get_sp_list/",
                                method: "GET",
                                data: {
                                    _token: $('meta[name="csrf-token"]').attr("content"),
                                    id: JSON.parse(storeduserserviceproviderValues),
                                },
                                dataType: "json",
                                beforeSend: function() {},
                                success: function(data) {

                                    var user_type = $("#user_type").val();

                                    $("#supervisor_id").empty();

                                    $("#supervisor_id").html('<option value="" disabled>' +
                                        translations.user_management_module.user_forms.label
                                        .choose_supervisor + '</option>');
                                    $.each(data.supervisor_list, function(key, value) {
                                        $("#supervisor_id").append(
                                            $("<option></option>")
                                            .attr("value", value.id)
                                            .text(value.name)
                                        );
                                    });

                                    var storedusersupervisoridValues = sessionStorage.getItem(
                                        'createuser_supervisor_id');
                                    if (storedusersupervisoridValues) {
                                        $('#supervisor_id').val(JSON.parse(
                                            storedusersupervisoridValues)).trigger('change');
                                    }

                                    $("#sp_admin_id").empty();
                                    $("#sp_admin_id").html('<option value="" selected disabled>' +
                                        translations.user_management_module.user_forms.label
                                        .employee_admin + '</option>');
                                    var selected = "";
                                    if (data.sp_admin_list.length == 1) {
                                        var selected = "selected";
                                    }
                                    $.each(data.sp_admin_list, function(key, value) {
                                        $("#sp_admin_id").append(
                                            $("<option " + selected + "></option>")
                                            .attr("value", value.id)
                                            .text(value.name)
                                        );
                                        // On request of Mr.Haitham I have changed this.
                                        //$("#sp_admin_id").val(value.id);
                                    });

                                    var storeduserspadminValues = sessionStorage.getItem(
                                        'createuser_sp_admin_id');
                                    if (storeduserspadminValues) {
                                        $('#sp_admin_id').val(JSON.parse(storeduserspadminValues))
                                            .trigger('change');
                                    }
                                },
                                error: function(data) {
                                    var errors = $.parseJSON(data.responseText);
                                    toastr.error(data, translations.general_sentence.validation
                                        .Error, {
                                            timeOut: 5000,
                                            positionClass: "toast-top-center",
                                            progressBar: true,
                                        });
                                },
                            });
                        }
                        return true;
                    }
                },
                error: function(data) {
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 5000,
                        positionClass: "toast-top-center",
                        progressBar: true,
                    });
                },
            });


            //alert(user_type_val);
            // if(user_type_val=='admin'||user_type_val=='spga_employee'||user_type_val=='building_manager'||user_type_val=='building_manager_employee'||user_type_val=='sp_admin')
            if (user_type_val != '') {
                $('.user_info').show();
                if (user_type_val == 'sp_admin') {
                    $('#interchangelabelemp').html(
                        `<label for="emp_name">{{ __('user_management_module.user_forms.label.create_user_as_sp_admin') }}</label> <small class="required">*</small>`
                        );
                    document.getElementById("name").placeholder =
                        "{{ __('user_management_module.user_forms.label.create_user_as_sp_admin') }}";

                    $('#2').hide();
                   
                    $('#2').next('.current').hide();
                    // @flip1@ hide statusbar for SPA
                    $('#3 span:first').html('2');
                    $('#4 span:first').html('3');
                    $('.vendor_akaunting').show();
                } else if (user_type_val == 'supervisor') {
                    $('.employee_label').html(
                        `<label for="emp_id">{{ __('user_management_module.user_forms.label.emp_id') }}<small class="required">*</small></label>`
                        );

                    $('#interchangelabelemp').html(
                        `<label for="emp_name">{{ __('user_management_module.user_forms.label.supervisor_name') }}<small class="required">*</small></label>`
                        );
                    document.getElementById("name").placeholder =
                        "{{ __('user_management_module.user_forms.label.supervisor_name') }}";

                    $('.employee_id').html(
                        `<label for="emp_id">{{ __('user_management_module.user_forms.label.supervisor_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.supervisor_id') }}">`
                        );

                    $('.employee_email_various').html(
                        `<label for="emp_email">{{ __('user_management_module.user_forms.label.supervisor_email') }}<small class="required">*</small></label>`
                        );
                    document.getElementById("email").placeholder =
                        "{{ __('user_management_module.user_forms.label.supervisor_email') }}";

                    $('.emp_dept_label-div').html(
                        `<label for="emp_dept">{{ __('user_management_module.user_forms.label.supervisor_department') }}</label>`
                        );
                    document.getElementById("emp_dept").placeholder =
                        "{{ __('user_management_module.user_forms.label.supervisor_department') }}";

                    $('#2').show();
                    $('#2').next('.current').show();
                    $('#3').show();
                    $('#3').next('.current').show();
                    $('#4 span:first').html('4');
                }else if (user_type_val == 'store_keeper') {
                    // console.log("User Type Value #1: "+user_type_val)
                } else {
                    //$('#user_type_name').html('Service Provider Name');
                    $('#2').show();
                    $('#2').next('.current').show();
                    $('#3').show();
                    $('#3').next('.current').show();
                    $('#4 span:first').html('4');
                    $('.company_info').hide();
                    $('#vendor_akaunting').show();
                    $('#service_provider').prop("required", false);
                    $('#interchangelabelemp').html(`<label for="emp_name">` + translations.user_management_module.user_forms
                        .label.emp_name + `<small class="required">*</small></label>`);
                }

                if (user_type_val == 'admin' || user_type_val == 'admin_employee') {
                    $('#2').hide();
                    $('#2').next('.current').hide();
                    $('#3').hide();
                    $('#3').next('.current').hide();
                    $('#4 span:first').html('2');
                }


                if (user_type_val == 'building_manager') {
                    $('#interchangelabelemp').html(`<label for="emp_name">` + translations.user_management_module.user_forms
                        .label.emp_name + `<small class="required">*</small></label>`);
                    $('.company_info').hide();
                    $('#service_provider').prop("required", false);
                    $(".area-bma-manager").show();
                }else{
                    $('.area-bma-manager').hide();
                    $('#area-bma-manager').prop('checked', false);
                }



                if (user_type_val == 'admin_employee') {
                    $('.spga_admin_info').show();
                    $('#spga_admin').prop("required", true);
                } else {
                    $('.spga_admin_info').hide();
                    $('#spga_admin').prop("false", true);
                }
                //let storeKeeperOptionValue = $('#store_keeper_id').val();
                //console.log(storeKeeperOptionValue);
                // console.log(user_type_val);
                if (user_type_val == 'store_keeper') {

                    console.log("User Type Value #2: "+user_type_val)
                    
                    {{--$('#interchangelabelemp').html(--}}
                    {{--    `<label for="emp_name">{{ __('user_management_module.user_forms.label.store_keeper_name') }}<small class="required">*</small></label>`--}}
                    {{--);--}}
                    {{--document.getElementById("name").placeholder =--}}
                    {{--    "{{ __('user_management_module.user_forms.label.store_keeper_name') }}";--}}

                    // $('#store_keeper_info').show();
                    // $('#phoneId').show();
                    // $('#store_keeper_name').prop("required", true);
                    // $('.user_info').hide();
                    // $('.email_box').hide();
                }

                if (user_type_val == 'building_manager_employee') {
                    $('.building_admin_info').show();
                    $('#building_admin').prop("required", true);
                } else {
                    $('.building_admin_info').hide();
                    $('#building_admin').prop("required", false);
                }

                if (user_type_val == 'sp_admin' || user_type_val == 'supervisor' || user_type_val == 'sp_worker' || user_type_val == 'team_leader') {

                    if (user_type_val == 'sp_worker') {

                        // @flip2@ added star for worker id
                        $('.employee_id').html(
                            `<label for="worker_id" id="lable-wi">{{ __('user_management_module.user_forms.label.worker_id') }} <small class="required">*</small> <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{ __('user_management_module.common.worker_id_description') }}"></i></label>
            <input maxlength="10" type="text" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="4466">`
                            );

                        $('.emp_dept_label-div').html(
                            `<label for="emp_dept">{{ __('user_management_module.user_forms.label.profession') }} <small class="required">*</small></label>`
                            );
                    }
                    else if (user_type_val == 'team_leader') {

                        // @flip2@ added star for worker id
                        $('.employee_id').html(
                            `<label for="worker_id" id="lable-wi">{{ __('user_management_module.team_leader_module.team_leader_id') }} <small class="required">*</small> <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{ __('user_management_module.common.worker_id_description') }}"></i></label>
            <input maxlength="10" type="text" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="4466">`
                            );

                    } else if (user_type_val == 'supervisor') {
                        $('.employee_id').html(
                            `<label for="emp_id">{{ __('user_management_module.user_forms.label.supervisor_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.supervisor_id') }}">`
                            );
                    } else {

                        $('.employee_id').html(
                            `<label for="emp_id">{{ __('user_management_module.user_forms.label.emp_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.emp_id') }}">`
                            );
                    }
                    $('.company_info').show();
                    $('#vendor_akaunting').show();
                    $('#service_provider').prop("required", true);
                } else {
                    if (user_type_val == 'sp_worker') {

                        $('.employee_id').html(
                            `<label for="worker_id">{{ __('user_management_module.user_forms.label.worker_id') }}  <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle='tooltip' data-placement="top" title="{{ __('user_management_module.common.worker_id_description') }}"></i></label>
           <input maxlength="10" type="text" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="4466">`
                            );

                    }
                    else if (user_type_val == 'team_leader') {

                        $('.employee_id').html(
                            `<label for="worker_id">{{ __('user_management_module.team_leader_module.team_leader_id') }}  <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle='tooltip' data-placement="top" title="{{ __('user_management_module.common.worker_id_description') }}"></i></label>
           <input maxlength="10" type="text" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="4466">`
                            );

                    } else {

                        $('.employee_id').html(
                            `<label for="emp_id">{{ __('user_management_module.user_forms.label.emp_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.emp_id') }}">`
                            );
                    }

                    $('.company_info').hide();
                    $('#service_provider').prop("required", false);
                }

                if (user_type_val == 'supervisor') {
                    $('.sup_info').show();
                    $('#sp_admin_id').prop("required", true);
                } else {
                    $('.sup_info').hide();
                    $('#sp_admin_id').prop("required", false);
                }

                if (user_type_val == 'sp_worker') {
                    $('#interchangelabelemp').hide();
                    $('#interchangelabelwrk').show();
                    $('#name').attr('placeholder', translations.user_management_module.user_forms.label.worker_name);
                    $('#interchangelabelwrk #user_type_name').html(translations.user_management_module.user_forms.label
                        .worker_name + ' <small class="required">*</small>');
                    $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label.profession);
                    $('.worker_info').show();
                    $('.emp_dept_label').hide();
                    $('.email_box').hide();
                    $('#email').attr("type", "hidden");
                    $('.user-country-city-block').hide();
                    $('#supervisor_id').prop("required", true);
                }
                else if (user_type_val == 'team_leader') {
                    $('#interchangelabelemp').hide();
                    $('#interchangelabelwrk').show();
                    $('#name').attr('placeholder', translations.user_management_module.team_leader_module.team_leader_name);
                    $('#interchangelabelwrk #user_type_name').html(translations.user_management_module.team_leader_module.team_leader_name + ' <small class="required">*</small>');
                    //$('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label.profession);
                    $('.worker_info').show();
                    $('.emp_dept_label').hide();
                    $('.email_box').hide();
                    $('#email').attr("type", "hidden");
                    $('.user-country-city-block').hide();
                    $('#supervisor_id').prop("required", true);
                } else if (user_type_val == 'supervisor') {
                    $('.worker_info').hide();
                    $('#email').attr("type", "email");
                    $('#interchangelabelemp').show();
                    $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label
                        .supervisor_department);
                    $('#interchangelabelwrk').hide();
                    $('.user-country-city-block').show();
                } else {

                    $('#interchangelabelwrk').hide();
                    $('#interchangelabelemp').show();
                    if (user_type_val == 'sp_admin') {
                        $('#name').attr('placeholder', translations.user_management_module.user_forms.label
                            .create_user_as_sp_admin);
                    } else {
                        $('.company_info').hide();
                        $('#service_provider').prop("required", false);
                        $('#name').attr('placeholder', translations.user_management_module.user_forms.label.emp_name);
                    }
                    $('#interchangelabelemp #user_type_name_label').html(translations.user_management_module.user_forms
                        .label.emp_name + ' <small class="required">*</small>');
                    $('.emp_dept_label-div').html(
                        `<label for="emp_dept">{{ __('user_management_module.user_forms.label.emp_dept') }}</label>`);
                    $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label.emp_dept);

                    $('.employee_email_various').html(
                        `<label for="emp_email">{{ __('user_management_module.user_forms.label.emp_email') }}<small class="required">*</small></label>`
                        );
                    document.getElementById("email").placeholder =
                        "{{ __('user_management_module.user_forms.label.emp_email') }}";
                    $('#email').attr('placeholder', translations.user_management_module.user_forms.label.emp_email);

                    $('.worker_info').hide();
                    $('.emp_dept_label').show();
                    $('#email').attr("type", "email");
                    if (user_type_val !== 'store_keeper') {
                        $('.email_box').show();
                    }
                    $('#supervisor_id').prop("required", false);
                    $('.user-country-city-block').show();


                }

                if(user_type_val == 'store_keeper'){
                    $(".store_keeper_info").show();
                     $('#interchangelabelemp').html(
                        `<label for="emp_name">{{ __('user_management_module.user_forms.label.store_keeper_name') }}<small class="required">*</small></label>`
                    );
                    document.getElementById("name").placeholder =
                        "{{ __('user_management_module.user_forms.label.store_keeper_name') }}";

                    $('.employee_id').html(
                        `<label for="emp_id">{{ __('user_management_module.user_forms.label.store_keeper_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.emp_id') }}">`
                    );

                    $('.emp_dept_label-div').html(
                        `<label for="emp_dept">{{ __('user_management_module.user_forms.label.store_keeper_department') }}</label>`
                    );
                    $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label
                        .store_keeper_department);

                    $('.employee_email_various').html(
                        `<label for="emp_email">{{ __('user_management_module.user_forms.label.store_keeper_email') }}<small class="required">*</small></label>`
                    );
                    document.getElementById("email").placeholder =
                        "{{ __('user_management_module.user_forms.label.store_keeper_email') }}";
                    $('#email').attr('placeholder', translations.user_management_module.user_forms.label.store_keeper);
                }

                if (user_type_val == 'building_manager') {
                    var stored_bmaAreaManager = sessionStorage.getItem('createuser_is_area_bma_manager');
                    if (stored_bmaAreaManager) {
                        $("#bma_area_manager").prop('checked', JSON.parse(stored_bmaAreaManager));
                    }
                }

                var storedusernameValues = sessionStorage.getItem('createuser_name');
                if (storedusernameValues) {
                    $("#name").val(JSON.parse(storedusernameValues));
                }
                var storedusebuildingadminValues = sessionStorage.getItem('createuser_building_admin');
                if (storedusebuildingadminValues) {
                    $("#building_admin").val(JSON.parse(storedusebuildingadminValues)).trigger('change');
                }
                var storeduserempidValues = sessionStorage.getItem('createuser_emp_id');
                if (storeduserempidValues) {
                    $("#emp_id").val(JSON.parse(storeduserempidValues));
                }
                var storeduseremailValues = sessionStorage.getItem('createuser_email');
                if (storeduseremailValues) {
                    $("#email").val(JSON.parse(storeduseremailValues));
                }
                var storeduserempdeptValues = sessionStorage.getItem('createuser_emp_dept');
                if (storeduserempdeptValues) {
                    $("#emp_dept").val(JSON.parse(storeduserempdeptValues));
                }
                var storeduserphoneValues = sessionStorage.getItem('createuser_phone');
                if (storeduserphoneValues) {
                    $("#phone").val(JSON.parse(storeduserphoneValues));
                }
                var storeduserisAkaunting_Vendor = sessionStorage.getItem('createuser_isAkaunting_Vendor');
if (storeduserisAkaunting_Vendor) {
    $("#isAkaunting_Vendor").prop('checked', JSON.parse(storeduserisAkaunting_Vendor));
}


                var stored_nationality = sessionStorage.getItem('createuser_nationality');
                if (stored_nationality) {
                   $("#nationality_id").val(JSON.parse(stored_nationality)).change();
                }

                var stored_favorite = sessionStorage.getItem('createuser_favorite');
                if (stored_favorite) {
                    $("#favorite_language").val(JSON.parse(stored_favorite)).change();
                }

                var stored_profession = sessionStorage.getItem('createuser_profession');
                if (stored_profession) {
                    $("#profession_id").val(JSON.parse(stored_profession)).change();
                }
            }
        }
        $("#building_admin").on("change", function() {
            sessionStorage.setItem('createuser_building_admin', JSON.stringify($(this).val()));
        });
        $("#user_type").on("change", function() {
            var user_type_val = $(this).val();
            sessionStorage.setItem('createuser_user_type', JSON.stringify(user_type_val));
            $('#inp_user_type').val(user_type_val) //@flip2@ to get the user_type
            if (user_type_val == 'sp_worker') {
                $('#country_id').prop('required', false);
                $('#city_id').prop('required', false);
                $(".phone_required_mark").hide();
                $('.profession').hide();
                $('.profession_select').show();
                $('.nationality_select').show();
                $('.favorite_language_select').show();
                $('#privi').text('{{ __('user_management_module.common.password') }}');
                $("#service_provider").show();
            }
            else if (user_type_val == 'team_leader') {
                $('#country_id').prop('required', false);
                $('#city_id').prop('required', false);
                $(".phone_required_mark").hide();
                $('.profession').hide();
                $('.profession_select').show();
                $('.nationality_select').show();
                $('.favorite_language_select').show();
                $('#privi').text('{{ __('user_management_module.common.password') }}');
                $("#service_provider").show();
            } else {
                $('.profession').show();
                $(".phone_required_mark").show();
                $('.profession_select').hide();
                $('.nationality_select').hide();
                $('.favorite_language_select').hide();
                $('#privi').text('{{ __('user_management_module.common.user_previleges') }}');
                $("#service_provider").hide();
            }

            $.ajax({
                url: $('#get_sp_admin_route_url').val(),
                method: "GET",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    user_type_val: user_type_val
                },
                dataType: "json",
                beforeSend: function() {},
                success: function(data) {
                    $('#service_provider').html('');
                    $('#store_keeper_service_provider').html('');
                    if (data.status == 1) {
                        var companyList = data.companyList;
                        var optHtml = '';
                        optHtml += '<option value="" selected disabled>' + translations
                            .user_management_module.user_forms.label.company_name + '</option>';
                        if (companyList.length > 0) {
                            for (var i = 0; i < companyList.length; i++) {
                                optHtml += '<option value="' + companyList[i].id + '">' + companyList[i]
                                    .name + '-' + companyList[i].service_provider_id + '</option>';
                            }
                        } else {
                            optHtml = '<option selected disabled value=""> ' + translations
                                .configration_checklist.checklist_forms.place_holder
                                .no_service_provider_avaliable + ' </option>';
                        }

                        $('#service_provider').html(optHtml);
                        $('#store_keeper_service_provider').html(optHtml);
                        return true;
                    }
                },
                error: function(data) {
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 5000,
                        positionClass: "toast-top-center",
                        progressBar: true,
                    });
                },
            });

            if (user_type_val != '') {
                $('.user_info').show();
                $('.profession_select').hide();
                $('.nationality_select').hide();
                $('.favorite_language_select').hide();
                if (user_type_val == 'sp_admin') { //alert("i am here");
                    //$('#interchangelabelwrk').hide();
                    $('#interchangelabelemp').html(
                        `<label for="emp_name">{{ __('user_management_module.user_forms.label.create_user_as_sp_admin') }}</label> <small class="required">*</small>`
                        );
                    document.getElementById("name").placeholder =
                        "{{ __('user_management_module.user_forms.label.create_user_as_sp_admin') }}";
                    $('#2').hide();
                    $('#2').next('.current').hide();
                    $('#3 span:first').html('2');
                    $('#4 span:first').html('3');
                } else if (user_type_val == 'supervisor') {
                    $('.employee_label').html(
                        `<label for="emp_id">{{ __('user_management_module.user_forms.label.emp_id') }}<small class="required">*</small></label>`
                        );

                    $('#interchangelabelemp').html(
                        `<label for="emp_name">{{ __('user_management_module.user_forms.label.supervisor_name') }}<small class="required">*</small></label>`
                        );
                    document.getElementById("name").placeholder =
                        "{{ __('user_management_module.user_forms.label.supervisor_name') }}";
                    $('.employee_id').html(
                        `<label for="emp_id">{{ __('user_management_module.user_forms.label.supervisor_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.supervisor_id') }}">`
                        );

                    $('.employee_email_various').html(
                        `<label for="emp_email">{{ __('user_management_module.user_forms.label.supervisor_email') }}<small class="required">*</small></label>`
                        );
                    document.getElementById("email").placeholder =
                        "{{ __('user_management_module.user_forms.label.supervisor_email') }}";

                    $('.emp_dept_label-div').html(
                        `<label for="emp_dept">{{ __('user_management_module.user_forms.label.supervisor_department') }}</label>`
                        );
                    document.getElementById("emp_dept").placeholder =
                        "{{ __('user_management_module.user_forms.label.supervisor_department') }}";

                    $('#2').show();
                    $('#2').next('.current').show();
                    $('#3').show();
                    $('#3').next('.current').show();
                    $('#4 span:first').html('4');
                } else if(user_type_val == 'store_keeper'){
                    
                    {{--$('#interchangelabelemp').html(--}}
                    {{--    `<label for="emp_name">{{ __('user_management_module.user_forms.label.store_keeper_name') }}<small class="required">*</small></label>`--}}
                    {{--);--}}
                    {{--document.getElementById("name").placeholder =--}}
                    {{--    "{{ __('user_management_module.user_forms.label.store_keeper_name') }}";--}}
                    
                    {{--$('.employee_email_various').html(--}}
                    {{--    `<label for="emp_email">{{ __('user_management_module.user_forms.label.store_keeper_email') }}<small class="required">*</small></label>`--}}
                    {{--);--}}
                    {{--document.getElementById("email").placeholder =--}}
                    {{--    "{{ __('user_management_module.user_forms.label.store_keeper_email') }}";--}}
                    
                } else {
                    //$('#user_type_name').html('Service Provider Name');
                    $('#2').show();
                    $('#2').next('.current').show();
                    $('#3').show();
                    $('#3').next('.current').show();
                    $('#4 span:first').html('4');


                    $('.company_info').hide();
                    $('#service_provider').prop("required", false);
                    $('#interchangelabelemp').html(`<label for="emp_name">` + translations.user_management_module
                        .user_forms.label.emp_name + `<small class="required">*</small></label>`);
                }

                if (user_type_val == 'admin' || user_type_val == 'admin_employee') {
                    $('#2').hide();
                    $('#2').next('.current').hide();
                    $('#3').hide();
                    $('#3').next('.current').hide();
                    $('#4 span:first').html('2');
                }


                if (user_type_val == 'building_manager') {
                    $('#interchangelabelemp').html(`<label for="emp_name">` + translations.user_management_module
                        .user_forms.label.emp_name + `<small class="required">*</small></label>`);

                    $('.company_info').hide();
                    $('#service_provider').prop("required", false);
                    $('.area-bma-manager').show();
                }else{
                    $('.area-bma-manager').hide();
                    $('#area-bma-manager').prop('checked', false);
                }


                if (user_type_val == 'admin_employee') {
                    $('.spga_admin_info').show();
                    $('#spga_admin').prop("required", true);
                } else {
                    $('.spga_admin_info').hide();
                    $('#spga_admin').prop("false", true);
                }
                
                // else {
                //     $('#store_keeper_info').hide();
                //     $('#store_keeper_name').prop("false", true);
                // }
                if (user_type_val == 'building_manager_employee') {
                    $('.building_admin_info').show();
                    $('#building_admin').prop("required", true);
                } else {
                    $('.building_admin_info').hide();
                    $('#building_admin').prop("required", false);
                }

                if (user_type_val == 'sp_admin' || user_type_val == 'supervisor' || user_type_val == 'sp_worker' || user_type_val == 'team_leader') {

                    if (user_type_val == 'sp_worker') {

                        // @flip2@ added star for worker id
                        $('.employee_id').html(
                            `<label for="worker_id" id="lable-wi">{{ __('user_management_module.user_forms.label.worker_id') }} <small class="required">*</small> <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{ __('user_management_module.common.worker_id_description') }}"></i></label>
            <input maxlength="10" type="text" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="4466">`
                            );

                        $('.emp_dept_label-div').html(
                            `<label for="emp_dept">{{ __('user_management_module.user_forms.label.profession') }} <small class="required">*</small></label>`
                            );
                        $('.profession').hide();
                        $('.profession_select').show();
                        $('.nationality_select').show();
                        $('.favorite_language_select').show();
                    }
                    else if (user_type_val == 'team_leader') {

                        // @flip2@ added star for worker id
                        $('.employee_id').html(
                            `<label for="worker_id" id="lable-wi">{{ __('user_management_module.team_leader_module.team_leader_id') }} <small class="required">*</small> <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{ __('user_management_module.common.worker_id_description') }}"></i></label>
            <input maxlength="10" type="text" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="4466">`
                            );

                        $('.profession').hide();
                        $('.profession_select').hide();
                        $('.nationality_select').show();
                        $('.favorite_language_select').show();
                    } else if (user_type_val == 'supervisor') {
                        $('.employee_id').html(
                            `<label for="emp_id">{{ __('user_management_module.user_forms.label.supervisor_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.supervisor_id') }}">`
                            );
                    } else {

                        $('.employee_id').html(
                            `<label for="emp_id">{{ __('user_management_module.user_forms.label.emp_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.emp_id') }}">`
                            );
                    }
                    $('.company_info').show();
                    $('#service_provider').prop("required", true);
                } else {
                    if (user_type_val == 'sp_worker') {

                        $('.employee_id').html(
                            `<label for="worker_id">{{ __('user_management_module.user_forms.label.worker_id') }}  <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle='tooltip' data-placement="top" title="{{ __('user_management_module.common.worker_id_description') }}"></i></label>
           <input maxlength="10" type="text" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="4466">`
                            );

                    }
                    else if (user_type_val == 'team_leader') {

                        $('.employee_id').html(
                            `<label for="worker_id">{{ __('user_management_module.team_leader_module.team_leader_id') }}  <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle='tooltip' data-placement="top" title="{{ __('user_management_module.common.worker_id_description') }}"></i></label>
           <input maxlength="10" type="text" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="4466">`
                            );

                    } else {

                        $('.employee_id').html(
                            `<label for="emp_id">{{ __('user_management_module.user_forms.label.emp_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.emp_id') }}">`
                            );
                    }

                    $('.company_info').hide();
                    $('#service_provider').prop("required", false);
                }

                if (user_type_val == 'supervisor') {
                    $('.sup_info').show();
                    $('#sp_admin_id').prop("required", true);
                } else {
                    $('.sup_info').hide();
                    $('#sp_admin_id').prop("required", false);
                }

                if (user_type_val == 'sp_worker') {
                    $('#interchangelabelemp').hide();
                    $('#interchangelabelwrk').show();
                    $('#name').attr('placeholder', translations.user_management_module.user_forms.label
                    .worker_name);
                    $('#interchangelabelwrk #user_type_name').html(translations.user_management_module.user_forms
                        .label.worker_name + ' <small class="required">*</small>');
                    $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label
                        .profession);
                    $('.worker_info').show();
                    $('.emp_dept_label').hide();
                    $('.email_box').hide();
                    $('#email').attr("type", "hidden");
                    $('.user-country-city-block').hide();
                    $('#supervisor_id').prop("required", true);
                    $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.user_forms.label.worker_admin')}}<small class="required">*</small></label>`);
      
      $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.worker_admin);
                }
                else if (user_type_val == 'team_leader') {
                    $('#interchangelabelemp').hide();
                    $('#interchangelabelwrk').show();
                    $('#name').attr('placeholder', translations.user_management_module.team_leader_module.team_leader_name);
                    $('#interchangelabelwrk #user_type_name').html(translations.user_management_module.team_leader_module.team_leader_name + ' <small class="required">*</small>');
                    // $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label
                    //     .profession);
                    $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.team_leader_module.team_leader_supervisor')}}<small class="required">*</small></label>`);
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.team_leader_supervisor);
                    $('.worker_info').show();
                    $('.emp_dept_label').hide();
                    $('.email_box').hide();
                    $('#email').attr("type", "hidden");
                    $('.user-country-city-block').hide();
                    $('#supervisor_id').prop("required", true);
                } else if (user_type_val == 'supervisor') {
                    $('.worker_info').hide();
                    $('#email').attr("type", "email");
                    $('#interchangelabelemp').show();
                    $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label
                        .supervisor_department);
                    $('#interchangelabelwrk').hide();
                    $('.user-country-city-block').show();
                } else {
                    $('#interchangelabelwrk').hide();
                    $('#interchangelabelemp').show();
                    if (user_type_val == 'sp_admin') {
                        $('#name').attr('placeholder', translations.user_management_module.user_forms.label
                            .create_user_as_sp_admin);
                    } else {
                        $('.company_info').hide();
                        $('#service_provider').prop("required", false);
                        $('#name').attr('placeholder', translations.user_management_module.user_forms.label
                            .emp_name);
                    }
                    $('#interchangelabelemp #user_type_name_label').html(translations.user_management_module
                        .user_forms.label.emp_name + ' <small class="required">*</small>');
                    $('.emp_dept_label-div').html(
                        `<label for="emp_dept">{{ __('user_management_module.user_forms.label.emp_dept') }}</label>`
                        );
                    $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label
                        .emp_dept);

                    $('.employee_email_various').html(
                        `<label for="emp_email">{{ __('user_management_module.user_forms.label.emp_email') }}<small class="required">*</small></label>`
                        );
                    document.getElementById("email").placeholder =
                        "{{ __('user_management_module.user_forms.label.emp_email') }}";
                    $('#email').attr('placeholder', translations.user_management_module.user_forms.label.emp_email);

                    $('.worker_info').hide();
                    $('.emp_dept_label').show();
                    $('#email').attr("type", "email");
                    if (user_type_val !== 'store_keeper') {
                        $('.email_box').show();
                    }
                    $('#supervisor_id').prop("required", false);
                    $('.user-country-city-block').show();
                }
                
                if (user_type_val == 'store_keeper') {
                    console.log("User Type When it changed")

                    $(".store_keeper_info").show();

                    $('#interchangelabelemp').html(
                        `<label for="emp_name">{{ __('user_management_module.user_forms.label.store_keeper_name') }}<small class="required">*</small></label>`
                    );
                    document.getElementById("name").placeholder =
                        "{{ __('user_management_module.user_forms.label.store_keeper_name') }}";

                    $('.employee_id').html(
                        `<label for="emp_id">{{ __('user_management_module.user_forms.label.store_keeper_id') }}</label>   <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{ __('user_management_module.user_forms.label.emp_id') }}">`
                    );

                    $('.emp_dept_label-div').html(
                        `<label for="emp_dept">{{ __('user_management_module.user_forms.label.store_keeper_department') }}</label>`
                    );
                    $('#emp_dept').attr('placeholder', translations.user_management_module.user_forms.label
                        .store_keeper_department);

                    $('.employee_email_various').html(
                        `<label for="emp_email">{{ __('user_management_module.user_forms.label.store_keeper_email') }}<small class="required">*</small></label>`
                    );
                    document.getElementById("email").placeholder =
                        "{{ __('user_management_module.user_forms.label.store_keeper_email') }}";
                    $('#email').attr('placeholder', translations.user_management_module.user_forms.label.store_keeper);

                }
                
                
            }
        });

        function getEmpiddata() {
            sessionStorage.setItem('createuser_emp_id', JSON.stringify($('#emp_id').val()));
        }
        $('#name').keyup(function() {
            sessionStorage.setItem('createuser_name', JSON.stringify($(this).val()));
        });

        $('#emp_id').keyup(function() {
            sessionStorage.setItem('createuser_emp_id', JSON.stringify($(this).val()));
        });

        $('#email').keyup(function() {
            sessionStorage.setItem('createuser_email', JSON.stringify($(this).val()));
        });

        $('#emp_dept').keyup(function() {
            sessionStorage.setItem('createuser_emp_dept', JSON.stringify($(this).val()));
        });

        $('#phone').keyup(function() {
            sessionStorage.setItem('createuser_phone', JSON.stringify($(this).val()));
        });
        $('#isAkaunting_Vendor').change(function() {
           sessionStorage.setItem('createuser_isAkaunting_Vendor', JSON.stringify($(this).prop('checked')));
        });

        $('#bma_area_manager').change(function () {
            if (event.target.checked) {
                $.ajax({
                    url: "{{ route('users.ajax_check_bma_area_manager_for_project') }}",
                    type: 'POST',
                    data: {
                        project_id: $("#project_id").val(),
                        locale: "en",
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },
                    success: function (returnedData) {
                        console.log(returnedData);
                        if (returnedData === '0')
                            $(".unique_area_manager_error").show();
                        else
                            $(".unique_area_manager_error").hide();
                    },
                    error: function (data) {
                        console.log(data);
                    }
                });
            } else {
                $(".unique_area_manager_error").hide();
            }
            sessionStorage.setItem('createuser_bma_area_manager', JSON.stringify($(this).prop('checked')));
        });

        $('#nationality_id').change(function() {
            sessionStorage.setItem('createuser_nationality', JSON.stringify($(this).val()));
        });

        $('#favorite_language').change(function() {
            sessionStorage.setItem('createuser_favorite', JSON.stringify($(this).val()));
        });

        $('#profession_id').change(function() {
            sessionStorage.setItem('createuser_profession', JSON.stringify($(this).val()));
        });
        
        /*** Store Keeper ***/
        $('#store_keeper_name').keyup(function() {
            sessionStorage.setItem('createuser_name', JSON.stringify($(this).val()));
        });
        $('#store_keeper_email').keyup(function() {
            sessionStorage.setItem('createuser_email', JSON.stringify($(this).val()));
        });

        $('#store_keeper_id').keyup(function () {
            sessionStorage.setItem('createuser_emp_id', JSON.stringify($(this).val()));
        });

        $('#store_keeper_department').keyup(function () {
            sessionStorage.setItem('createuser_emp_dept', JSON.stringify($(this).val()));
        });
        $('#phone2').keyup(function () {
            sessionStorage.setItem('createuser_phone', JSON.stringify($(this).val()));
        });
        
    </script>
    <script type="text/javascript">
        $(document).ready(function() {
            $('body').tooltip({
                selector: '.fas'
            });
            $(".dropdown-check").hide();
            $(".multi-field input").click(function() {
                $(".dropdown-check").show();
            });
            $(document).mouseup(function(e) {
                var container = $(".dropdown-check");
                // if the target of the click isn't the container nor a descendant of the container
                if (!container.is(e.target) && container.has(e.target).length === 0) {
                    container.hide();
                }
            });

            $("#filter").keyup(function() {
                var filter = $(this).val(),
                    count = 0;
                $(".multi-field  li").each(function() {
                    if (filter == "") {
                        $(this).css("visibility", "visible");
                        $(this).fadeIn();
                        $('.check-all').css("visibility", "visible");
                        $('.check-all').fadeIn();
                        $(".dropdown-check").css("visibility", "hidden");
                        $('.dropdown-check').fadeOut();
                    } else if ($(this).text().search(new RegExp(filter, "i")) < 0) {
                        $('.check-all').css("visibility", "hidden");
                        $('.check-all').fadeOut();
                        $(this).css("visibility", "hidden");
                        $(this).fadeOut();
                        $(".dropdown-check").css("visibility", "visible");
                        $('.dropdown-check').fadeIn();

                    } else {
                        $(this).css("visibility", "visible");
                        $('.check-all').css("visibility", "hidden");
                        $('.check-all').fadeOut();
                        $(this).fadeIn();
                        $(".dropdown-check").css("visibility", "visible");
                        $('.dropdown-check').fadeIn();
                    }
                });
            });

            //Check Checkboxes of ul
            $(".multi-field input:checkbox").click(function() {
                $(this).parent().find('input:checkbox').not(this).prop('checked', this.checked);
            });
            $(".multi-field .check-all input:checkbox").click(function() {
                $('.multi-field input:checkbox').not(this).prop('checked', this.checked);

                //add checked boxes to array
                var tmp = [];
                $("input[type='checkbox']").change(function() {
                    var checked = $(this).val();
                    if ($(this).is(':checked')) {
                        tmp.push(checked);
                    } else {
                        tmp.splice($.inArray(checked, tmp), 1);
                    }
                    $(".selected-checkbox").text(tmp);
                });
            });
        });


        $("#city_id").on("change", function() {
            if ($("#city_id").val() != '') {
                $("#city_option_erro").empty();

            }

        })
        $("#country_id").on("change", function() {
            if ($("#country_id").val() != '') {
                $("#country_id_error").empty();
                $("#city_option_erro").empty();

            }

        })


        $("#sp_admin_id").on("change", function() {
            sessionStorage.setItem('createuser_sp_admin_id', JSON.stringify($(this).val()));
        });


        function removeServiceProvider() {
            $("#store_keeper_service_provider").val('').change();
            sessionStorage.removeItem('createuser_service_provider')
        }
            
        $(document).ready(function() {
            $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
            $('.profession_select').hide();
            $('.nationality_select').hide();
            $('.favorite_language_select').hide();
            $('#emp_dept').prop('required', false); // Make input not required
            if (user_type_val != '') {
                if (user_type_val == 'sp_worker') {
                    $('.profession').hide();
                    $('.profession_select').show();
                    $('.nationality_select').show();
                    $('.favorite_language_select').show();
                }
                else if (user_type_val == 'team_leader') {
                    $('.profession').hide();
                    $('.profession_select').hide();
                    $('.nationality_select').show();
                    $('.favorite_language_select').show();
                } else {
                    $('.profession_select').hide();
                    $('.nationality_select').hide();
                    $('.favorite_language_select').hide();
                }
            }
            $('#profession_id').on('change', function() {
                var selectedOption = $(this).val();

                if (selectedOption === '10') {
                    $('.profession').show();
                    $('#emp_dept').prop('required', true); // Make input required
                    $('#emp_dept').attr('maxlength', 15); // Set the new max-length attribute
                } else {
                    $('.profession').hide();
                    $('#emp_dept').prop('required', false); // Make input not required
                    $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
                }
            });
        });
    </script>
@endsection
