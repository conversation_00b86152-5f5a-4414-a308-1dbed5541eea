@extends('layouts.app')
@section('styles')
@endsection
@section('content')

<div class="contents">
   <div class="container-fluid">
      <div class="row">
         <div class="col-lg-12">
            <div class="page-title-wrap">
               @if(Request()->route()->getName() == 'users.edit.info.sp')
                <!--{{ Breadcrumbs::render('edit-sp') }}-->
                @else
                <!--{{ Breadcrumbs::render('add-user') }}-->
                @endif
                <div class="page-title d-flex justify-content-between">
                    <div class="page-title__left">
                        <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a>
                        <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                            <h4 class="text-capitalize fw-500 breadcrumb-title">{{__('user_management_module.user_button.edit_user')}}</h4>
                         </div>
                    </div>
                </div>
            </div>
         </div>
      </div>
   </div>
   <div class="container-fluid">
      <div class=" checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
         <div class="row justify-content-center">
            <div class="col-xl-8">
               <div class="checkout-progress-indicator content-center col-md-10">
                  <div class="checkout-progress">
                     @if($data['u_data']->user_type == 'admin')
                        <div class="step current" id="1">
                           <span>1</span>
                           <span>{{__('user_management_module.common.user_info')}}</span>
                        </div>

                        <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="2">
                        <span>2</span>
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>
                     @else
                     <div class="step current" id="1">
                           <span>1</span>
                           <span>{{__('user_management_module.common.user_info')}}</span>
                        </div>

                     @if($data['u_data']->user_type != 'admin_employee')
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="2">
                        <span>2</span>
                        <span>{{__('user_management_module.common.user_role')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="3">
                        <span>3</span>
                        <span>{{__('user_management_module.common.user_previleges')}}</span>
                     </div>

                     @endif

                     @if($data['u_data']->user_type == 'sp_worker' || $data['u_data']->user_type == 'team_leader')
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="4">
                        <span>4</span>
                        <span>{{__('user_management_module.common.password')}}</span>
                     </div>

                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="5">
                        <span> 5</span>
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>
                     @endif
                     @if($data['u_data']->user_type == 'sp_admin')
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="2">
                        <span>2</span>
                        <span>{{__('user_management_module.common.user_previleges')}}</span>
                     </div>
                     @endif
                     @if($data['u_data']->user_type != 'sp_worker' && $data['u_data']->user_type != 'team_leader')

                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="4">
                        <span> @if($data['u_data']->user_type != 'admin_employee') 4 @elseif($data['u_data']->user_type == 'sp_admin')3 @else 2 @endif</span>
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>
                     @endif
               @endif
                  </div>
               </div>
               <!-- checkout -->
               <div class="row justify-content-center">
                  <div class="col-lg-8 col-sm-12">
                     <div class="card checkout-shipping-form px-sm-30 pt-2 pb-30 border-0">
                        <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0">
                           <h4 class="fw-400">{{__('user_management_module.common.user_info')}}</h4>
                        </div>
                        <div class="card-body px-0 pb-0">
                             <div class="edit-profile__body">
                                 <?php
                                 $form_id = 'user_create_form_edit';
                                 ?>
                                 @if($data['u_data']->user_type == 'sp_worker' || $data['u_data']->user_type == 'team_leader')
                                 <?php
                                 $form_id = 'user_create_form_edit_worker';
                                 ?>
                                 @endif

                                <form method="post" id="{{ $form_id }}" class="edit_user_form" action="{{ route('users.edit.role', Crypt::encryptString($data['u_data']->id))}}" enctype="multipart/form-data" autocomplete="off">
                                @csrf
                                <input type="hidden" id="user_id" name="user_id" value="{{ $data['u_data']->id }}">

                                <div class="account-profile d-flex align-items-center mb-4 ">
                                    <div class="pro_img_wrapper">
                                       <input id="file_upload" type="file" name="profile_img" class="d-none" accept="image/*">
                                       <!-- Profile picture image-->
                                       {{-- @flip1@ add input hidden --}}
                                       <input type="hidden" name="isImageRemove" value="">
                                       <label for="file_upload">
                                       <img class="ap-img__main rounded-circle wh-120 bg-lighter d-flex" src="{{($data['u_data']->profile_img) ?ImagesUploadHelper::displayImage($data['u_data']->profile_img, 'uploads/profile_images') : '/img/upload.png' }}" alt="profile" id="output_pic">
                                       <span class="cross" id="remove_pro_pic" >
                                       <span data-feather="camera" ></span>
                                       </span>
                                       </label>
                                       <?php
                                          if(trim($data['u_data']->profile_img) != "")
                                          {
                                             $hideclass = '';
                                          }
                                          else
                                          {
                                             $hideclass = 'hide';
                                          }
                                       ?>
                                       <span class="remove-img text-white btn-danger rounded-circle <?=$hideclass;?>" data-toggle="modal" data-target="#confirmDeletePhoto">
                                         <span data-feather="x"></span>
                                       </span>
                                    </div>
                                    <div class="account-profile__title">
                                       <h6 class="fs-15 ml-20 fw-500 text-capitalize">{{__('user_management_module.user_forms.label.photo')}}</h6>
                                    </div>
                                 </div>

                                  @if($data['u_data']->user_type != 'admin' && $data['u_data']->user_type != 'admin_employee')
                                  <div class="form-group mb-20">
                                      <div class="usertype_option">
                                         <label for="user_type">
                                         {{__('user_management_module.user_forms.label.user_type')}} <small class="required">*</small>
                                         </label>
                                         <input type="hidden" name ="user_type" value="{{$data['u_data']->user_type}}" >

                                         <select class="form-control" id="user_type" name="user_type" disabled  required>

                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.user_type')}}</option>
                                            @foreach($data['usertypeList'] as $user_type)
                                                @if(App::getLocale()=='en')
                                                   <option @if(isset($data['u_data']->user_type) && ($data['u_data']->user_type==$user_type->slug)) selected @endif value="{{$user_type->slug}}">{{$user_type->name}}</option>
                                                @else
                                                   <option @if(isset($data['u_data']->user_type) && ($data['u_data']->user_type==$user_type->slug)) selected @endif value="{{$user_type->slug}}">{{$user_type->name_ar}}</option>
                                                @endif
                                            @endforeach
                                         </select>
                                      </div>
                                   </div>
                                   @else
                                   <input type="hidden" name="user_type" value="{{ $data['u_data']->user_type }}">
                                   @endif

                                   <div class="form-group mb-20 company_info">
                                      <div  id="fg-company-info" class="service_provider">
                                         <label for="service_provider">
                                         {{__('user_management_module.user_forms.label.company_name')}}<small class="required">*</small>
                                         </label>
                                         <input type="hidden" name="service_provider" value="{{$data['u_data']->service_provider}}" >
                                         <select class="form-control" id="service_provider" name="service_provider" disabled>
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.company_name')}}</option>
                                            @foreach($data['companyList'] as $company)
                                              <option value="{{$company->id}}" @if($data['u_data']->service_provider==$company->id) selected @endif>{{$company->name}} - {{$company->service_provider_id}}</option>
                                            @endforeach
                                         </select>
                                      </div>

                                   </div>

                                   <div class="form-group mb-20 worker_info">
                                      <div class="usertype_option">
                                       <div id="supervisor_id_label">
                                          @if($data['u_data']->user_type == 'team_leader')
                                          <label for="user_type">
                                          {{__('user_management_module.team_leader_module.team_leader_supervisor')}} <small class="required">*</small>
                                         </label>
                                          @else
                                          <label for="user_type">
                                          {{__('user_management_module.user_forms.label.worker_admin')}} <small class="required">*</small>
                                         </label>
                                          @endif
                                         
                                         </div>
                                         <select class="form-control" id="supervisor_id" name="supervisor_id[]" multiple>
                                            <option value="" disabled>{{__('user_management_module.user_forms.label.worker_admin')}}</option>

                                            @if(isset($data['sp_supervisor_list']))
                                            @foreach($data['sp_supervisor_list'] as $sp_supervisor)
                                              <option value="{{$sp_supervisor->id}}" @if(in_array($sp_supervisor->id, explode(',',$data['u_data']->supervisor_id))) selected @endif>{{$sp_supervisor->name}}</option>
                                            @endforeach
                                            @endif
                                         </select>
                                      </div>

                                   </div>

                                   <div class="form-group mb-20 sup_info">
                                      <div class="usertype_option">
                                         <label for="user_type">
                                         {{__('user_management_module.user_forms.label.employee_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="sp_admin_id" >
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.employee_admin')}}</option>
                                           @if(isset($data['sp_admin_list']))
                                            @foreach($data['sp_admin_list'] as $sp_admin)
                                              <option value="{{$sp_admin->id}}" @if($data['u_data']->sp_admin_id==$sp_admin->id) selected @endif>{{$sp_admin->name}}</option>
                                            @endforeach
                                            @endif
                                         </select>
                                      </div>

                                   </div>

                                    <div class="form-group mb-20 building_admin_info">
                                      <div class="usertype_option">
                                         <label for="building_admin">
                                         {{__('user_management_module.user_forms.label.employee_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="building_admin" >
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.employee_admin')}}</option>
                                            @if(isset($data['building_manager_list']))

                                              @foreach($data['building_manager_list'] as $building_manager)
                                                <option value="{{$building_manager->id}}" @if($data['u_data']->sp_admin_id==$building_manager->id) selected @endif>{{$building_manager->name}}</option>
                                              @endforeach

                                            @endif
                                         </select>
                                      </div>

                                   </div>

                                   <div class="form-group mb-20 spga_admin_info">
                                      <div class="usertype_option">
                                         <label for="spga_admin">
                                         {{__('user_management_module.user_forms.label.employee_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="spga_admin" >
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.employee_admin')}}</option>
                                            @if(isset($data['spga_admin_list']))

                                              @foreach($data['spga_admin_list'] as $spga_admin)
                                                <option value="{{$spga_admin->id}}" @if($data['u_data']->sp_admin_id==$spga_admin->id) selected @endif>{{$spga_admin->name}}</option>
                                              @endforeach

                                            @endif
                                         </select>
                                      </div>

                                   </div>
                                   <input type="hidden" name="sp_admin_id" id="employee_admin_id" value="{{$data['u_data']->sp_admin_id}}">


                                   <div class="form-group mb-20 user_info">
                                   @if($data['u_data']->user_type == 'sp_worker')

                                   <label for="emp_name" id="user_type_name">{{__('user_management_module.user_forms.label.worker_name')}} <small class="required">*</small></label>
                                   @elseif($data['u_data']->user_type == 'team_leader')

                                   <label for="emp_name" id="user_type_name">{{__('user_management_module.team_leader_module.team_leader_name')}} <small class="required">*</small></label>
                                   @else
                                   <label for="emp_name" id="user_type_name">{{__('user_management_module.user_forms.label.emp_name')}} <small class="required">*</small></label>

                                   @endif
                                      <input type="text" class="form-control" name="name" id="name" placeholder="{{__('user_management_module.user_forms.place_holder.emp_name')}}" value="{{($data['u_data']->name) ? $data['u_data']->name  : '' }}">
                                   </div>
                                   <div class="form-group mb-20 user_info">
                                       @if($data['u_data']->user_type == 'sp_worker')
                                          <label for="worker_id">{{__('user_management_module.user_forms.label.worker_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}"></i><small class="required">*</small></label>
                                       @elseif($data['u_data']->user_type == 'team_leader')
                                          <label for="worker_id">{{__('user_management_module.team_leader_module.team_leader_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}"></i><small class="required">*</small></label>
                                       @else
                                          <label for="emp_id">{{__('user_management_module.user_forms.label.emp_id')}}</label>
                                       @endif
                                       <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{__('user_management_module.user_forms.place_holder.emp_id')}}" value="{{($data['u_data']->emp_id) ? $data['u_data']->emp_id  : '' }}">
                                    </div>
                                    <div class="form-group mb-20 user_info email_box">
                                       <label for="emp_email">{{__('user_management_module.user_forms.label.emp_email')}} <small class="required">*</small></label>
                                       <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" value="{{($data['u_data']->email) ? $data['u_data']->email  : '' }}">
                                    </div>

                                    <div class="form- group mb-20 user_info nationality_select">
                                       <div class="">
                                          <label for="nationality_id">
                                          {{__('user_management_module.user_forms.label.nationality')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="nationality_id" name="nationality_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_nationality')}}</option>

                                             @foreach($data['nationalities'] as $nationality)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$nationality->id}}" {{ $data['u_data']->country_id == $nationality->id ? 'selected' : '' }}>{{$nationality->name_en}}</option>
                                                @else
                                                <option value="{{$nationality->id}}" {{ $data['u_data']->country_id == $nationality->id ? 'selected' : '' }}>{{$nationality->name_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="nationality-id-error"></div>
                                    </div>

                                    <div class="form- group mb-20 user_info favorite_language_select">
                                       <div class="">
                                          <label for="favorite_language">
                                          {{__('user_management_module.user_forms.label.favorite_language')}}
                                          </label>
                                          <select class="form-control" id="favorite_language" name="favorite_language">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_favorite_language')}}</option>
                                             <option value="en" {{ $data['u_data']->favorite_language == "en" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.english')}}</option>
                                             <option value="ar" {{ $data['u_data']->favorite_language == "ar" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.arabic')}}</option>
                                             <option value="ur" {{ $data['u_data']->favorite_language == "ur" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.urdu')}}</option>

                                          </select>
                                       </div>
                                       <div id="favorite-language-error"></div>
                                    </div>
                                    @if($data['u_data']->user_type != 'team_leader')
                                    <div class="form- group mb-20 user_info profession_select">
                                       <div class="">
                                          <label for="profession_id">
                                          {{__('user_management_module.user_forms.label.select_profession_heading')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="profession_id" name="profession_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_profession')}}</option>

                                             @foreach($data['workerProfessions'] as $profession)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$profession->id}}" {{ $data['u_data']->profession_id == $profession->id ? 'selected' : '' }}>{{$profession->profession_en}}</option>
                                                @else
                                                <option value="{{$profession->id}}" {{ $data['u_data']->profession_id == $profession->id ? 'selected' : '' }}>{{$profession->profession_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="profession-id-error"></div>
                                    </div>

                                    <div class="form-group mb-20 user_info profession" {{ ($data['u_data']->profession_id == 10 || $data['u_data']->profession_id == NULL) ? 'style=display:block' : 'style=display:none' }}>
                                       <label for="emp_dept">
                                          <span class="emp_dept_label">{{__('user_management_module.user_forms.label.emp_dept')}}</span>
                                          <span class="worker_info">{{__('user_management_module.user_forms.label.profession')}} <small class="required">*</small></span>
                                       </label>
                                       <input type="text" class="form-control" name="emp_dept" id="emp_dept" placeholder="{{__('user_management_module.user_forms.place_holder.emp_dept')}}" value="{{($data['u_data']->emp_dept) ? $data['u_data']->emp_dept  : '' }}" {{ $data['u_data']->profession_id == 10 ? 'required' : '' }}>
                                    </div>
                                    @endif

                                    <div class="form-group mb-20 user_info">
                                       <label for="emp_phone_number">{{__('user_management_module.user_forms.label.emp_phone')}}
                                          {{-- <small class="required phone_required_mark">*</small> --}}
                                       </label>
                                       <div class="input-group mb-3 phone-ltr">
                                          <div class="input-group-prepend">
                                             <span class="input-group-text" id="basic-addon1">+966</span>
                                          </div>
                                          <input type="tel" class="form-control" id="phone" name="phone" placeholder="576428964" value="{{($data['u_data']->phone) ? $data['u_data']->phone  : '' }}">
                                          </div>

                                    </div>

                                    @if(in_array(auth()->user()->user_type,['super_admin','osool_admin','admin','admin_employee']))
                                        <div class="form-group mb-20 area-bma-manager" id="area-bma-manager">
                                            <div class="mt-4 d-flex ">
                                                <div>
                                                    <div class="checkbox-theme-default custom-checkbox ">
                                                        <input class="checkbox" type="checkbox"
                                                               id="bma_area_manager"
                                                               name="bma_area_manager"
                                                               value="1"
                                                        >
                                                        <label for="bma_area_manager"
                                                               onfocus="theFocus(this);"
                                                               data-toggle="tooltip" data-placement="auto"
                                                               title="">
                                                                    <span class="checkbox-text text-dark">
                                                                        &nbsp;
                                                                    </span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div>
                                                    <p class="mb-2">
                                                        {{__('user_management_module.user_previleges.assign_bma_area_manager')}}
                                                    </p>

                                                    <p class="mb-2 text-danger unique_area_manager_error" id="unique_area_manager_error">
                                                        {{__('user_management_module.user_validation.unique_area_manager')}}
                                                    </p>

                                                </div>
                                            </div>
                                        </div>
                                    @endif


                                    <div class="form-group mb-20 store_keeper_info">
                                        <div id="fg-service-provider" class="service_provider">
                                            <input type="hidden" name="service_provider"
                                                   value="{{$data['u_data']->service_provider}}">
                                          @if(
                                             (
                                                auth()->user()->user_type == 'admin' ||
                                                auth()->user()->user_type == 'admin_employee' ||
                                                auth()->user()->user_type == 'sp_admin' ||
                                                auth()->user()->user_type == 'super_admin' ||
                                                auth()->user()->user_type == 'sp_worker'
                                             )
                                             &&
                                             (
                                                $data['u_data']->user_type == 'sp_worker' || $data['u_data']->user_type == 'team_leader' ||
                                                $data['u_data']->user_type == 'sp_admin'
                                             )
                                          )
                                                 <label for="service_provider">
                                                   {{ __('user_management_module.user_forms.label.store_keeper_service_provider') }}
                                                </label>
                                                @if(is_null($data['u_data']->service_provider))
                                                      <select class="form-control"
                                                         disabled>
                                                      <option value="" selected
                                                                  disabled>
                                                         -
                                                      </option>
                                                </select>
                                                @else
                                                      <select class="form-control" id="service_provider" name="service_provider"
                                                         disabled>
                                                      <option value="" selected
                                                                  disabled>{{__('user_management_module.user_forms.label.company_name')}}</option>
                                                         @foreach($data['companyList'] as $company)
                                                            <option value="{{$company->id}}"
                                                                     @if($data['u_data']->service_provider==$company->id) selected @endif>{{$company->name}}
                                                                  - {{$company->service_provider_id}}</option>
                                                         @endforeach
                                                </select>
                                                @endif
                                          @endif

                                        </div>
                                    </div>
                                    @if(\App\Services\AkauntingService::allow() && auth()->user()->user_type == 'admin')
                                    @if ($data['u_data']->user_type=='sp_admin')


                                    <div class="form-group mb-20 vendor_akaunting " id="vendor_akaunting">
                                       <div class="mt-4 d-flex " >
                                          @if ($data['u_data']->akaunting_vendor_id)
                                          <div >
                                             <div class="checkbox-theme-default custom-checkbox ">
                                                 <input type="hidden" value="on"  name="isAkaunting_Vendor">
                                                 <input disabled class="checkbox" type="checkbox"
                                                     id="isAkaunting_Vendor" checked

                                                    >
                                                 <label for="isAkaunting_Vendor" onfocus="theFocus(this);" data-toggle="tooltip" data-placement="auto" title="{{ __('purchase_request.common.message_option_description_edit',['id'=>$data['u_data']->akaunting_vendor_id]) }}">
                                                     <span class="checkbox-text text-dark">
                                                         &nbsp;
                                                     </span>
                                                 </label>
                                             </div>
                                          </div>
                                          <div>
                                             <h6 class="mb-2">
                                                 {{ __('purchase_request.common.mark_sp_admin_as_vendor') }}
                                             </h5>
                                             <span>{{ __('purchase_request.common.message_the_vendor_has_related') }}</span>
                                          </div>

                                         @else


                                         <div >
                                          <div class="checkbox-theme-default custom-checkbox " >

                                              <input class="checkbox" type="checkbox"
                                                  id="isAkaunting_Vendor"
                                                  name="isAkaunting_Vendor">
                                              <label for="isAkaunting_Vendor" onfocus="theFocus(this);" data-toggle="tooltip" data-placement="auto" title="{{ __('purchase_request.common.message_option_decription_create') }}" >
                                                  <span class="checkbox-text text-dark">
                                                      &nbsp;
                                                  </span>
                                              </label>
                                          </div>
                                      </div>
                                      <div>
                                          <h6 class="mb-2">
                                              {{ __('purchase_request.common.mark_sp_admin_as_vendor') }}
                                          </h5>
                                          <span>{{ __('purchase_request.common.message_once_this_option_as_enabled_update') }}</span>
                                      </div>
                                          @endif


                                       </div>
                                   </div>
                                   @endif
                                   @endif
                                    <div class="user-country-city-block">
                                          <input type="hidden" name="country_id" id="country_id1" value="1">
                                    </div>
                                    <input type="hidden" name="city_id" id="city_id1" value="1">

                                    <input type="hidden" id="ajax_check_useremail_unique" value="{{route('users.ajax_check_unique_useremail_edit')}}">
                                    <input type="hidden" id="ajax_check_userphone_unique_edit" value="{{route('users.ajax_check_userphone_unique_edit')}}">

                                    <input type="hidden" id="ajax_check_employee_id_unique_edit" value="{{route('users.ajax_check_unique_emp_id')}}">

                                    <div class="button-group d-flex pt-25 justify-content-end">
                                       <a href="{{ route('users.list') }}" class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md">{{__('user_management_module.user_button.cancel')}}</a>
                                       <button onclick="custom_submit()" type="button" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('user_management_module.user_button.save_next')}}
                                       </button>
                                   </div>
                                </form>
                             </div>
                        </div>
                     </div>
                     <!-- ends: card -->
                  </div>
                  <!-- ends: col -->
               </div>
            </div>
            <!-- ends: col -->
         </div>
      </div>
      <!-- End: .global-shadow-->
   </div>
</div>
<!-- CONFIRM DELETE Photo MODAL START -->

<div class="modal new-member  bouncein-new" id="confirmDeletePhoto" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content  radius-xl  bouncein-new">

                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-3 fs-20"><i class="fa fa-exclamation-circle mr-1 color-warning"
                                aria-hidden="true"></i>
                            {{ __('data_properties.property_forms.label.sure_remove_photo') }} </h2>
                    </div>

                        <div class="button-group d-flex justify-content-end pt-25">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" class="btn btn-light   btn-squared text-capitalize"
                                    data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.cancel') }}

                                </button>
                                <button type="button" class="btn btn-danger btn-default btn-squared text-capitalize confirm_remove_photo" data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.remove') }}
                                </button>

                            </div>

                        </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- CONFIRM DELETE Photo MODAL ENDS -->
@endsection

@section('scripts')
<script type="text/javascript" src="{{asset('js/admin/users/create.js')}}"></script>
<script type="text/javascript">
$(document).ready(function() {
   setInterval(hideDuplicatedSP, 1000);
    $(".unique_area_manager_error").hide()
});
function hideDuplicatedSP(){
   let theShowen = $('#fg-company-info');
   let theHidden = $('#fg-service-provider');
   if(theShowen.length > 0 && theShowen.is(":visible")){
      theHidden.hide();
   }else{
      theHidden.show();
   }
}
$('#supervisor_id').on('select2:select', function (e) {
    sessionStorage.setItem('edituser_supervisor_id', JSON.stringify($(this).val()));
   });

   $('#supervisor_id').on('select2:unselecting', function (e) {
      let value_cnt1 = $(this).val();
         value_cnt1 = jQuery.grep(value_cnt1, function(value) {
               return value != e.params.args.data.id;
         });
         sessionStorage.setItem('edituser_supervisor_id', JSON.stringify(value_cnt1));
   });

   var storeduserspadminValues = sessionStorage.getItem('edituser_sp_admin_id');
                        if(storeduserspadminValues)
                        {
                              $('#sp_admin_id').val(JSON.parse(storeduserspadminValues)).trigger('change');
                        }

   var storedusertypeValues = sessionStorage.getItem('edituser_user_type');
   if(storedusertypeValues)
   {
      var selecteddusertypeValues = JSON.parse(storedusertypeValues);
         // Set the selected values in the Select2 dropdown
         $('#user_type').val(selecteddusertypeValues).trigger('change');
         var user_type_val = selecteddusertypeValues;
         if(user_type_val == 'sp_worker' )
         {
            $("#user_create_form_edit").prop('id','user_create_form_edit_worker');
            $(".phone_required_mark").hide();
            $('.user-country-city-block').hide()
         }
         else if(user_type_val == 'team_leader')
         {
            $("#user_create_form_edit").prop('id','user_create_form_edit_worker');
            $(".phone_required_mark").hide();
            $('.user-country-city-block').hide()
         } else {
            $("#user_create_form_edit_worker").prop('id','user_create_form_edit');
            $(".phone_required_mark").show();
            $('.user-country-city-block').show()
         }



         if(user_type_val!='')
    {
      $('.user_info').show();
      if(user_type_val=='sp_admin' || user_type_val=='admin_employee')
      {
        $('#user_type_name').html(translations.data_service_provider.serviceProvider_forms.label.service_provider_name+' <small class="required">*</small>');
        $('#2').hide();
        $('#3').hide();
      }

      if(user_type_val=='sp_admin'||user_type_val=='supervisor'||user_type_val=='sp_worker'||user_type_val=='team_leader')
      {
        $('.company_info').show();
      }

      if(user_type_val=='supervisor')
      {
        $('.sup_info').show();
      }
      else
      {
         $('.sup_info').hide();
      }
      if(user_type_val=='sp_worker')
      {
        $('.worker_info').show();
        $('.emp_dept_label').hide();
        $('.email_box').hide();
        $('#email').attr("type","hidden");
      }
      else if(user_type_val=='team_leader')
      {
        $('.worker_info').show();
        $('.emp_dept_label').hide();
        $('.email_box').hide();
        $('#email').attr("type","hidden");
      }
      else
      {
        $('.worker_info').hide();
        $('.emp_dept_label').show();
        $('#email').attr("type","email");
        $('.email_box').show();
      }

    }
   }

   var storedusernameValues = sessionStorage.getItem('edituser_name');
         if(storedusernameValues)
         {
            $("#name").val(JSON.parse(storedusernameValues));
         }
         var storedusebuildingadminValues = sessionStorage.getItem('edituser_building_admin');
         if(storedusebuildingadminValues)
         {
            $("#building_admin").val(JSON.parse(storedusebuildingadminValues)).trigger('change');
         }
         var storeduserempidValues = sessionStorage.getItem('edituser_emp_id');
         if(storeduserempidValues)
         {
            $("#emp_id").val(JSON.parse(storeduserempidValues));
         }
         var storeduseremailValues = sessionStorage.getItem('edituser_email');
         if(storeduseremailValues)
         {
            $("#email").val(JSON.parse(storeduseremailValues));
         }


         var storeduserisAkaunting_Vendor = sessionStorage.getItem('edituser_isAkaunting_Vendor');
if (storeduserisAkaunting_Vendor) {
    $("#isAkaunting_Vendor").prop('checked', JSON.parse(storeduserisAkaunting_Vendor));
}
         var storeduserempdeptValues = sessionStorage.getItem('edituser_emp_dept');
         if(storeduserempdeptValues)
         {
            $("#emp_dept").val(JSON.parse(storeduserempdeptValues));
         }
         var storeduserphoneValues = sessionStorage.getItem('edituser_phone');
         if(storeduserphoneValues)
         {
            $("#phone").val(JSON.parse(storeduserphoneValues));
         }

        var stored_bmaAreaManager = sessionStorage.getItem('edituser_is_area_bma_manager');
        if (stored_bmaAreaManager) {
            $("#bma_area_manager").prop('checked', JSON.parse(stored_bmaAreaManager));
        }else{
            $("#bma_area_manager").prop('checked', {{ (old('bma_area_manager') || (!old('bma_area_manager') && $data['u_data']->is_bma_area_manager) ) ? true:false  }});
        }

   $("#building_admin").on("change", function () {
      sessionStorage.setItem('edituser_building_admin', JSON.stringify($(this).val()));
   });

   function custom_submit() {
      var user_type = $('#user_type').val();
      if(user_type == 'supervisor' ) {
         swal({
            title: translations.general_sentence.modal.edit_warning_title,
            text: translations.general_sentence.modal.edit_warning_sp,
            icon: "warning",
            buttons: true,
            dangerMode: true,
            showCancelButton: true,
            confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
            cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
            /*

            buttons: [
                translations.general_sentence.swal_buttons.cancel,
                translations.general_sentence.swal_buttons.confirm,
            ],
            */
        },
        function(willDelete) {

            if (willDelete) {
               $('.edit_user_form').submit();
            }
        });
      }
      else if(user_type == 'building_manager' ) {

          if($('.unique_area_manager_error').is(':visible')){
              alert(`${translations.user_management_module.user_validation.unique_area_manager}`);
              return;
          }
         swal({
            title: translations.general_sentence.modal.edit_warning_title,
            text: translations.general_sentence.modal.edit_warning_bm,
            icon: "warning",
            buttons: true,
            dangerMode: true,
            showCancelButton: true,
            confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
            cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
            /*

            buttons: [
                translations.general_sentence.swal_buttons.cancel,
                translations.general_sentence.swal_buttons.confirm,
            ],
            */
        },
        function(willDelete) {

            if (willDelete) {
               $('.edit_user_form').submit();
            }
        });
      }
      else {
         $('.edit_user_form').submit();
      }

   }
   $("#sp_admin_id").on("change", function () {
         sessionStorage.setItem('edituser_sp_admin_id', JSON.stringify($(this).val()));
      })
  $('#user_type').on('change', function() {
      var user_type = $(this).val();
      sessionStorage.setItem('edituser_user_type', JSON.stringify(user_type_val));
      if(user_type == 'sp_worker' ) {
         $("#user_create_form_edit").prop('id','user_create_form_edit_worker');
         $(".phone_required_mark").hide();
         $('.user-country-city-block').hide()
      }
      else if(user_type == 'team_leader' ) {
         $("#user_create_form_edit").prop('id','user_create_form_edit_worker');
         $(".phone_required_mark").hide();
         $('.user-country-city-block').hide()
      } else {
         $("#user_create_form_edit_worker").prop('id','user_create_form_edit');
         $(".phone_required_mark").show();
         $('.user-country-city-block').show()
      }
   });
   $('.sup_info').hide();
   $('.worker_info').hide();
   $('.company_info').hide();
   $('.building_admin_info').hide();
   $('.area-bma-manager').hide();
   $('.spga_admin_info').hide();
      var selected_user_type_val = $('#user_type').val();
      var user_type_val = $('#user_type').val();
      //alert(selected_user_type_val);
      if(selected_user_type_val=='sp_worker')
      {
         $('.worker_info').show();
         $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
         $('.emp_dept_label').hide();
         $('.email_box').hide();
         $('.user-country-city-block').hide()
         $('#email').attr("type","hidden");
         $(".phone_required_mark").hide();
         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.user_forms.label.worker_admin')}}<small class="required">*</small></label>`);
      
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.worker_admin);
      }
      else if(selected_user_type_val=='team_leader')
      {
         $('.worker_info').show();
        // $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
         $('.emp_dept_label').hide();
         $('.email_box').hide();
         $(".profession").hide();
         $('.user-country-city-block').hide()
         $('#email').attr("type","hidden");
         $(".phone_required_mark").hide();
         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.team_leader_module.team_leader_supervisor')}}<small class="required">*</small></label>`);
      
         $('#supervisor_id option:first').text(translations.user_management_module.team_leader_module.team_leader_supervisor);
      }
      else
      {
         $('.worker_info').hide();
         $('.emp_dept_label').show();
         $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.emp_dept);
         $('#email').attr("type","email");
         $('.email_box').show();
         $(".phone_required_mark").show();
      }
      if(selected_user_type_val=='sp_admin')
      {
         $('.company_info').show();
      }
      else
      {
         $('.company_info').hide();
      }
      if(selected_user_type_val=='sp_admin')
      {
        $('#user_type_name').html(translations.data_service_provider.serviceProvider_forms.label.service_provider_name+' <small class="required">*</small>');
        $('#2').hide();
        $('#2').next('.current').hide();
        $('#3').hide();
        $('#3').next('.current').hide();
        $('#4 span:first').html('3');// @flip1@ change html content
        var sp_admin_company_list=$('#sp_admin_company_list').html();
        $('#service_provider').html(sp_admin_company_list);
      }
      if(selected_user_type_val=='admin')
      {
        $('#2').hide();
        $('#2').next('.current').hide();
        $('#3').hide();
        $('#3').next('.current').hide();
        $('#4 span:first').html('2');
      }


      if(user_type_val=='admin')
      {
        $('#2').hide();
        $('#2').next('.current').hide();
        $('#3').hide();
        $('#3').next('.current').hide();
        $('#4 span:first').html('2');
      }


      if(user_type_val=='building_manager')
      {
         //alert();
        //$('#3').hide();
        $('#4').next('.current').hide();
        $('#4 span:first').html('4');
      }

    if (user_type_val == 'building_manager') {
        $(".area-bma-manager").show();
    } else {
        $('.area-bma-manager').hide();
        $('#area-bma-manager').prop('checked', false);
    }

      if(user_type_val=='building_manager_employee')
      {
        $('.building_admin_info').show();
        $('#building_admin').prop("required", true);
      }


     if(user_type_val=='sp_admin'||user_type_val=='supervisor'||user_type_val=='sp_worker'||user_type_val=='team_leader')
      {
        $('.company_info').show();
      }
      else
      {
         $('.company_info').hide();
      }

      if(user_type_val=='supervisor')
      {
        $('.sup_info').show();
      }
      else
      {
        $('.sup_info').hide();
      }
      if(user_type_val=='sp_worker'||user_type_val=='team_leader')
      {
        $('.worker_info').show();
      }
      else
      {
        $('.worker_info').hide();
      }


   $("#user_type").on("change", function () {
    var user_type_val = $(this).val();
    sessionStorage.setItem('edituser_user_type', JSON.stringify(user_type_val));
    //alert(user_type_val);
    // if(user_type_val=='admin'||user_type_val=='spga_employee'||user_type_val=='building_manager'||user_type_val=='building_manager_employee'||user_type_val=='sp_admin')
    if(user_type_val!='')
    {
      $('.user_info').show();
      if(user_type_val=='sp_admin' || user_type_val=='admin_employee')
      {
        $('#user_type_name').html(translations.data_service_provider.serviceProvider_forms.label.service_provider_name+' <small class="required">*</small>');
        $('#2').hide();
        $('#3').hide();
      }

      if(user_type_val=='sp_admin'||user_type_val=='supervisor'||user_type_val=='sp_worker'||user_type_val=='team_leader')
      {
        $('.company_info').show();
      }

      if(user_type_val=='supervisor')
      {
        $('.sup_info').show();
      }
      else
      {
         $('.sup_info').hide();
      }
      if(user_type_val=='sp_worker')
      {
        $('.worker_info').show();
        $('.emp_dept_label').hide();
        $('.email_box').hide();
        $('#email').attr("type","hidden");
      }
      else if(user_type_val=='team_leader')
      {
        $('.worker_info').show();
        $('.emp_dept_label').hide();
        $('.email_box').hide();
        $('#email').attr("type","hidden");
      }
      else
      {
        $('.worker_info').hide();
        $('.emp_dept_label').show();
        $('#email').attr("type","email");
        $('.email_box').show();
      }

        if (user_type_val == 'building_manager') {
            $(".area-bma-manager").show();
        } else {
            $('.area-bma-manager').hide();
            $('#area-bma-manager').prop('checked', false);
        }

    }

});

function getEmpiddata(){
   sessionStorage.setItem('edituser_emp_id', JSON.stringify($('#emp_id').val()));
}

$('#name').keyup(function() {
      sessionStorage.setItem('edituser_name', JSON.stringify($(this).val()));
   });

   $('#emp_id').keyup(function() {
      sessionStorage.setItem('edituser_emp_id', JSON.stringify($(this).val()));
   });
   $('#isAkaunting_Vendor').change(function() {
           sessionStorage.setItem('edituser_isAkaunting_Vendor', JSON.stringify($(this).prop('checked')));
        });
   $('#email').keyup(function() {
      sessionStorage.setItem('edituser_email', JSON.stringify($(this).val()));
   });

   $('#emp_dept').keyup(function() {
      sessionStorage.setItem('edituser_emp_dept', JSON.stringify($(this).val()));
   });

   $('#phone').keyup(function() {
      sessionStorage.setItem('edituser_phone', JSON.stringify($(this).val()));
   });

$('#bma_area_manager').change(function (event) {
    // console.log(event)
    if (event.target.checked) {
        $.ajax({
            url: "{{ route('users.ajax_check_bma_area_manager_for_project',['userID'=>$data['u_data']->id]) }}",
            type: 'POST',
            data: {
                project_id: '{{$data['u_data']->project_id}}',
                locale: "en",
                _token: $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (returnedData) {
                console.log(returnedData);
                if(returnedData === '0')
                    $(".unique_area_manager_error").show();
                else
                    $(".unique_area_manager_error").hide();
            },
            error: function (data) {
                console.log(data);
            }
        });
    } else {
        $(".unique_area_manager_error").hide();
    }
    sessionStorage.setItem('edituser_bma_area_manager', JSON.stringify($(this).prop('checked')));
});
</script>
<script type="text/javascript">
  $(".select2").select2();
</script>
@if($data['u_data']->user_type != 'admin')
<script type="text/javascript">
 $("#user_type,#service_provider,#supervisor_id,#sp_admin_id,#building_admin,#spga_admin,#country_id,#city_id").select2({
   placeholder:translations.data_contract.contract_forms.place_holder.please_choose,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
 });

   $("#profession_id").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_profession,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });

   $("#nationality_id").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_nationality,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });
   $("#favorite_language").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_favorite_language,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });
</script>
@endif

@endsection
