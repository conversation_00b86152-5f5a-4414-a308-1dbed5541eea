@extends('layouts.app')
@section('content')
<div class="contents">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="shop-breadcrumb">
                    <div class="breadcrumb-main page-title__left">
                        <h4 class="text-capitalize breadcrumb-title">
<a href="{{ route('workorder.openWorkOrdersList', ['all', Crypt::encryptString(1)]) }}"><i class="las la-arrow-left"></i></a> {{__('work_order.bread_crumbs.create_new_work_order')}}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid">
        <div class="checkout wizard9 global-shadow pt-2 mb-30 w-100">
            <div class="row justify-content-center">
                <div class="col-xl-12">
                    <div class="row justify-content-center">
                        <div class="col-lg-3">
                            <div class="checkout-progress-indicator border-color p-30 radius-md  bg-white ">
                                <div class="checkout-progress2">
                                    <div class="step completed" id="1">
                                        <span><i class="fa fa-check" aria-hidden="true"></i></span>
                                        <span>{{__('work_order.bread_crumbs.maintenance_type')}}</span>
                                    </div>
                                    <div class="step completed" id="2">
                                        <span><i class="fa fa-check" aria-hidden="true"></i></span>
                                        <span>{{__('work_order.bread_crumbs.location')}}</span>
                                    </div>
                                    <div class="step completed" id="3">
                                        <span><i class="fa fa-check" aria-hidden="true"></i></span>
                                        <span>{{__('work_order.bread_crumbs.confirmation')}}</span>
                                    </div>            
                                </div>
                            </div>
                            <!-- checkout -->
                        </div>
                        <div class="col-lg-9">
                            <div class="card checkout-shipping-form px-30 pt-2 pb-0 border-color mt-lg-0 radius-md mt-30 pt-20">
                                <div class="row justify-content-center mt-30">
                            
                                    <div class="card-body px-20 pb-0 pt-0">
                                        {!! Form::open(['route' => 'asset.checklist.check-list-confirmation', 'method'=>'get']) !!}
                                        <div class="edit-profile__body px-30 py-30 after-success">
                                            <div class="text-center">
                                                <span class="las la-check s-complete"></span>
                                                <h3 class="fw-500 pb-15 pt-15">  {{__('work_order.forms.label.has_been_created')}}</h3>
                                                @if(App::getLocale()=='en')
                                                <p>The work order will be triggered <?=$data['frequency'];?> starting from <?=date('d/m/Y h:i A', strtotime($data['start_date']));?></p>
                                                @else
                                                <p>سيتم تشغيل أمر العمل <?=$data['frequency'];?> ابتداءً من  <span dir="ltr"> 
                                             <span class="time-posted">{{date('d/m/Y h:i A',strtotime($data['start_date']))}}</span>
                                            </span></p>
                                                @endif
                                            </div>

                                            


                                               <div class="button-group d-flex pt-25 justify-content-center mt-15">
                                                <a href = "@if(in_array(Auth::user()->user_type, array('osool_admin', 'superadmin'))) {{ route('workorder.openServiceProvidersList','all') }} @else {{ route('workorder.openWorkOrdersList', ['all', Crypt::encryptString(1)]) }} @endif" class = "btn btn-light btn-default btn-squared  radius-md shadow2 ml-20">@lang('work_order.button.Go_To_Work_Order_List')</a>
                                                  <!-- <a href="{{ route('workorder.show',"xyzxyz") }}" class="btn btn-primary btn-default btn-squared  radius-md shadow2 ml-20">{{__('work_order.button.view_work_order')}}</a> -->
                                               </div>
                                        </div>
                                        {!! Form::close() !!}
                                    </div>
                                </div>
                            </div>
                            <!-- ends: card -->
                        </div>
                        <!-- ends: col -->
                    </div>
                </div>
                <!-- ends: col -->
            </div>
        </div>
        <!-- End: .global-shadow-->
    </div>
</div>
@endsection
@section('scripts')
<script type="text/javascript">
    for (let key in sessionStorage) {
        if (key.startsWith('create_')) {
            sessionStorage.removeItem(key);
        }
    }   
    $("#contract_type, #property_name").select2({
        // placeholder: "Choose..",
        placeholder:translations.configration_checklist.checklist_forms.place_holder.please_choose,
        dropdownCssClass: "tag",
        //tags: ["red", "green", "blue"],
        allowClear: true,
        language: {
            noResults: function () {
                 return translations.general_sentence.validation.No_results_found;
            }
        }
    });
    $("#start_date, #end_date").datepicker({
        minDate: -20,
        maxDate: "+1M +10D"
    });

</script>
@endsection
