{{--@if(isset($floor_details))
@php $roomarr=array(); @endphp
    @foreach($floor_details as $fd)
    @php $rooms = json_decode($fd->rooms) @endphp
        @if(isset($rooms) && !empty($rooms))
            @foreach($rooms as $fl)
               @php $roomarr[]=$fl->floor; @endphp
            @endforeach
        @endif
    @endforeach
@endif  
@php $roomarr=array_unique($roomarr); @endphp --}}
@extends('layouts.app')
@section('content')
{{-- @dd($w_data->checklist_id) --}}
<div class="contents">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="shop-breadcrumb">
                    <div class="breadcrumb-main page-title__left">
                        <h4 class="text-capitalize breadcrumb-title"><a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a> 
                            {{__('work_order.bread_crumbs.Edit_Work_Order')}}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid">
        <div class="checkout wizard9 global-shadow pt-2 mb-30 w-100">
            <div class="row justify-content-center">
                <div class="col-xl-12">
                    <div class="row justify-content-center">
                        <div class="col-md-4">
                            <div class="checkout-progress-indicator card">
                                <div class="checkout-progress2 card-body px-15">
                                    <div class="step completed" id="1">
                                        <span><i class="fa fa-check" aria-hidden="true"></i></span>
                                        <span>{{__('work_order.bread_crumbs.maintenance_type')}}</span>
                                    </div>
                                    <div class="step current" id="2">
                                        <span>2</span>
                                        <span>{{__('work_order.bread_crumbs.location')}}</span>
                                    </div>
                                    <div class="step" id="3">
                                        <span>3</span>
                                        <span>{{__('work_order.bread_crumbs.confirmation')}}</span>
                                    </div>
                                </div>
                            </div>
                            <!-- checkout -->
                        </div>
                        <div class="col-md-8">
                            <div
                                class="card checkout-shipping-form px-30 pt-2 pb-0 mt-md-0 mt-30 pt-20">
                                <div class="row justify-content-center mt-30">
                                    <div class="card-body px-0 pb-0 pt-0 col-xl-7 col-lg-8 col-sm-10">
                                        <div class="edit-profile__body">
                                            {!! Form::open(['route' => 'workorder.edit-p.location.validate',
                                            'method' =>'post', 'id'=>'preventive_location']) !!}
                                            @csrf
                                            <input type="hidden" name="service_type" value="{{ $service_type }}">
                                            {{ Form::hidden('w_id', $w_data->id) }}
                                            {{ Form::hidden('w_unique_id', $w_data->unique_id) }}
                                            <div class="form-group row">
                                                <div class="col-md-6">
                                                    <label for="floor">{{__('work_order.forms.label.workorder_floor')}}
                                                        <span class="required">*</span></label>
                                                    <div class="atbd-select">
                                                    <select name="floor" id="floor" class="form-control" data-pro="{{$proprety_id}}" data-url="{{route('workorder.ajax-get-room-data')}}">
                                                            <option value="" selected disabled>{{__('work_order.forms.place_holder.Choose_Floor')}}</option>
                                                            @if(isset($building_floors))
                                                                @foreach($building_floors as $fl)
                                                                    <?php $deleted = '';
                                                                    // if($fl->deleted_at != NULL ){
                                                                    //     $deleted = __('general_sentence.modal.deleted');
                                                                    // }
                                                                    ?>
                                                                    <option value="{{ $fl->floor}}" {{($w_data->floor==$fl->floor)? "selected" : ""}}>{{ $fl->floor }}{{$deleted}}</option>
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                    </div>
                                                    <div id="floor_error"></div>
                                                </div>
                                                <input type="hidden" id="selected_room" value="{{ $w_data->unit_id }}">
                                                <div class="col-md-6 mt-sm-0 mt-3">
                                                    <label for="room">{{__('work_order.forms.label.workorder_room')}}
                                                        <span class="required">*</span></label>
                                                    <div class="atbd-select">
                                                        

                                                        <select name="room" id="room" class="form-control" data-url="{{route('workorder.ajax-get-asset-categories')}}">
                                                       
                                                       @foreach($building_rooms as $fl)
                                                       <?php 
                                                       $room_deleted = '';
                                                       if($fl->deleted_at != ''){
                                                           $room_deleted = __('general_sentence.modal.deleted');

                                                       } 
                                                       if( $fl->deleted_at == '' || $fl->id == $w_data->unit_id){
                                                       ?>
                                                           <option value="{{$fl->room}}" @if($w_data->unit_id == $fl->id) selected @endif>
                                                               {{$fl->room}} {{$room_deleted }}
                                                           </option>
                                                       <?php } ?>
                                                       @endforeach
                                                       </select>


                                                    </div>
                                                    <div id="room_error"></div>
                                                </div>
                                            </div>
                                            <input type="hidden" name="property_id" id="property_id" value="<?=$proprety_id;?>">
                                            @if($service_type=='hard_service')
                                            <div class="hard_services">
                                                
                                                <div class="form-group mb-25">
                                                    <label
                                                        for="asset_category">{{__('work_order.forms.label.asset_category')}}
                                                        <span class="required">*</span></label>
                                                    <div class="atbd-select">
                                                        <select name="asset_category" id="asset_category"
                                                            class="form-control"
                                                            data-url="{{ route('workorder.ajax-asset-name-get-s') }}">
                                                            @if($asset_category!="no")
                                                            @foreach ($asset_category as $av)
                                                            @if($av->deleted_at == '' || $w_data->asset_category_id==$av->id )
                                                            <option value="{{ $av->id }}" {{($w_data->asset_category_id==$av->id)? "selected" : ""}}>{{ $av->asset_category }}
                                                            </option>
                                                            @endif
                                                            @endforeach
                                                            @endif
                                                        </select>
                                                    </div>
                                                    <div id="asset_category_error"></div>
                                                </div>
                                            <div id="asset_names_div" class="@if(isset($edit_asset_name) && count($edit_asset_name) == 0) d-none @endif"> 
                                                <div class="form-group mb-25">
                                                    <div class="checkbox-theme-default custom-checkbox ">
                                                        <input class="checkbox" type="checkbox" <?php if(Empty($w_data->asset_name_id) ||Empty($w_data->asset_number_id) || $w_data->asset_number_id ==0 || $w_data->asset_name_id == 0){echo 'checked'; } ?>
                                                            id="asset_check_preventive" name="asset_check_preventive">
                                                        <label for="asset_check_preventive">
                                                            <span class="checkbox-text">
                                                                {{__('work_order.forms.label.Asset_is_not_registered_in_the_system')}}
                                                            </span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-25">
                                                    <label for="asset_name">{{__('work_order.forms.label.asset_name')}}
                                                        <span class="required">*</span></label>
                                                    <div class="atbd-select">
                                                        <select name="asset_name" id="asset_name" class="form-control"
                                                            data-url="{{ route('workorder.ajax-asset-number-get-s') }}">
                                                            <option value="">
                                                                {{__('work_order.forms.place_holder.Choose_asset_name')}}
                                                            </option>
                                                            @foreach ($edit_asset_name as $av)
                                                            <option value="{{ $av->id }}" {{($w_data->asset_name_id==$av->id)? "selected" : ""}}>{{ $av->asset_name }} - {{ $av->asset_symbol }}@if($av->deleted_at != NULL || $av->is_deleted != 'no'){{__('general_sentence.modal.deleted')}}@endif
                                                            </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div id="asset_name_error"></div>
                                                </div>
                                                <div class="form-group mb-25">
                                                    <label
                                                        for="asset_number">{{__('work_order.forms.label.asset_number')}}
                                                        <span class="required">*</span></label>
                                                    <div class="atbd-select d-flex">
                                                        
                                                        <select name="asset_number[]" id="asset_number"
                                                            class="form-control" multiple>
                                                            @if ($edit_asset_number)
                                                                @foreach ($edit_asset_number as $av)
                                                                @if($w_data->asset_number_id==$av->id || ($av->deleted_at == NULL && $av->is_deleted == 'no'))
                                                                <option value="{{ $av->id }}" {{($w_data->asset_number_id==$av->id)? "selected" : ""}}>{{ $av->asset_number }} @if(!$av->hide_asset_symbol) - {{ $av->asset_symbol }} @endif @if($av->deleted_at != NULL || $av->is_deleted != 'no'){{__('general_sentence.modal.deleted')}}@endif
                                                                </option>
                                                                @endif
                                                                @endforeach
                                                            @endif
                                                            
                                                        </select>
                                                        {{--
                                                        <!--===========Veiw Asset=========-->
                                                         <ul class="orderDatatable_actions view_checklist_ul mt-1 ml-2 max-w-0 min-w-0 fw-normal"><li>                                                  
                                                        <a id="view_checklist" href="javascript:void(0);" data-toggle="modal" data-target="#checklist-yes" class="text-primary">{{__('general_sentence.status_button.view')}}</a></li></ul>
                                                        <!--===========Veiw Asset=========-->
                                                        --}}
                                                    </div>
                                                    <div id="asset_number_error"></div>
                                                </div>
                                            </div>

                                            </div>

                                            @endif
                                            <!--end hard service -->

                                            @if($service_type=='soft_service')
                                            <div class="form-group mb-25">
                                                <label
                                                    for="service_category">{{__('work_order.forms.label.service_category')}}
                                                    <span class="required">*</span></label>
                                                <div class="atbd-select">
                                                    <select name="service_category" id="service_category" data-url="{{ route('workorder.ajax-asset-name-get-s') }}"
                                                        class="form-control">
                                                        <option value="" selected disabled>
                                                            {{__('work_order.forms.place_holder.Choose_asset_name')}}
                                                        </option>
                                                        @if($asset_category!="no")
                                                            @foreach ($asset_category as $av)
                                                            <option value="{{ $av->id }}" {{($w_data->service_category_id==$av->id)? "selected" : ""}}>{{ $av->asset_category }}
                                                            </option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                                <div id="service_category_error"></div>
                                            </div>
                                            @endif
                                            <div class="form-group mb-25 d-none" id="contract_div" >
                                            <div class="form-group mb-25">
                                                <label for="contract"
                                                    class="">{{__('work_order.forms.label.contract')}}
                                                    <span class="required">*</span></label>
                                                <div class="atbd-select">
                                                    <select name="contract" id="contract" class="form-control">
                                                        <option value="" disabled>
                                                            {{__('work_order.forms.place_holder.Choose_contracts')}}
                                                        </option>
                                                        <?php
                                                            if($w_data->contract_type == 'warranty') {
                                                                $w_data->contract_type = 'Warrenty';
                                                            } else {
                                                                $w_data->contract_type = 'Regular';
                                                            }
                                                        ?>
                                                        @foreach ($contracts_list as $cv)

                                                        <?php
                                                            $selected = '';
                                                            $deleted = '';
                                                            $loop_key = $cv['id'].'-'.$cv['contract_types'];

                                                            $sel_key = $w_data->contract_id.'-'.$w_data->contract_type;
                                                            //echo $sel_key;
                                                            //echo  $loop_key;
                                                        ?>
                                                        @if($loop_key == $sel_key)
                                                        <?php
                                                        $selected = 'selected';
                                                        ?>
                                                        <script>
                                                            document.selected_contract = <?=json_encode($cv);?>
                                                        </script>
                                                        @endif
                                                        <?php
                                                        $deleted = '';
                                                        if($cv['deleted_at'] != ''){
                                                            $deleted = __('general_sentence.modal.deleted');
                                                        }
                                                        ?>
                                                        @if($cv['deleted_at'] == '' ||  $loop_key == $sel_key)
                                                       
                                                        <option value="{{ $cv['id'] }}-{{ $cv['contract_types'] }}" {{ $selected }}>
                                                            {{ 'CONT'.str_pad($cv['id'], 4, '0', STR_PAD_LEFT) }}({{ $cv['contract_number'] }})-{{ $cv['contract_types'] }} {{$deleted}}
                                                        </option>
                                                        @endif                                                            

                                                        @endforeach

                                                        <?php //die; ?>
                                                    </select>
                                                </div>
                                                <div id="contract_error"></div>
                                            </div>
                                            </div>
                                            <input type="hidden" id="singleContract" value="" />
                                            <input type="hidden" id="ppm_report_status" name="ppm_report_status" value="{{$ppm_report_status}}" />
                                            @if(($w_data->is_force_edit == 0 && $ppm_report_status == 'approved') || ($ppm_report_status == 'pending'))
                                            <input type="hidden" id="force_edit_status" name="force_edit_status" value="disabled" />
                                            @else
                                            <input type="hidden" id="force_edit_status" name="force_edit_status" value="enabled" />
                                            @endif
                                            <input type="hidden" id="wo_is_force_edit" name="wo_is_force_edit" value="{{$w_data->is_force_edit or 0}}" />
                                            
                                            <input type="hidden" id="old_frequency" name="old_frequency" value="{{ $w_data->frequency_id }}" />
                                            <input type="hidden" id="old_startdate" name="old_startdate" value="{{date('d-m-Y', strtotime($w_data->start_date)) }}" />
                                            @php
                                                if(isset($pm_end_date) && $pm_end_date !='' )
                                                {
                                                    $old_enddate = $pm_end_date;
                                                }
                                                else
                                                {
                                                    $old_enddate = date('d-m-Y', strtotime($max_date->max_date_entered));
                                                }
                                            @endphp
                                            <input type="hidden" id="old_enddate" name="old_enddate" value="{{$old_enddate}}" />
                                            <input type="hidden" id="old_customdate" name="old_customdate" value="{{isset($preSelectedDates) && !empty($preSelectedDates) ? implode(',',$preSelectedDates) : '' }}" />
                                            @php
                                                        $current_checkId=null;
                                                        if(!empty($get_current_checklist)){
                                                            $current_checkId=$get_current_checklist->id;
                                                        }
                                                         
                                                    @endphp
                                            <input type="hidden" id="old_checklist" name="old_checklist" value="{{$current_checkId}}" />
                                            @if(($w_data->is_force_edit == 0 && $ppm_report_status == 'approved') || ($ppm_report_status == 'pending'))
                                            <div class="form-group">
                                                <div class="alert alert-warning force-alert" role="alert">
                                                    <div class="d-flex gap-10 align-items-center">
                                                  <div class="">
                                                      <strong class="">{{__('ppm.Edit_disabled')}}</strong>
                                                      <p class="mb-0 fs-12">{{__('ppm.force_edit_term_1')}}</p>
                                                  </div>
                                                  @if($ppm_report_status == 'approved')                                     
                                                    <div class="">
                                                        <input class="checkbox d-none" type="radio" id="force_edit" name="asset_check_preventive">
                                                        <label for="force_edit" class="py-2 px-3 bg-white rounded-pill no-wrap cursor-pointer mb-0">
                                                        {{__('ppm.Force_Edit')}}
                                                        </label>
                                                    </div>
                                                    @endif
                                                    </div>
                                                </div>

                                                <div class="alert alert-danger cancel-alert" role="alert">
                                                    <div class="d-flex gap-10 align-items-center my-2">
                                                  <div class="">
                                                      <strong class="">{{__('ppm.Edit_enabled')}}</strong>
                                                      <p class="mb-0 fs-12">{{__('ppm.force_edit_term_2')}}</p>
                                                  </div>                                     
                                                    <div class="">
                                                        <input class="checkbox d-none" type="radio" id="cancel_edit" name="asset_check_preventive">
                                                        <label for="cancel_edit" class="py-2 px-3 bg-white rounded-pill no-wrap cursor-pointer mb-0">
                                                        {{__('ppm.Cancel_Edit')}}
                                                        </label>
                                                    </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endif

                                            <div class="form-group mb-25">
                                                <label for="frequency">{{__('work_order.forms.label.frequency')}}
                                                    <span class="required">*</span></label>
                                                <div class="atbd-select">
                                                    <?php
                                                    $disabled = "";
                                                    if($check_wo_started == 1)
                                                    {
                                                        $disabled = "disabled";
                                                        ?>
                                                        <input type="hidden" name="frequency" id="hidden_frequency" value="{{ $w_data->frequency_id }}">
                                                        <?php
                                                    }
                                                    else
                                                    {
                                                        ?>
                                                        <input type="hidden" name="frequency" id="hidden_frequency" value="{{ $w_data->frequency_id }}" disabled>
                                                        <?php
                                                    }
                                                    ?>
                                                    <select name="frequency" id="frequency" class="form-control force-enable" <?=$disabled;?> disabled>
                                                        {{-- <option value="" selected disabled>
                                                        {{__('work_order.forms.place_holder.Choose_Frequency')}}
                                                        </option> --}}
                                                        @foreach ($frequency_list as $fl)
                                                            <option value="{{ $fl->id }}" {{($w_data->frequency_id==$fl->id)? "selected" : ""}}>@if( Config::get('app.locale')=='ar'){{ $fl->title_ar }}  @else {{ $fl->title}} @endif </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div id="frequency_error"></div>
                                            </div>

                                            <div class="form-group mb-25">
                                                <label for="priority">{{__('work_order.forms.label.select_priority')}}
                                                    <span class="required">*</span></label>
                                                <div class="atbd-select">
                                                    <select  name="priority_id" id="priority" class="form-control">
                                                        <option value=""  disabled>
                                                        {{__('work_order.forms.label.select_priority')}}
                                                        </option>
                                                    </select>
                                                </div>
                                                <div id="priority_id_error"></div>
                                            </div>
                                            
                                            <div id = "changeForCustomFrequency">

                                            <div id = "twoCalendar" class="twoCalendar">
                                            @if($w_data->frequency_id != 25)
                                            <div class="form-group mb-25 form-group-calender">
                                                <label for="w_start_date">{{__('work_order.forms.label.start_date')}}
                                                    <span class="required">*</span></label>
                                                <div class="position-relative">
                                                    <?php
                                                    $disabled = "";
                                                    if($check_wo_started == 1)
                                                    {
                                                        $disabled = "disabled";
                                                        ?>
                                                        <input type="hidden" name="w_start_date" id="hidden_w_start_date" value="{{date('d-m-Y', strtotime($w_data->start_date)) }}" class="bg-white" />
                                                        <?php
                                                    }
                                                    else
                                                    {
                                                        ?>
                                                        <input type="hidden" name="w_start_date" id="hidden_w_start_date" value="{{date('d-m-Y', strtotime($w_data->start_date)) }}"   class="bg-white" disabled />
                                                        <?php
                                                    }
                                                    ?>
                                                    <input type="text"
                                                        class="form-control  ih-medium ip-light radius-xs px-15 force-enable"
                                                        name="w_start_date" id="w_start_date" value="{{date('d-m-Y', strtotime($w_data->start_date)) }}"
                                                        autocomplete="off"   <?=$disabled;?> disabled>
                                                    <a><span data-feather="calendar"></span></a>                                                    
                                                </div>
                                                <div id="w_start_date_error"></div>
                                            </div>
                                            
                                            <div class="form-group mb-25 form-group-calender">
                                                <label for="w_end_date">{{__('work_order.forms.label.end_date')}}
                                                    <span class="required">*</span></label>
                                                <div class="position-relative">
                                                    <input readonly type="text"
                                                        class="form-control  ih-medium ip-light radius-xs b-light px-15 force-enable"
                                                        name="w_end_date" id="w_end_date" value="@if(isset($pm_end_date) && $pm_end_date !='' ){{$pm_end_date}} @else{{date('d-m-Y', strtotime($max_date->max_date_entered)) }} @endif"
                                                        autocomplete="off" disabled>
                                                    <a><span data-feather="calendar"></span></a>
                                                </div>
                                                <div id="w_end_date_error"></div>
                                            </div>

                                            @else
                                            <script>
                                                
                                            </script>
                                            <div id="oneCalendar" class="twoCalendar"> 
                                                <div class="form-group mb-1 form-group-calender "> <label for="w_custom_dates">{{__('work_order.forms.label.select_multiple_dates')}}<span class="required">*</span></label>
                                            <div class="position-relative"> 
                                                <div class="">
                                                    <input readonly type="text" readonly  required   value="<?php if(isset($a)){echo'';}?>"     class="form-control px-15 bg-white force-enable"   name="w_custom_dates" id="w_custom_dates" placeholder="01/10/2021"   autocomplete="off"> 
                                                    <a><span data-feather="calendar"></span></a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group mb-25 ">
                                        <div class="position-relative"> 
                                            <div class="">
                                                    <input type="text"     disabled   class="form-control px-15  mt-2"    id="w_custom_dates_count" placeholder="{{__('work_order.forms.label.select_multiple_dates')}}"   autocomplete="off"></div> </div> <span class="text-danger error-text w_custom_dates_error "></span>
                                                </div> 
                                            </div>             
                                            @endif
                                            </div>
                                            </div> 
                                            
                                            <div class="form-group mb-25 checklist-div" @if(isset($w_data->contract_type) && $w_data->contract_type != "Regular") style="display:none" @endif>
                                                <label
                                                    for="choose_asset_checklist">{{__('work_order.forms.label.choose_asset_checklist')}}</label>
                                                <!-- <a href="{{ route('asset.checklist.add-check-list') }}" target="_blank"
                                                    class="float-right"> <small
                                                        class="fs-12">{{__('work_order.button.Create_new_checklist')}}</small></a> -->
                                                <div class="atbd-select d-flex">
                                                    @php
                                                        $current_checkId=null;
                                                        if(!empty($get_current_checklist)){
                                                            $current_checkId=$get_current_checklist->id;
                                                        }
                                                         
                                                    @endphp
                                                    <select name="choose_asset_checklist" id="choose_asset_checklist"
                                                        class="form-control force-enable" disabled>
                                                        <option value="" selected>
                                                            {{__('work_order.forms.place_holder.Choose_Checklist')}}
                                                        </option>
                                                        @if (!empty($get_check_list))

                                                            @foreach ($get_check_list as $chklist)
                                                            @if($chklist->deleted_at != '' || $chklist->is_deleted != 'no')
                                                            <?php $deleted = __('general_sentence.modal.deleted'); ?>
                                                            @else
                                                            <?php $deleted = ''; ?>

                                                            @endif
                                                            @if(($chklist->deleted_at == '' && $chklist->is_deleted == 'no') || $current_checkId==$chklist->id )

                                                            <option value="{{ $chklist->id }}" {{($current_checkId==$chklist->id)? "selected" : ""}}>{{ $chklist->checklist_title}} {{ $deleted}}
                                                            </option>
                                                            @endif
                                                        @endforeach
                                                        @endif
                                                    </select>
                                                    {{-- @flip1@ add ul for add view option --}}
                                                    <ul style=" @if(isset($w_data->checklist_id)&&$w_data->checklist_id== '0') display:none; @endif" class="orderDatatable_actions view_checklist_ul mt-1 ml-2 max-w-0 min-w-0 fw-normal"><li>                                                  
                                                        <a id="view_checklist" href="javascript:void(0);" data-toggle="modal" data-target="#checklist-yes" class="text-primary ">{{__('general_sentence.status_button.view')}}</a></li></ul>
                                                </div>
                                                <span
                                                    class="text-danger error-text choose_asset_checklist_error "></span>
                                            </div>
                                            {{-- @if($service_type=='soft_service') --}}
                                            <div class="form-group mb-25">
                                                <label
                                                    for="work_order_description">{{__('work_order.forms.label.description')}}
                                                    <span class="required">*</span></label>
                                                <!--input type="text"
                                                    class="form-control ih-medium ip-lightradius-xs b-light"
                                                    id="work_order_description" name="work_order_description"
                                                    placeholder="{{__('work_order.forms.place_holder.description')}}" value="{{$w_data->description}}"-->
                                                    <textarea class="form-control m-h-150" id="work_order_description" name="work_order_description" placeholder="{{__('work_order.forms.place_holder.description')}}">{{$w_data->description}}</textarea>
                                                    <div id="work_order_description_error"></div>
                                            </div>
                                            <div class="@if((Auth::user()->user_type == 'building_manager'|| Auth::user()->user_type == 'building_manager_employee') && Helper::checkSubPrivilege(Auth::user()->id,'work_order_approve')==false) d-none @endif ">
                                                <input type="checkbox" id="bm_approove"  <?php if($w_data->bm_approove==0){echo 'checked';} ?> name="bm_approove" value="0">
                                            
                                                <label for="bm_approove">{{__('work_order.forms.label.skip_approvals')}} <i class="fa fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('work_order.forms.label.skip_approvals_hint_bm')}}"></i></label>
                                            </div>
                                            {{-- @endif --}}
                                            <div class="button-group d-flex pt-25 justify-content-end mb-40">
                                                {{-- <a href="{{route('workorder.workorders.list')}}" class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md">{{__('work_order.button.cancel')}}</a> --}}
                                                <a href="Javascript:history.back()" class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md"> <i class="las la-arrow-left mr-10"></i> {{__('work_order.button.previous')}}</a>
                                                <button type="submit"
                                                    class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.button.save_nd_next')}}
                                                </button>
                                            </div>
                                            {!! Form::close() !!}
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <!-- ends: card -->
                        </div>
                        <!-- ends: col -->
                    </div>
                </div>
                <!-- ends: col -->
            </div>
        </div>
        <!-- End: .global-shadow-->
    </div>
</div>

<input type="hidden" name="get_checklists_asset_ids" id="get_checklists_asset_ids" value="<?=route('workorder.get_checklists_asset_ids')?>" />
{{-- @flip1@ modal --}}
<div class="modal fade" id="checklist-yes" tabindex="-1" role="dialog" aria-labelledby="checklistModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{__('configration_checklist.checklist_table.checklist_details')}}: <a class="text-primary checklist_heading">{{ $checklists!=[]?$checklists['checklist_title']:'' }}</a></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>        
            <div class="modal-body">
                <div class="checkout-shipping-form asset_form_container pb-0 mt-0">
                    <div class="">
                        <h6>{{__('configration_checklist.checklist_bread_crumbs.checklist_tasks')}}</h6>
                    </div>

                    <div class="my-3">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="about-projects">
                                @if(isset($checklists['tasks']))
                                @foreach($checklists['tasks'] as $key => $checklist_tasks)
                                <div class="atbd-collapse atbd-collapse-custom">                        
                                    <div class="atbd-collapse-item mb-0">
                                        <div class="atbd-collapse-item__header @if($key == 0) active @endif">
                                            <a href="#" class="item-link @if($key == 0) border-bottom @else collapsed @endif" data-toggle="collapse" data-target="#collapse-body-c-{{$key}}" @if($key == 0) aria-expanded="true" @else aria-expanded="false" @endif aria-controls="collapse-body-c-{{$key}}">
                                                <i class="la la-angle-right"></i>
                                                <h6>{{$checklist_tasks['task_title']}}</h6>
                                            </a>
                                        </div>
                                        <div id="collapse-body-c-{{$key}}" class="collapse bg-white pt-3 atbd-collapse-item__body @if($key == 0) show @endif">
                                            <div class="collapse-body-text">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="closed_at" class="text-dark">{{__('configration_checklist.checklist_table.task_number')}}</label>
                                                            <p> {{str_replace('Task',__('general_sentence.breadcrumbs.task'), $checklist_tasks['task_number'])}}</p>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="closed_at" class="text-dark">{{__('configration_checklist.checklist_table.task_title')}}</label>
                                                            <p> {{$checklist_tasks['task_title']}}</p>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <label for="closed_at" class="text-dark">{{__('configration_checklist.checklist_table.description')}}</label>
                                                            <p> {{$checklist_tasks['description']}} </p>
                                                        </div>
                                                    </div>

                                                    <div class="col-12">
                                                        <h6 class="mb-3">{{__('configration_checklist.common.selected_actions')}}</h6>
                                                    </div>
                                                    @if($checklist_tasks['photos'] == 'yes' || $checklist_tasks['comment'] == 'yes' || $checklist_tasks['multiple_options'] != NULL)
                                                        @if($checklist_tasks['photos'] == 'yes')
                                                        <div class="col-12">
                                                            <div class="">
                                                                <label class="text-dark"> <span data-feather="check"></span> {{__('configration_checklist.checklist_table.photos')}} </label>                                                
                                                            </div>
                                                        </div>
                                                        @endif
                                                        @if($checklist_tasks['comment'] == 'yes')
                                                        <div class="col-12">
                                                            <div class="">
                                                                <label for="closed_at" class="text-dark"><span data-feather="check"></span> {{__('configration_checklist.checklist_table.comment')}} </label>
                                                            </div>
                                                        </div>
                                                        @endif
                                                        @if($checklist_tasks['multiple_options'] != NULL)
                                                        <div class="col-12">
                                                            <div class="form-group">
                                                                <label for="closed_at" class="text-dark"><span data-feather="check"></span> {{__('configration_checklist.checklist_forms.label.multiple_options')}}  </label>
                                                                <div class="pl-sm-4">
                                                                    <div class="row">
                                                                        <?php $multiple_options = explode(',', $checklist_tasks['multiple_options']); ?>
                                                                        @foreach($multiple_options as $key => $mo)
                                                                        <div class="col-6 mb-3">
                                                                            <label class="text-dark">{{__('configration_checklist.checklist_forms.label.option')}} {{$key + 1}}</label>
                                                                            <p>{{$mo}}</p>
                                                                        </div>
                                                                        @endforeach
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @endif
                                                    @else
                                                    <div class="col-12">
                                                        <div class="">
                                                            <label class="">{{__('configration_checklist.common.no_actions_added')}}</label>
                                                        </div>
                                                    </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    
                                    </div>
                                </div>
                                @endforeach                                            
                                @endif                                            
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>                    
</div>
@endsection
@section('scripts')
<script>
<?php
//$floor_details = json_decode(json_encode($floor_details), true);
//dd($service_category);
//if(!in_array($w_data->floor, $floor_details))
if($w_data->property_changed == 1)
{
    //dd("yes");
    ?>
    $("#floor").empty();
    $("#floor").append(
        $("<option ></option>")
            .attr("value", "")
            .text(translations.configration_checklist.checklist_forms.place_holder.please_choose)
    );
    $("#service_category").empty();
    $("#service_category").append(
        $("<option ></option>")
            .attr("value", "")
            .text(translations.configration_checklist.checklist_forms.place_holder.please_choose)
    );
    <?php
    if(isset($floor_details) && !empty($floor_details))
    {
        ?>
        var floor_details = <?=$floor_details;?>;//alert(floor_details);
        $.each(floor_details, function (key, value) {
            $("#floor").append(
                $("<option></option>")
                    .attr("value", value)
                    .text(value)
            );
        });
        <?php
    }
    if(isset($service_category) && !empty($service_category))
    {
        ?>
        var service_category = <?=$service_category;?>;//alert(floor_details);
        $.each(service_category, function (key, value) {
            $("#service_category").append(
                $("<option></option>")
                    .attr("value", value.id)
                    .text(value.asset_category)
            );
        });
        <?php
    }
    ?>
    $("#room").empty();
    $("#asset_category").empty();    
    $("#asset_name").empty();
    $("#asset_number").empty();
    $("#contract").empty();
    //$("#frequency").empty();
    $("#priority").empty();
    //$("#w_start_date").val('');
    //$("#hidden_w_start_date").val('');
    $("#w_end_date").val('');
    //$("#choose_asset_checklist").empty();
    $('#asset_check_preventive').prop('checked',false);
    <?php
}
?>
if($('#asset_check_preventive').prop("checked") == true)
{
    $("#asset_number").prop("disabled", true);
    $("#asset_number").empty();                
    $("#asset_name").prop("disabled", true);
    $("#asset_name").prop("required", false);
    $("#asset_name_error").text('');
    $("#asset_number_error").html('');
    var elements = document.getElementById("asset_name").options;
    for(var i = 0; i < elements.length; i++){
        elements[i].selected = false;
    }
    $("#asset_name").change();
}
else
{
    $("#asset_number").prop("disabled", false);
    $("#asset_name").prop("disabled", false);
    $("#asset_name").prop("required", true);
}
$('#asset_check_preventive').change(function () {
    if (this.checked) {
        $("#asset_number").prop("disabled", true);
        $("#asset_number").empty();                
        $("#asset_name").prop("disabled", true);
        $("#asset_name").prop("required", false);
        $("#asset_name_error").text('');
        $("#asset_number_error").html('');
        var elements = document.getElementById("asset_name").options;
        for(var i = 0; i < elements.length; i++){
            elements[i].selected = false;
        }
        $("#asset_name").change();                
    } else {
        $("#asset_number").prop("disabled", false);
        $("#asset_name").prop("disabled", false);
        $("#asset_name").prop("required", true);
    }
});

$("#asset_category").on("change", function () {
    let value_cnt = $(this).val();
    let property_id = $("#property_id").val();
    let floor = $("#floor").val();
    let room = $("#room").val();

    var edit_selectedasset_category = 'edit_selectedasset_category';
        sessionStorage.setItem(edit_selectedasset_category, JSON.stringify(value_cnt));

    $('.view_checklist_ul').hide();// @flip1@ when asset cat chenge then view opt should be hidden.
    
    $.ajax({
        url: $(this).data("url"),
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            id: value_cnt,
            property_id: property_id,
            floor: floor,
            room: room,
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
            console.log(data);
            $("#asset_name").empty();
            $("#asset_number").empty();

            if(data[0].length>0){
                $('#asset_names_div').removeClass('d-none')
                $('#asset_check_preventive').prop("checked", false)
                $("#asset_name").prop("disabled", false);
                $("#asset_number").prop("disabled", false);
            }
            else{
                $('#asset_names_div').addClass('d-none')
                $('#asset_check_preventive').prop("checked", true)
                $("#asset_name").prop("disabled", true);
                $("#asset_number").prop("disabled", true);
                $(".asset_name_error").text('');
                $(".asset_number_error").html('');
            }
            $("#asset_name").append(
                $("<option></option>")
                    .attr("value", "")
                    .text(translations.work_order.forms.place_holder.choose_asset_number)
            );
            $("#asset_number").append(
                $("<option></option>")
                    .attr("value", "")
                    .text(translations.work_order.forms.place_holder.choose_asset_number)
            );

            $.each(data[0], function (key, value) {
                $("#asset_name").append(
                    $("<option></option>")
                        .attr("value", value.id)
                        .text(value.asset_name + " - " + value.asset_symbol)
                );
            });



            $("#contract").empty();
            $("#contract").append(
                $("<option></option>")
                    .attr("value", "")
                    .text(translations.work_order.forms.place_holder.Choose_contracts)
            );
            $("#contract").empty();
                if(data[1].length > 0){

                $("#contract").append(
                    $("<option selected disabled></option>")
                        .attr("value", "")
                        .text(translations.work_order.forms.place_holder.Choose_contracts)
                );
                }
                else{
                    $("#contract").empty();

                    $("#contract").append(
                    $("<option selected disabled></option>")
                        .attr("value", "")
                        .text(translations.general_sentence.validation.No_contracts_found1)
                );
                }
                
                if(data[1].length == 1){
                $.each(data[1], function (key, value) {
                var id = value.id;
                num = id.toString();
                while (num.length < 3) num = "000" + num;
                $("#contract").append(
                    $("<option></option>")
                        .attr("value", value.id+'-'+value.contract_types)
                        .text('CONT'+num+'('+value.contract_number+')-'+value.contract_types).prop("selected", true)
                );
                $("#singleContract").val(value.id+'-'+value.contract_types);
            });
            
            $("#contract_div").addClass("d-none");
            populateFrequencies()
            contratChanged();
            }
            else{
                $.each(data[1], function (key, value) {
                var id = value.id;
                num = id.toString();
                while (num.length < 3) num = "000" + num;
                $("#contract").append(
                    $("<option></option>")
                        .attr("value", value.id+'-'+value.contract_types)
                        .text('CONT'+num+'('+value.contract_number+')-'+value.contract_types)
                        
                );
            });
            $("#contract_div").removeClass("d-none");

            }
            document.contract= data[1];
            
            console.log(document.contract);
        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 1500,
                positionClass: "toast-top-center",
                progressBar: true,
                preventDuplicates: true,
                preventOpenDuplicates: true,
            });
        },
    });


    $.ajax({
        url: '<?=route('workorder.get_checklists_asset_ids')?>',
        method: "POST",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            asset_category_id: value_cnt,
            property_id: '<?=$proprety_id?>',
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
           console.log(data)

           
           $("#choose_asset_checklist").empty();
           $("#choose_asset_checklist").append(
                $("<option></option>")
                    .attr("value", "")
                    .text(translations.work_order.forms.place_holder.Choose_Checklist)
            );
            $.each(data, function (key, value) {
                $("#choose_asset_checklist").append(
                    $("<option></option>")
                        .attr("value", value.id)
                        .text(value.checklist_title)
                );
            });


        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 1500,
                positionClass: "toast-top-center",
                progressBar: true,
                preventDuplicates: true,
                preventOpenDuplicates: true,
            });
        },
    });


});
</script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-ui-multidatespicker/1.6.6/jquery-ui.multidatespicker.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-ui-multidatespicker/1.6.6/jquery-ui.multidatespicker.min.css" rel="stylesheet"/>
<script type="text/javascript" src="{{asset('js/admin/workorder/Preventive/edit.js')}}"></script>

<script type="text/javascript">

function isInArray(array, value) {

return (array.find(item => {return item == value}) || []).length > 0;
}

    $("#asset_category, #asset_name, #frequency,#priority, #contract, #service_category, #floor, #room")
        .select2({
            // placeholder: "Choose..",
            placeholder:translations.configration_checklist.checklist_forms.place_holder.please_choose,
            language: {
                noResults: function () {
                    return translations.general_sentence.validation.No_results_found;
                }
            }
        });
    $("#choose_asset_checklist").select2({
        language: {
            noResults: function () {
                 return translations.general_sentence.validation.No_results_found;
            }
        }
    }); 
    $("#asset_number").select2({
        // placeholder: "Choose..",
        placeholder:translations.configration_checklist.checklist_forms.place_holder.please_choose,
        dropdownCssClass: "tag",
        language: {
            noResults: function () {
                 return translations.general_sentence.validation.No_results_found;
            }
        }
    });

$(document).ready(function () {
    var language = '<?php echo Config::get('app.locale'); ?>';

var select_multipleDates = 'Select Multiple Dates';
if(language == 'ar'){
    select_multipleDates = 'حدد عدة تواريخ'; 
}
    document.twoCalendar = `<div id="twoCalendar" class="twoCalendar"> 
        <div class="form-group mb-25 form-group-calender"> <label for="w_start_date">{{__('work_order.forms.label.start_date')}}<span class="required">*</span></label><div class="position-relative"> <input readonly type="text" class="form-control  ih-medium ip-light radius-xs b-light px-15"  name="w_start_date" id="w_start_date" placeholder="01/10/2021" autocomplete="off" style="background:#fff !important;"><a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-calendar"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg></a> </div> <span class="text-danger error-text w_start_date_error "></span></div> <div class="form-group mb-25 form-group-calender">
          <label for="w_end_date">{{__('work_order.forms.label.end_date')}}  <span class="required">*</span></label>  <div class="position-relative">   <input readonly type="text" class="form-control  ih-medium ip-light radius-xs px-15"  name="w_end_date" id="w_end_date" placeholder="01/10/2021"  autocomplete="off" style="background:#fff !important;"> <a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-calendar"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg></a> </div>  <span class="text-danger error-text w_end_date_error "></span>  </div> </div>`;

    document.oneCalendar = `<div id="oneCalendar"> <div class="form-group mb-1 form-group-calender"> <label for="w_custom_dates">
    ${select_multipleDates} <span class="required">*</span></label><div class="position-relative"> <div class=""><input readonly type="text" novalidate required  novalidate  class="form-control px-15 force-enable"   name="w_custom_dates" id="w_custom_dates" placeholder="01/10/2021"   autocomplete="off" style="background:#fff !important;"><a><span data-feather="calendar"></span></a></div></div></div>
        <div class="form-group mb-25 "><div class="position-relative">  <div class=""> <input readonly type="text"     disabled   class="form-control px-15  mt-2"     id="w_custom_dates_count" placeholder="${select_multipleDates}" autocomplete="off"  style="background:#fff !important;"> <a><span data-feather="calendar"></span></a></div> </div> <span class="text-danger error-text w_custom_dates_error "></span></div> </div>`;
    
    document.contract = <?php echo json_encode($contracts_list); ?>;
    console.log(document.contract);
    console.log(document.selected_contract);
    if(document.contract.length != 0){
        if(document.selected_contract){
            document.mindate = document.selected_contract.start_date;
            document.maxdate = document.selected_contract.end_date;
            document.mindate2 = new Date('<?=$w_data->start_date;?>');
        }
        //alert(document.mindate2);
        var check_wo_started = <?=$check_wo_started;?>;//alert(check_wo_started);
        if(check_wo_started == 1)
        {
            $("#w_start_date").prop("disabled", true);
            $("#w_start_date").val("<?=date('d-m-Y', strtotime($w_data->start_date));?>");
            document.mindate = <?=date('d/m/Y', strtotime($start_date));?>;
            var end_start_date = <?=date('d/m/Y', strtotime($end_start_date));?>;
            if(end_start_date > document.mindate)
            {
                document.minenddate = <?=date('d/m/Y', strtotime($end_start_date));?>;
            }
        }
        // alert(document.selected_contract.end_date);
        // alert(document.maxdate);
        var array = []
        <?php
        $start_date = date('Y-m-d');
        $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
        /**end work day information */
        if ((strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) && (strtotime($w_data->start_date) >= strtotime(date('Y-m-d H:i:s')))) {
            ?>
            var array = ["<?=$start_date;?>"]
            <?php
        }
        ?>
        $("#w_start_date, #w_end_date").datepicker({
            minDate: document.mindate2,
            maxDate: document.maxdate,
            dateFormat: 'dd-mm-yy',
            changeMonth: true,
            changeYear: true,
            beforeShowDay: function(date) {
                var day = date.getDay();
                var wtfs = <?=json_encode($wtfs)?>;
                //console.log(wtfs);
                var number =1;
                result = [true];
                var string = jQuery.datepicker.formatDate('yy-mm-dd', date);
                result = [ array.indexOf(string) == -1 ]
                
                function pad(s) { return (s < 10) ? '0' + s : s; }
                var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                    if($.trim(wtfs.official_vacation_days) != "")
                    {
                        var official_vacation_days = wtfs.official_vacation_days.split(',');

                        var official_vacation_days_array = official_vacation_days.map(element => {
                                        return element.trim();
                                    });
                        if(isInArray(official_vacation_days_array, formatted_date)){
                            result = [false];
                        }
                    }

                if(day == 1 && wtfs.monday ==0){
                    result = [false];
                }
                else if(day == 2 && wtfs.tuesday ==0){
                    result = [false];
                }
                else if(day == 3 && wtfs.wednesday ==0){
                    result = [false];
                }
                else if(day == 4 && wtfs.thursday ==0){
                    result = [false];
                }
                else if(day == 5 && wtfs.friday ==0){
                    result = [false];
                }
                else if(day == 6 && wtfs.saturday ==0){
                    result = [false];
                }
                else if(day == 0 && wtfs.sunday ==0){
                    result = [false];
                }                               
                return result;
            },
        });
        var array = []
        <?php
        $start_date = date('Y-m-d');
        $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
        /**end work day information */
        if ((strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) && (strtotime($w_data->start_date) >= strtotime(date('Y-m-d H:i:s')))) {
            ?>
            var array = ["<?=$start_date;?>"]
            <?php
        }
        ?>
        $("#w_custom_dates").datepicker('destroy').multiDatesPicker({
            minDate: document.mindate2,
            //minDate: 0,
            maxDate: document.maxdate,
            dateFormat: 'dd-mm-yy',
            changeMonth: true,
            changeYear: true,
            beforeShowDay: function(date) {
                var day = date.getDay();
                var wtfs = <?=json_encode($wtfs)?>;
                var number =1;
                result = [true];
                var string = jQuery.datepicker.formatDate('yy-mm-dd', date);
                result = [ array.indexOf(string) == -1 ]

                function pad(s) { return (s < 10) ? '0' + s : s; }
                    var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                    if($.trim(wtfs.official_vacation_days) != "")
                    {
                        var official_vacation_days = wtfs.official_vacation_days.split(',');

                        var official_vacation_days_array = official_vacation_days.map(element => {
                                        return element.trim();
                                    });
                        if(isInArray(official_vacation_days_array, formatted_date)){
                            result = [false];
                        }
                    }

                if(day == 1 && wtfs.monday ==0){
                    result = [false];
                }
                else if(day == 2 && wtfs.tuesday ==0){
                    result = [false];
                }
                else if(day == 3 && wtfs.wednesday ==0){
                    result = [false];
                }
                else if(day == 4 && wtfs.thursday ==0){
                    result = [false];
                }
                else if(day == 5 && wtfs.friday ==0){
                    result = [false];
                }
                else if(day == 6 && wtfs.saturday ==0){
                    result = [false];
                }
                else if(day == 0 && wtfs.sunday ==0){
                    result = [false];
                }                         
                return result;
            },
            // showOn: 'button', 
            // buttonImage: 'https://img.icons8.com/material-two-tone/24/000000/calendar--v1.png', 
            buttonImageOnly: true,
            onSelect: function() {
                    getSelectedDays();
            }
        });
        function getSelectedDays(){
            $('#w_custom_dates_count').val(''+$("#w_custom_dates").get(0).multiDatesPicker.dates.picked.length+' Days Selected');
        }
        //alert();        
    }
    // Pre selecting Priority
    var priority_id = <?= $w_data->priority_id ?>;
    console.log(priority_id) ;
    var route = '<?= route('workorder.AjaxPrioritiesGet') ?>';
    var contractId;

    if ($('#contract').val() !== '') {
        contractId = $('#contract').val();
    } else if ($('#singleContract').val() !== '') {
        contractId = $('#singleContract').val();
    } else {
        // Handle the case when both values are empty
        contractId = null; // Or any default value you want to assign
    }
        $.ajax({
            url: route,
            method: "GET",
            data: {
                _token: $('meta[name="csrf-token"]').attr("content"),
                contract_id: contractId,
            },
            dataType: "json",
            success: function (data) {
                console.log(data);
                $("#priority").empty();
                $("#priority").append(
                    $("<option></option>")
                        .attr("value", "")
                        .text("Select")
                );
                $.each(data, function (key, value) {
                    selected = '';
                    if(priority_id == value.id ){
                        selected = 'selected';
                    }
                    $("#priority").append(
                        $("<option "+selected+" ></option>")
                            .attr("value", value.id)
                            .text(value.priority_level)
                    );
                });
                
            }
        });

        // function isInArray(array, value) {

        //     return (array.find(item => {return item == value}) || []).length > 0;
        //     }

    if(document.contract.length != 0){
        if(document.getElementById('w_custom_dates')){
            var preSelectedDates = <?php echo json_encode($preSelectedDates); ?>;
            var preSelectedCODates = <?php echo json_encode($preSelectedCODates); ?>;
          
            //$('#w_custom_dates_count').val(''+preSelectedDates.length+' Days Selected');

            var language = '<?php echo Config::get('app.locale'); ?>';
            if(language == 'ar'){
                $('#w_custom_dates_count').val(' '+translations.work_order.forms.place_holder.Days_Selected+' '+preSelectedDates.length+'');        }   
            else{
                $('#w_custom_dates_count').val(''+preSelectedDates.length+' '+translations.work_order.forms.place_holder.Days_Selected+'');
            }
            var array = []
            <?php
            $start_date = date('Y-m-d');
            $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
            /**end work day information */
            if ((strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) && (strtotime($w_data->start_date) >= strtotime(date('Y-m-d H:i:s')))) {
                ?>
                var array = ["<?=$start_date;?>"]
                <?php
            }
            ?>
            $("#w_custom_dates").multiDatesPicker({
                minDate: document.contract.start_date,
                //minDate: 0,
                maxDate: document.contract.end_date,
                dateFormat: 'dd-mm-yy',
                changeMonth: true,
                changeYear: true,
                beforeShowDay: function(date) {
                    var day = date.getDay();
                    var wtfs = <?=json_encode($wtfs)?>;
                    var wo_is_started = '{{$wo_is_started}}';
                    var number =1;
                    flag = 0;
                    var cdate = date.getDate()+'-'+("0" + (date.getMonth() + 1)).slice(-2)+'-'+date.getFullYear();
                    

                    if(isInArray(preSelectedCODates, cdate)){
                        flag = 1
                    }

                    result = [true];

                    var string = jQuery.datepicker.formatDate('yy-mm-dd', date);
                    result = [ array.indexOf(string) == -1 ]

                    function pad(s) { return (s < 10) ? '0' + s : s; }
                    var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                    if($.trim(wtfs.official_vacation_days) != "")
                    {
                        var official_vacation_days = wtfs.official_vacation_days.split(',');

                        var official_vacation_days_array = official_vacation_days.map(element => {
                                        return element.trim();
                                    });
                        if(isInArray(official_vacation_days_array, formatted_date))
                        {
                            if(wo_is_started == 'yes' && isInArray(preSelectedDates, cdate))
                            {
                                result = [true];
                            }
                            else
                            {
                                result = [false];
                            } 
                        }

                        // if(wo_is_started == 'yes' && isInArray(preSelectedDates, cdate))
                        // {
                        //     result = [false];
                        // }
                    }

                    if(wo_is_started == 'yes' && isInArray(preSelectedDates, cdate))
                    {
                        result = [true];
                    }
                    else if(day == 1 && wtfs.monday ==0){
                        result = [false];
                    }
                    else if(day == 2 && wtfs.tuesday ==0){
                        result = [false];
                    }
                    else if(day == 3 && wtfs.wednesday ==0){
                        result = [false];
                    }
                    else if(day == 4 && wtfs.thursday ==0){
                        result = [false];
                    }
                    else if(day == 5 && wtfs.friday ==0){
                        result = [false];
                    }
                    else if(day == 6 && wtfs.saturday ==0){
                        result = [false];
                    }
                    else if(day == 0 && wtfs.sunday ==0){
                        result = [false];
                    }   
                    if(flag == 1){
                        result = [false,"eventClass",''];
                    }
                    return result;
                },
                buttonImageOnly: true,
                addDates: preSelectedDates, 
                   
                onSelect: function() {
                        getSelectedDays();
                }
            });
        }
        $.datepicker._selectDateOverload = $.datepicker._selectDate;
        $.datepicker._selectDate = function (id, dateStr) {
            var target = $(id);
            var inst = this._getInst(target[0]);
            inst.inline = true;
            $.datepicker._selectDateOverload(id, dateStr);
            inst.inline = false;
            if (target[0].multiDatesPicker != null) {
                target[0].multiDatesPicker.changed = false;
            } else {
                target.multiDatesPicker.changed = false;
            }
            this._updateDatepicker(inst);
        };
    }
    
});

function getSelectedDays(){
    var language = '<?php echo Config::get('app.locale'); ?>';
if(language == 'ar'){
    $('#w_custom_dates_count').val(' '+translations.work_order.forms.place_holder.Days_Selected+' '+$("#w_custom_dates").get(0).multiDatesPicker.dates.picked.length+'');        }   
else{
    $('#w_custom_dates_count').val(''+$("#w_custom_dates").get(0).multiDatesPicker.dates.picked.length+' '+translations.work_order.forms.place_holder.Days_Selected+'');
}       
}
    
    $('#frequency').change(function(){
        var edit_selectedfrequency = 'edit_selectedfrequency';
            sessionStorage.setItem(edit_selectedfrequency, JSON.stringify($(this).val()));
        if($('#frequency').val() == 25){ //alert(document.oneCalendar);

            if(document.getElementById('twoCalendar')){
                document.getElementById('twoCalendar').remove();
                $('.twoCalendar').remove();

            }
            if(document.getElementById('oneCalendar')){
                document.getElementById('oneCalendar').remove();
                $('.oneCalendar').remove();

            }
            $('#changeForCustomFrequency').append(document.oneCalendar)
            var array = []
            <?php
            $start_date = date('Y-m-d');
            $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
            /**end work day information */
            if ((strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) && (strtotime($w_data->start_date) >= strtotime(date('Y-m-d H:i:s')))) {
                ?>
                var array = ["<?=$start_date;?>"]
                <?php
            }
            ?>
            $("#w_custom_dates").multiDatesPicker({
                        
                        minDate: document.mindate2,
                        //minDate: 0,
                        maxDate: document.maxdate,
                        dateFormat: 'dd-mm-yy',
                        changeMonth: true,
                        changeYear: true,
                        beforeShowDay: function(date) {
                                var day = date.getDay();
                                var wtfs = <?=json_encode($wtfs)?>;
                                var number =1;
                                result = [true];
                                var string = jQuery.datepicker.formatDate('yy-mm-dd', date);
                                result = [ array.indexOf(string) == -1 ]

                                function pad(s) { return (s < 10) ? '0' + s : s; }
                                var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                                if($.trim(wtfs.official_vacation_days) != "")
                                {
                                    var official_vacation_days = wtfs.official_vacation_days.split(',');

                                    var official_vacation_days_array = official_vacation_days.map(element => {
                                                    return element.trim();
                                                });
                                    if(isInArray(official_vacation_days_array, formatted_date)){
                                        result = [false];
                                    }
                                }


                                if(day == 1 && wtfs.monday ==0){
                                    result = [false];
                                }
                                else if(day == 2 && wtfs.tuesday ==0){
                                    result = [false];
                                }
                                else if(day == 3 && wtfs.wednesday ==0){
                                    result = [false];
                                }
                                else if(day == 4 && wtfs.thursday ==0){
                                    result = [false];
                                }
                                else if(day == 5 && wtfs.friday ==0){
                                    result = [false];
                                }
                                else if(day == 6 && wtfs.saturday ==0){
                                    result = [false];
                                }
                                else if(day == 0 && wtfs.sunday ==0){
                                    result = [false];
                                }
                                
                                return result;
                            },
                        // showOn: 'button', 
                        // buttonImage: 'https://img.icons8.com/material-two-tone/24/000000/calendar--v1.png', 
                        buttonImageOnly: true,
                        onSelect: function() {
                                getSelectedDays();
                        }
                    });
                    // document.getElementById('changeForCustomFrequency').append(oneCalendar);                               
        }
        else{
            if(document.getElementById('oneCalendar')){
                document.getElementById('oneCalendar').remove();

                $('#changeForCustomFrequency').append(document.twoCalendar)

                var array = []
                <?php
                $start_date = date('Y-m-d');
                $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
                /**end work day information */
                if ((strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) && (strtotime($w_data->start_date) >= strtotime(date('Y-m-d H:i:s')))) {
                    ?>
                    var array = ["<?=$start_date;?>"]
                    <?php
                }
                ?>
                $("#w_start_date, #w_end_date").datepicker({
                        minDate: document.mindate2,
                        //minDate: 0,
                        maxDate: document.contract.end_date,
                        dateFormat: 'dd-mm-yy',
                        changeMonth: true,
                        changeYear: true,
                        beforeShowDay: function(date) {
                                var day = date.getDay();
                                var wtfs = <?=json_encode($wtfs)?>;
                                var number =1;
                                result = [true];
                                var string = jQuery.datepicker.formatDate('yy-mm-dd', date);
                                result = [ array.indexOf(string) == -1 ]

                                function pad(s) { return (s < 10) ? '0' + s : s; }
                    var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                    if($.trim(wtfs.official_vacation_days) != "")
                    {
                        var official_vacation_days = wtfs.official_vacation_days.split(',');

                        var official_vacation_days_array = official_vacation_days.map(element => {
                                        return element.trim();
                                    });
                        if(isInArray(official_vacation_days_array, formatted_date)){
                            result = [false];
                        }
                    }

                                if(day == 1 && wtfs.monday ==0){
                                    result = [false];
                                }
                                else if(day == 2 && wtfs.tuesday ==0){
                                    result = [false];
                                }
                                else if(day == 3 && wtfs.wednesday ==0){
                                    result = [false];
                                }
                                else if(day == 4 && wtfs.thursday ==0){
                                    result = [false];
                                }
                                else if(day == 5 && wtfs.friday ==0){
                                    result = [false];
                                }
                                else if(day == 6 && wtfs.saturday ==0){
                                    result = [false];
                                }
                                else if(day == 0 && wtfs.sunday ==0){
                                    result = [false];
                                }
                                
                                return result;
                            },
                    });
            //     document.getElementById('changeForCustomFrequency').append(twoCalendar);
            console.log("non custom");
            }


        }
        console.log("change");
    });

    $('#w_start_date').change(function() {
        var edit_selectedw_start_date = 'edit_selectedw_start_date';
    sessionStorage.setItem(edit_selectedw_start_date, JSON.stringify($("#w_start_date").val()));
    });

    $('#w_end_date').change(function() {
        var edit_selectedw_end_date = 'edit_selectedw_end_date';
    sessionStorage.setItem(edit_selectedw_end_date, JSON.stringify($("#w_end_date").val()));
    });
      $('#contract').change(function(){
        $('#w_start_date').val('')
        $('#w_end_date').val('')
        $('#w_custom_dates').val('')

        var storedstartdateValues = sessionStorage.getItem('edit_selectedw_start_date');
        if (storedstartdateValues) 
        {
             // Parse the stored values
            var selectedstartdateValues = JSON.parse(storedstartdateValues);
            // Set the selected values in the Select2 dropdown
            $('#w_start_date').val(selectedstartdateValues);                                                
        }


        var storedenddateValues = sessionStorage.getItem('edit_selectedw_end_date');
        if (storedenddateValues) 
        {
             // Parse the stored values
            var selectedenddateValues = JSON.parse(storedenddateValues);
            // Set the selected values in the Select2 dropdown
            $('#w_end_date').val(selectedenddateValues);                                                
        }

        var strArray = $(this).val().split("-");
        var selected_contract_id =  strArray[0];

        console.log("contract selected")
        var route = '<?= route('workorder.AjaxPrioritiesGet') ?>';
        $.ajax({
        url: route,
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            contract_id: $(this).val(),
            
        },
        dataType: "json",
       
        success: function (data) {
            console.log(data);
           
            $("#priority").empty();
            $("#priority").append(
                $("<option></option>")
                    .attr("value", "")
                    .text("Select")
            );

            $.each(data, function (key, value) {
                $("#priority").append(
                    $("<option></option>")
                        .attr("value", value.id)
                        .text(value.priority_level)
                );
            });
            
        },
       
    });
    
    $.ajax({
                url: '<?= route('workorder.GetContractFrequency') ?>',
                method: "GET",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    contract_id: $(this).val(),
                    
                },
                dataType: "json",
            
                success: function (data) {
                    console.log(data);
                    var check_wo_started = <?=$check_wo_started;?>;
                    if(check_wo_started == 0)
                    {
                        $("#frequency").empty();
                        $("#frequency").append(
                            $("<option></option>")
                                .attr("value", "")
                                .text(translations.work_order.forms.label.select)
                        );
                    } 
                    //$("#frequency").append(
                        // $("<option></option>")
                        //     .attr("value", "25")
                        //     .text(translations.work_order.forms.label.Custom)
                    //);
                    $.each(data, function (key, value) {
                    if(value.title=='Daily') {
                        value.title=translations.work_order.list.Daily;

                    }
                    else if(value.title=='Weekly'){
                        value.title=translations.work_order.list.Weekly;

                    }
                    else if(value.title=='Bi-weekly'){
                        value.title=translations.work_order.list.Bi_Weekly;

                    }
                    else if(value.title=='Monthly'){
                        value.title=translations.work_order.list.Monthly;
                    }
                    else if(value.title=='Quarterly'){
                        value.title=translations.work_order.list.Quarterly;
                    }
                    else if(value.title=='Half-annually'){
                        value.title=translations.work_order.list.Half_Annually;
                    }
                    else if(value.title=='Annually'){
                        value.title=translations.work_order.list.Annually;
                    }
                    var check_wo_started = <?=$check_wo_started;?>;
                    if(check_wo_started == 0)
                    {          
                        $("#frequency").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.title)
                        );

                        $("#hidden_frequency").prop("disabled", true);
                    }
                    else
                    {
                        $("#hidden_frequency").prop("disabled", false);
                    }                 
                    });
                    
                },
            });
            
            $.each(document.contract, function (key, value) {
                console.log(selected_contract_id)
                console.log(value)
                if(value.id == selected_contract_id){
                    document.mindate = value.start_date;
                    document.maxdate = value.end_date;
                    document.minenddate = value.start_date;
                    document.maxenddate = value.end_date;
                }
            })
            var check_wo_started = <?=$check_wo_started;?>;
            if(check_wo_started == 1)
            {
                $("#w_start_date").prop("disabled", true);
                $("#w_start_date").val("<?=date('d-m-Y', strtotime($w_data->start_date));?>");
                document.mindate = <?=date('d/m/Y', strtotime($start_date));?>;
                var end_start_date = <?=date('d/m/Y', strtotime($end_start_date));?>;
                if(end_start_date > document.minenddate)
                {
                    document.minenddate = <?=date('d/m/Y', strtotime($end_start_date));?>;
                }
                //document.minenddate = $end_start_date;
            }
            else{
                // document.mindate = <?=date('d/m/Y');?>;

            }
            // alert(document.mindate);
            $("#w_start_date").datepicker('destroy').datepicker({
                minDate: 0,
                //minDate: 0,
                maxDate: document.maxdate,
                // maxDate: "{$max_date->max_date_entered}",
                dateFormat: 'dd-mm-yy',
                changeMonth: true,
                changeYear: true,
                beforeShowDay: function(date) {
                    var day = date.getDay();
                    var wtfs = <?=json_encode($wtfs)?>;
                    var number =1;
                    result = [true];

                    function pad(s) { return (s < 10) ? '0' + s : s; }
                    var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                    if($.trim(wtfs.official_vacation_days) != "")
                    {
                        var official_vacation_days = wtfs.official_vacation_days.split(',');

                        var official_vacation_days_array = official_vacation_days.map(element => {
                                        return element.trim();
                                    });
                        if(isInArray(official_vacation_days_array, formatted_date)){
                            result = [false];
                        }
                    }

                    if(day == 1 && wtfs.monday ==0){
                        result = [false];
                    }
                    else if(day == 2 && wtfs.tuesday ==0){
                        result = [false];
                    }
                    else if(day == 3 && wtfs.wednesday ==0){
                        result = [false];
                    }
                    else if(day == 4 && wtfs.thursday ==0){
                        result = [false];
                    }
                    else if(day == 5 && wtfs.friday ==0){
                        result = [false];
                    }
                    else if(day == 6 && wtfs.saturday ==0){
                        result = [false];
                    }
                    else if(day == 0 && wtfs.sunday ==0){
                        result = [false];
                    }
                    
                    return result;
                },
            });
            $("#w_end_date").datepicker('destroy').datepicker({
                minDate: 0,
                maxDate: document.maxenddate,
                dateFormat: 'dd-mm-yy',
                changeMonth: true,
                changeYear: true,
                beforeShowDay: function(date) {
                    var day = date.getDay();
                    var wtfs = <?=json_encode($wtfs)?>;
                    var number =1;
                    result = [true];

                    function pad(s) { return (s < 10) ? '0' + s : s; }
                    var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                    if($.trim(wtfs.official_vacation_days) != "")
                    {
                        var official_vacation_days = wtfs.official_vacation_days.split(',');

                        var official_vacation_days_array = official_vacation_days.map(element => {
                                        return element.trim();
                                    });
                        if(isInArray(official_vacation_days_array, formatted_date)){
                            result = [false];
                        }
                    }


                    if(day == 1 && wtfs.monday ==0){
                        result = [false];
                    }
                    else if(day == 2 && wtfs.tuesday ==0){
                        result = [false];
                    }
                    else if(day == 3 && wtfs.wednesday ==0){
                        result = [false];
                    }
                    else if(day == 4 && wtfs.thursday ==0){
                        result = [false];
                    }
                    else if(day == 5 && wtfs.friday ==0){
                        result = [false];
                    }
                    else if(day == 6 && wtfs.saturday ==0){
                        result = [false];
                    }
                    else if(day == 0 && wtfs.sunday ==0){
                        result = [false];
                    }
                    
                    return result;
                },
            });
    
            $("#w_custom_dates").datepicker('destroy').multiDatesPicker({
                minDate: 0,
                //minDate: 0,
                maxDate: document.maxdate,
                dateFormat: 'dd-mm-yy',
                changeMonth: true,
                changeYear: true,
                beforeShowDay: function(date) {
                    //console.log(formatted_date);
                    var day = date.getDay();
                    var wtfs = <?=json_encode($wtfs)?>;
                    var number =1;
                    result = [true];

                    function pad(s) { return (s < 10) ? '0' + s : s; }
                    var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                    if($.trim(wtfs.official_vacation_days) != "")
                    {
                        var official_vacation_days = wtfs.official_vacation_days.split(',');

                        var official_vacation_days_array = official_vacation_days.map(element => {
                                        return element.trim();
                                    });
                        if(isInArray(official_vacation_days_array, formatted_date)){
                            result = [false];
                        }
                    }

                    
                    if(day == 1 && wtfs.monday ==0){
                        result = [false];
                    }
                    else if(day == 2 && wtfs.tuesday ==0){
                        result = [false];
                    }
                    else if(day == 3 && wtfs.wednesday ==0){
                        result = [false];
                    }
                    else if(day == 4 && wtfs.thursday ==0){
                        result = [false];
                    }
                    else if(day == 5 && wtfs.friday ==0){
                        result = [false];
                    }
                    else if(day == 6 && wtfs.saturday ==0){
                        result = [false];
                    }
                    else if(day == 0 && wtfs.sunday ==0){
                        result = [false];
                    }                    
                    return result;
                },
                // showOn: 'button', 
                // buttonImage: 'https://img.icons8.com/material-two-tone/24/000000/calendar--v1.png', 
                buttonImageOnly: true,
                onSelect: function() {
                        getSelectedDays();
                }
            });
        })

         $("#service_category").on("change", function () {
            let value_cnt = $(this).val();
            let property_id = $("#property_id").val(); //alert(property_id);

    $.ajax({
        url: $(this).data("url"),
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            id: value_cnt,
            property_id: property_id,
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) { //alert(data[1]);
            $("#contract").empty();
            $("#contract").append(
                $("<option></option>")
                    .attr("value", "")
                    .text(translations.work_order.forms.place_holder.Choose_asset_name)
            );
            $.each(data[1], function (key, value) {
                var id = value.id;
                num = id.toString();
                while (num.length < 3) num = "000" + num;
                $("#contract").append(
                    $("<option></option>")
                        .attr("value", value.id+'-'+value.contract_types)
                        .text('CONT'+num+'('+value.contract_number+')-'+value.contract_types)
                );
            });
            document.contract= data[1];
            $("#contract_div").removeClass("d-none");
            console.log(document.contract);
        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 2000,
                positionClass: "toast-top-center",
                progressBar: true,
                preventDuplicates: true,
                preventOpenDuplicates: true,
            });
        },
    });
});


         




$('#contract').change(function () {
    var contract_type = $('#contract option:selected').val();
    if(contract_type != '') {
        var arr =  contract_type.split('-');
        //alert(arr[1])
        if(arr[1] == 'Warrenty') {
            $('.checklist-div').hide();
        } else {
            $('.checklist-div').show();
        }
    }
});   

var storedstartdateValues = sessionStorage.getItem('edit_selectedw_start_date');
        if (storedstartdateValues) 
        {
             // Parse the stored values
            var selectedstartdateValues = JSON.parse(storedstartdateValues);
            // Set the selected values in the Select2 dropdown
            $('#w_start_date').val(selectedstartdateValues);                                                
        }


        var storedenddateValues = sessionStorage.getItem('edit_selectedw_end_date');
        if (storedenddateValues) 
        {
             // Parse the stored values
            var selectedenddateValues = JSON.parse(storedenddateValues);
            // Set the selected values in the Select2 dropdown
            $('#w_end_date').val(selectedenddateValues);                                                
        }

// @flip1@ add event for view checklist details
$('#choose_asset_checklist').change(function () {

checklist = $(this).val();
console.log(checklist);
if(checklist != ''){
    $('.view_checklist_ul').show();
    $('#view_checklist').attr('data-value', checklist);
}else{
    $('.view_checklist_ul').hide();
    $('#view_checklist').removeAttr('data-value');
}
});

// @flip1@ add event for view checklist details
$('#view_checklist').click(function () {
    $('.checklist_heading').empty();
    $('.about-projects').empty();
if($('#choose_asset_checklist').val()!= 'undefined'){
    $.ajax({
        url: '<?=route('workorder.result.checkListDetailsAjax')?>',
        method: "get",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            checklist_id: $('#choose_asset_checklist').val(),
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
            console.log(data);
            $('.checklist_heading').empty().append(data.heading);
            $('.about-projects').empty().append(data.html);
        },
        // error: function (data) {
        //     var errors = $.parseJSON(data.responseText);
        //     toastr.error(data, translations.general_sentence.validation.Error, {
        //         timeOut: 1500,
        //         positionClass: "toast-top-center",
        //         progressBar: true,
        //         preventDuplicates: true,
        //         preventOpenDuplicates: true,
        //     });
        // },
    });

}
});

function populateFrequencies(){
        var route = '<?= route('workorder.AjaxPrioritiesGet') ?>';

        $.ajax({
                url: route,
                method: "GET",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    contract_id: $('#contract').val(),
                },
                dataType: "json",
                success: function (data) {
                    $("#priority").empty();
                    $("#priority").append(
                        $("<option></option>")
                            .attr("value", "")
                            .text("Select")
                    );
                    $.each(data, function (key, value) {
                        console.log(value);
                        if(value.is_deleted == 'no'){

                       
                        $("#priority").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.priority_level)
                        );
                    }
                    });

                    var edit_selectedpriority1 = 'edit_selectedpriority';
                    var storedpriorityValues = sessionStorage.getItem(edit_selectedpriority1);
                    if (storedpriorityValues) 
                    {
                                                            // Parse the stored values
                        var selectedpriorityValues = JSON.parse(storedpriorityValues);
                                                        // Set the selected values in the Select2 dropdown
                        $('#priority').val(selectedpriorityValues).trigger('change');
                    }
                },
            });
        $.ajax({
                url: '<?= route('workorder.GetContractFrequency') ?>',
                method: "GET",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    contract_id: $("#contract").val(),
                },
                dataType: "json",
                success: function (data) {
                    $("#frequency").empty();
                    $("#frequency").append(
                        $("<option></option>")
                            .attr("value", "")
                            .text(translations.work_order.forms.label.select)
                    );
                    // $("#frequency").append(
                    //     $("<option></option>")
                    //         .attr("value", "25")
                    //         .text(translations.work_order.forms.label.Custom)
                    // );
                    $.each(data, function (key, value) {
                    if(value.title=='Daily') {
                        value.title=translations.work_order.list.Daily;
                    }
                    else if(value.title=='Weekly'){
                        value.title=translations.work_order.list.Weekly;
                    }
                    else if(value.title=='Bi-weekly'){
                        value.title=translations.work_order.list.Bi_Weekly;

                    }
                    else if(value.title=='Monthly'){
                        value.title=translations.work_order.list.Monthly;
                    }
                    else if(value.title=='Quarterly'){
                        value.title=translations.work_order.list.Quarterly;
                    }
                    else if(value.title=='Half-annually'){
                        value.title=translations.work_order.list.Half_Annually;
                    }
                    else if(value.title=='Annually'){
                        value.title=translations.work_order.list.Annually;
                    } 
                    else if(value.title=='Custom'){
                        value.title=translations.work_order.list.Custom;
                    }                    
                        $("#frequency").append(
                            $("<option></option>")
                                .attr("value", value.id)
                                .text(value.title)
                        );
                    });

                    var edit_selectedfrequency1 = 'edit_selectedfrequency';
                    var storedfrequencyValues = sessionStorage.getItem(edit_selectedfrequency1);
                    if (storedfrequencyValues) 
                    {
                            // Parse the stored values
                            var selectedfrequencyValues = JSON.parse(storedfrequencyValues);
                            // Set the selected values in the Select2 dropdown
                            $('#frequency').val(selectedfrequencyValues).trigger('change');
                    }
                    
                },
            });
    }
    function contratChanged(){
         $('#w_start_date').val('')
        $('#w_end_date').val('')
        $('#w_custom_dates').val('')
        var storedstartdateValues = sessionStorage.getItem('edit_selectedw_start_date');
        if (storedstartdateValues) 
        {
             // Parse the stored values
            var selectedstartdateValues = JSON.parse(storedstartdateValues);
            // Set the selected values in the Select2 dropdown
            $('#w_start_date').val(selectedstartdateValues);                                                
        }


        var storedenddateValues = sessionStorage.getItem('edit_selectedw_end_date');
        if (storedenddateValues) 
        {
             // Parse the stored values
            var selectedenddateValues = JSON.parse(storedenddateValues);
            // Set the selected values in the Select2 dropdown
            $('#w_end_date').val(selectedenddateValues);                                                
        }
        var strArray = $('#contract').val().split("-");
        var selected_contract_id =  strArray[0];
        console.log("contract selected")
        var route = '<?= route('workorder.AjaxPrioritiesGet') ?>';
        populateFrequencies();
        $.each(document.contracts, function (key, value) {
            console.log(selected_contract_id)
            console.log(value)
            
            if(value.id == selected_contract_id){
                document.mindate = value.start_date;
                document.maxdate = value.end_date;
            }
        });
        var array = []
        <?php
        $start_date = date('Y-m-d');
        $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
        /**end work day information */
        if (strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) {
            ?>
            var array = ["<?=$start_date;?>"]
            <?php
        }
        ?>
        var array = []
        <?php
        $start_date = date('Y-m-d');
        $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
        /**end work day information */
        if (strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) {
            ?>
            var array = ["<?=$start_date;?>"]
            <?php
        }
        ?>
        $("#w_start_date, #w_end_date").datepicker('destroy').datepicker({
            //minDate: document.mindate,
            minDate: 0,
            maxDate: document.maxdate,
            dateFormat: 'dd-mm-yy',
            changeMonth: true,
            changeYear: true,
            todayHighlight: true,
            beforeShowDay: function(date) {
                var day = date.getDay();
                var wtfs = <?=json_encode($wtfs)?>;
                console.log(wtfs);
                var number =1;
                result = [true];
                var string = jQuery.datepicker.formatDate('yy-mm-dd', date);
                result = [ array.indexOf(string) == -1 ]

                function pad(s) { return (s < 10) ? '0' + s : s; }
                    var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                    if($.trim(wtfs.official_vacation_days) != "")
                    {
                        var official_vacation_days = wtfs.official_vacation_days.split(',');

                        var official_vacation_days_array = official_vacation_days.map(element => {
                                        return element.trim();
                                    });
                        if(isInArray(official_vacation_days_array, formatted_date)){
                            result = [false];
                        }
                    }
                if(day == 1 && wtfs.monday ==0){
                    result = [false];
                }
                else if(day == 2 && wtfs.tuesday ==0){
                    result = [false];
                }
                else if(day == 3 && wtfs.wednesday ==0){
                    result = [false];
                }
                else if(day == 4 && wtfs.thursday ==0){
                    result = [false];
                }
                else if(day == 5 && wtfs.friday ==0){
                    result = [false];
                }
                else if(day == 6 && wtfs.saturday ==0){
                    result = [false];
                }
                else if(day == 0 && wtfs.sunday ==0){
                    result = [false];
                }                
                //result = [false];
                return result;
            },
        });

        var array = []
        <?php
        $start_date = date('Y-m-d');
        $day_time = date('Y-m-d', strtotime($start_date)) . ' ' . $wtfs->end_time;
        /**end work day information */
        if (strtotime($day_time) <= strtotime(date('Y-m-d H:i:s'))) {
            ?>
            var array = ["<?=$start_date;?>"]
            <?php
        }
        ?>
        $("#w_custom_dates").multiDatesPicker('destroy').multiDatesPicker({
        //minDate: document.mindate,
            minDate: 0,
            maxDate: document.maxdate,
            dateFormat: 'dd-mm-yy',
            changeMonth: true,
            changeYear: true,
            minDate: 0,
            beforeShowDay: function(date) {
                var day = date.getDay();
                var wtfs = <?=json_encode($wtfs)?>;
                var number =1;
                result = [true];
                var string = jQuery.datepicker.formatDate('yy-mm-dd', date);
                result = [ array.indexOf(string) == -1 ]

                function pad(s) { return (s < 10) ? '0' + s : s; }
                    var formatted_date = [pad(date.getDate()), pad(date.getMonth()+1), date.getFullYear()].join('-');
                if($.trim(wtfs.official_vacation_days) != "")
                {
                    var official_vacation_days = wtfs.official_vacation_days.split(',');

                    var official_vacation_days_array = official_vacation_days.map(element => {
                                    return element.trim();
                                });
                    if(isInArray(official_vacation_days_array, formatted_date)){
                        result = [false];
                    }
                }

                
            if(day == 1 && wtfs.monday ==0){
                result = [false];
            }
            else if(day == 2 && wtfs.tuesday ==0){
                result = [false];
            }
            else if(day == 3 && wtfs.wednesday ==0){
                result = [false];
            }
            else if(day == 4 && wtfs.thursday ==0){
                result = [false];
            }
            else if(day == 5 && wtfs.friday ==0){
                result = [false];
            }
            else if(day == 6 && wtfs.saturday ==0){
                result = [false];
            }
            else if(day == 0 && wtfs.sunday ==0){
                result = [false];
            }                
            return result;
        },
        onSelect: function() {
            getSelectedDays();
        }
        });
    }
</script>
    <style>
        .eventClass {
            background: #743620!important;
            display: block;
            border-radius: 50%;
            line-height: 2rem;
            transition: 0.3s all;
            font-size: 0.875rem;
            text-decoration: none;
            width: 2rem;
            height: 2rem;
            line-height: 2rem;
            border: 1px solid transparent;
            margin: 4px;
            /* color: #ffffff!important; */
        }
        .eventClass span{
            background: #743620
            ;
            background-color: #743620!important;
            color: #ffffff;
        }
        .ui-datepicker-calendar tbody td {
    width: 2rem;
        }
/* 
        .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
            background-color: transparent !important;

        } */
    </style>

<script type="text/javascript">
    $(document).ready(function () {
        // Toggle the disabled state of inputs when checkbox is clicked
        $(document).ready(function () {
            var ppm_report_status = $("#ppm_report_status").val();
            var wo_is_force_edit = $("#wo_is_force_edit").val();

            
            if(ppm_report_status == 'pending')
            {
                $('.force-enable').prop('disabled', true);
            }
            else if(ppm_report_status == 'approved' && wo_is_force_edit == 0)
            {
                $('.force-enable').prop('disabled', true);
            }
            else
            {
                $('.force-enable').prop('disabled', false);
            }

            $("#force_edit").change(function () {
                if (this.checked) {
                    $("#force_edit_status").val('enabled');
                    // Remove 'disabled' attribute when checkbox is checked
                    $('.force-enable, .force-enable + .select2').prop('disabled', false).fadeOut(300).fadeIn(300);;
                    $(".force-alert").fadeOut(300, function () {
                        $(".cancel-alert").fadeIn(300);
                    });
                } else {
                    // Re-enable 'disabled' attribute when checkbox is unchecked
                    $('.force-enable').prop('disabled', true);
                    $("#force_edit_status").val('disabled');
                }
            });

            $("#cancel_edit").change(function () {
                if (this.checked) {
                    $(".cancel-alert").fadeOut(300, function () {
                        $(".force-alert").fadeIn(300);
                    });
                    $('.force-enable,  .force-enable + .select2').prop('disabled', true).fadeOut(300).fadeIn(300);;
                    $("#force_edit").prop("checked", false);
                }
            });
        });

    });
</script>
@endsection