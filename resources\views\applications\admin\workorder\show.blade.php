@extends('layouts.app')
@section('styles')
<link rel="stylesheet" type="text/css" href="{{ asset('vendor_assets/js/datetime/css/bootstrap-datetimepicker.css') }}">
<style>
    .flex-display {
        display: flex !important;
    }
    .none-display {
        display: none !important;
    }
    .modal-dialog-scrollable .modal-body{
            max-height: calc(100vh - 200px);
        }
    @media only screen and (max-width:576px){
        #sp-complete-job .modal-body, #sp-approve-job .modal-body{
            max-height: calc(100vh - 250px); 
        }
    }
</style>
@endsection
@section('content') 
<input type="hidden" name="workorderid" id="workorderid" value="<?=$work_orders_details->id;?>">
<div class="contents wo-management">
    <div class="container-fluid">
        <div class="row justify-content-between align-items-center">
            <div class="page-title-wrap">
                <div class="page-title d-flex justify-content-between">
                    <div class="page-title__left">
                    <div class="col-lg-12">
                        <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25 ml-0">
                            <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                <!-- Javascript:history.back()-->
                                <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a>
                                <?=$work_orders_details->work_order_id;?>
                                <?php
                                if(count($work_orders_details->related_wos) > 1)
                                {
                                    echo ': '.$work_orders_details->unique_id;
                                }
                                ?>
                                </h4>
                        </div>
                    </div>
                    </div>
                </div>

                <div class="col-lg-12">
                    <ul class="atbd-breadcrumb nav">
                        <li class="atbd-breadcrumb__item">
                            <a>{{__('work_order.bread_crumbs.work_order')}}</a>
                            <span class="breadcrumb__seperator">
                                <span class="la la-angle-right"></span>
                            </span>
                        </li>
                        @if($work_orders_details->work_order_type == 'preventive')
                            <!--========= PM title ==========-->
                        <li class="atbd-breadcrumb__item">
                            <a href="{{route('workorder.sp_preventive_details', [Crypt::encryptString($work_orders_details->id)])}}" class="text-primary">{{$work_orders_details->pm_title}}</a>
                            <span class="breadcrumb__seperator">
                                <span class="la la-angle-right"></span>
                            </span>
                        </li>
                            <!--========= End PM title ==========-->
                        @endif
                        <li class="atbd-breadcrumb__item">
                            <a><?=$work_orders_details->work_order_id;?></a>
                        </li>
                    </ul>
                </div>
            </div>
            <!--====Design for Export PDF===--> 
             <div class="">
                <div class="col-lg-12">
                            
                                 <a href="javascript:void(0);" class="border-primary btn-outline-primary btn-default btn-squared btn-xs btn-add ml-10 float-right  mt-sm-0 btn-svg mt-2 hover-2-img d-flex align-items-center" data-toggle="modal" id="showExportModal" data-target="#export-modal">   <img src="{{ asset('img/svg/export-icon.svg') }}" class="mr-2 w-11 img-1">
                                    <img src="{{ asset('img/svg/export-icon-white.svg') }}" class="mr-2 w-11 img-2">
                              <span> {{ __('work_order.forms.label.export') }} </span> </a>
                        </div>
                        </div>
            <!--====End Design for Export PDF===-->
            
        </div>

        
        <div class="projects-tab-content mb-3">
                <div class="">
                    <div class="row mb-20">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                <div id="counter_or_button" class="d-flex">
                                    @if (!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'super_admin') || !empty(Auth::user()->user_type == 'osool_admin') || !empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee'))
                                            @if($work_orders_details->status == 1 || $work_orders_details->status == 2 || $work_orders_details->status == 6 || $work_orders_details->bm_approve_issue == 1)
                                                    <!-----------------Send Remider to sp Design-------------->
                                                    <div id="button_div" class="d-none">
                                                    <a class="btn btn-xs btn-default send_reminder_to_sp btn-squared btn-outline-primary  btn-add dropdown-toggle float-right px-sm-3 px-1  mt-sm-0 mt-2"  title="{{__('work_order.button.Send_Reminder_for_Service_Provider')}}" href="javascript:void(0)" data-value="{{$work_orders_details->id}}">
                                                        <i class="la la-clock"></i> {{__('work_order.button.Send_Reminder_for_Service_Provider')}}
                                                    </a>
                                                    </div>
                                                    <div id="counter_div" class="d-none">

                                                    <section class="countdown wrapper">
                                                        <section id="countdown-container" class="countdown-container">
                                                            <article id="js-countdown" class="countdown color-primary">
                                                            
                                                                <section id="js-days" class="number"></section>
                                                                <section id="js-separator" class="separator">:</section>
                                                                <section id="js-hours" class="number"></section>
                                                                <section id="js-separator" class="separator">:</section>
                                                                <section id="js-minutes" class="number"></section>
                                                                <!-- <section id="js-separator" class="separator">:</section>
                                                                <section id="js-seconds" class="number"></section> -->
                                                            </article>
                                                        </section>
                                                    </section>
                                                    </div>

                                            @endif
                                    
                                    @endif
                                </div>
                        <div>
                            
                            @php $var_sbn=Helper::valiAccess(); @endphp
                            @if (!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee'))
                            
                            {{--edit work order condition--}}
                            @if($work_orders_details->workorder_journey=="submitted" && $work_orders_details->status==1)

                                @if($var_sbn!="empty")
                                    @if(!empty($var_sbn->workorder) && in_array('edit',$var_sbn->workorder))
                                        @if($work_orders_details->work_order_type=="reactive")
                                            <a class="btn btn-xs btn-default btn-squared btn-outline-primary  btn-add ml-10 dropdown-toggle float-right mt-sm-0 mt-2"  title="Edit" href="{{route('workorder.single.edit.reactive.info', Crypt::encryptString($work_orders_details->id))}}">
                                                <i class="las la-pencil-alt fs-16"></i> {{__('work_order.bread_crumbs.Edit_Work_Order')}}
                                            </a>
                                        @endif
                                        @if($work_orders_details->work_order_type=="preventive" && Auth::user()->user_type != 'building_manager_employee' && Auth::user()->user_type != 'building_manager')
                                            <a class="btn btn-xs btn-default btn-squared btn-outline-primary  btn-add ml-10 dropdown-toggle float-right mt-sm-0 mt-2"  title="Edit" href="{{route('workorder.edit.preventive.info',$work_orders_details->id)}}"> 
                                                <i class="las la-pencil-alt fs-16"></i> {{__('work_order.bread_crumbs.Edit_Work_Order')}}
                                            </a>
                                        @endif
                                    @endif {{--edit check--}}
                                @else
                                    @if($work_orders_details->work_order_type=="reactive")
                                        <a class="btn btn-xs btn-default btn-squared btn-outline-primary  btn-add ml-10 dropdown-toggle float-right mt-sm-0 mt-2"  title="Edit" href="{{route('workorder.single.edit.reactive.info',Crypt::encryptString($work_orders_details->id))}}">
                                            <i class="las la-pencil-alt fs-16"></i> {{__('work_order.bread_crumbs.Edit_Work_Order')}}
                                        </a>
                                    @endif
                                    @if($work_orders_details->work_order_type=="preventive" && Auth::user()->user_type != 'building_manager_employee' && Auth::user()->user_type != 'building_manager')
                                        <a class="btn btn-xs btn-default btn-squared btn-outline-primary  btn-add ml-10 dropdown-toggle float-right mt-sm-0 mt-2"  title="Edit" href="{{route('workorder.edit.preventive.info',$work_orders_details->id)}}">
                                            <i class="las la-pencil-alt fs-16"></i> {{__('work_order.bread_crumbs.Edit_Work_Order')}}
                                        </a>
                                    @endif
                                @endif {{--empty check--}}



                             @endif
                             {{--delete work order condition--}}
                             @if(($work_orders_details->workorder_journey=="submitted" && $work_orders_details->status==1) || ($work_orders_details->workorder_journey=="job_execution" && $work_orders_details->status==2) ||($work_orders_details->workorder_journey=="job_execution" && $work_orders_details->status==3))
                            {{-- @@ check if logged in user has create privileges  --}}
                            @if(Helper::checkLoggedinUserPrivileges('create','workorder')['success'])
                             <a class="btn btn-xs btn-default btn-squared btn-outline-danger  btn-add ml-10 dropdown-toggle float-right ml-10 delete_workorder_bm px-sm-3 px-1  mt-sm-0 mt-2"  title="remove" href="javascript:void(0)" data-value="{{$work_orders_details->id}}">
                                <i class="la la-trash-alt"></i> {{__('work_order.bread_crumbs.Delete_work_order')}}
                            </a>

                             @endif
                             @endif


                            @endif

                            @if($checkItemRequestsByWorkerApprovedOrRejected != 'no')
                                <a class="btn btn-outline-primary btn-default btn-squared btn-xs btn-add ml-10 float-right mt-sm-0 mt-2"  title="View" href="javascript:void(0)" data-toggle="modal" data-target="#view-items-requested">
                                    <i class="las la la-eye fs-16"></i> {{__('work_order.bread_crumbs.view_items_requested')}}
                                </a>
                            @endif
                            
                            @if($work_orders_details->checklist_id != 0 && ($work_orders_details->workorder_journey == "job_evaluation" || $work_orders_details->workorder_journey == "job_approval" || $work_orders_details->workorder_journey == "finished") && (!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor') || !empty(Auth::user()->user_type == 'admin') || !empty(Auth::user()->user_type == 'admin_employee') || !empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee') || !empty(Auth::user()->user_type == 'osool_admin') || !empty(Auth::user()->user_type == 'super_admin')) )
                                <a class="btn btn-outline-primary btn-default btn-squared btn-xs btn-add ml-10 float-right  mt-sm-0 mt-2"  title="View" href="{{route('workorder.checklist',Crypt::encryptString($work_orders_details->id))}}">
                                    <i class="las la la-eye fs-16"></i>{{__('work_order.bread_crumbs.View_Checklist_Result')}}
                                </a>
                            @endif

                            @if($work_orders_details->workorder_journey == "finished" && $work_orders_details->status != 5)
                                <a class="btn btn-outline-primary btn-default btn-squared btn-xs btn-add ml-10 float-right mt-sm-0 mt-2"  title="View" href="{{route('workorder.result.show',Crypt::encryptString($work_orders_details->id))}}">
                                    <i class="las la la-eye fs-16"></i> {{__('work_order.bread_crumbs.View_Result')}}
                                </a>
                            @endif

                            {{-- @if(($work_orders_details->workorder_journey == "job_approval" || $work_orders_details->workorder_journey == "finished")  && !empty(Auth::user()->user_type == 'building_manager'))
                            <a class="btn btn-outline-primary btn-default btn-squared btn-xs btn-add ml-10 float-right mt-sm-0 mt-2"  title="View" href="{{route('workorder.checklist',$work_orders_details->id)}}">
                                <i class="las la la-eye fs-16"></i>{{__('work_order.bread_crumbs.View_Checklist_Result')}}
                             </a>
                            @endif --}}

                            <!-- <button type="button" class="btn btn-sm btn-default btn-squared btn-outline-primary btn-add float-right ml-10" data-toggle="modal" data-target="#modal-basic"><i class="la la-edit"></i> Action</button> -->

                            <!-- <div class="dropdown action-btn pb-lg-0 pb-xs-2 float-right ml-15">
                                <button class="btn btn-xs btn-default btn-squared btn-outline-primary  btn-add ml-10 dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="las la-pencil-alt fs-16"></i> Edit work order
                                </button>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenu2">
                                    <a href="{{-- route('workorder.c-wo-st') --}}" target="_blank" class="dropdown-item">Building Manger Edit</a>
                                    <a href="{{-- route('workorder.c-wo-st') --}}" target="_blank" class="dropdown-item">Service Provider Edit</a>
                                 </div>
                            </div> -->                            
                            <?php    
                                if($action <> \App\Enums\WorkOrderStatus::Scheduled->value) {
                                    if((!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) && $work_orders_details->contract_type == "warranty" && $work_orders_details->status != 4)
                            {
                                ?>
                                @if(Helper::checkLoggedinUserPrivileges('edit','workorder')['success'])
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#modal-warranty"><i class="la la-edit"></i>{{__('work_order.button.edit_work_order_properties')}}</button>
                                @endif
                                <?php
                            }
                            if($work_orders_details->start_date <= date('Y-m-d') )
                            {
                                if((!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) && ($work_orders_details->contract_type == "regular" && $work_orders_details->status == 1) && $work_orders_details->proposed_new_date == '' && $work_orders_details->bm_approve_issue == 0 && ($work_orders_details->is_collaborative != 1))
                                {
                                    $disabled = "";
                                    $data_toggle = "";
                                    $data_title = "";
                                    $disabled_btn_title = "";
                                    if(!in_array(Auth::user()->id, explode(',', $work_orders_details->supervisor_id)) && Auth::user()->user_type == "supervisor")
                                    {
                                        $disabled = "disabled";
                                        $disabled_btn_title = "disabled-btn-title";
                                        $data_toggle = "tooltip";
                                        $data_title = __('work_order.button.edit_not_allowed');
                                    }
                                    ?>
                                    
                                    @unless(isset($workOrderItemRequestBySp) && isset($workOrderItemRequestBySp->status) && $workOrderItemRequestBySp->status =="requested")
                                    <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                        <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-work-order" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                    </span>
                                    @endif
                                    <?php
                                }
                            }

                            if((!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) && ($work_orders_details->contract_type == "regular" && $work_orders_details->status == 1) && $work_orders_details->proposed_new_date != '' && $work_orders_details->bm_approve_issue == 0)
                            {
                                ?>
                                @if(Helper::checkLoggedinUserPrivileges('edit','workorder')['success'])                                
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#modal-approve-date"><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}} </button>
                                @endif
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) && ($work_orders_details->contract_type == "regular" && $work_orders_details->status == 1) && $work_orders_details->proposed_new_date != '' && $work_orders_details->bm_approve_issue == 1)
                            {
                                $disabled = "";
                                $data_toggle = "";
                                $data_title = "";
                                $disabled_btn_title = "";
                                if(!in_array(Auth::user()->id, explode(',', $work_orders_details->supervisor_id)) && Auth::user()->user_type == "supervisor")
                                {
                                    $disabled = "disabled";
                                    $disabled_btn_title = "disabled-btn-title";
                                    $data_toggle = "tooltip";
                                    $data_title = __('work_order.button.edit_not_allowed');
                                }
                                ?>
                                <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#modal-approve-date" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                </span>
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) && ($work_orders_details->contract_type == "regular" && ($work_orders_details->status == 2 || $work_orders_details->status == 6) && $work_orders_details->workorder_journey == "job_execution") && (!isset($workOrderItemRequestBySp) || $workOrderItemRequestBySp->status != 'requested'))
                            {
                                $disabled = "";
                                $data_toggle = "";
                                $data_title = "";
                                $disabled_btn_title = "";
                                if(!in_array(Auth::user()->id, explode(',', $work_orders_details->supervisor_id)) && Auth::user()->user_type == "supervisor")
                                {
                                    $disabled = "disabled";
                                    $disabled_btn_title = "disabled-btn-title";
                                    $data_toggle = "tooltip";
                                    $data_title = __('work_order.button.edit_not_allowed');
                                }
                                ?>
                        @if(!is_null($work_orders_details->schedule_start_time) && trim($work_orders_details->schedule_start_time) != "")
                            @php
                                $scheduled_date_time = date('Y-m-d', strtotime($work_orders_details->start_date)) . ' ' . $work_orders_details->schedule_start_time;
                            @endphp
                            @if (strtotime($scheduled_date_time) <= strtotime(date('Y-m-d H:i:s')))
                                <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-complete-job" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}} </button>
                                </span>
                            @endif
                        @elseif(!is_null($work_orders_details->wtf_start_time) && trim($work_orders_details->wtf_start_time) != "")
                            @php
                                $scheduled_date_time = date('Y-m-d', strtotime($work_orders_details->start_date)) . ' ' . $work_orders_details->wtf_start_time;
                            @endphp
                            @if (strtotime($scheduled_date_time) <= strtotime(date('Y-m-d H:i:s')))
                                <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-complete-job" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}} </button>
                                </span>
                            @endif
                        @else
                        <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-complete-job" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}} </button>
                                </span>
                        @endif
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) && ($work_orders_details->contract_type == "regular" && ($work_orders_details->status == 3) && $work_orders_details->workorder_journey == "job_execution" && $work_orders_details->pause_start_time == NULL))
                            {
                                $disabled = "";
                                $data_toggle = "";
                                $data_title = "";
                                $disabled_btn_title = "";
                                if(!in_array(Auth::user()->id, explode(',', $work_orders_details->supervisor_id)) && Auth::user()->user_type == "supervisor")
                                {
                                    $disabled = "disabled";
                                    $disabled_btn_title = "disabled-btn-title";
                                    $data_toggle = "tooltip";
                                    $data_title = __('work_order.button.edit_not_allowed');
                                }
                                ?>
                                @if(!is_null($work_orders_details->schedule_start_time) && trim($work_orders_details->schedule_start_time) != "")
                            @php
                                $scheduled_date_time = date('Y-m-d', strtotime($work_orders_details->start_date)) . ' ' . $work_orders_details->schedule_start_time;
                            @endphp
                            @if (strtotime($scheduled_date_time) <= strtotime(date('Y-m-d H:i:s')))
                                <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-complete-job" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                </span>
                            @endif
                        @elseif(!is_null($work_orders_details->wtf_start_time) && trim($work_orders_details->wtf_start_time) != "")
                            @php
                                $scheduled_date_time = date('Y-m-d', strtotime($work_orders_details->start_date)) . ' ' . $work_orders_details->wtf_start_time;
                            @endphp
                            @if (strtotime($scheduled_date_time) <= strtotime(date('Y-m-d H:i:s')))
                                <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-complete-job" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                </span>
                            @endif
                        @else
                        <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-complete-job" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                </span>
                        @endif
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) && ($work_orders_details->contract_type == "regular" && ($work_orders_details->status == 3 || $work_orders_details->status == 2 || $work_orders_details->status == 6) && $work_orders_details->workorder_journey == "job_evaluation") && $work_orders_details->sp_approve_job != 1)
                            {
                                ?>
                                <!--button type="button" class="btn btn-xs btn-default btn-squared btn-outline-primary btn-add float-right ml-10" data-toggle="modal" data-target="#sp-approve-job"><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button-->
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) && ($work_orders_details->contract_type == "regular" && ($work_orders_details->status == 3 || $work_orders_details->status == 2 || $work_orders_details->status == 6) && $work_orders_details->workorder_journey == "job_evaluation") && $work_orders_details->bm_approve_job == 1)
                            {
                                $disabled = "";
                                $data_toggle = "";
                                $data_title = "";
                                $disabled_btn_title = "";
                                if(!in_array(Auth::user()->id, explode(',', $work_orders_details->supervisor_id)) && Auth::user()->user_type == "supervisor")
                                {
                                    $disabled = "disabled";
                                    $disabled_btn_title = "disabled-btn-title";
                                    $data_toggle = "tooltip";
                                    $data_title = __('work_order.button.edit_not_allowed');
                                }
                                ?>
                                <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#bm-rejects-job" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                </span>
                                <?php
                            }

                            ?>
                             @if(!empty(Auth::user()->user_type == 'admin') && $checkItemRequestsForPo == 'yes')
                             
                                <span class="d-inline-block red-dot-unread position-relative" data-toggle="tooltip" title="">
                                    <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#requests-by-sp"><i class="las la-hand-paper"></i>{{__('work_order.inventory.item_request_from_sp')}}</button>
                                </span>
                            @endif
                            @if($isItemRequest == 'yes')
                                <span class="d-inline-block red-dot-unread position-relative" data-toggle="tooltip" title="">
                                    <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#requests-by-worker"><i class="las la-hand-paper"></i> {{__('work_order.inventory.requests_by_worker')}}</button>
                                </span>
                            @elseif($isItemRequest == 'admin_yes')
                                <span class="d-inline-block red-dot-unread position-relative" data-toggle="tooltip" title="">
                                    <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#requests-by-worker"><i class="las la-hand-paper"></i> {{__('work_order.inventory.requests_by_service_provider')}}</button>
                                </span>
                            @endif
                            <?php
                            if((!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) && ($work_orders_details->contract_type == "regular" && ($work_orders_details->status == 3 || $work_orders_details->status == 2 || $work_orders_details->status == 6) && $work_orders_details->workorder_journey == "job_evaluation") && $work_orders_details->bm_approve_job == 0)
                            {
                                $disabled = "";
                                $data_toggle = "";
                                $data_title = "";
                                $disabled_btn_title = "";
                                if(!in_array(Auth::user()->id, explode(',', $work_orders_details->supervisor_id)) && Auth::user()->user_type == "supervisor")
                                {
                                    $disabled = "disabled";
                                    $disabled_btn_title = "disabled-btn-title";
                                    $data_toggle = "tooltip";
                                    $data_title = __('work_order.button.edit_not_allowed');
                                }
                                ?>
                                <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-approve-job" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                </span>
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) && ($work_orders_details->contract_type == "regular" && ($work_orders_details->status == 3 || $work_orders_details->status == 2 || $work_orders_details->status == 6) && $work_orders_details->workorder_journey == "job_approval") && $work_orders_details->sp_approve_job == 2 && $work_orders_details->bm_approve_job == 0)
                            {
                                ?>
                                {{-- @flip@ if user has edit privileges  --}}
                              @if(Helper::checkLoggedinUserPrivileges('edit','workorder')['success'])
                                
                                @if($work_orders_details->workorder_journey == "job_approval" && Helper::checkSubPrivilege(Auth::user()->id,'work_order_approve')==false)
                                <button  class="btn btn-xs btn-default btn-squared btn-primary btn-add float-right ml-10 mt-sm-0 mt-2" data-toggle="tooltip" title="{{__('work_order.button.you_dont_have_permissions')}}" disabled><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}} </button>
                                @else
                                    <button  class="btn btn-xs btn-default btn-squared btn-primary btn-add float-right ml-10 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-approve-job"><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}} </button>
                                @endif

                            @endif
                                {{-- <a target="_blank" href="{{ route('workorder.checklist',$work_orders_details->id) }}" type="button" class="btn btn-xs btn-default btn-squared btn-outline-primary btn-add float-right ml-10" ><i class="la la-eye"></i> {{__('work_order.button.view_work_order_checklist')}}</a> --}}
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) && ($work_orders_details->contract_type == "regular" && ($work_orders_details->status == 3 || $work_orders_details->status == 2 || $work_orders_details->status == 6) && $work_orders_details->workorder_journey == "job_approval") && $work_orders_details->sp_approve_job == 3 && $work_orders_details->bm_approve_job == 0)
                            {
                                ?>
                                @if(Helper::checkLoggedinUserPrivileges('edit','workorder')['success'])
                                <button  class="btn btn-xs btn-default btn-squared btn-primary btn-add float-right ml-10 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-resentto-bm-job"><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                @endif
                                {{-- <a target="_blank" href="{{ route('workorder.checklist',$work_orders_details->id) }}" type="button" class="btn btn-xs btn-default btn-squared btn-outline-primary btn-add float-right ml-10 mt-sm-0 mt-2" ><i class="la la-eye"></i> {{__('work_order.button.view_work_order_checklist')}}</a> --}}
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) && ($work_orders_details->contract_type == "regular" && $work_orders_details->status == 4 && $work_orders_details->workorder_journey == "finished") && ($work_orders_details->job_completion_date != NULL))
                            {
                                $now = time();
                                $your_date = strtotime($work_orders_details->job_completion_date);
                                $datediff = $now - $your_date;
                                $days = round($datediff / (60 * 60 * 24));
                                //echo $days;
                                //echo $workorder_reopen_periods;
                                if($days <= $workorder_reopen_periods)
                                {
                                    ?>
                                    {{-- @flip@ if user has edit privileges  --}}
                                @if(Helper::checkLoggedinUserPrivileges('create','workorder')['success'])
                                    <button type="button" class="btn btn-xs btn-default btn-squared btn-outline-primary btn-add float-right ml-10 mt-sm-0 mt-2" data-toggle="modal" data-target="#re-open-job"><i class="la la-refresh"></i>{{__('work_order.button.Reopen_Workorder')}} </button>
                                @endif
                                    <?php
                                }
                            }

                            if((!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor')) && ($work_orders_details->contract_type == "regular" && $work_orders_details->status == 6 && $work_orders_details->workorder_journey == "submitted") && $work_orders_details->sp_reopen_status == 0)
                            {
                                $disabled = "";
                                $data_toggle = "";
                                $data_title = "";
                                $disabled_btn_title = "";
                                if(!in_array(Auth::user()->id, explode(',', $work_orders_details->supervisor_id)) && Auth::user()->user_type == "supervisor")
                                {
                                    $disabled = "disabled";
                                    $disabled_btn_title = "disabled-btn-title";
                                    $data_toggle = "tooltip";
                                    $data_title = __('work_order.button.edit_not_allowed');
                                }
                                ?>
                                <span class="d-inline-block {{$disabled_btn_title}}" data-toggle="tooltip" title="{{ $data_title }}">
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2" data-toggle="modal" data-target="#sp-approve-reopen" {{$disabled}}><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                </span>
                                <?php
                            }

                            if((!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee')) && ($work_orders_details->contract_type == "regular" && $work_orders_details->status == 6 && $work_orders_details->workorder_journey == "submitted") && $work_orders_details->sp_reopen_status == 1)
                            {
                                ?>
                                @if(Helper::checkLoggedinUserPrivileges('edit','workorder')['success'])
                                <button type="button" class="btn btn-xs btn-primary btn-default btn-squared  btn-add float-right ml-10 mt-sm-0 mt-2" data-toggle="modal" data-target="#bm-approve-reopen"><i class="la la-edit"></i> {{__('work_order.button.edit_work_order_properties')}}</button>
                                @endif
                                <?php
                            }
                                }                       
                            
                            ?>
                        </div>
                        @if($work_orders_details->is_collaborative == 1 && ($work_orders_details->status == 1 &&
                        $work_orders_details->workorder_journey == "submitted") && (Auth::user()->user_type == 'sp_admin' ||
                        Auth::user()->user_type == 'supervisor'))
                            <!--New button for Doubt2-->
                            <a class="btn btn-xs btn-default btn-squared btn-outline-primary  btn-add float-right px-sm-3 px-1  mt-sm-0 mt-2"  data-toggle="modal" data-target="#add-workers" href="javascript:void(0)"><i class="las la-plus"></i> {{__('work_order.forms.label.add_multipe_workers')}}</a>
                            <!--New button for Doubt2-->
                        @endif
                        @can('SPAssignWorker', $work_orders_details)
                            <button type="button" class="btn btn-xs btn-primary btn-default btn-squared btn-add float-right px-sm-3 px-1 mt-sm-0 mt-2"
                                data-toggle="modal" data-target="#sp-work-order2"><i class="la la-edit"></i>
                                {{__('work_order.button.assign_worker')}}
                            </button>
                        @endcan
                        
                        </div>
                    </div>

                    </div>
                    <?php
                    $submitted = '';$job_execution = '';$job_evaluation = "";$job_approval = '';$finished = '';
                    if($work_orders_details->workorder_journey == "submitted")
                    {
                        $submitted = 'current';
                    }
                    elseif($work_orders_details->workorder_journey == "job_execution")
                    {
                        $submitted = 'completed';
                        $job_execution = 'current';
                    }
                    elseif($work_orders_details->workorder_journey == "job_evaluation" && $work_orders_details->status != 4)
                    {
                        $submitted = 'completed';
                        $job_execution = 'completed';
                        $job_evaluation = 'current';
                    }
                    elseif($work_orders_details->workorder_journey == "job_approval")
                    {
                        $submitted = 'completed';
                        $job_execution = 'completed';
                        $job_evaluation = 'completed';
                        $job_approval = 'current';
                    }
                    elseif($work_orders_details->workorder_journey == "finished" || $work_orders_details->status == 4)
                    {
                        $submitted = 'completed';
                        $job_execution = 'completed';
                        $job_evaluation = 'completed';
                        $job_approval = 'completed';
                        $finished = 'completed';
                    }
                    ?>
                    <div class="row">
                        <div class="col-xxl-9 col-lg-9">
                            <div class="checkout-progress-indicator content-center card pb-10">
                                <div class="card-body pt-15">
                                <div class="checkout-progress justify-content-center">
                                   <div class="step <?=$submitted;?>" id="1">
                                    <?php
                                    if($submitted == "completed"){
                                        echo '<span class="las la-check"></span>';
                                    }else{
                                        echo '<span>1</span>';
                                    }
                                    ?>
                                    <span>{{__('work_order.table.submitted')}}</span>
                                   </div>
                                   <?php
                                    if($submitted == "completed"){ ?>
                                        <div class="current"><img src="{{ asset('img/svg/green.svg') }}" alt="img" class="svg"></div> <?php
                                    }else{ ?>
                                        <div class="current"><img src="{{ asset('img/svg/checkoutin.svg') }}" alt="img" class="svg"></div> <?php
                                    }
                                    ?>

                                   <div class="step <?=$job_execution;?>" id="2">
                                        <?php
                                        if($job_execution == "completed"){
                                            echo '<span class="las la-check"></span>';
                                        }else{
                                            echo '<span>2</span>';
                                        }
                                        ?>
                                      <span>{{__('work_order.table.job_execution')}}</span>
                                   </div>
                                   <?php
                                    if($job_execution == "completed"){ ?>
                                        <div class="current"><img src="{{ asset('img/svg/green.svg') }}" alt="img" class="svg"></div> <?php
                                    }else{ ?>
                                        <div class="current"><img src="{{ asset('img/svg/checkoutin.svg') }}" alt="img" class="svg"></div> <?php
                                    }
                                    ?>
                                   <div class="step <?=$job_evaluation;?>" id="3">
                                      <?php
                                        if($job_evaluation == "completed"){
                                            echo '<span class="las la-check"></span>';
                                        }else{
                                            echo '<span>3</span>';
                                        }
                                        ?>
                                      <span>{{__('work_order.table.Job_Evaluation')}}</span>
                                   </div>
                                   <?php
                                    if($job_evaluation == "completed"){ ?>
                                        <div class="current"><img src="{{ asset('img/svg/green.svg') }}" alt="img" class="svg"></div> <?php
                                    }else{ ?>
                                        <div class="current"><img src="{{ asset('img/svg/checkoutin.svg') }}" alt="img" class="svg"></div> <?php
                                    }
                                    ?>
                                   <div class="step <?=$job_approval;?>" id="4">
                                      <?php
                                        if($job_approval == "completed"){
                                            echo '<span class="las la-check"></span>';
                                        }else{
                                            echo '<span>4</span>';
                                        }
                                        ?>
                                      <span>{{__('work_order.table.Job_Approval')}}</span>
                                   </div>
                                   <?php
                                    if($job_approval == "completed"){ ?>
                                        <div class="current"><img src="{{ asset('img/svg/green.svg') }}" alt="img" class="svg"></div> <?php
                                    }else{ ?>
                                        <div class="current"><img src="{{ asset('img/svg/checkoutin.svg') }}" alt="img" class="svg"></div> <?php
                                    }
                                    ?>
                                   <div class="step <?=$finished;?>" id="5">
                                      <?php
                                        if($finished == "completed"){
                                            echo '<span class="las la-check"></span>';
                                        }else{
                                            echo '<span>5</span>';
                                        }
                                        ?>
                                      <span>{{__('work_order.table.finished')}}</span>
                                   </div>
                                </div>
                            </div>
                                <!-- ends: checkout progress -->
                             </div>
                        </div>
                        <div class="col-xxl-3 col-lg-3 col-12 pl-lg-0 mt-lg-0 mt-10">
                            <div class="progress-box px-20 pt-10 pb-10 bg-white radius-xl h-100  d-flex align-items-center">
                                <div class="application-task d-flex align-items-center flex-fill">
                                    <div class="application-task-icon wh-60 bg-opacity-secondary content-center">
                                        <img class="svg wh-25 text-secondary" src="{{ asset('img/svg/at2.svg') }}"
                                            alt="img">
                                    </div>
                                    <div class = "application-task-content">
                                        <?php
                                            if($action == \App\Enums\WorkOrderStatus::Scheduled->value){
                                                echo '<span class="bg-opacity-dark color-dark rounded-pill userDatatable-content-status active">';
                                                echo __('work_order.bread_crumbs.scheduled');
                                            }
                                               
                                            else{
                                                if($work_orders_details->contract_type == "regular"){
                                                    switch ($work_orders_details->status){
                                                        case 1:
                                                            echo '<span class="bg-opacity-success color-success rounded-pill userDatatable-content-status active">';
                                                            echo __('work_order.bread_crumbs.open');
                                                            break;
                                                        case 2:
                                                        echo '<span class="bg-opacity-info color-info rounded-pill userDatatable-content-status active">';
                                                            echo __('work_order.bread_crumbs.in_progress');
                                                            break;
                                                        case 3:
                                                            echo '<span class="bg-opacity-warning color-warning rounded-pill userDatatable-content-status active">';
                                                            echo __('work_order.bread_crumbs.on_hold');
                                                            break;
                                                        case 4:
                                                            echo '<span class="bg-opacity-dark color-light rounded-pill userDatatable-content-status active">';
                                                            echo __('work_order.bread_crumbs.closed');
                                                            break;
                                                        case 5:
                                                            echo '<span class="bg-opacity-danger color-danger rounded-pill userDatatable-content-status active">';
                                                            echo __('work_order.bread_crumbs.deleted');
                                                            break;
                                                        case 6:
                                                            echo '<span class="bg-opacity-primary color-primary rounded-pill userDatatable-content-status active">';
                                                            echo __('work_order.bread_crumbs.re_open');
                                                            break;
                                                        case 7:
                                                            echo '<span class="bg-opacity-dark color-dark rounded-pill userDatatable-content-status active">';
                                                            echo __('work_order.bread_crumbs.scheduled');
                                                            break;
                                                        case 8:
                                                            echo __('work_order.bread_crumbs.scheduled');
                                                            break;
                                                    }
                                                }

                                            else{
                                                if($work_orders_details->status == 4){
                                                    echo '<span class="bg-opacity-dark color-light rounded-pill userDatatable-content-status active">';
                                                    echo __('work_order.bread_crumbs.closed');
                                                }

                                                else{
                                                    echo '<span class="bg-opacity-secondary color-secondary rounded-pill userDatatable-content-status active">';
                                                    echo __('work_order.bread_crumbs.warrenty');
                                                }
                                            }
                                            }
                                        ?>
                                  </span>
                                        <span class="text-light fs-14 mt-1 text-capitalize">{{__('work_order.bread_crumbs.status')}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-xxl-9 col-lg-9">
                    <div class="row">
                        <div class="col-xxl-4 col-lg-4 col-md-6 col-12 mb-20 pr-lg-0 d-flex">
                            <div class="card w-100">
                                <div class="card-body">
                                    <div class="application-task d-flex align-items-center mb-30">
                                        <div class="application-task-icon wh-40 bg-opacity-primary content-center">
                                            <img class="svg wh-25 text-primary" src="{{ asset('img/svg/at.svg') }}" alt="img">
                                        </div>
                                        <div class="application-task-content">
                                            <h4 class="fw-400 fs-14"><?=$work_orders_details->service_type=='soft'?'SS':'HS';?>-<?=$work_orders_details->work_order_type=='preventive'?'PM':'RM';?></h4>
                                            <span class="text-light fs-12 mt-1 text-capitalize">{{__('work_order.bread_crumbs.maintenance_type')}}</span>
                                            <i data-toggle="tooltip" class="fas fa-question-circle ml-1 cursor-pointer" onfocus="theFocus(this);" title="" data-html="true" data-original-title="<p><strong>HS-RM</strong><br>
                                            {{__('work_order.common.hs_rm')}}</p>
                                            <p><strong>HS-PM</strong><br>
                                            {{__('work_order.common.hs_pm')}}</p>
                                            <p><strong>SS-RM</strong><br>
                                            {{__('work_order.common.ss_rm')}}</p>
                                            <p class='mb-0'><strong>SS-PM</strong><br>
                                            {{__('work_order.common.ss_pm')}}</p>  "></i>
                                        </div>
                                    </div>
                                    <div class="application-task d-flex align-items-center mb-30">
                                        <div class="application-task-icon wh-40 bg-opacity-secondary content-center">
                                            <img class="svg wh-25 text-secondary" src="{{ asset('img/svg/at2.svg') }}"
                                                alt="img">
                                        </div>
                                        <div class="application-task-content">
                                            <h4 class="fw-400 fs-14"><a data-toggle="modal" data-target="#level-modal" href="javascript:void(0);" class="fw-400 hover-underline"><?=$work_orders_details->priority_level;?></a></h4>
                                            <span class="text-light fs-12 mt-1 text-capitalize">{{__('work_order.list.priority')}}</span>
                                        </div>
                                    </div>
                                    <div class="application-task d-flex align-items-center mb-30">
                                        <div class="application-task-icon wh-40 bg-opacity-warning content-center">
                                            <img class="svg wh-25 text-warning" src="{{ asset('img/svg/at3.svg') }}" alt="img">
                                        </div>
                                        <div class="application-task-content">
                                            <h4 class="fw-400 fs-14"><?=($work_orders_details->target_date == "")?'':date('d/m/Y H:i', strtotime($work_orders_details->target_date));?></h4>
                                            <span class="text-light fs-12 mt-1 text-capitalize">{{__('work_order.list.target_date')}}</span>
                                        </div>
                                    </div>

                                    <div class="application-task d-flex align-items-center mb-30">
                                        <div class="application-task-icon wh-40 bg-opacity-secondary content-center">
                                            <img class="wh-25 text-warning" src="{{ asset('img/color-palette.png') }}" alt="img">
                                        </div>
                                        <div class="application-task-content max-w-100">
                                            <h4 class="fw-400 fs-14 w-b-name">
                                                <?php
                                                if(!Empty($work_orders_details->property_type=='complex'))
                                                {
                                                    $complex_name = $work_orders_details->complex_name.' ';
                                                }
                                                else
                                                {
                                                    $complex_name = '';
                                                }
                                                $building_name = $work_orders_details->building_name.$complex_name;
                                                if(strlen($building_name) > 14)
                                                {
                                                    ?>
                                                    {{-- @flip@ remove condition from class --}}
                                                    {{-- @if($work_orders_details->is_building_deleted != ''|| Helper::checkLoggedinUserPrivileges('no_view','property')['success']
) disabled @endif --}}
                                                    <a class=""  @if($work_orders_details->is_building_deleted != '') disabled @endif @if($work_orders_details->is_building_deleted == '' && Helper::checkLoggedinUserPrivileges('view','property')['success']
)) href="{{ route('property.details', Crypt::encryptString(Helper::first_building_id_from_property_id($work_orders_details->property_id))) }}" @endif data-toggle="tooltip" onfocus="theFocus(this);" title="" data-html="true" data-original-title="<?php if(!Empty($work_orders_details->property_type=='complex')){echo $work_orders_details->complex_name.' '; };?><span><?=$work_orders_details->building_name; ?></span> @if($work_orders_details->is_building_deleted != '')
                                                <span>{{__('general_sentence.modal.deleted')}} </span>

                                                @endif">
                                                    <?php
                                                }
                                                else
                                                {
                                                    ?>
                                                    {{-- @flip@ remove condition from class  --}}
                                                    <a  class="" @if($work_orders_details->is_building_deleted == '' && Helper::checkLoggedinUserPrivileges('view','property')['success']
)) href="{{ route('property.details', Crypt::encryptString(Helper::first_building_id_from_property_id($work_orders_details->property_id))) }}" @endif data-toggle="tooltip" data-html="true" data-original-title="<?php if(!Empty($work_orders_details->property_type=='complex')){echo $work_orders_details->complex_name.' '; };?><span><?=$work_orders_details->building_name;?></span>
                                                <span>
                                                @if($work_orders_details->is_building_deleted != '')
                                                {{__('general_sentence.modal.deleted')}} 
                                                @endif</span>" >
                                                    <?php
                                                }
                                                if(!Empty($work_orders_details->property_type=='complex')){echo $work_orders_details->complex_name.' '; };?><span><?=$work_orders_details->building_name;?></span>
                                                <span>
                                                @if($work_orders_details->is_building_deleted != '')
                                                {{__('general_sentence.modal.deleted')}} 
                                                @endif</span>
                                                </a>
                                            </h4>
                                            <span class="text-light fs-12 mt-1 text-capitalize">{{__('work_order.user_previleges.property')}}</span>
                                        </div>
                                    </div>
                                    <div class="application-task d-flex align-items-center mb-30">
                                        <div class="application-task-icon wh-40 bg-opacity-info content-center">
                                            <img class="svg wh-25 text-warning" src="{{ asset('img/svg/Customer.svg') }}" alt="img">
                                        </div>
                                        <div class="application-task-content">
                                            <h4 class="fw-400 fs-14"><?=$work_orders_details->raised_by;?></h4>
                                            <span class="text-light fs-12 mt-1 text-capitalize">{{__('work_order.list.raised_by')}}</span>
                                        </div>
                                    </div>
                                    <div class="application-task d-flex align-items-center mb-30">
                                    <div class="application-task-icon wh-40 bg-opacity-warning content-center">
                                            <img class="svg wh-25 text-warning" src="{{ asset('img/svg/documents.svg') }}" alt="img">
                                        </div>
                                        <div class="application-task-content">
                                            @php
                                                $is_contact_show=true;
                                                $is_contact_show=(Helper::checkLoggedinUserPrivileges('view','workorder')['success'] && Helper::checkLoggedinUserPrivileges('no_view','contracts',false)['success'])
                                            @endphp
                                            <h4 class="fw-400 fs-14">
                                                @if($work_orders_details->contract_deleted == false)
                                                <a @if($is_contact_show) href="{{ route('data.contracts.show',Crypt::encryptString($work_orders_details->contract_id)) }}" @endif><?=$work_orders_details->contract_number;?>
                                                </a>
                                                @else
                                                <?=$work_orders_details->contract_number;?>
                                                @endif
                                            </h4>
                                            <span class="text-light fs-12 mt-1 text-capitalize">
                                                {{$status}}</span>
                                        </div>
                                    </div>

                                    <!--=========New Design for Checklist view=========-->
                                    <div class="application-task d-flex align-items-center mb-30">
                                        <div class="application-task-icon wh-40 bg-opacity-warning content-center">
                                            <img class="svg wh-25 text-warning" src="{{ asset('img/svg/checklist.svg') }}" alt="img">
                                        </div>
                                        <div class="application-task-content">
                                            @if(isset($data['checklists']->checklist_title))
                                                <h4 class="fw-400 fs-14 text-success">  
                                                    <a data-toggle="modal" data-target="#checklist-yes" href="javascript:void(0);" class="fw-400 hover-underline">
                                                    {{ $data['checklists']->checklist_title }}
                                                    </a>
                                                </h4>
                                            @else
                                                <h4 class="fw-400 fs-14">{{__('data_contract.contract_forms.label.no')}}</h4>
                                            @endif
                                            <span class="text-light fs-12 mt-1 text-capitalize">{{__('work_order.bread_crumbs.Checklist')}}</span>
                                        </div>
                                    </div>
                                    
                                    @if(!empty($usedItems))
                                        <div class="application-task d-flex align-items-center mb-30">
                                            <div class="application-task-icon wh-40 bg-opacity-warning content-center">
                                                <img class="svg wh-25 text-warning" src="{{ asset('img/product.png') }}" alt="img">
                                            </div>
                                            <div class="application-task-content">
                                                <h4 class="fw-400 fs-14 text-success">
                                                    <a data-toggle="modal" data-target="#used-spare-parts" href="javascript:void(0);" class="fw-400 hover-underline">
                                                        {{__('data_contract.contract_forms.label.used_items_list')}}
                                                    </a>
                                                </h4>
                                                <span class="text-light fs-12 mt-1 text-capitalize">{{__('work_order.bread_crumbs.inventory_management')}}</span>
                                            </div>
                                        </div>
                                    @endif

                                    <!--=========New Design for Re-Opens view=========-->
                                    @if($data['reopen_wo_count'] > 0)
                                    <div class="application-task d-flex align-items-center">
                                        <div class="application-task-icon wh-40 bg-opacity-info content-center">
                                            <img class="svg wh-25 text-warning" src="{{ asset('img/svg/re-open.svg') }}" alt="img">
                                        </div>
                                        <div class="application-task-content">
                                            <h4 class="fw-400 fs-14">{{$data['reopen_wo_count']}} <a href="{{route('workorder.reopen_workorder_details',Crypt::encryptString($work_orders_details->id))}}" class="fs-12 ml-1 border-primary rounded-pill px-2">{{__('work_order.reopen_wo_details.view')}}</a></h4>
                                            <span class="text-light fs-12 mt-1 text-capitalize">{{__('work_order.reopen_wo_details.reopens')}}</span>
                                        </div>
                                    </div>
                                    @endif
                                    <!--=========New Design for Re-Opens view=========-->
                                </div>
                            </div>
                            <!-- ends: .card -->
                        </div>
                        <!-- ends: .card -->
                        <div class="col-xxl-8 col-lg-8 col-md-6 d-flex align-content-around flex-wrap">
                            <div class="card mb-20 w-100">
                                <div class="card-header">
                                    <h6 class="fs-13">{{__('work_order.bread_crumbs.Workorder_Details')}}</h6>
                                </div>
                                <div class="card-body pt-0 pb-0">
                                    <div class="about-projects">
                                        <div class="landing-pages-table table-responsive pb-0">
                                            <table class="table table--default align-left mb-0">
                                                <tbody>
                                                    <tr>
                                                        <td width="30%">
                                                        {{__('work_order.table.work_order_id')}}
                                                        </td>
                                                        <td width="70%"><?=$work_orders_details->work_order_id;?></td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                        {{__('work_order.table.Reported_At')}}
                                                        </td>
                                                        <td>
                                                            <?=date('d/m/Y', strtotime($work_orders_details->created_at));?> 
                                                            <?=date('h:i', strtotime($work_orders_details->created_at));?><span class="pl-1"><?=date('A', strtotime($work_orders_details->created_at));?></span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>@lang('work_order.forms.label.worker')</td> 
                                                        <td>
                                                            <span> 
                                                                @if(is_null($worker))
                                                                    @lang('work_order.forms.label.not_yet_assigned')
                                                                @else
                                                                    {{ !is_null($worker) ? $worker->name : __('work_order.forms.label.not_yet_assigned') }}
                                                                @endif
                                                            </span>
                                                            @if(!is_null($worker) && $worker->isDeleted())
                                                                <span class = "bg-opacity-danger color-danger active mx-1 py-1 px-2" style = "border-radius: 10px; font-size:12px">
                                                                    @lang('work_order.bread_crumbs.deleted')
                                                                </span>
                                                            @endif
                                                            @if($work_orders_details->assign_type == \App\Enums\AssignType::Smart->value && isset($criteria))
                                                                <a class = "text-primary border-primary rounded-pill px-2 d-inline-block" href = "javascript:void(0);" data-toggle = "modal" data-target = "#criteria-modal">
                                                                    <i class = "las la-tasks"></i>  
                                                                    @lang('work_order.forms.label.criteria')
                                                                </a>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                    @if($work_orders_details->is_collaborative == 1 && ($work_orders_details->status == 1 &&
                                                    $work_orders_details->workorder_journey == "submitted") && (Auth::user()->user_type == 'sp_admin' ||
                                                    Auth::user()->user_type == 'supervisor'))
                                                    <tr>
                                                        <td>{{__('work_order.forms.label.maintenance_workers')}}</td>
                                                        <td>
                                                            <a href="" class="border-primary rounded py-1 text-primary px-3 view_all_button" data-toggle="modal" data-target="#add-workers">
                                                                <i class="las la-plus"></i> {{__('work_order.forms.label.add_multipe_workers')}}
                                                            </a>
                                                            <!--after assigning the workers-->
                                                            {{--<span class="text-primary">
                                                               {{__('work_order.forms.label.multiple_workers_assigned')}} 
                                                            </span>
                                                            --}}
                                                        </td>
                                                    </tr>
                                                    @endcan

                                                    @if($work_orders_details->contract_type =='regular')
                                                    <tr>
                                                        <td>
                                                        {{__('work_order.bread_crumbs.Processed_By')}}
                                                        </td>
                                                        <td><?php if(!Empty($supervisors) && ($work_orders_details->worker_id != 0 || $work_orders_details->assigned_to != "sp_worker")){
                                                            echo $supervisors;        
                                                        }
                                                        else{
                                                            echo __('work_order.table.no_action_taken_from_sps');
                                                        }
                                                        if($data['user_type'] == "sp_admin" && $work_orders_details->status != 4)
                                                        {
                                                            echo '<div>
                                                            <a href="" class="d-inline-block btn-add-sp rounded text-center" data-toggle="modal" data-target="#assign-sp"><span class="la la-plus"></span> '.__('work_order.forms.label.Assign_Supervisor').' </a>
                                                            </div>';
                                                        }
                                                        ?>
                                                        </td>
                                                    </tr>
                                                    @endif
                                                    {{--
                                                    <!--========= PM title ==========-->
                                                    <tr>
                                                        <td>Title
                                                        </td>
                                                        <td>
                                                            <a href="" class="text-primary">Preventive Work order Title</a>
                                                        </td>
                                                    </tr>
                                                    <!--========= End PM title ==========-->
                                                    --}}
                                                    <tr>
                                                        <td>
                                                        {{__('work_order.table.description')}}
                                                        </td>
                                                        <td><?=$work_orders_details->description;?></td>
                                                    </tr>
                                                    @if($work_orders_details->floor != '')
                                                    <tr>
                                                        <td>
                                                        {{__('work_order.forms.label.floor')}}
                                                        </td>
                                                        <td><?=$work_orders_details->floor;?></td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                        {{__('work_order.forms.label.room')}}
                                                        </td>
                                                        <td><?=$work_orders_details->room;?></td>
                                                        
                                                    </tr>
                                                    @else
                                                    <tr>
                                                        <td>
                                                        {{__('work_order.forms.label.place_of_maintenance')}}
                                                        </td>
                                                        <td><?=$work_orders_details->place;?></td>
                                                        
                                                    </tr>
                                                    @endif
                                                    @if(isset($work_orders_details->maintenance_request) && (Auth::user()->user_type != 'sp_admin' && Auth::user()->user_type != 'supervisor'&& Auth::user()->user_type != 'super_admin' && Auth::user()->user_type != 'osool_admin'))
                                                    <tr>
                                                        <td>
                                                        {{__('slider.lists.maintenance_request')}}
                                                        </td>
                                                        <td>
                                                            {{-- @flip1@ as poa and poe doesn't have access--}}
                                                            @if(Auth::user()->user_type == 'admin'||Auth::user()->user_type == 'admin_employee')
                                                            #<?=$work_orders_details->maintenance_request->id;?>
                                                            @else
                                                            <a href="{{ route('maintenance_requests.list') }}?id=<?=$work_orders_details->maintenance_request->id?>">#<?=$work_orders_details->maintenance_request->id;?></a>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                    @endif
                                                    @if(isset($work_orders_details->maintenance_request) && (Auth::user()->user_type != 'building_manager' && Auth::user()->user_type != 'building_manager_employee'))
                                                    @if($work_orders_details->maintenance_request->group_id != 0 || $work_orders_details->maintenance_request->app_type == "tenant")
                                                    <tr>
                                                        <td>
                                                        {{__('user_management_module.user_forms.label.tenants_appartment_and_villa')}}
                                                        </td>
                                                        <td>
                                                            {{ optional($work_orders_details->maintenance_request->user)->apartment ?? '-' }}
                                                        </td>
                                                    </tr>
                                                    @endif
                                                    @endif
                                                    @if((isset($data['projectDetails']->use_tenant_module) && $data['projectDetails']->use_tenant_module == 1) && (isset($data['projectDetails']->tenant_status) && $data['projectDetails']->tenant_status == 1) && isset($data['projectSettings']->enable_warranty) && $data['projectSettings']->enable_warranty == 1 && (isset($data['maintanance_request']->generated_from) && ($data['maintanance_request']->generated_from == "tenant" || ($data['maintanance_request']->generated_from == "app" && $data['maintanance_request']->user_id == 0))))
                                                    <tr>
                                                        <td>
                                                        {{__('user_management_module.user_forms.label.warranty_status')}}
                                                        </td>
                                                        <td>
                                                            {{ $data['warrantyStatus'] }} 
                                                        </td>
                                                    </tr>
                                                    @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                @if (isset($work_orders_details->maintanance_request_id) && $work_orders_details->maintanance_request_id!= null)
                                <div class="card-header">
                                    <h6 class="fs-13">{{__('work_order.forms.label.maintenance_req_info')}}</h6>
                                </div>
                                <div class="card-body pt-3">
                                    <div class="landing-pages-table table-responsive mb-0 pb-0">
                                            <table class="table table--default align-left mb-0">
                                                <tbody>
                                                    <tr>
                                                    {{-- @php dd($work_orders_details);@endphp --}}
                                                        <td width="30%">{{__('work_order.forms.label.submitted_by')}}</td>
                                                        <td width="70%">
                                                            {{ $work_orders_details->getMaintenanceCreatedName }}
                                                            @if($work_orders_details->maintenance_request->phone) ({{ $work_orders_details->maintenance_request->phone }}) @endif
                                                        </td>                                                    
                                                    </tr>
                                                    <tr>
                                                        <td>{{__('work_order.forms.label.submitted_at')}}</td>
                                                        <td>
                                                            <?=date('d/m/Y', strtotime($work_orders_details->maintenance_request->created_at));?> 
                                                            <?=date('h:i', strtotime($work_orders_details->maintenance_request->created_at));?><span class="pl-1"><?=date('A', strtotime($work_orders_details->created_at2));?></span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                </div>
                                @endif
                            </div>

                            <div class="card mb-20 w-100">
                                <div class="card-header">
                                    <h6 class="fs-13">{{__('work_order.bread_crumbs.Asset_Information')}}</h6>
                                </div>
                                <div class="card-body pb-0 pt-0">
                                    <div class="about-projects">
                                        <div class="landing-pages-table table-responsive pb-0">
                                            <table class="table table--default align-left">
                                                <tbody>
                                                    <tr>
                                                        <td width="30%">
                                                        {{__('work_order.forms.label.asset_tag')}}
                                                        </td>
                                                        <td width="70%"><?=$work_orders_details->asset_tag?$work_orders_details->asset_tag:__('work_order.forms.label.Asset_is_not_registered_in_the_system');?>@if($work_orders_details->asset_n_deleted != '') {{__('general_sentence.modal.deleted')}} @endif</td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                        {{__('work_order.forms.label.asset_category')}}
                                                        </td>
                                                        <td><?=$work_orders_details->asset_category;?> @if($work_orders_details->asset_cat_deleted_at != '') {{__('general_sentence.modal.deleted')}} @endif</td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                        {{__('work_order.forms.label.asset_name')}}
                                                        </td>
                                                        <td>
                                                            @php
                                                                $is_contact_clickable=true;
                                                                $is_asset_clickable=(Helper::checkLoggedinUserPrivileges('view','workorder')['success'] && Helper::checkLoggedinUserPrivileges('no_view','assets',false)['success'])
                                                            @endphp
                                                            
                                                            @if(isset($work_orders_details->asset_name))
                                                            {{-- @flip@ check view privileges --}}
                                                           
                                                            <a id="openpopup"  @if($is_asset_clickable) href="javascript:void(0)" data-toggle="modal" data-target="#view-asset" @endif>{{ucfirst($work_orders_details->asset_name)}} </a>
                                                            
                                                            @if($work_orders_details->asset_name_deleted_at != '') {{__('general_sentence.modal.deleted')}} @endif
                                                            @else
                                                            {{__('work_order.forms.label.Asset_is_not_registered_in_the_system')}}
                                                            @endif
                                                            {{-- @@ if user has workorder view and assets view privileges --}}
                                                            @if($is_contact_clickable)
                                                            @include('applications.admin.property.asset_view_modal')
                                                            @endif
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- ends: col -->
                        </div>
                    </div>
                    
                    {{-- start chat --}}
                    <div class="">
                        <!--------------------Related work order New Design---------------->
                        @if(count($work_orders_details->related_wos)>1)
                            <div class="card mb-20 w-100">
                                <div class="card-header">
                                    <h6 class="fs-13">{{__('work_order.bread_crumbs.related_wos')}}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="suborders">
                                        @foreach($work_orders_details->related_wos as $related_wos)
                                            <a href="{{route('workorder.show', [Crypt::encryptString($related_wos->id)])}}" class="fw-400 badge badge-round @if($related_wos->status == 4) badge-dull @else badge-primary @endif badge-lg badge-outlined px-3 py-2 d-inline-block mr-2 @if($work_orders_details->work_order_id == $related_wos->work_order_id) @if($related_wos->status == 4) bg-dull @else bg-primary text-white @endif @endif">{{$related_wos->work_order_id}}</a>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endif
                        <!-- =============End Related work order New Design============= -->
                     @if($work_orders_details->is_collaborative == 1)
                        <div class="card mb-20">
                            <div class="card-header">
                                <h6 class="fs-13">{{__('work_order.forms.label.worker_time_record')}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="userDatatable projectDatatable project-table bg-white radius-xl w-100">
                                    <div class="d-flex justify-content-end"></div>
                                    <div class="table-responsive">
                                        <table class="table mb-0">
                                            <thead>
                                                <tr class="userDatatable-header">
                                                    <th width="25%">
                                                        <span class="projectDatatable-title">{{__('work_order.forms.label.worker_name')}}</span>
                                                    </th>
                                                    <th width="25%">
                                                        <span class="projectDatatable-title">{{__('work_order.forms.label.start_date')}}</span>
                                                    </th>
                                                    <th width="25%">
                                                        <span class="projectDatatable-title"> {{__('work_order.forms.label.end_date')}} </span>
                                                    </th>
                                                    <th width="25%">
                                                        <span class="projectDatatable-title">{{__('work_order.forms.label.record_time')}}</span>
                                                    </th>
                                                    <th width="25%">
                                                        <span class="projectDatatable-title">{{__('work_order.task_number_popup.result')}}</span>
                                                    </th>
                                                    <th width="25%">
                                                        <span class="projectDatatable-title">{{__('work_order.forms.label.spa')}}</span>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody id="">
                                            @if($data['workersDetails'])
                                                @foreach($data['workersDetails'] as $worker)
                                                    <tr>
                                                        <td>{{ $worker['worker_name'] }}</td>
                                                        <td>
                                                            @if($worker['start_time'])
                                                                {{ \Carbon\Carbon::parse($worker['start_time'])->format('d/m/Y h:i A') }}
                                                            @else
                                                                <span class="badge-pending rounded-pill userDatatable-content-status active">{{__('data_contract.contract_documents.pending')}}</span>
                                                            @endif
                                                            </td>
                                                        <td>
                                                            @if($worker['end_time'])
                                                                {{ \Carbon\Carbon::parse($worker['end_time'])->format('d/m/Y h:i A') }}
                                                            @else
                                                                <span class="badge-pending rounded-pill userDatatable-content-status active">{{__('data_contract.contract_documents.pending')}}</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @if($worker['end_time'])
                                                                @php
                                                                    $startTime = \Carbon\Carbon::parse($worker['start_time']);
                                                                    $endTime = \Carbon\Carbon::parse($worker['end_time']);
                                                                    $diffInMinutes = $startTime->diffInMinutes($endTime);
                                                                    $hours = floor($diffInMinutes / 60);
                                                                    $minutes = $diffInMinutes % 60;
                                                                @endphp

                                                                {{ sprintf('%02d', $hours) }} {{ __('configration_assets.comminucation_table.Hours') }} {{ sprintf('%02d', $minutes) }} {{ __('configration_assets.comminucation_table.Minutes') }}
                                                            @else
                                                                -
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <ul class="flex-wrap mb-0 orderDatatable_actions d-flex">
                                                                <li>
                                                                    @if($worker['is_result'] == 1)
                                                                        <a class="view submitted-checklist-popup" title="View" href="javascript:void(0);" data-toggle="modal" data-target="#results-feedback" data-worker-id="{{ $worker['worker_id'] }}" data-checklist-id="{{ $worker['checklist_id'] }}" data-work-order-id="{{ $worker['work_order_id'] }}" id="submitted-checklist-popup"><i class="la la-eye"></i></a>
                                                                    @else
                                                                        <span class="text-center">-</span>
                                                                    @endif
                                                                </li>
                                                            </ul>
                                                        </td>
                                                        <td>{{ $worker['service_provider_name'] }}</td>
                                                    </tr>
                                                @endforeach
                                            @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                        <div class="chat-area d-flex mb-20" @if(!in_array(Auth::user()->id, explode(',', $work_orders_details->supervisor_id)) && Auth::user()->user_type == "supervisor")
                                style="width: 100%; display:none !important"
                                @else
                                style="width: 100%;"
                                @endif>
                            <div class="chat">
                                <div class="chat-body bg-white radius-xl">
                                    <div class="chat-header">
                                        <div class="media chat-name align-items-center">
                                            <div class="media-body align-self-center ">
                                                <h5 class="mb-0 fw-500 fs-13">{{__('work_order.forms.label.chat')}}</h5>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chat-box p-xl-30 pl-lg-20 pr-lg-0">
                                        <!-- <div class="image1-block view-img">
                                            <img class="image1 image rounded" src="{{ asset('img/svg/checkoutin.svg') }}">
                                        </div> -->
                                        <!-- Call this when file uploaded from chat-->
                                        {{-- <div class="flex-1 justify-content-start d-flex incoming-chat mb-20">
                                            <div class="chat-text-box w-100">
                                                <div class="media">
                                                    <div class="media-body d-flex justify-content-end">
                                                        <div class="chat-text-box__content">
                                                            <div class="chat-text-box__title d-flex align-items-center justify-content-end mb-2">
                                                                <h6 class="fs-14">Building Manager</h6>
                                                                <span class="chat-text-box__time fs-12 color-light fw-400 ml-15">02 Jun 2022 13:13</span>
                                                            </div>
                                                            <div class="align-items-center justify-content-end d-flex">
                                                                <div class="chat-text-box__subtitle p-20 bg-deep">
                                                                            <div class="sent-message file-list-57 rounded-pill">
                                                                                <a class="file-name d-flex align-items-center">
                                                                                    <img src="{{ asset('img/ar.png') }}" class="mr-2">
                                                                                    <span class="name-text" id="file_name"><span>about-pic.png</span></span>
                                                                                </a>
                                                                            </div>
                                                                            <div class="sent-message file-list-57 rounded-pill text-dark">
                                                                                <a class="file-name" href="#">
                                                                                    <i class="las la-download rounded-circle mr-2"></i>
                                                                                    <span class="name-text" id="file_name"><span>about-pic.png</span></span>
                                                                                </a>
                                                                            </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="chat-text-box__photo">
                                                            <img src="http://localhost/Osool-B2G/uploads/profile_images/1629018822.jpg" class="align-self-start ml-15 wh-46" alt="Building Manager" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div> --}}
                                        <!--End Call this when file uploaded from chat-->
                                        <div id="chatLoad">
                                            <?php
                                                if(!empty($chats))
                                                {
                                                    ?>

                                                    <?php
                                                    $counter=0;
                                                    $usertype =  '';
                                                    $cu_la = App::getLocale();
                                                    $sender_id = $chats[0]['user_id'] ;
                                                    $reciever_id = $chats[0]['receiver_id'] ;
                                                    foreach($chats as $key=>$chat)
                                                    {
                                                        if($chat['user_type'] == "sp_worker"){
                                                            if($cu_la == "ar"){
                                                                $usertype =  'عامل'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Worker';

                                                            }
                                                        }
                                                        else if($chat['user_type'] == "super_admin"){
                                                            //$usertype = 'Super Admin';
                                                            if($cu_la == "ar"){
                                                                $usertype =  'Super Admin'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Super Admin';

                                                            }

                                                        }
                                                        else if($chat['user_type'] == "osool_admin"){
                                                            //$usertype = 'Super Admin';
                                                            if($cu_la == "ar"){
                                                                $usertype =  'Osool Admin'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Osool Admin';

                                                            }
                                                        }
                                                        else if($chat['user_type'] == "admin"){
                                                            if($cu_la == "ar"){
                                                                $usertype =  'مالك المشروع'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Project Owner Admin';

                                                            }

                                                        }
                                                        else if($chat['user_type'] == "admin_employee"){
                                                            if($cu_la == "ar"){
                                                                $usertype =  'نائب مالك المشروع'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Project Owner Employee';

                                                            }
                                                            //$usertype = 'Admin Employee';
                                                        }
                                                        else if($chat['user_type'] == "building_manager"){
                                                            if($cu_la == "ar"){
                                                                $usertype =  'مدير المبنى'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Building Manager Admin';

                                                            }
                                                        }
                                                        else if($chat['user_type'] == "building_manager_employee"){

                                                            if($cu_la == "ar"){
                                                                $usertype =  'نائب مدير المبنى'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Building Manager Employee';

                                                            }

                                    
                                                        }
                                                        else if($chat['user_type'] == "sp_admin"){
                                                            
                                                            if($cu_la == "ar"){
                                                                $usertype =  'مسؤول مقدم الخدمة'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Service Provider Admin';

                                                            }
                                    
                                                        }
                                                        else if($chat['user_type'] == "supervisor"){
                                                            if($cu_la == "ar"){
                                                                $usertype =  'مشرف مقدم الخدمة'  ;

                                                            }
                                                            else{
                                                                $usertype = 'Service Provider Supervisor';
                                                            }
                                                        }

                                                        
                                                        if(auth()->user()->user_type=='super_admin' || auth()->user()->user_type=='osool_admin' || auth()->user()->user_type=='admin' || auth()->user()->user_type=='admin_employee' )
                                                        {
                                                            if(!isset($chat['profile_img']) && $chat['profile_img'] == "")
                                                            {
                                                                $img_path = "dummy_profile_image.png";
                                                            }
                                                            else
                                                            {
                                                                $img_path = $chat['profile_img'];
                                                            }
                                                            ?>
                                                            <!-- <p class="social-connector text-center text-capitalize">
                                                                <span>today</span>
                                                            </p> -->
                                                            <!-- Start: Outgoing -->
                                                            <?php
                                                            $bgColor= 'bg-deep';
                                                            $txtColor='color-grey';
                                                            $className= 'flex-1 justify-content-end d-flex incoming-chat mb-20';
                                                            $className2= 'chat-text-box__title d-flex align-items-center justify-content-end mb-2';
                                                            $className3='align-items-center justify-content-end';
                                                            $userTypeExcluded =['building_manager','building_manager_employee','sp_admin','supervisor'];
                                                            $className= 'flex-1 justify-content-end d-flex incoming-chat mb-20';
                                                            $className2= 'chat-text-box__title d-flex align-items-center justify-content-end mb-2';
                                                            $className3='d-flex align-items-center justify-content-end';
                                                            ?>

                                                            <div class=" {{$className}}">
                                                                <div class="chat-text-box w-100">
                                                                    <div class="media">

                                                                        <div class="media-body d-flex justify-content-end">
                                                                            <div class="chat-text-box__content">
                                                                                <div class="{{$className2}}">
                                                                                    <h6 class="fs-14">
                                                                                <span class="text-light"><?=$usertype;?></span> <span class="fa fa-dot-circle"></span>   <span><?=$chat['name'];?> </span>
                                                                            
                                                                                </h6> 
                                                                                    
                                                                                </div>
                                                                                <div class="{{$className3}}">
                                                                                    <div class="chat-text-box__subtitle p-20 {{$bgColor}}">
                                                                                        <p class="{{$txtColor}}"><?=$chat['message'];?></p>

                                                                                        @if($chat['attach_file'] && $chat['file_exist_path'])
                                                                                    @php
                                                                                        // @check if file is shown or not
                                                                                        $is_show_image=false;
                                                                                        $img_ext=["image/jpeg",'image/png'];

                                                                                        $is_show_image=in_array($chat['attach_file_mime_type'],$img_ext)?true:false;
                                                                                    //    @ if file name is >100 then split to substring
                                                                                        $attach_file_name=$chat['attach_file_name'];
                                                                                        if(strlen($attach_file_name)>100){
                                                                                            // dd(substr($attach_file_name,0,5));
                                                                                            $attach_file_name=substr($chat['attach_file_name'],0,20).'...'.substr($chat['attach_file_name'],-5);

                                                                                        }

                                                                                        // dd($attach_file_name);


                                                                                    @endphp
                                                <div class="sent-message file-list-57 rounded-pill">
                                                    <a class="file-name d-flex align-items-center" {!! ($is_show_image)?'':' download href="'.$chat['file_path'].'"' !!}>

                                                        @if($is_show_image)
                                                          <div class="image1-block view-img">
                                                            <img onclick="chatImageClick(this)" src="{{ $chat['file_path'] }}" class="mr-2 wh-50">
                                                          </div>
                                                        @else
                                                    <i class="las la-download rounded-circle mr-2"></i>
                                                    @endif
                                                        <span title="{{ $chat['attach_file_name'] }}" class="name-text " id="file_name"><span>{{ $attach_file_name }}</span></span></a>
                                                </div>
                                                    @endif

                                                                                    </div>
                                                                                </div>
                                                                                <span class="align-items-center justify-content-end d-flex chat-text-box__time fs-12 color-light fw-400 d-block mb-20 mt-1"><?=date('d M Y H:i', strtotime($chat['created_at']));?></span>
                                                                            </div>
                                                                            <?php
                                                                        if($img_path!='')
                                                                        {
                                                                            ?>
                                                                            <div class="chat-text-box__photo">
                                                                                    <img src="{{ImagesUploadHelper::displayImage($img_path, 'uploads/profile_images')}}"
                                                                                    class="align-self-start ml-15 wh-46" alt="" />
                                                                            </div>
                                                                            <?php
                                                                        } ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <?php
                                                        }
                                                        else{

                                                            $counter++;
                                                            //if($chat['user_type'] == "sp")
                                                            if($chat['user_id']==auth()->user()->id)
                                                            {
                                                                if($chat['user_type'] == "sp")
                                                                {
                                                                    if($chat['profile_img'] == "")
                                                                    {
                                                                        $img_path = "dummy_profile_image.png";
                                                                    }
                                                                    else
                                                                    {
                                                                        $img_path = $chat['profile_img'];
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    // $img_path = '';
                                                                    // @flip1@ add condition for sended msg user img show
                                                                    if($chat['profile_img'] != "")
                                                                    {
                                                                        $img_path = $chat['profile_img'];
                                                                    }
                                                                    else
                                                                    {
                                                                        $img_path = 'dummy_profile_image.png';
                                                                    }
                                                                }
                                                                /*if($chat['profile_img'] == "")
                                                                {
                                                                    $img_path = "dummy_profile_image.png";
                                                                }
                                                                else
                                                                {
                                                                    $img_path = $chat['profile_img'];
                                                                }*/
                                                                ?>
                                                                <!-- Start: Incomming -->
                                                                <div class="flex-1 outgoing-chat  justify-content-start">
                                                                <div class="chat-text-box">
                                                                    <div class="media ">
                                                                        <div class="media-body d-flex justify-content-start">
                                                                        <?php
                                                                        if($img_path!='')
                                                                        {
                                                                        ?>
                                                                        <div class="chat-text-box__photo ">
                                                                            
                                                                            <img src="{{ImagesUploadHelper::displayImage($img_path, 'uploads/profile_images')}}" class="align-self-start mr-15 wh-46" alt="<?=$chat['name'];?>">
                                                                        </div>
                                                                        <?php } ?>
                                                                            <div class="chat-text-box__content">
                                                                                <div class="chat-text-box__title text-right d-flex justify-content-start">
                                                                                    <h6 class="fs-14"><span><?=$chat['name'];?></span>
                                                                                    <span class="fa fa-dot-circle"></span> 
                                                                                <span class="text-light"><?=$usertype;?></span> 
                                                                                    </h6>
                                                                                </div>
                                                                                <div class="d-flex align-items-center justify-content-start mt-10">
                                                                                    <div class="chat-text-box__subtitle p-20 bg-primary">
                                                                                            @if($chat['message'])
                                                                                            <p class="color-white {{ $chat['attach_file'] && $chat['file_exist_path'] ?'mb-3':'' }}"><?=$chat['message'];?></p>
                                                                                            @endif

                                                                                            @if($chat['attach_file'] && $chat['file_exist_path'])
                                                                                    @php
                                                                                        // @check if file is shown or not
                                                                                        $is_show_image=false;
                                                                                        $img_ext=["image/jpeg",'image/png'];

                                                                                        $is_show_image=in_array($chat['attach_file_mime_type'],$img_ext)?true:false;
                                                                                    //    @ if file name is >100 then split to substring
                                                                                        $attach_file_name=$chat['attach_file_name'];
                                                                                        if(strlen($attach_file_name)>100){
                                                                                            // dd(substr($attach_file_name,0,5));
                                                                                            $attach_file_name=substr($chat['attach_file_name'],0,20).'...'.substr($chat['attach_file_name'],-5);

                                                                                        }

                                                                                        // dd($attach_file_name);


                                            @endphp
                                                <div class="sent-message file-list-57 rounded-pill">
                                                    <a class="file-name d-flex align-items-center" {!! ($is_show_image)?'':' download href="'.$chat['file_path'].'"' !!}>

                                                    @if($is_show_image)
                                                    <div class="image1-block view-img">
                                                        <img onclick="chatImageClick(this)" src="{{ $chat['file_path'] }}" class="mr-2 wh-50">
                                                    </div>
                                                @else
                                                    <i class="las la-download rounded-circle mr-2"></i>
                                                @endif
                                            <span title="{{ $chat['attach_file_name'] }}" class="name-text color-white" id="file_name"><span>{{ $attach_file_name }}</span></span>
                                                                                            </a>
                                                                                        </div>
                                                                                @endif
                                                                                </div>
                                                                                </div>
                                                                                    <span class="  chat-text-box__time fs-12 color-light fw-400 d-block mb-20 mt-1"><?=date('d M Y H:i', strtotime($chat['created_at']));?></span>
                                                                            </div>

                                                                        
                                                                            





                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                </div>
                                                                <!-- End: Incomming -->

                                                                <?php
                                                            }
                                                            else
                                                            {
                                                                if(!isset($chat['profile_img']) && $chat['profile_img'] == "")
                                                                {
                                                                    $img_path = "dummy_profile_image.png";
                                                                }
                                                                else
                                                                {
                                                                    $img_path = $chat['profile_img'];
                                                                }
                                                                ?>
                                                                <!-- <p class="social-connector text-center text-capitalize">
                                                                    <span>today</span>
                                                                </p> -->
                                                                <!-- Start: Outgoing -->
                                                                <?php
                                                                $bgColor= 'bg-deep';
                                                                $txtColor='color-grey';
                                                                $className= 'flex-1 justify-content-start d-flex incoming-chat mb-20';
                                                                $className2= 'chat-text-box__title d-flex align-items-center justify-content-end mb-2';
                                                                $className3='align-items-center justify-content-end d-flex';
                                                                $userTypeExcluded =['building_manager','building_manager_employee','sp_admin','supervisor'];
                                                                $loggedUserType = auth()->user()->user_type;
                                                                if(!in_array($loggedUserType,$userTypeExcluded) ){
                                                                    if($sender_id== $chat['user_id']){
                                                                        $className= 'flex-1 justify-content-start d-flex outgoing-chat mb-20';
                                                                        $className2= 'chat-text-box__title d-flex align-items-center justify-content-start mb-2';
                                                                        $className3='d-flex align-items-center justify-content-start';
                                                                    }
                                                                    else{
                                                                        $className= 'flex-1 incoming-chat justify-content-end';
                                                                        $className2= 'chat-text-box__title text-right d-flex justify-content-end';
                                                                        $className3='d-flex align-items-center justify-content-end mb-20 mt-10';
                                                                        $bgColor= 'bg-deep';
                                                                        $txtColor='color-gray';
                                                                    }
                                                                }
                                                                ?>
                                                                <div class=" {{$className}}">
                                                                    <div class="chat-text-box w-100">
                                                                        <div class="media">
                                                                            <div class="media-body d-flex justify-content-end">
                                                                                <div class="chat-text-box__content">
                                                                                    <div class="{{$className2}}">
                                                                                        <h6 class="fs-14"> <span class="text-light"><?=$usertype;?></span><span class="fa fa-dot-circle"></span> 
                                                                                    <span><?=$chat['name'];?></span>
                                                                                    </h6>
                                                                                    </div>
                                                                                    <div class="{{$className3}}">
                                                            <div class="chat-text-box__subtitle p-20 {{$bgColor}}">
                                                                @if($chat['message'])
                                                                <p class="{{$txtColor}} {{ $chat['attach_file'] && $chat['file_exist_path']?'mb-3':'' }}"><?=$chat['message'];?></p>
                                                                                            @endif

                                                                                            @if($chat['attach_file'] && $chat['file_exist_path'])
                                                                                            @php
                                                                                                // @check if file is shown or not
                                                                                                $is_show_image=false;
                                                                                                $img_ext=["image/jpeg",'image/png'];

                                                                                                $is_show_image=in_array($chat['attach_file_mime_type'],$img_ext)?true:false;
                                                                                            //    @ if file name is >100 then split to substring
                                                                                                $attach_file_name=$chat['attach_file_name'];
                                                                                                if(strlen($attach_file_name)>100){

                                                                                                    $attach_file_name=substr($chat['attach_file_name'],0,20).'...'.substr($chat['attach_file_name'],-5);

                                                                                                }
                                                                                            @endphp




                                                                                                <div class="sent-message file-list-57 rounded-pill">
                                                                                                    <a class="file-name {!! ($is_show_image)?'d-flex align-items-center':'' !!} " {!! ($is_show_image)?'':'href="'.$chat['file_path'].'"' !!}>

                                                                                                        @if($is_show_image)
                                                                                                        <div class="image1-block view-img">
                                                                                                            <img onclick="chatImageClick(this)" style="width: 50px;height: 50px;" src="{{ $chat['file_path'] }}" class="mr-2">
                                                                                                        </div>
                                                                                                        @else
                                                                                                            <i class="las la-download rounded-circle mr-2"></i>
                                                                                                        @endif
                                                                                                            <span title="{{ $chat['attach_file_name'] }}" class="name-text " id="file_name"><span>{{ $attach_file_name }}</span></span>
                                                                                                    </a>
                                                                                                </div>
                                                                                        @endif
                                                                                        </div>
                                                                                    </div>
                                                                                        <span class="chat-text-box__time fs-12 color-light fw-400 d-flex justify-content-end mt-1"><?=date('d M Y H:i', strtotime($chat['created_at']));?></span>
                                                                                </div>
                                                                                <?php
                                                                            if($img_path!='')
                                                                            {
                                                                                ?>
                                                                            <div class="chat-text-box__photo">
                                                                               
                                                                                <img src="{{ImagesUploadHelper::displayImage($img_path, 'uploads/profile_images')}}" class="align-self-start ml-15 wh-46" alt="<?=$chat['name'];?>">
                                                                            </div>
                                                                            <?php } ?>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <!-- End: Outgoing  -->
                                                                <?php
                                                            }
                                                        }
                                                    }
                                                }
                                            ?>
                                        </div>
                                    </div>
                                    <div class="chat-footer px-xl-30 px-lg-20 pb-lg-30 pt-1">
                                        <div class="chat-type-text">
                                            @if(Auth::user()->user_type == 'building_manager' || Auth::user()->user_type == 'building_manager_employee' || Auth::user()->user_type == 'sp_admin' || Auth::user()->user_type == 'supervisor')
                                                <div class="pt-0 outline-0 pb-0 pr-0 pl-0 rounded-0 position-relative d-flex align-items-center" tabindex="-1">
                                                    <div class="d-flex justify-content-between align-items-center w-100 flex-wrap input-container">
                                                        <div class=" flex-1 d-flex align-items-center chat-type-text__write ml-0 mr-3 rounded-pill mb-sm-0 mb-2">
                                                            {{--<textarea placeholder="{{__('work_order.forms.place_holder.Type_your_message')}}..." class="form-control border-0 p-0 bg-transparent box-shadow-none chattextbox" name="message" id="message" required=""></textarea>--}}
                                                            <input type="text" placeholder="{{__('work_order.forms.place_holder.Type_your_message')}}..." class="form-control border-0 p-0 bg-transparent box-shadow-none chattextbox" name="message" id="message" required="">
                                                        </div>
                                                        <div class="chat-type-text__btn d-flex">
                                                            <!--use this commented code for uloade-->
                                                            <div class="chat-upload mr-2">
                                                            <div class="atbd-upload__file display-none" id="hidden_label_file_name">
                                                                <ul>
                                                                    <li class="file-list-57 rounded-pill">
                                                                        <a class="file-name">
                                                                            <i class="las la-paperclip"></i>
                                                                            <div class="view-img">
                                                                            <img onclick="chatImageClick(this)" id="attach_file_img" class="mr-1 image">
                                                                            </div>
                                                                            <span class="name-text" ><span id="attach_file_name">about-pic.png</span></span>
                                                                        </a>
                                                                        <a class="btn-delete"></a>
                                                                        <div class="" onclick="removeAttachFile()">
                                                                            <a class="btn-delete"><i class="la la-trash"></i></a>
                                                                        </div>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                            </div>
                                                            <!--use this commented code for uloade-->
                                                            <!--This is the upload button-->
                                                            <label type="button" class=" btn-attachement border-0 p-10 rounded-circle wh-50 mr-2 mb-0" for="chat-file">
                                                                <span data-feather="paperclip" class="text-dark"></span>
                                                            </label>
                                                                <input type="file" id="chat-file" name="">
                                                                <!--End of upload button-->
                                                            <input type="hidden" name="work_order_id" id="work_order_id" value="<?=$work_orders_details->id;?>">
                                                            <button type="button" class="border-0 btn-primary p-10 rounded-circle wh-50" id="send_message" onclick="submit_message()">
                                                                <span data-feather="send"></span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div><!-- ends: .chat-->
                        </div><!-- Ends: .chat-area -->
                    </div>
                    {{-- end chat --}}
                </div>
                <div class="col-xxl-3 col-lg-3 mb-35 pl-lg-0">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="fw-500 fs-13">{{__('work_order.bread_crumbs.Timeline')}}</h6>
                            {{-- <button type="button"
                                class="border rounded-lg color-primary fw-500 fs-12 bg-transparent acButton">
                                <span data-feather="user-plus"></span> add user</button> --}}
                        </div>
                        <div class="card-body overflow-x-auto py-20 pl-25 pr-sm-20 pr-30">
                            <div class="timeline-box--3 timeline-vertical left-middle basic-timeline scroll-timeline">
                                <ul class="timeline">
                                 

                            
                               
                                    <?php
                                    if(!empty($timeline))
                                    {
                                        foreach($timeline as $tl)
                                        {
                                            if($tl['notification_sub_type']=='new_chat_message')continue;
                                            ?>
                                            <li class="timeline-inverted">
                                                @if($tl['is_automatic'] == 'yes')
                                                    <span class="timeline-single__buble bg-success"></span>
                                                @else
                                                    <span class="timeline-single__buble bg-primary"></span>
                                                @endif
                                                <div class="timeline-single">
                                                    <div class="timeline-single__days">
                                                        @if(($tl['notification_sub_type'] == "new_work_order_created" || $tl['message'] == 'Work order Submitted') && $tl['work_order_type'] == "preventive")
                                                        <span><?=date('d M Y - H:i', strtotime($work_orders_details->created_at));?></span>
                                                        @else
                                                        <span><?=date('d M Y - H:i', strtotime($tl['created_at']));?></span>
                                                        @endif
                                                    </div>
                                                    <div class="timeline-single__content">
                                                        @if($tl['notification_sub_type'] == "pause_workorder")
                                                        <p>{{__('work_order.notifications.pause_due_to_approval_of_spare_part_request_without_wo')}}</p>
                                                        @elseif (App::getLocale()=='en')
                                                        <p><?=$tl['message'];?></p>
                                                        @elseif (App::getLocale()=='ar')
                                                        <p><?=$tl['message_ar'];?></p>
                                                        @endif
                                                    </div>
                                                </div>
                                                <!-- ends: .timelline-single -->
                                            </li>
                                            <?php
                                        }
                                    }
                                    ?>
                                     @if (  $work_orders_details->status==7 || $work_orders_details->status==8)
 
                                     <li class="timeline-inverted">
                                         <span class="timeline-single__buble bg-danger"></span>
                                         <div class="timeline-single">
                                             <div class="timeline-single__days">
                                                 <span>
                                               {{date('d M Y - H:i', strtotime($work_orders_details->created_at))}}
                                                 </span>
                                             </div>
                                             <div class="timeline-single__content">
                                                 
                                                 {{__('work_order.bread_crumbs.Work_Order_Scheduled_by')}}
                                                 <strong>
                                                     @if (!is_null($work_orders_details->createdByUser))
                                                     {{$work_orders_details->createdByUser->name}}
                                                       @else
                                                       ----
                                                   @endif
                                                 </strong>
                                               
                                             </div>
                                         </div>
                                      
                                     </li>
                                 @endif 
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Maintenance Request Images Card -->
                    @if(isset($work_orders_details->maintanance_request_id) && isset($data['maintanance_request']) && $work_orders_details->maintanance_request_id != null && $data['maintanance_request'] != null)
                        @php
                            $maintenanceImages = [];
                            $imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];

                            // Check image1
                            if($data['maintanance_request']->image1 != null) {
                                $extension = strtolower(pathinfo($data['maintanance_request']->image1, PATHINFO_EXTENSION));
                                if(in_array($extension, $imageExtensions)) {
                                    $maintenanceImages[] = $data['maintanance_request']->image1;
                                }
                            }

                            // Check image2
                            if($data['maintanance_request']->image2 != null) {
                                $extension = strtolower(pathinfo($data['maintanance_request']->image2, PATHINFO_EXTENSION));
                                if(in_array($extension, $imageExtensions)) {
                                    $maintenanceImages[] = $data['maintanance_request']->image2;
                                }
                            }

                            // Check image3
                            if($data['maintanance_request']->image3 != null) {
                                $extension = strtolower(pathinfo($data['maintanance_request']->image3, PATHINFO_EXTENSION));
                                if(in_array($extension, $imageExtensions)) {
                                    $maintenanceImages[] = $data['maintanance_request']->image3;
                                }
                            }
                        @endphp

                        @if(count($maintenanceImages) > 0)
                            <div class="card mt-20">
                                <div class="card-header">
                                    <h6 class="fs-13">{{__('work_order.forms.place_holder.maintenance_request_images')}}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        @foreach($maintenanceImages as $image)
                                            @php
                                                $imageUrl = ImagesUploadHelper::displayImage($image, 'uploads/maintanance_request');
                                            @endphp
                                            <div class="col-sm-4 col-6 mb-4">
                                                <div class="d-flex align-items-center p-2">
                                                    <div class="view-img wo-img-div rounded" style="background: url('{{ $imageUrl }}'); background-size: cover; background-position: center;">
                                                        <img onclick="chatImageClick(this)" src="{{$imageUrl}}" alt="Maintenance Request Image" class="uploaded-image" width="100%">
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="card mt-20">
                                <div class="card-header">
                                    <h6 class="fs-13">Maintenance Request Attachments</h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center p-3">
                                        <span class="userDatatable-content-status">
                                            No images found for this Maintenance Request.
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif
                    <!-- End Maintenance Request Images Card -->

                    <!-- Work order Images New Design -->
                    @if(trim($work_orders_details->wo_images) != "")
                        <div class="card mt-4 mb-4 mt-20">
                            <div class="card-header">
                                <h6 class="fs-13">{{__('work_order.common.Work_order_images')}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex  justify-content-center flex-grow-1">
                                    <div>
                                    
                                    @if(trim($work_orders_details->wo_images) != "")
                                    @foreach(explode(',',trim($work_orders_details->wo_images)) as $k=>$v)
                                    @php
                                        $extension = pathinfo($v, PATHINFO_EXTENSION);
                                        $mime = '';
                                        if($extension != 'pdf') {
                                            $filePath = ImagesUploadHelper::displayImage($v, 'uploads/workorder', 0, false, true);
                                            $mime = 'image/jpeg';
                                        } else {
                                            $filePath = public_path('uploads/workorder/' . $v);
                                            $mime = mime_content_type($filePath);
                                        } 
                                    @endphp
                                    @if ($mime === 'application/pdf')
                                        <div class="view-img wo-img-div rounded" style="background: url('{{ asset('img/pdf.png')}}'); background-position: center; background-repeat: no-repeat; border: 1px solid #f4f1f1!important;background-size: 50% !important;">
                                            <a target="_blank" href="{{ url('/uploads/workorder/'.$v) }}"><img src="{{ asset('img/pdf.png')}}" height="60px" class="mr-3 rounded"></a>
                                        </div>
                                    @else
                                        <div class="view-img wo-img-div rounded" style="background: url('{{$filePath}}'); background-size: cover; background-position: center;">
                                            <img src="{{$filePath}}" onclick="chatImageClick(this)" height="60px" class="mr-3 rounded">
                                        </div>
                                    @endif                                    
                                    @endforeach
                                    @endif


                                    </div>
                                </div>
                                </div>
                            </div> 
                    @endif

                    {{--<div class="card mb-4 mt-20">
                        <div class="card-header">
                            <h6 class="fs-13">{{__('work_order.forms.label.added_by_sp')}}</h6>
                        </div>
                        <div class="card-body d-flex align-items-center">
                            <div class="d-flex justify-content-center flex-grow-1">
                                <div class="">
                                    <div>
                                        @foreach($images as $img)
                                            @if(trim(array_reverse(explode('.',url('storage/'.$img)))[0]) != 'pdf')
                                                @php
                                                    $actionImage = ImagesUploadHelper::displayImage($img, 'actions', 0, false, true);
                                                @endphp
                                                <div class="view-img wo-img-div rounded" style="background: url('{{$actionImage}}'); background-size: cover; background-position: center;">
                                                    <img src="{{$actionImage}}" height="60px" onclick="chatImageClick(this)" class="mr-3 rounded">
                                                </div>
                                            @else
                                            <div class="view-img wo-img-div rounded" style="background: url('{{ asset('img/pdf.png')}}'); background-position: center; background-repeat: no-repeat; border: 1px solid #f4f1f1!important;">
                                                <a target="_blank" href="{{url('storage/'.$img)}}"><img src="{{ asset('img/pdf.png')}}" height="60px" class="mr-3 rounded"></a>
                                            </div>
                                            @endif
                                        @endforeach 
                                    </div>
                                </div>
                            </div>
                        </div>  
                    </div> --}}
                    @if(!empty($images) && $work_orders_details->is_collaborative != 1)
                    <!-- Added by SP Images New Design Ends-->
                    <div class="card mb-4 mt-20">
                        <div class="card-header">
                            <h6 class="fs-13">{{__('work_order.forms.label.added_by_sp')}}</h6>
                        </div>
                        <div class="card-body d-flex align-items-center">
                            <div class="d-flex justify-content-center flex-grow-1">
                                <div class="">
                                    <div>
                                        @foreach($images as $img)
                                            @if(trim(array_reverse(explode('.',url('storage/'.$img)))[0]) != 'pdf')
                                                @php
                                                    $actionImage = ImagesUploadHelper::displayImage($img, 'actions', 0, false, true);
                                                @endphp
                                                <div class="view-img wo-img-div rounded" style="background: url('{{$actionImage}}'); background-size: cover; background-position: center;">
                                                    <img src="{{$actionImage}}" height="60px" onclick="chatImageClick(this)" class="mr-3 rounded">
                                                </div>
                                            @else
                                            <div class="view-img wo-img-div rounded" style="background: url('{{ asset('img/pdf.png')}}'); background-position: center; background-repeat: no-repeat; border: 1px solid #f4f1f1!important;">
                                                <a target="_blank" href="{{url('storage/'.$img)}}"><img src="{{ asset('img/pdf.png')}}" height="60px" class="mr-3 rounded"></a>
                                            </div>
                                            @endif
                                        @endforeach 
                                    </div>
                                </div>
                            </div>
                        </div>  
                    </div> 
                </div>
                <!-- Added by SP Images New Design Ends-->
                @endif
                <!-- ends: col -->
            </div>
        </div>
        <!-- ends: project tab -->
    </div>
</div>
<div class="wo-management">
<div class="modal fade new-member" id="sp-complete-job" role="dialog" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('workorder.workorders.list', Crypt::encryptString(1))
                         }}">{{__('work_order.button.work_order')}}  </a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <!--<li class="atbd-breadcrumb__item">
                        <a>{{__('support_tickets.table.Requests')}}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>-->
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?></a>
                    </li>

                </ul>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span>
                </button>
            </div>

            <form action="javascript:void(0);" method="get" id="validateEditSupportTickets" class="date_change_request_form" enctype="multipart/form-data">
            <div class="new-member-modal modal-body">
                @if($work_orders_details->is_collaborative == 1)
                    <div class="form-group">
                        <div class="alert-icon-big alert alert-warning" role="alert">
                            <div class="alert-icon">
                                <i class="las la-info-circle"></i>
                            </div>
                            <div class="alert-content">
                                <h6 class="alert-heading d-inline-block mb-0 pb-0">{{__('data_maintanance_request.common.note')}} : </h6>
                                <span>{{ trans('work_order.forms.label.there_exists_workers_who_still_not_completed_the_wo', ['count' => $data['totalWorkersofPendingWork']]) }}</span>
                            </div>
                        </div>
                    </div>
                @endif
               
                    <!--Begin : New Design-->
                     <div class="col-md-8 mx-auto mb-20 set-action-sp">
                        @if($work_orders_details->is_collaborative == 1)
                            <div class="mb-20 mt-20">
                                <div class="form-group">
                                    <label class="text-light text-dark fw-100">{{__('work_order.forms.label.justification_new')}}<small class="required">*</small></label>
                                    <textarea class="form-control textarea" placeholder="{{__('work_order.forms.label.write_a_justification')}}" id="sp_comment" name="sp_comment"></textarea>
                                </div>
                                <div id="complete_job_error2"></div>
                            </div>
                            <input type="hidden" name="complete_job" value="job_completed" />
                        @else
                            <div class="form-row mb-20 mt-20">
                                <div class="col">
                                    <div class="form-group">
                                        <label class="text-light text-dark fw-100">{{__('work_order.bread_crumbs.action')}}</label>
                                        <div class="atbd-select">
                                            <select class="form-control select2 complete_job" id="select-action" placeholder="{{__('work_order.forms.label.Select_response_action')}}" id="" name="select-action" style= "height:auto;">
                                                <option value = "show5" data-value = "job_started_on_behalf" {{ (!empty($work_orders_details->worker_started_at) && !is_null($work_orders_details->worker_started_at)) ||  ($work_orders_details->is_handle_by_team_leader == 1) ? 'disabled' : 'selected' }} data-tooltip="{{ $work_orders_details->is_handle_by_team_leader == 1 ? __('work_order.bread_crumbs.prevent_to_assign_due_to_manage_by_tl') : __('work_order.forms.label.start_on_behalf_worker') }}">@lang('work_order.forms.label.start_on_behalf_worker')</option>
                                                <option value = "show1" data-value = "job_completed" {{ empty($work_orders_details->worker_started_at) || is_null($work_orders_details->worker_started_at) ? 'disabled' : 'selected' }} data-tooltip="{{  __('work_order.bread_crumbs.Set_the_job_as_complete') }}">@lang('work_order.bread_crumbs.Set_the_job_as_complete')</option>
                                                <option value="show2" data-value="propose_new_date" data-tooltip="{{  __('work_order.bread_crumbs.Propose_new_date') }}">{{__('work_order.bread_crumbs.Propose_new_date')}}</option>
                                                <option value="show3" data-value="reassign_worker" {{  $work_orders_details->is_handle_by_team_leader == 1 ? 'disabled' : '' }} data-tooltip="{{ $work_orders_details->is_handle_by_team_leader == 1 ? __('work_order.bread_crumbs.prevent_to_assign_due_to_manage_by_tl') : __('work_order.forms.label.Re_assign_worker') }}">{{__('work_order.forms.label.Re_assign_worker')}}</option>
                                                @empty($workOrderItemRequestBySp->status)
                                                    @if($work_orders_details->requested_missing_spare_parts === 0)
                                                        <option value="show4" data-value="missing_spare_parts">{{__('work_order.forms.label.missing_spare_parts')}}</option>
                                                    @endif
                                                @endempty
                                            </select>
                                        </div>
                                    
                                    </div>
                                </div>
                            </div>
  
                            <div class="missing-spare-parts show-hide display-none" id="show4">

                                <!--  US 6: Collecting the missing items -->
                                <div class="form-group">
                                    <label class="text-light text-dark fw-100">{{__('work_order.inventory.select_items')}}<small class="required">*</small></label>
                                    <a href="javascript:void(0);" class="rounded text-center w-100 py-3 d-block bg-primary text-white spare-parts-missing-href" data-target="#missing-spare-parts" data-toggle="modal">{{__('work_order.inventory.select')}}</a>
                                    <span class="d-flex p-3 justify-content-between align-items-center border-primary rounded spare-parts-missing-span none-display">
                                        <label class="mb-0"><span class="selectedItemCount">0</span> {{__('work_order.inventory.items_selected')}}</label> <a href="javascript:void(0);"  data-target="#missing-spare-parts" data-toggle="modal">{{__('work_order.inventory.view')}}</a>
                                    </span>
                                </div>
                                <input type="hidden" name="stored-missing-item-id" id="stored-missing-item-id" />
                                <input type="hidden" name="stored-missing-quantity" id="stored-missing-quantity" />
                                <!--End  US 6: Collecting the missing items -->

                            </div>
                            
                            <div class="set-job show-hide {{ empty($work_orders_details->worker_started_at) || is_null($work_orders_details->worker_started_at) ? 'display-none' : '' }}" id="show1">

                                @empty($workOrderItemRequestBySp || $workOrderRequestedItems)
                                <!--  US 6: Collecting the missing items -->
                                <div class="form-group">
                                    <label class="text-light text-dark fw-100">{{__('work_order.inventory.used_spare_parts')}} <small>({{__('work_order.forms.label.optional')}})</small></label>
                                    <a href="javascript:void(0);" class="rounded text-center w-100 py-3 d-block bg-primary text-white spare-parts-href" data-target="#spare-parts" data-toggle="modal">{{__('work_order.inventory.select')}}</a>
                                    <span class="d-flex p-3 justify-content-between align-items-center border-primary rounded spare-parts-span none-display">
                                        <label class="mb-0"><span class="selectedItemCount">0</span> {{__('work_order.inventory.items_selected')}}</label> <a href="javascript:void(0);"  data-target="#spare-parts" data-toggle="modal">{{__('work_order.inventory.view')}}</a>
                                    </span>
                                </div>
                                <input type="hidden" name="stored-item-id" id="stored-item-id" />
                                <input type="hidden" name="stored-quantity" id="stored-quantity" />
                                <!--End  US 6: Collecting the missing items -->
                                @endif
                                
                                <div class="form-group">
                                    <label class="text-light text-dark fw-100">{{__('work_order.task_number_popup.Upload_Attachments')}}</label>
                                    <div class="">
                                        <div class="ant-upload ant-upload-select ant-upload-select-text">
                                            <span tabindex="0" class="ant-upload" role="button">
                                                <input type="file" accept="image/png, image/jpg, image/jpeg, application/pdf" name="pictures[]" id="file_upload" multiple="" class="display-none">
                                                <label outlined="1" transparent="0" raised="0" data="light" type="button" class="text-center py-3 w-100 border rounded"  for="file_upload">
                                                    <span data-feather="upload" class="mr-2"></span>
                                                    <span> {{__('general_sentence.click_to_upload')}}</span>
                                                </label>
                                            </span>
                                        </div>
                                        <div class="atbd-upload__file">
                                            <ul id="">

                                            </ul>
                                        </div>
                                        <div id="complete_job_error1"></div>
                                    </div>
                                    <div class="">
                                        <div class="atbd-upload__file">
                                            <ul id="preview">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="text-light text-dark fw-100">{{__('work_order.task_number_popup.comment')}}<small class="required">*</small></label>
                                    <textarea class="form-control textarea" placeholder="{{__('work_order.task_number_popup.your_comment')}}" id="sp_comment" name="sp_comment"></textarea>
                                </div>
                                <div id="complete_job_error2"></div>

                                @if($work_orders_details->sp_approove == 1)
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label for="phoneNumber">{{__('work_order.forms.label.pass_fail')}}</label><br>
                                            <div class="userDatatable-content d-inline-block">
                                            <?php
                                            //echo $work_orders_details->pass_fail;
                                            if(is_null($work_orders_details->job_started_at)){
                                                ?>
                                                    <span class = "text-muted">
                                                        @lang('work_order.common.job_not_started_yet')
                                                    </span>
                                                <?php
                                            }

                                            else{
                                                if($work_orders_details->pass_fail == 'Pass' || $work_orders_details->pass_fail == 'pass'){
                                                    ?>
                                                        <span class="bg-opacity-success  color-success rounded-pill userDatatable-content-status active">
                                                            {{__('work_order.forms.label.pass')}}
                                                        </span>
                                                    <?php
                                                }

                                                else{
                                                    ?>
                                                        <span class="bg-opacity-danger  color-danger rounded-pill userDatatable-content-status active">
                                                            {{__('work_order.forms.label.fail')}}
                                                        </span>
                                                    <?php
                                                }
                                            }
                                            ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label for="phoneNumber">{{__('work_order.forms.label.Actual_time_spent_on_job')}} </label><br>
                                            <div class="userDatatable-content d-inline-block">
                                                @if(is_null($work_orders_details->job_started_at))
                                                    <span class = "text-muted">
                                                        @lang('work_order.common.job_not_started_yet')
                                                    </span>
                                                @else
                                            <span class="bg-opacity-success  color-success rounded-pill userDatatable-content-status active">
                                                <?php
                                                
                                                if(trim($work_orders_details->pause_time_spent_minutes) != "" && $work_orders_details->pause_time_spent_minutes > 0)
                                                {
                                                    
                                                    $job_submitted_at = \Carbon\Carbon::now()->subMinutes($work_orders_details->pause_time_spent_minutes);
                                                    $job_submitted_at = $job_submitted_at->format('Y-m-d H:i:s');
                                                }
                                                else
                                                {
                                                    $job_submitted_at = date('Y-m-d H:i:s');
                                                }
                                                $job_started_at = $work_orders_details->job_started_at;
                                                //$job_submitted_at = $work_orders_details->job_submitted_at;
                                                //$job_submitted_at = date('Y-m-d H:i:s');
                                                $datetime1 = strtotime($job_started_at);
                                                $datetime2 = strtotime($job_submitted_at);
                                                $interval  = abs($datetime2 - $datetime1);                                  
                                                $ret = "";
                                                /*** get the days ***/
                                                $days = intval(intval($interval) / (3600*24));
                                                if($days> 0)
                                                {
                                                    $ret .= "$days ". __("configration_assets.comminucation_table.Days").' ';
                                                }
                                
                                                /*** get the hours ***/
                                                $hours = (intval($interval) / 3600) % 24;
                                                if($hours > 0)
                                                {
                                                    $ret .= "$hours ". __("configration_assets.comminucation_table.Hours").' ';
                                                }
                                
                                                /*** get the minutes ***/
                                                $minutes = (intval($interval) / 60) % 60;
                                                if($minutes > 0)
                                                {
                                                    $ret .= "$minutes ". __("configration_assets.comminucation_table.Minutes").' ';
                                                }
                                
                                                /*** get the seconds ***/
                                                $seconds = intval($interval) % 60;
                                                if ($seconds > 0) {
                                                    $ret .= "$seconds ".__("configration_assets.comminucation_table.Seconds");
                                                }
                                                if($ret == "")
                                                {
                                                    $ret = __("work_order.bread_crumbs.automatically_assigned");
                                                }               
                                                 
                                                echo $ret;
                                                ?> </span>
                                                @endif
                                                {{-- @flip1@ add span for show difference between start date and submited date
                                                <span class="userDatatable-content-status">
                                                    @php $days = Helper::timeFormate($work_orders_details->job_started_at, $work_orders_details->job_submitted_at);@endphp
                                                    {{$days->d}} {{__('configration_assets.comminucation_table.Days')}} 
                                                    {{$days->h}} {{__('configration_assets.comminucation_table.Hours')}} 
                                                    {{$days->i}} {{__('configration_assets.comminucation_table.Minutes')}} 
                                                    {{$days->s}} {{__('configration_assets.comminucation_table.Seconds')}} 
                                                </span> --}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group mt-10" id="cost_receipt">
                                    <br>
                                    <div class="radio-horizontal-list">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label for="name1">{{__('work_order.forms.label.Cost')}} <small>({{__('work_order.forms.label.optional')}})</small></label>
                                                    <input type="text" name="cost" id="cost"  class="form-control" placeholder="<?=$work_orders_details->cost;?>">
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label for="name1">{{__('work_order.forms.label.Upload_Receipt')}} <small>({{__('work_order.forms.label.optional')}})</small></label>
                                                    <div class="atbd-upload">
                                                        <div class="atbd-upload__button">
                                                            <label class="btn btn-lg btn-outline-lighten btn-upload w-100 mb-0" for="upload_receipt"><span data-feather="upload"></span> {{__('work_order.forms.label.Upload_Receipt')}}</label>
                                                            <br />
                                                            <input type="file" name="upload_receipt" id="upload_receipt" value="">
                                                        </div>
                                                    </div>                                  
                                                    <!-- if the file uploaded is an image-->
                                                    <div class="dynamic_switch">
                                                        
                                                    </div>
                                                    
                                                <!-- End if the file uploaded is a document-->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>

                            <div class="propose-date show-hide display-none" id="show2">
                                <div class="form-group">
                                    <label class="text-light text-dark fw-100">{{__('work_order.bread_crumbs.Propose_new_date')}}<small class="required">*</small></label>
                                    <div class="atbd-date-picker">
                                        <div class="form-group mb-0 form-group-calender">
                                            <div class="position-relative" id="">
                                                <input type="text" class="form-control proposed_new_date_sp" id="sbn_calender_01" placeholder="January 20, 2018" name="proposed_new_date" autocomplete="off">
                                                <a><span data-feather="calendar"></span></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="complete_job_error3"></div>
                                <div class="form-group">
                                    <label class="text-light text-dark fw-100" for="reason4">{{__('work_order.forms.label.Reason')}}<small class="required">*</small></label>
                                    <textarea class="form-control textarea" placeholder="{{__('work_order.forms.place_holder.Please_enter_the_reason')}}" id="reason4" name="reason4"></textarea>
                                </div>
                                <div id="complete_job_error4"></div>
                            </div>

                            <div class="assign-worker show-hide display-none" id="show3">
                                <div class="form-group">
                                    <label class="text-light text-dark fw-100">{{__('work_order.forms.label.Assign_Worker')}}<small class="required">*</small></label>
                                    <div class="atbd-select">
                                        <select class="form-control select2" placeholder="Selct " id="worker_id4" name="worker_id4" style= "height:auto;">
                                            <option value="">{{__('work_order.forms.label.Choose_Worker')}}</option>
                                        @if(!empty($workers))
                                            @foreach($workers as $wr)
                                                @if($wr['is_subcontractors_worker'])
                                                    <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>>[SUB] <?=$wr['name'];?></option>
                                                @else
                                                    <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>><?=$wr['name'];?></option>
                                                @endif
                                            @endforeach
                                        @endif
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div id="complete_job_error5"></div>
                        @endif
                    </div>                    

                    
                    <!--End: New Design-->
            </div>
            <div class="modal-footer">
            <div class="col-md-12 px-sm-40 px-15">
                        <div class="button-group d-flex justify-content-end">
                            <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve">{{__('work_order.button.cancel')}}</button>
                            <button type="submit" id="complete_job_by_sp_call" class="btn btn-primary btn-default btn-squared text-capitalize">{{__('work_order.button.update')}}</button>
                        </div>
                    </div>
            </div>
            </form>

        </div>
    </div>
</div>


<div class="modal-warranty modal fade show" id="modal-warranty" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content modal-bg-white ">
            <div class="modal-header">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-title-wraps">
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{route('workorder.workorders.list', Crypt::encryptString(1))}}"> {{__('work_order.bread_crumbs.work_order_list')}} </a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a href="">{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?></a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <span>{{__('work_order.bread_crumbs.Warranty_Workorder_Closure')}}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h6 class="modal-title"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span></button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                    <form>
                        <div class="form-group mt-10">
                            <label for="phoneNumber">{{__('work_order.forms.label.Comment')}}</label><br>
                            <textarea class="form-control" rows="8" columns="5" name="warranty_comment" id="warranty_comment"></textarea>
                        </div>
                        <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md">{{__('work_order.button.cancel')}}</button>
                            <!-- <button class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">Save</button> -->
                            <button href="" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 submit_warranty_comment" onclick="submit_warranty_comment()">{{__('work_order.button.update')}}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-warranty modal fade show" id="re-open-job" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content modal-bg-white ">
            <div class="modal-header">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-title-wraps">
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{route('workorder.workorders.list', Crypt::encryptString(1))}}"> {{__('work_order.bread_crumbs.work_order_list')}} </a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?> ({{__('work_order.button.re_open')}})</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h6 class="modal-title"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span></button>
            </div>
            <div class="modal-body">
                <div class="col-md-8 offset-md-2">
                    <form>
                        <div class="form-group mt-10">
                            <label for="phoneNumber">{{__('work_order.forms.label.Reason_to_re_open')}}</label><br>
                            <textarea class="form-control" rows="8" columns="5" name="reopen_reason" id="reopen_reason"></textarea>
                        </div>
                        <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md">{{__('work_order.button.cancel')}}</button>
                            <!-- <button class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">Save</button> -->
                            <button class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 re_open_job" onclick="re_open_job()">{{__('work_order.button.update')}}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade new-member" id="bm-rejects-job" role="dialog" tabindex="-1"
            aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('workorder.workorders.list', Crypt::encryptString(1)) }}"> {{__('work_order.button.work_order')}} </a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.bread_crumbs.Workorder_Details')}}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?> ({{__('work_order.button.Rejected_by_BM')}})</a>
                    </li>

                </ul>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span>
                </button>
            </div>
                <div class="new-member-modal">
                    <form action="javascript:void(0);" method="get" id="validateEditSupportTickets"
                        enctype="multipart/form-data">
                        <div class="col-md-12 mb-20 px-sm-40">
                            <br>
                            <div class="form-row mb-20">
                                <div class="col-sm-6">
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.The_bm_has_rejects_the_job')}}:</label><br>
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.Justification')}}: <strong><?=$work_orders_details->reason;?></strong></label>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="col-sm-6">
                                    <label for="phone_number" class="text-dark"><b>{{__('work_order.forms.label.Select_response_action')}}</b></label>
                                </div>
                            </div>
                            <div class="form-row">
                                 <!-------------------------NEw Design for Selection--------------->
                                 <div class="col-md-8 mb-3">
                                        <select class="form-control select2" name="reject_bm" id="bm-radio-02">
                                            <option value="2">{{__('work_order.forms.label.Send_back_to_worker')}}</option>
                                            <option value="1">{{__('work_order.forms.label.Resend_to_building_manager')}}</option>
                                        </select>
                                 </div>
                                 <!-------------------------NEw Design for Selection--------------->
                                <div class="w-100 flex-fill px-1">
                                    {{-- <div class="form-group">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" type="radio" name="reject_bm" value='2' id="bm-radio-02" onchange="set_sp_action(2)" checked="">
                                            <label for="bm-radio-02">
                                                <span class="radio-text">{{__('work_order.forms.label.Send_back_to_worker')}}</span>
                                            </label>
                                        </div>
                                    </div> --}}

                                     <div class="form-row form-group show-radio-div active" id="worker_block">
                                        <div class="col-md-6">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                        <label for="ticket_status" class="fw-300 mb-0">{{__('work_order.forms.label.Assign_Worker')}}</label>
                                        <medium data-toggle="modal" data-target="#not-find" class="text-dark cursor-pointer">{{__('work_order.forms.label.couldnt_find_worker')}}</medium>
                                    </div>
                                        <div class="atbd-select">
                                            <select class="form-control select2" name="reject_wo_worker_id" id="reject_wo_worker_id" style= "height:auto;">
                                                <option value="">{{__('work_order.forms.label.Choose_Worker')}}</option>
                                                @if(!empty($workers))
                                                    @foreach($workers as $wr)
                                                        @if($wr['is_subcontractors_worker'])
                                                            <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>>[SUB] <?=$wr['name'];?></option>
                                                        @else
                                                            <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>><?=$wr['name'];?></option>
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name='assigned_to_user' id='assigned_to_user' value='<?=Auth::user()->user_type;?>'>
                                            <label class="form-check-label" for="assigned_to_user" >
                                                <span class="">{{__('work_order.forms.label.start_on_behalf_worker')}}</span>
                                            </label>
                                            </div>
                                        </div>
                                           <div class="form-group mt-10 reason">
                                        <label for="phoneNumber">{{__('work_order.forms.label.Notes_to_worker')}}</label><br>
                                       <textarea class="form-control" rows="8" columns="5" name="rejection_reason3" id="rejection_reason3"></textarea>
                                    </div>
                                </div>
                                    </div>

                                </div>
                                <div class="w-100 flex-fill px-1">
                                    
                                    {{-- <div class="form-group ">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" onchange="set_sp_action(1)" type="radio" name="reject_bm" value='1'
                                            id="bm-radio-03" >
                                            <label for="bm-radio-03">
                                                <span class="radio-text">{{__('work_order.forms.label.Resend_to_building_manager')}}</span>
                                            </label>
                                        </div>
                                    </div> --}}


                                    <div class="form-row form-group show-radio-div mt-10 reason" id="resend_to_BM">
                                        <div class="col-md-6">
                                        <label for="reason">{{__('work_order.forms.label.Reason')}}</label><br>
                                       <textarea class="form-control" rows="8" columns="5" placeholder="{{__('work_order.forms.label.Write_a_reason')}}" name="rejection_reason6" id="rejection_reason6"></textarea>
                                    </div>
                                </div>



                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col mr-25" >
                                    {{--
                                     <!--div class="form-group">
                                        <label for="ticket_status" class="text-light fw-300">{{__('work_order.forms.label.Assign_Worker')}}</label>
                                        <div class="atbd-select">
                                            <select class="form-control" name="worker_id" id="worker_id" style= "height:auto;">
                                                <option value="">{{__('work_order.forms.label.Choose_Worker')}}</option>
                                                <?php
                                                if(!empty($workers))
                                                {
                                                    foreach($workers as $wr)
                                                    {
                                                        ?>
                                                        <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>><?=$wr['name'];?></option>
                                                        <?php
                                                    }
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div-->
                                    --}}
                                </div>
                            </div>

                            <input type="hidden" name="work_order_id" id="work_order_id" value="<?=$work_orders_details->work_order_id;?>">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve"> {{__('work_order.button.cancel')}} </button>
                                <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize" id="sp_action_on_rejects_wo_by_bm"> {{__('work_order.button.submit')}} </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
</div>


<div class="modal fade new-member" id="sp-resentto-bm-job" role="dialog" tabindex="-1"
            aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('workorder.workorders.list', Crypt::encryptString(1)) }}"> {{__('work_order.button.work_order')}} </a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.bread_crumbs.Workorder_Details')}}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?> ({{__('work_order.button.Rejected_by_BM')}})</a>
                    </li>

                </ul>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span>
                </button>
            </div>
                <div class="new-member-modal">
                    <form action="javascript:void(0);" method="get" id="validateEditSupportTickets"
                        enctype="multipart/form-data">
                        <div class="col-md-12 mb-20 px-sm-40">
                            <br>
                            <div class="form-row mb-20">
                                <div class="col">
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.The_building_manager_has_rejects_the_job')}}</label><br>
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.Justification')}}: <strong><?=$work_orders_details->reason;?></strong></label>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="col">
                                    <label for="phone_number" class="text-dark"><b>{{__('work_order.forms.label.Select_response_action')}}</b></label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col mr-25">

                                </div>
                                <div class="col">



                                </div>
                            </div>
                            <!-------------------------NEw Design for Selection--------------->
                            <div class="form-row">
                                <div class="col-sm-8 mb-3">
                                <select class="form-control select2" name="bm_action" id="bm-radio-04">
                                    <option value='2'>{{__('work_order.forms.label.I_approve_the_work_order')}}</option>
                                    <option value='1'>{{__('work_order.forms.label.Respond_to_service_provider')}}</option>
                                </select>
                                </div>
                            </div>
                            <!-------------------------NEw Design for Selection--------------->
                            <div class="form-row ">
                            <div class="col-md-6">
                                    <div class="mr-25 rating display-none" >
                                    {{-- <div class="form-group">
                                        <div class="radio-theme-default custom-radio">
                                            <input class="radio" type="radio" name="bm_action" value='2' id="bm-radio-04" onchange="set_bm_action(2)" checked="">
                                            <label for="bm-radio-04">
                                                <span class="radio-text">{{__('work_order.forms.label.I_approve_the_work_order')}}</span>
                                            </label>
                                        </div>
                                    </div> --}}
                                        <div class="bm_rate_block">
                                        <div class="form-group">
                                            <label for="name1">{{__('work_order.forms.label.Rate_this_Job')}} <small class="required">*</small></label>

                                            <div class="atbd-rating-wrap d-flex align-items-center">
                                                <span class="rater"></span>
                                                <span class="rater-text">
                                                <span id="rate-count-bm" class="rate-count">5</span>{{__('work_order.list.Stars')}}</span>
                                            </div>
                                            <!--end rating section-->
                                        </div>


                                        <div class="form-group estimated_price rating display-none">
                                        <label for="name1">{{__('work_order.forms.label.Estimated_Job_Cost_Including_Spare_Part')}}</label>
                                        <input type="number" class="form-control" name="estimated_price" id="estimated_price_bm" value="" placeholder="2750 {{__('work_order.forms.place_holder.sar')}}">
                                    </div>
                                    </div>
                                    </div>

                                    <div class="reason reason_sp" >
                                    {{-- <div class="form-group">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" onchange="set_bm_action(1)" type="radio" name="bm_action" value='1'
                                            id="bm-radio-05" >
                                            <label for="bm-radio-05">
                                                <span class="radio-text">{{__('work_order.forms.label.Respond_to_service_provider')}}</span>
                                            </label>
                                        </div>
                                    </div> --}}
                                        <div class="form-group">
                                            <label for="phoneNumber">{{__('work_order.forms.label.Please_justify')}}</label><br>
                                        <textarea class="form-control" rows="8" columns="5" name="rejection_reason4" id="rejection_reason4"></textarea>
                                        </div>
                                    </div>
                            </div>
                        </div>
                            <!-- <div class="form-row bm_respond_sp">

                                    <div class="form-group estimated_price" style="display: none">
                                        <label for="name1">{{__('work_order.forms.label.Estimated_Job_Cost_Including_Spare_Part')}}</label>
                                        <input type="number" class="form-control" name="estimated_price" id="estimated_price_bm" value="" placeholder="2750 {{__('work_order.forms.place_holder.sar')}}">
                                    </div>
                            </div> -->


                            <input type="hidden" name="work_order_id" id="work_order_id" value="<?=$work_orders_details->work_order_id;?>">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve"> {{__('work_order.button.cancel')}} </button>
                                <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize" id="bm_action_on_rejects_wo_by_sp"> {{__('work_order.button.submit')}} </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
</div>

<div class="modal fade new-member" id="sp-approve-reopen" role="dialog" tabindex="-1"
            aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('workorder.workorders.list', Crypt::encryptString(1)) }}">{{__('work_order.button.work_order')}} </a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('support_tickets.table.Requests')}}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?> ({{__('work_order.button.Rejected_by_BM')}})</a>
                    </li>

                </ul>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span>
                </button>
            </div>
                <div class="new-member-modal">
                    <form action="javascript:void(0);" method="get" id="validateEditSupportTickets"
                        enctype="multipart/form-data">
                        <div class="col-md-12 mb-20 px-sm-40">
                            <br>
                            <div class="form-row mb-20">
                                <div class="col">
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.The_building_manager_has_re_opened_the_job')}}</label><br>
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.Justification')}}: <strong><?=$work_orders_details->reason;?></strong></label>
                                </div>
                            </div>
                            <!-- <div class="form-row mb-20">
                                <div class="col">

                                </div>
                            </div> -->

                            <div class="form-row mb-20">
                                <div class="col">
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.Select_response_action')}}</label>
                                </div>
                            </div>
                            <!-------------------------NEw Design for Selection--------------->
                            <div class="form-row">
                                <div class="col-sm-8 mb-3">
                                <select class="form-control select2" name="reopen_option" id="bm-ro">
                                    <option value='2'>{{__('work_order.forms.label.Send_back_to_worker')}}</option>
                                    <option value='1'>{{__('work_order.forms.label.Respond_to_building_manager')}}</option>
                                </select>
                                </div>
                            </div>
                            <!-------------------------NEw Design for Selection--------------->
                            <div class="form-row">
                                <div class="col-md-6" id="send_back">
                                    {{-- <div class="form-group mb-30">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" type="radio" name="reopen_option" value='2' id="bm-ro" checked="" >
                                            <label for="bm-ro">
                                                <span class="radio-text">{{__('work_order.forms.label.Send_back_to_worker')}}</span>
                                            </label>
                                        </div>
                                    </div> --}}
                                    <div>
                                        <div class="form-group">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label for="ticket_status" class="text-light fw-300 mb-0">{{__('work_order.forms.label.Assign_Worker')}}</label>
                                                <medium data-toggle="modal" data-target="#not-find" class="text-dark cursor-pointer">{{__('work_order.forms.label.couldnt_find_worker')}}</medium>
                                            </div>

                                            <div class="atbd-select">
                                                <select class="form-control select2" name="worker_id6" id="worker_id6" style= "height:auto;">
                                                    <option value="">{{__('work_order.forms.label.Choose_Worker')}}</option>
                                                    @if(!empty($workers))
                                                        @foreach($workers as $wr)
                                                            @if($wr['is_subcontractors_worker'])
                                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>>[SUB] <?=$wr['name'];?></option>
                                                            @else
                                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>><?=$wr['name'];?></option>
                                                            @endif
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name='assigned_to_user_type' id='assigned_to_user_type' value='<?=Auth::user()->user_type;?>'>
                                            <label class="form-check-label" for="assigned_to_user_type" >
                                                <span class="">{{__('work_order.forms.label.start_on_behalf_worker')}}</span>
                                            </label>
                                            </div>
                                        </div>
                                        <div class="form-group mt-10 reason">
                                            <label for="phoneNumber">{{__('work_order.forms.label.Write_a_reason')}}</label><br>
                                        <textarea class="form-control" rows="3" columns="5" name="reopen_reason1" id="reopen_reason1"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 reason display-none" id="respond_bm">
                                    {{-- <div class="form-group">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" type="radio" name="reopen_option" value='1'
                                            id="bm-ro02">
                                            <label for="bm-ro02">
                                                <span class="radio-text">{{__('work_order.forms.label.Respond_to_building_manager')}}</span>
                                            </label>
                                        </div>
                                    </div> --}}

                                    <div class="form-group mt-10">
                                        <label for="phoneNumber">{{__('work_order.forms.label.Write_a_reason')}}</label><br>
                                       <textarea class="form-control" rows="8" columns="5" name="rejection_reason2" id="rejection_reason2"></textarea>
                                    </div>

                                </div>
                            </div>
                            <input type="hidden" name="work_order_id" id="work_order_id" value="<?=$work_orders_details->work_order_id;?>">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve">{{__('work_order.button.cancel')}}  </button>
                                <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize" id="sp_reopen_job"> {{__('work_order.button.submit')}} </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


<div class="modal fade new-member" id="bm-approve-reopen" role="dialog" tabindex="-1"
            aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('workorder.workorders.list', Crypt::encryptString(1)) }}"> {{__('work_order.button.work_order')}} </a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('support_tickets.table.Requests')}}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?> ({{__('work_order.button.Rejected_by_BM')}})</a>
                    </li>

                </ul>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span>
                </button>
            </div>
                <div class="new-member-modal">
                    <form action="javascript:void(0);" method="get" id="validateEditSupportTickets"
                        enctype="multipart/form-data">
                        <div class="col-md-12 mb-20 px-sm-40">
                            <br>
                            <div class="form-row mb-20">
                                <div class="col">
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.The_service_provider_has_responded_to_your_work_rejection_with')}}:</label><br>
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.Justification')}}: <strong><?=$work_orders_details->reason;?></strong></label>
                                </div>
                            </div>
                            <!-- <div class="form-row mb-20">
                                <div class="col">
                                </div>
                            </div> -->
                            <div class="form-row mb-20">
                                <div class="col">
                                    <label for="phone_number" class="text-dark">{{__('work_order.forms.label.Select_response_action')}}</label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="col mr-25">
                                    <div class="form-group mb-30">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" type="radio" name="accept_reject_bm" value='2' id="bm-radio-02-approve" checked="">
                                            <label for="bm-radio-02-approve">
                                                <span class="radio-text">{{__('work_order.forms.label.I_approve_the_work_order')}}</span>
                                            </label>
                                        </div>
                                    </div>

                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" type="radio" name="accept_reject_bm" value='1' id="bm-radio-03-respond-sp" >
                                            <label for="bm-radio-03-respond-sp">
                                                <span class="radio-text">{{__('work_order.forms.label.Respond_to_service_provider')}}</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group mt-10 reason">
                                        <label for="phoneNumber">{{__('work_order.forms.label.Write_a_reason')}}</label><br>
                                        {{-- @flip1@ couse reason is going to null || 17-BM rejects SP reopening WO --}}
                                       <textarea class="form-control" rows="8" columns="5" name="rejection_reason3" id="rejection_reason3_bm"></textarea>
                                    </div>

                                </div>
                            </div>
                            <input type="hidden" name="work_order_id" id="work_order_id" value="<?=$work_orders_details->work_order_id;?>">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve"> {{__('work_order.button.cancel')}} </button>
                                <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize" id="bm_accept_reject_reopen"> {{__('work_order.button.submit')}} </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


<div class="modal fade new-member" id="sp-work-order" role="dialog" tabindex="-1"
            aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('workorder.workorders.list', Crypt::encryptString(1)) }}"> {{__('work_order.button.work_order')}} </a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('support_tickets.table.Requests')}}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?> ({{__('work_order.button.Received')}})</a>
                    </li>

                </ul>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span>
                </button>
            </div>
                <div class="new-member-modal modal-body px-20">
                    <form action="javascript:void(0);" method="get" id="validateEditSupportTickets"
                        enctype="multipart/form-data">
                        <div class="col-md-10 offset-md-1 mb-20">
                            <div class="form-row mb-20 mt-20">
                                <div class="col">
                                    <h6 for="phone_number" class="text-dark">{{__('work_order.forms.label.Select_response_action')}}</h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" type="radio" name="start_job" value='2' id="start_job1" checked="">
                                            <label for="start_job1">
                                                <span class="radio-text">{{__('work_order.forms.label.I_am_ready_to_start_the_job')}}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="radio-theme-default custom-radio ">
                                            <input class="radio" type="radio" name="start_job" value='1'
                                            id="start_job2">
                                            <label for="start_job2">
                                                <span class="radio-text">{{__('work_order.forms.label.I_have_an_issue_with_the_job')}}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="show-radio start_job1 active">
                                        <div class="form-group">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label for="ticket_status" class="text-light fw-300 mb-0">{{__('work_order.forms.label.Assign_Worker')}}</label>
                                                <medium data-toggle="modal" data-target="#not-find" class="text-dark cursor-pointer">{{__('work_order.forms.label.couldnt_find_worker')}}</medium>
                                            </div>
                                            <div class="atbd-select">
                                                <select class="form-control select2" name="worker_id" id="worker_id" style= "height:auto;">
                                                    <option value="">{{__('work_order.forms.label.Choose_Worker')}} </option>
                                                    @if(!empty($workers))
                                                        @foreach($workers as $wr)
                                                            @if($wr['is_subcontractors_worker'])
                                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>>[SUB] <?=$wr['name'];?></option>
                                                            @else
                                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>><?=$wr['name'];?></option>
                                                            @endif
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                        </div>
                                    
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name='assigned_to' id='assigned_to' value='<?=Auth::user()->user_type;?>'>
                                                <label class="form-check-label" for="assigned_to" >
                                                    <span class="text-light">{{__('work_order.forms.label.start_on_behalf_worker')}}</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="show-radio start_job2 display-none">

                                    <div class="form-group">
                                        <label for="ticket_status" class="text-light fw-300">{{__('work_order.forms.label.What_Kind_of_problem_do_you_have')}}<small class="required">*</small></label>
                                        <div class="atbd-select">
                                            <select class="form-control h-auto" name="reason" id="reason">
                                            @empty($workOrderItemRequestBySp->status)
                                                @if(\App\Services\AkauntingService::allow())
                                                <option value="{{__('work_order.forms.label.I_don_t_have_the_required_parts')}}">{{__('work_order.forms.label.I_don_t_have_the_required_parts')}}</option>
                                                @endif
                                            @endempty
                                                <option value="{{__('work_order.forms.label.No_Permission_to_enter_facility')}}">{{__('work_order.forms.label.No_Permission_to_enter_facility')}}</option>
                                                <option value="{{__('work_order.forms.label.Other_reason')}}">{{__('work_order.forms.label.Other_reason')}}</option>
                                            </select>
                                        </div>
                                    </div>

                                    @empty($workOrderItemRequestBySp->status)
                                    <div class="form-group" id="missing-item-selection-button-div">
                                        <label class="text-light text-dark fw-100">{{__('work_order.inventory.select_items')}} 
                                            @if(Auth::user()->user_type != 'sp_admin' && Auth::user()->user_type != 'supervisor')
                                                <small>({{__('work_order.forms.label.optional')}})</small>
                                            @endif
                                        </label> <span id="sp-missing-items-selected-count">0</span> {{__('work_order.forms.place_holder.selected')}}
                                        <a href="javascript:void(0);" class="rounded text-center w-100 py-3 d-block bg-primary text-white" data-target="#sp-request-spare-parts" data-toggle="modal">{{__('work_order.inventory.select')}}</a>
                                        <!-- <span class="d-flex p-3 justify-content-between align-items-center border-primary rounded">
                                            <label class="mb-0">21 Items Selected</label> <a href="javascript:void(0);"  data-target="#spare-parts" data-toggle="modal">View</a>
                                        </span> -->
                                    </div>
                                    @endempty

                                    <div class="form-group mt-10 other_reason">
                                        <label for="phoneNumber" class="text-light">{{__('work_order.forms.label.Write_a_reason')}}</label><br>
                                            <textarea class="form-control" rows="8" columns="5" name="reject_reason" id="reject_reason"></textarea>
                                    </div>

                                    <div class="form-group" id="sp-propose-new-date-div">
                                        <label for="ticket_status" class="text-light fw-300">{{__('work_order.bread_crumbs.Suggest_new_date')}}<small class="required">*</small>
                                        <i data-toggle="tooltip" class="fas fa-question-circle ml-1 cursor-pointer" onfocus="theFocus(this);" title="" data-html="true" data-original-title="<p>{{__('work_order.bread_crumbs.Suggest_new_date_tooltip')}}</p>  "></i>
                                        </label>
                                        <div class="atbd-date-picker">
                                            <div class="form-group mb-0 form-group-calender">
                                                <div class="position-relative" id="">
                                                    <input type="text" class="form-control proposed_new_date" id="datetimepicker1" placeholder="January 20, 2018" name="proposed_new_date" autocomplete="off">
                                                    <a><span data-feather="calendar"></span></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group" id="sp-assigning-worker-div">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                        <label for="ticket_status" class="text-light fw-300 mb-0">{{__('work_order.forms.label.Assign_Worker')}} <small class="text-light fw-300 mb-0">({{__('work_order.forms.label.optional') }})</small></label>
                                        <medium data-toggle="modal" data-target="#not-find" class="text-dark cursor-pointer">{{__('work_order.forms.label.couldnt_find_worker')}}</medium>
                                    </div>
                                        <div class="atbd-select">
                                            <select class="form-control h-auto" name="worker_id2" id="worker_id2">
                                                <option value="">{{__('work_order.forms.label.Choose_Worker')}}</option>
                                                @if(!empty($workers))
                                                    @foreach($workers as $wr)
                                                        @if($wr['is_subcontractors_worker'])
                                                            <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>>[SUB] <?=$wr['name'];?></option>
                                                        @else
                                                            <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>><?=$wr['name'];?></option>
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        
                            <input type="hidden" name="work_order_id" id="work_order_id" value="<?=$work_orders_details->work_order_id;?>">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve"> {{__('work_order.button.cancel')}} </button>
                                <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize update_button_wo" onclick="update_response_action()"> {{__('work_order.button.update')}} </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@can('SPAssignWorker', $work_orders_details)
<div class="modal fade" id="sp-work-order2" data-backdrop="static" role="dialog" tabindex="-1" aria-labelledby="staticBackdropLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('workorder.workorders.list', Crypt::encryptString(1)) }}">
                            {{__('work_order.button.work_order')}} </a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('support_tickets.table.Requests')}}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?>
                            ({{__('work_order.button.Received')}})</a>
                    </li>

                </ul>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span>
                </button>
            </div>
            <div class="modal-body px-20">
                <form action="javascript:void(0);" method="get"
                    enctype="multipart/form-data">
                    <div class="col-md-10 offset-md-1 mb-20">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="show-radio start_job1 active">
                                    <div class="form-group">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <label for="ticket_status"
                                                class="text-light fw-300 mb-0">{{__('work_order.forms.label.Assign_Worker')}}</label>
                                            <medium data-toggle="modal" data-target="#not-find"
                                                class="text-dark cursor-pointer">
                                                {{__('work_order.forms.label.couldnt_find_worker')}}</medium>
                                        </div>
                                        <div class="atbd-select">
                                            <select class="form-control select2" name="worker_id"
                                                style="height:auto;" required>
                                                <option value="">{{__('work_order.forms.label.Choose_Worker')}}
                                                </option>
                                                @if(!empty($workers))
                                                @foreach($workers as $wr)
                                                @if($wr['is_subcontractors_worker'])
                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->
                                                    worker_id)?"selected":""?>>[SUB]
                                                    <?=$wr['name'];?>
                                                </option>
                                                @else
                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->
                                                    worker_id)?"selected":""?>>
                                                    <?=$wr['name'];?>
                                                </option>
                                                @endif
                                                @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="button-group d-flex justify-content-end pt-25">
                            <button type="button" data-dismiss="modal"
                                class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve">
                                {{__('work_order.button.cancel')}} </button>
                            <button type="submit"
                                class="btn btn-primary btn-default btn-squared text-capitalize update_button_wo"
                                > {{__('work_order.button.update')}} </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endcan
</div>


<!-- Modal Coudn't find a permanent worker? -->
<div class="modal fade new-member not-find-popup" id="not-find" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content radius-xl">
      <div class="modal-header">
        <h6>{{__('work_order.forms.label.couldnt_find_worker')}}</h6>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

        <div class="col-md-12 mx-auto">
        <h6 class="mb-3 text-dark">{{__('work_order.forms.label.make_sure')}}</h6>

        <ul class="dotted-list pl-3 list-disc">
        @php 
         $worker_assigned_contract = __('work_order.forms.label.worker_assigned_contract');
         $worker_assigned_property = __('work_order.forms.label.worker_assigned_property');
         $worker_assigned_asset_category = __('work_order.forms.label.worker_assigned_asset_category');
        @endphp
            <li class="mb-2"><?= html_entity_decode($worker_assigned_contract);?></li>
            <li class="mb-2"><?= html_entity_decode($worker_assigned_property);?></li>
            <li class="mb-2"><?= html_entity_decode($worker_assigned_asset_category);?></li>
        </ul>
    </div>

        <!-- <div class="d-flex justify-content-end">
            <button type="button" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light" data-dismiss="modal">Close</button>
        </div> -->
      </div>
    </div>
  </div>
</div>

<!-- Modal for approve Job -->
<div class="modal-basic modal fade show" id="modal-approve-date" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content modal-bg-white ">
            <div class="modal-header">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-title-wraps">
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{ route('workorder.workorders.list', Crypt::encryptString(1)) }}"> {{__('work_order.bread_crumbs.work_order_list')}}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{__('support_tickets.table.Requests')}}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <span>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?> ({{__('work_order.button.Has_issue')}})</span>
                                </li>
                            </ul>

                        </div>
                    </div>
                </div>

                <h6 class="modal-title"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span></button>
            </div>
            <div class="modal-body">
                <div class="col-md-8 offset-md-2">
                    <form action="javascript:void(0);">
                        <div class="form-group">
                            <label for="name1">{{__('work_order.forms.label.Work_Order_Number')}}<small class="required">*</small></label>
                            <input type="text" readonly class="form-control" id="" value="<?=$work_orders_details->work_order_id;?>" name="work_order_id">
                        </div>

                        <div class="form-group">
                            <label for="phoneNumber">{{__('work_order.forms.label.Has_the_following_issue')}}<small class="required">*</small></label>
                            @if($work_orders_details->reason == "Other Reason" || $work_orders_details->reason == "سبب آخر")
                            <textarea class="form-control" rows="8" columns="5" readonly style='resize: none;'>{{$work_orders_details->reject_reason}}</textarea>                    
                            @else
                            <input type="text" readonly class="form-control" id="" value="<?=$work_orders_details->reason;?>">
                            @endif
                        </div>

                        <div class="form-group">
                            <label for="phoneNumber">{{__('work_order.forms.label.For_the_Maintanance_supervisor_would_suggest_new_target_date')}}<small class="required">*</small></label>
                            <?php if(Session::get('locale') != 'en') {?>
                                <input type="text" name="proposed_date_sp" readonly class="form-control direction-ltr text-left" id="" value="<?=date('d-M-Y h:i A', strtotime($work_orders_details->proposed_new_date));?>">
                            <?php } else {?>
                                <input type="text" name="proposed_date_sp" readonly class="form-control" id="" value="<?=date('d-M-Y h:i A', strtotime($work_orders_details->proposed_new_date));?>">
                            <?php }?>
                            
                        </div>

                        <div class="form-group mt-10">
                            <label for="phoneNumber">{{__('work_order.forms.label.Do_you_approve_this_job')}}<small class="required">*</small></label><br>
                            <div class="radio-horizontal-list d-flex">
                                <div class="radio-theme-default custom-radio ">
                                    <input class="radio" type="radio" name="approve_proposed_date" value="2" id="approve_proposed_date1" checked="">
                                    <label for="approve_proposed_date1">
                                        <span class="radio-text">{{__('work_order.forms.label.Approve')}}</span>
                                    </label>
                                </div>
                                <div class="radio-theme-default custom-radio ">
                                    <input class="radio" type="radio" name="approve_proposed_date" value="1" id="approve_proposed_date2">
                                    <label for="approve_proposed_date2">
                                        <span class="radio-text">{{__('work_order.forms.label.Reject')}}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group suggested_date display-none">
                            <label for="ticket_status" class="fw-300">{{__('work_order.bread_crumbs.Suggest_new_date')}} <small class="required">*</small>
                            <i data-toggle="tooltip" class="fas fa-question-circle ml-1 cursor-pointer" onfocus="theFocus(this);" title="" data-html="true" data-original-title="<p>{{__('work_order.bread_crumbs.Suggest_new_date_tooltip')}}</p>  "></i>
                            </label>
                            <div class="atbd-date-picker">
                                <div class="form-group mb-0 form-group-calender">
                                    <div class="position-relative">
                                        <input type="text" class="form-control new_suggested_date" id="datepicker15" placeholder="03-09-2018 04:10 AM"  onchange="$('.approve_reject_sp_date').prop('disabled', false)" >
                                        <a href="#"><span data-feather="calendar"></span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-10 reason display-none">
                            <label for="phoneNumber">{{__('work_order.forms.label.Write_a_reason')}}<small class="required">*</small></label><br>
                           <textarea class="form-control" rows="8" columns="5" name="reject_reason" id="reject_reason_approve_reject_sp" onkeyup="$('.approve_reject_sp_date').prop('disabled', false)" onchange="$('.approve_reject_sp_date').prop('disabled', false)"></textarea>

                        </div>
                        @if(!empty(Auth::user()->user_type == 'sp_admin'))
                        <div class="form-group worker display-none">
                            <label for="ticket_status" class="text-light fw-300">{{__('work_order.forms.label.Assign_Worker')}}</label>
                            <div class="atbd-select">
                                <select class="form-control h-auto" name="worker_id3" id="worker_id3">
                                    <option value="">{{__('work_order.forms.label.Choose_Worker')}}</option>
                                    @if(!empty($workers))
                                        @foreach($workers as $wr)
                                            @if($wr['is_subcontractors_worker'])
                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>>[SUB] <?=$wr['name'];?></option>
                                            @else
                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>><?=$wr['name'];?></option>
                                            @endif
                                        @endforeach
                                    @endif
                               1 </select>
                            </div>
                        </div>
                        @endif

                        <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md">{{__('work_order.button.cancel')}}</button>
                            <!-- <button class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">Save</button> -->
                            <button href="javascript:void(0)" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 approve_reject_sp_date" onclick="approve_reject_sp_date()">{{__('work_order.button.save')}}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@include('applications.admin/workorder/export-modals')

<div class="modal-basic modal fade show" id="sp-approve-job" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-scrollable" role="document">
        <div class="modal-content modal-bg-white ">
            <div class="modal-header">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-title-wraps">
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="#">{{__('work_order.bread_crumbs.work_order_list')}} </a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a href="#">{{__('support_tickets.table.Requests')}}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <span>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?> ({{__('work_order.button.Submitted_for_review')}})</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <h6 class="modal-title"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span></button>
            </div>
                    <form>
            <div class="modal-body">
                <div class="">
                        <div class="form-group">
                            <label for="name1">{{__('work_order.forms.label.asset_tag')}}</label>
                            <!-- <input type="text" readonly class="form-control" id="" value="<?=$work_orders_details->asset_tag?$work_orders_details->asset_tag:__('work_order.forms.label.Asset_is_not_registered_in_the_system');?>"> -->
                            <span class="form-control lh-32" readonly><?=$work_orders_details->asset_tag?$work_orders_details->asset_tag:__('work_order.forms.label.Asset_is_not_registered_in_the_system');?></span>
                        </div>

                        <div class="form-group">
                            <label for="exampleFormControlSelect2">{{__('work_order.forms.label.priority')}}</label><br>
                            <div class="userDatatable-content d-inline-block">
                                <span class="bg-opacity-d-blue  color-d-blue p-3 rounded-pill userDatatable-content-status active"><?=ucfirst($work_orders_details->priority_level);?></span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="phoneNumber">{{__('work_order.forms.label.target_date')}}</label>
                            <!-- <input type="text" readonly class="form-control" id="" value="<?=date('d/m/Y h:i A', strtotime($work_orders_details->target_date));?>"> -->
                            <span class="form-control lh-32" readonly><?=date('d/m/Y h:i A', strtotime($work_orders_details->target_date));?></span>
                        </div>
                        <div class="form-group">
                            <label for="phoneNumber">{{__('work_order.forms.label.description')}}</label>
                            <!-- <input type="text" readonly class="form-control" id="" value="<?=$work_orders_details->description;?>"> -->
                            <span class="form-control lh-32 h-auto d-block" readonly><?=$work_orders_details->description;?></span>
                        </div>
                        <div class="form-group">
                            <label for="phoneNumber">{{__('work_order.forms.label.pass_fail')}}</label><br>
                            <div class="userDatatable-content d-inline-block">
                            <?php
                            //echo $work_orders_details->pass_fail;
                            if($work_orders_details->pass_fail == 'Pass' || $work_orders_details->pass_fail == 'pass')
                            {
                                ?>
                                <span class="bg-opacity-success  color-success rounded-pill userDatatable-content-status active">
                                {{__('work_order.forms.label.pass')}}
                                </span>
                                <?php
                            }
                            else
                            {
                                ?>
                                <span class="bg-opacity-danger  color-danger rounded-pill userDatatable-content-status active">
                                {{__('work_order.forms.label.fail')}}
                                </span>
                                <?php
                            }
                            ?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="phoneNumber">{{__('work_order.forms.label.Actual_time_spent_on_job')}}</label><br>
                            <div class="userDatatable-content d-inline-block">
                            <span class="bg-opacity-success  color-success rounded-pill userDatatable-content-status active">
                                <?php
                                $job_started_at = $work_orders_details->job_started_at;
                                $job_submitted_at = $work_orders_details->job_submitted_at;
                                if(trim($work_orders_details->time_spent_by_worker) != "" && $job_submitted_at != "")
                                {
                                    // Step 1: Split the input (hours:minutes:seconds)
                                    list($hours, $minutes, $seconds) = explode(':', $work_orders_details->time_spent_by_worker);

                                    // Convert hours, minutes, and seconds to total seconds
                                    $totalSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;

                                    // Step 2: Convert total seconds to days, hours, minutes, and seconds
                                    $days = floor($totalSeconds / 86400); // 86400 seconds in a day
                                    $seconds = $totalSeconds % 86400;

                                    $hours = floor($seconds / 3600); // 3600 seconds in an hour
                                    $seconds %= 3600;

                                    $minutes = floor($seconds / 60); // 60 seconds in a minute
                                    $seconds %= 60;

                                }
                                else
                                {
                                    if(trim($work_orders_details->pause_time_spent_minutes) != "" && $work_orders_details->pause_time_spent_minutes > 0)
                                    {

                                        $new_datetime = strtotime("-{$work_orders_details->pause_time_spent_minutes} minutes", strtotime($job_submitted_at));
                                        $job_submitted_at = date('Y-m-d H:i:s', $new_datetime);
                                    }
                                    else
                                    {
                                        $job_submitted_at = $work_orders_details->job_submitted_at;
                                    }
                                    $datetime1 = strtotime($job_started_at);
                                    $datetime2 = strtotime($job_submitted_at);
                                    $interval  = abs($datetime2 - $datetime1);  
                                    /*** get the days ***/
                                    $days = intval(intval($interval) / (3600*24));
                                    /*** get the hours ***/
                                    $hours = (intval($interval) / 3600) % 24;
                                    /*** get the minutes ***/
                                    $minutes = (intval($interval) / 60) % 60;
                                    /*** get the seconds ***/
                                    $seconds = intval($interval) % 60;
                                }
                                                                  
                                  $ret = "";
                                  
                                  if($days> 0)
                                  {
                                      $ret .= "$days ". __("configration_assets.comminucation_table.Days").' ';
                                  }
                  
                                  
                                  if($hours > 0)
                                  {
                                      $ret .= "$hours ". __("configration_assets.comminucation_table.Hours").' ';
                                  }
                  
                                  
                                  if($minutes > 0)
                                  {
                                      $ret .= "$minutes ". __("configration_assets.comminucation_table.Minutes").' ';
                                  }
                  
                
                                  if ($seconds > 0) {
                                      $ret .= "$seconds ".__("configration_assets.comminucation_table.Seconds");
                                  }
                                  if($ret == "")
                                  {
                                      $ret = __("work_order.bread_crumbs.automatically_assigned");
                                  }                
                                  echo $ret;
                                  ?> </span>
                                  {{-- @flip1@ add span for show difference between start date and submited date
                                  <span class="userDatatable-content-status">
                                    @php $days = Helper::timeFormate($work_orders_details->job_started_at, $work_orders_details->job_submitted_at);@endphp
                                    {{$days->d}} {{__('configration_assets.comminucation_table.Days')}} 
                                    {{$days->h}} {{__('configration_assets.comminucation_table.Hours')}} 
                                    {{$days->i}} {{__('configration_assets.comminucation_table.Minutes')}} 
                                    {{$days->s}} {{__('configration_assets.comminucation_table.Seconds')}} 
                                  </span> --}}
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="phoneNumber">{{__('work_order.forms.label.Attachments')}}</label><br>
                            <div class="item-inner">
                                <div class="item-content">
                                    <label for="" class="w-100">
                                        <div class="row">
                                        <?php
                                        if(!empty($images))
                                        {
                                            foreach($images as $key => $row)
                                            {
                                                if(trim(array_reverse(explode('.',url('storage/'.$row)))[0]) != 'pdf')
                                                {
                                                    ?>
                                                    <div class="col-sm-4 col-6 mb-4">
                                                    <div class="d-flex align-items-center h-100 border p-2">
                                                        <div class="view-img">
                                                        @php
                                                            $actionImage = ImagesUploadHelper::displayImage($row, 'actions');
                                                        @endphp
                                                        <img onclick="chatImageClick(this)" src="{{$actionImage}}" alt="" class="uploaded-image" width="100%">
                                                        </div>
                                                    </div>
                                                    </div>
                                                    <?php
                                                }
                                                else
                                                {
                                                    ?>
                                                    <div class="col-sm-4 col-6 mb-4">
                                                        <div class="align-items-center d-flex justify-content-center h-100 border p-2">
                                                            <a target="_blank" href="{{url('storage/'.$row)}}"><img src="{{ asset('img/pdf.png')}}" class="d-block mx-auto"></a>
                                                        </div>
                                                    </div>
                                                    <?php
                                                }
                                            }
                                        }
                                        else
                                        {
                                            ?>
                                            <span class="userDatatable-content-status p-3">
                                               {{__('work_order.forms.label.No_Attachments_uploaded')}}
                                            </span>
                                            <?php
                                        }
                                        ?>

                                        <!-- <img src="{{asset('img/author-more.png')}}"  alt="" class="uploaded-image" width="150"> -->
                                    </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                        @if (!empty($worker_comment))
                        <div class="form-group">
                            <label for="phoneNumber">{{__('work_order.bread_crumbs.Comment')}}</label><br>
                            <div class="item-inner">
                                <div class="item-content">
                                    <strong>
                                    <?php
                                    if(!empty($worker_comment))
                                    {
                                        echo $worker_comment;
                                    }
                                    ?>
                                    </strong>
                                </div>
                            </div>
                        </div>
                        @endif
                        @if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee'))
                        <div class="form-group mt-10">
                            <div class="row">
                                <div class="col-md-6">
                                <label for="phoneNumber">{{__('work_order.forms.label.Cost')}}</label><br>
                                <input readonly type="text" name="cost" id="cost_readonly" class="form-control" value="<?=$work_orders_details->cost;?>">
                                </div>
                                <div class="col-md-6">
                                <label for="phoneNumber">{{__('work_order.forms.label.Receipt')}}</label><br>
                                <!-- New Design for the Receipt view-->
                                @if($work_orders_details->upload_receipt!='')
                                    @if(trim(array_reverse(explode('.',url('uploads/workorder/'.$work_orders_details->upload_receipt)))[0]) != 'pdf')
                                        <div class="view-img receipt-img">
                                            <!-- <img src="{{url('uploads/workorder/'.$work_orders_details->upload_receipt)}}" class="ap-img__main rounded-squere wo-img"  onclick="chatImageClick(this)"> -->
                                            <img src="{{ImagesUploadHelper::displayImage($work_orders_details->upload_receipt, 'uploads/workorder')}}" class="ap-img__main rounded-squere wo-img"  onclick="chatImageClick(this)">
                                        </div>
                                    @else
                                    <div class="">
                                        <a target="_blank" href="{{url('uploads/workorder/'.$work_orders_details->upload_receipt)}}" class="d-inline-block border p-2"><img src="{{ asset('img/pdf.png')}}" class="d-block mx-auto"></a>
                                    </div>
                                    @endif
                                @else
                                    {{__('work_order.forms.label.no_receipt')}}
                                @endif                                
                                </div>
                        </div>
                        </div>
                        @endif

                        @if($work_orders_details->is_collaborative == 1)
                        <!--============== Workers names and Record time list ==========-->
                        <div class="form-group mt-10">
                            <div class="row">
                                <div class="col-7">
                                    <label for="">{{__('work_order.forms.label.worker_name')}}</label>
                                </div>
                                <div class="col-5">
                                    <label for="">{{__('work_order.forms.label.record_time')}}</label>
                                </div>
                            </div>
                            <div class="p-3 border rounded bg-grey text-dark">
                                <div class="row">
                                    @if($data['workersDetails'])
                                        @foreach($data['workersDetails'] as $worker)
                                            <div class="col-7">
                                                <label for="">{{ $worker['worker_name'] }}</label>
                                            </div>
                                            <div class="col-5">
                                                <label for="">
                                                    @if($worker['end_time'])
                                                        {{ \Carbon\Carbon::parse($worker['start_time'])->diffInHours($worker['end_time']) }} Hours
                                                    @else
                                                        -
                                                    @endif
                                                </label>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        </div>
                        <!--==============End Workers names and Record time list ==========-->
                        @endif

                        <div class="form-group mt-10">
                            <label for="phoneNumber">{{__('work_order.forms.label.Do_you_approve_this_job')}}</label><br>
                            <div class="radio-horizontal-list d-flex">
                                <div class="radio-theme-default custom-radio ">
                                    <input class="radio" type="radio" name="approve_job" onclick="$('.building_manager_feedback').show(); $('#worker_id5').prop('required', false)" value="2" id="approve_job1" checked="">
                                    <label for="approve_job1">
                                        <span class="radio-text">{{__('work_order.forms.label.Approve')}}</span>
                                    </label>
                                </div>
                                <div class="radio-theme-default custom-radio ">
                                    <input class="radio" type="radio" name="approve_job" onclick="$('.building_manager_feedback').hide(); $('#worker_id5').prop('required', true);" value="1" id="approve_job2">
                                    <label for="approve_job2">
                                        <span class="radio-text">{{__('work_order.forms.label.Reject')}}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        @if(!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor'))
                        <div class="form-group mt-10" id="cost_receipt">
                            <br>
                            <div class="radio-horizontal-list">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label for="name1">{{__('work_order.forms.label.Cost')}} <small>({{__('work_order.forms.label.optional')}})</small></label>
                                            <input type="text" name="cost_main" id="cost_main" class="form-control" placeholder="<?=$work_orders_details->cost;?>">
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label for="name1">{{__('work_order.forms.label.Upload_Receipt')}}</label>
                                            <div class="atbd-upload">
                                                <div class="atbd-upload__button">
                                                    <label class="btn btn-lg btn-outline-lighten btn-upload w-100 mb-0" for="upload_receipt"><span data-feather="upload"></span> {{__('work_order.forms.label.Upload_Receipt')}}</label>
                                                    <br />
                                                    <input type="file" name="upload_receipt" id="upload_receipt"  class="upload-one" value="">
                                                </div>
                                            </div>
                                            <!-- if the file uploaded is an image-->
                                            <div class="dynamic_switch">
                                                
                                            </div>
                                            
                                        <!-- End if the file uploaded is a document-->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @else
                        <input type="hidden" name="cost" id="cost_hidden" value="<?=$work_orders_details->cost;?>">
                        @endif
                        <input type="hidden" id="upload_receipt_action" value="{{route('workorder.workorder_edit_image')}}">

                        @if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee'))
                        <div class="row">
                            <div class="col-md-6">
                        <div class="form-group rating display-none">
                            <label for="name1">{{__('work_order.forms.label.Rate_this_Job')}} <small>{{__('work_order.forms.label.rating_info_text')}}</small> 
                                <!-- <small class="required">*</small> -->
                            </label>
                            <!--rating section-->
                            <div class="atbd-rating-wrap d-flex align-items-center">
                                <span class="rater"></span>
                                <span class="rater-text">
                                <span class="rate-count">5</span>{{__('work_order.list.Stars')}}</span>
                            </div>                            
                            <!--end rating section-->
                        </div>
                        </div>
                        @if($work_orders_details->job_completed_by != 'SP')
                        <!--KPI 25 - New opportunity in Evaluation-->
                        <div class="col-md-6">
                            <div class="form-group rating">
                                <label for="name1">{{__('work_order.forms.label.uniform_specified_by_the_authority')}} 
                                </label>
                                <div class="atbd-rating-wrap d-flex align-items-center">
                                    <span class="d-flex align-items-center rater2"></span>
                                    <span class="rater-text">
                                    <span class="rate-count2">5</span> {{__('work_order.list.Stars')}}</span>
                                </div>                            
                                <!--end rating section-->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group rating">
                                <label for="name1">{{__('work_order.forms.label.extent_of_cleanliness')}}
                                </label>
                                <div class="atbd-rating-wrap d-flex align-items-center">
                                    <span class="d-flex align-items-center rater3"></span>
                                    <span class="rater-text">
                                    <span class="rate-count3">5</span> {{__('work_order.list.Stars')}}</span>
                                </div>                            
                                <!--end rating section-->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group rating">
                                <label for="name1">{{__('work_order.forms.label.safety_procedure')}}
                                </label>
                                <div class="atbd-rating-wrap d-flex align-items-center">
                                    <span class="d-flex align-items-center rater4"></span>
                                    <span class="rater-text">
                                    <span class="rate-count4">5</span> {{__('work_order.list.Stars')}}</span>
                                </div>                            
                                <!--end rating section-->
                            </div>
                        </div>
                        @endif
                    </div>
                        <!--End KPI 25 - New opportunity in Evaluation-->
                        <div class="form-group estimated_price display-none">
                            <label for="name1">{{__('work_order.forms.label.Estimated_Job_Cost')}} <small>{{__('work_order.forms.label.Including_Spare_Part')}}</small></label>
                            <input type="number" class="form-control" name="estimated_price" id="estimated_price" value="" placeholder="2750 {{__('work_order.forms.place_holder.sar')}}">
                        </div>
                        <div class="form-group mt-10 rejection_reason5 display-none">
                            <label for="phoneNumber">{{__('work_order.forms.label.Write_a_reason_for_rejection')}} <small class="required">*</small></label><br>
                           <textarea class="form-control" rows="8" columns="5" name="rejection_reason" id="rejection_reason"></textarea>
                           <div id ="reject_reason_error" class="error text-danger"></div>

                        </div>
                        @endif
                        @if(!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor'))
                        <div class="form-group mt-10 rejection_reason5 display-none">
                            <label for="phoneNumber">{{__('work_order.forms.label.Write_a_reason_for_rejection')}} <small class="required">*</small></label><br>
                           <textarea class="form-control" rows="8" columns="5" name="rejection_reason" id="rejection_reason"></textarea>
                        </div>
                        <div class="form-group worker_id5 display-none">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                            <label for="ticket_status" class="text-light fw-300 mb-0">{{__('work_order.forms.label.Assign_Worker')}} <small class="required">*</small></label>
                            <medium data-toggle="modal" data-target="#not-find" class="text-dark cursor-pointer">{{__('work_order.forms.label.couldnt_find_worker')}}</medium>
                        </div>

                            <div class="atbd-select">
                                <select class="form-control h-auto" name="worker_id5" id="worker_id5">
                                    <option value="">{{__('work_order.forms.label.Choose_Worker')}}</option>
                                    @if(!empty($workers))
                                        @foreach($workers as $wr)
                                            @if($wr['is_subcontractors_worker'])
                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>>[SUB] <?=$wr['name'];?></option>
                                            @else
                                                <option value="<?=$wr['id'];?>" <?=($wr['id']==$work_orders_details->worker_id)?"selected":""?>><?=$wr['name'];?></option>
                                            @endif
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                        </div>
                        @endif
                        <!--building manager can write feedback-->
                        @if(!empty(Auth::user()->user_type == 'building_manager') || !empty(Auth::user()->user_type == 'building_manager_employee'))
                        <div class="form-group mt-10 building_manager_feedback">
                            <label for="building_manager_feedback">{{__('work_order.forms.label.comment')}} <small>({{__('work_order.forms.label.optional')}})</small></label><br>
                           <textarea class="form-control" rows="4" columns="4" name="building_manager_feedback" id="building_manager_feedback" maxlength="200" onkeypress="if (this.value.length >= 200) { alert('Only 200 Words Allowed');  $this.val($this.val().substring(0, 200)); }"></textarea>
                           <small id="building_manager_feedbackHelp" class="form-text text-muted">{{__('work_order.forms.label.write_feedback_about_workorder')}}</small>
                        </div>

                        @endif
                        <!--end building manager can write feedback-->
                        <input type="hidden" name="old_worker_id" id="old_worker_id" value="<?=$work_orders_details->worker_id;?>">
                </div>
            </div>
            <div class="modal-footer">
                        <div class="d-flex justify-content-md-end justify-content-center button-group d-flex justify-content-end">
                            <button type="button" data-dismiss="modal" class="btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md">{{__('work_order.button.back')}}</button>
                            <!-- <button class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">Save</button> -->
                            <button type="submit" id="approve_job_by_sp" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.button.submit')}}</button>
                        </div>
            </div>
                    </form>
        </div>
    </div>
</div>


<div class="e-info-modal modal fade" id="level-modal" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog modal-sm e-info-dialog modal-dialog-centered" id="c-event" role="document">
         <div class="modal-content">
            <div class="modal-header e-info-header bg-primary">
                <h6 class="modal-title e-info-title"><span>{{__('work_order.list.priority')}}: </span>
                    <div class="d-inline-block">
                        <span class=""><?=$work_orders_details->priority_level;?></span>
                    </div>
                </h6>
                <div class="e-info-action">
                    <button type="button" class="btn-icon btn-close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span></button>
                </div>
            </div>
            <div class="modal-body">
               <ul class="e-info-list">
                  <li>
                     <span data-feather="package"></span>
                     <span class="list-line">
                     <span class="list-label"><strong>{{__('work_order.forms.label.asset_category')}}:</strong></span>
                     <span class="list-meta e-info-deadline"><?=$work_orders_details->asset_category;   if($work_orders_details->asset_cat_deleted_at != ''){ echo __('general_sentence.modal.deleted');}  ?></span>
                     </span>
                  </li>                  
                  <li>
                    <span data-feather="clock"></span>
                    <span class="list-line">
                    <span class="list-label "><strong> {{__('work_order.forms.label.Response_time')}}:</strong></span>
                    <span class="list-meta e-info-workordertype"><div id=""></div>  <?=$work_orders_details->sla_response_time;?> (<?=$work_orders_details->response_time_priority_level;?>)</span>
                    </span>
                 </li>
                 <li>
                     <span data-feather="clock"></span>
                     <span class="list-line">
                     <span class="list-label"><strong>{{__('work_order.forms.label.Execution_time')}}:</strong></span>
                     <span class="list-meta"><?=$work_orders_details->sla_excecution_time;?> (<?=$work_orders_details->priority_level;?>)</span>
                     </span>
                  </li>
                  <li>
                     <span data-feather="clock"></span>
                     <span class="list-line">
                     <span class="list-label"><strong>{{__('work_order.forms.label.Received_at')}}:</strong></span>
                     <span class="list-meta"><?=$work_orders_details->created_at2;?> </span>
                     </span>
                  </li>
                  <li>
                     <span data-feather="clock"></span>
                     <span class="list-line">
                     <span class="list-label"><strong> {{__('work_order.forms.label.Actual_Response_time')}} :</strong></span>
                     <span class="list-meta"><?=$work_orders_details->actual_response_time;?> </span>
                     </span>
                  </li>
                  <li>
                     <span data-feather="clock"></span>
                     <span class="list-line">
                     <span class="list-label"><strong> {{__('work_order.forms.label.Actual_Execution_time_popup')}} :</strong></span>
                     <span class="list-meta"><?=$work_orders_details->actual_execution_time;?> </span>
                     </span>
                  </li>
                  <li>
                     <span data-feather="clock"></span>
                     <span class="list-line">
                     <span class="list-label"><strong> {{__('work_order.forms.label.Actual_Execution_time')}}:</strong></span>
                     <span class="list-meta"><?=$work_orders_details->job_evaluation_time;?> </span>
                     </span>
                  </li>

                  <!-- <li>
                     <span data-feather="shield"></span>
                     <span class="list-line">
                     <span class="list-label"><strong>Priority :</strong></span>
                     <span class="list-meta">
                        <div class="userDatatable-content d-inline-block">
                            <span class="bg-opacity-danger  color-danger rounded-pill userDatatable-content-status active e-info-priority">Medium</span>
                        </div>
                     </span>
                     </span>
                  </li> -->
               </ul>
            </div>
         </div>
      </div>
   </div>


    <div class="modal fade" id="assign-sp" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel">{{__('work_order.forms.label.Assign_Maintenance_Supervisor')}}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            </div>
            {{ Form::open(['route' => 'workorder.assign_supervisor', 'method'=>'POST', 'id'=>'supervisor_id_form']) }}
            <div class="modal-body">
                <div class="properties-list">
                    <select class="form-control select2" name="supervisor_id" id="supervisor_id" required>
                        <option value="" disabled selected>{{ __('user_management_module.user_forms.label.choose_supervisor') }}</option>
                        @if(!empty($data['all_supervisors']) && count($data['all_supervisors']) > 0)
                            @foreach($data['all_supervisors'] as $supervisors)
                            <option value="{{ $supervisors->id }}" {{ ( $supervisors->id == $work_orders_details->supervisor_id && $work_orders_details->assigned_to == 'supervisor') ? 'selected' : '' }}>{{ $supervisors->name }}</option>
                            @endforeach
                        @endif
                    </select>
                    <input type="hidden" name="work_order_id" id="work_order_id" value="{{$work_orders_details->id}}" />
                    <input type="hidden" name="building_ids" id="building_ids" value="{{$work_orders_details->building_id}}" />
                    <span id="supervisor_id_error"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn border" data-dismiss="modal">{{__('data_contract.table.close')}}</button>
                <button type="submit" class="btn btn-primary">{{__('work_order.forms.label.Update')}}</button>
            </div>
            {{ Form::close() }}
        </div>
        </div>
    </div>
<div class="image-zoom align-items-center justify-content-center">
    <div class="close-img"> <span data-feather="x" class=""></span></div>
    <div class="d-flex image-zoom-btn justify-content-center rounded-pill">
        <span class="bg-white border-right  download-image" title="Download"><span data-feather="download"></span></span>
        <span class="bg-white rotate-img" title="Rotate"><span data-feather="rotate-cw" ></span></span>
    </div>
</div>


<div class="modal fade not-find-popup" id="add-workers" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">
                    {{__('work_order.forms.label.assign_multiple_workers')}}
                    <span class="d-inline-block fs-14 text-primary">
                        (<span id="selectedWorkerCount">0</span> {{__('work_order.forms.label.selected_workers')}})
                    </span>
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="input-group search-group properties-search border-bottom">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="basic-addon1"><i class="fa fa-search" aria-hidden="true"></i></span>
                </div>
                <input type="text" class="form-control rounded search_building" placeholder="{{__('work_order.forms.label.search_for_the_workers')}}..." aria-label="Username" aria-describedby="basic-addon1" />
            </div>
            <div class="modal-body pt-0">
                <div class="properties-list mt-2">
                    <!--========New Design for Multiple Properties=====-->
                    <div class="radio-horizontal-list d-sm-flex align-items-center">
                        <div class="d-sm-flex align-items-center border p-1 rounded tabs-glide">
                            <div class="radio-theme-default custom-radio flex-fill">
                                <input class="radio" type="radio" name="show_workers_filter" checked="" value="show_all" id="active_t1" />
                                <label for="active_t1">
                                    <span class="radio-text">{{__('work_order.forms.label.all_workers')}}</span>
                                </label>
                            </div>
                            <div class="radio-theme-default custom-radio flex-fill">
                                <input class="radio" type="radio" name="show_workers_filter" value="selected_only" id="active_t2" />
                                <label for="active_t2">
                                    <span class="radio-text">{{__('work_order.forms.label.selected_workers')}}</span>
                                </label>
                            </div>
                        </div>
                        <div class="radio-theme-default custom-radio d-flex justify-content-end flex-fill mr-0">
                            <button class="btn btn-outline-danger clear_selection"><i class="las la-times"></i> {{__('user_management_module.common.clear_selection')}}</button>
                        </div>
                    </div>

                    <div class="border rounded business_list">
                        <div class="border-bottom p-3">
                            <input type="checkbox" name="check_all_property" id="check_all_property" class="mr-2" />
                            <span>{{__('user_management_module.common.select_all')}}</span>
                        </div>
                        <ul class="site-scrollbar-one d-block drop-check-multy border-0">
                            @if(!empty($workers))
                                @foreach($workers as $worker)
                                    <li class="border-top mb-0 mt-0 d-flex align-items-center worker-list-item">
                                        <input type="checkbox" name="multi_worker" id="{{ $worker['id'] }}" class="mr-2 worker_checkbox" value="{{ $worker['id'] }}" data-fullname="{{ $worker['name'] }}" />
                                        <span class="text-light">{{ $worker['is_subcontractors_worker'] ? '[SUB] ' : '' }} {{ $worker['name'] }}</span>
                                    </li>
                                @endforeach
                            @endif
                        </ul>
                    </div>

            </div>
            <div class="modal-footer d-flex justify-content-end align-items-center">
                <button type="button" class="btn border" data-dismiss="modal">{{__('work_order.button.close')}}</button>
                <button type="button" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn_save_building">{{__('work_order.button.save')}}</button>
            </div>
        </div>
    </div>
<!--===============End Checklist Popup=================-->
</div>
</div>



<!---------------------------------Checklist Popup----------------------------------->
@if(isset($data['checklists']))
<div class="modal fade" id="checklist-yes" tabindex="-1" role="dialog" aria-labelledby="checklistModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{__('configration_checklist.checklist_table.checklist_details')}}: <a class="text-primary checklist_heading"> {{$data['checklists']->checklist_title}} </a></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                @if(count($data['checklists']['tasks']) > 0)
                <div class="checkout-shipping-form asset_form_container pb-0 mt-0">
                    <div class="">
                        <h6>{{__('configration_checklist.checklist_bread_crumbs.checklist_tasks')}}</h6>
                    </div>
                    <div class="my-3">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="about-projects">
                            @foreach($data['checklists']['tasks'] as $key => $checklist_tasks)
                            {{-- @dd($checklist_tasks['checklist_subtasks']) --}}
                                <div class="atbd-collapse atbd-collapse-custom">
                                    
                                    <div class="atbd-collapse-item mb-0">
                                        <div class="atbd-collapse-item__header @if($key == 0) active @endif">
                                            <a href="#" class="item-link @if($key == 0) border-bottom @else collapsed @endif" data-toggle="collapse" data-target="#collapse-body-c-{{$key}}" @if($key == 0) aria-expanded="true" @else aria-expanded="false" @endif aria-controls="collapse-body-c-{{$key}}">
                                                <i class="la la-angle-right"></i>
                                                <h6>{{$checklist_tasks['task_title']}}</h6>
                                            </a>
                                        </div>
                                        <div id="collapse-body-c-{{$key}}" class="collapse bg-white pt-3 atbd-collapse-item__body @if($key == 0) show @endif">
                                            <div class="collapse-body-text">
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label for="closed_at" class="text-dark">{{__('configration_checklist.checklist_table.task_number')}}</label>
                                                        <p> {{str_replace('Task',__('general_sentence.breadcrumbs.task'), $checklist_tasks['task_number'])}}</p>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label for="closed_at" class="text-dark">{{__('configration_checklist.checklist_table.task_title')}}</label>
                                                        <p> {{$checklist_tasks['task_title']}}</p>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label for="closed_at" class="text-dark">{{__('configration_checklist.checklist_table.description')}}</label>
                                                        <p> {{$checklist_tasks['description']}} </p>
                                                    </div>
                                                </div>

                                                <div class="col-12">
                                                    <h6 class="mb-3">{{__('configration_checklist.common.selected_actions')}}</h6>
                                                </div>
                                                @if($checklist_tasks['photos'] == 'yes' || $checklist_tasks['comment'] == 'yes' || $checklist_tasks['multiple_options'] != NULL || $checklist_tasks['sub_tasks'] == 'yes')
                                                    @if($checklist_tasks['photos'] == 'yes')
                                                    <div class="col-12">
                                                        <div class="">
                                                            <label class="text-dark"> <span data-feather="check"></span> {{__('configration_checklist.checklist_table.photos')}} </label>                                                
                                                        </div>
                                                    </div>
                                                    @endif
                                                    @if($checklist_tasks['comment'] == 'yes')
                                                    <div class="col-12">
                                                        <div class="">
                                                            <label for="closed_at" class="text-dark"><span data-feather="check"></span> {{__('configration_checklist.checklist_table.comment')}} </label>
                                                        </div>
                                                    </div>
                                                    @endif
                                                    @if($checklist_tasks['multiple_options'] != NULL)
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <label for="closed_at" class="text-dark"><span data-feather="check"></span> {{__('configration_checklist.checklist_forms.label.multiple_options')}}  </label>
                                                            <div class="pl-sm-4">
                                                            <div class="row">
                                                                <?php $multiple_options = explode(',', $checklist_tasks['multiple_options']); ?>
                                                                @foreach($multiple_options as $key => $mo)
                                                                <div class="col-6 mb-3">
                                                                    <label class="text-dark">{{__('configration_checklist.checklist_forms.label.option')}} {{$key + 1}}</label>
                                                                    <p>{{$mo}}</p>
                                                                </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    </div>
                                                    </div>
                                                    @endif
                                                    {{-- @flip1@ show sub task --}}
                                                @if(isset($checklist_tasks['checklist_subtasks'])&&count($checklist_tasks['checklist_subtasks'])>0)
                                                <div class="col-12">
                                                <div class="form-group">
                                                    <label for="closed_at" class="text-dark"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check mr-1"><polyline points="20 6 9 17 4 12"></polyline></svg>{{__('configration_checklist.checklist_forms.label.sub_tasks')}}</label>
                                                    <div class="pl-sm-4">
                                                        <div class="row">
                                                            @php $i =1; @endphp
                                                            @foreach($checklist_tasks['checklist_subtasks'] as $st)
                                                                <div class="col-6 mb-3">
                                                                    <label class="text-dark">{{__('configration_checklist.checklist_forms.label.sub_task')}} {{$i++}}</label>
                                                                    <p>{{$st['name']}}</p>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                                
                                                @endif
                                                
                                            {{-- </div>
                                                </div>
                                            </div> --}}

                                            @else
                                            <div class="col-12">
                                                <div class="">
                                                    <label class="">{{__('configration_checklist.common.no_actions_added')}}</label>
                                                </div>
                                            </div>
                                                @endif

                                            </div>
                                        </div>
                                    </div>
                                    
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>                    
</div>
                                            </div>
                                            </div>
@endif

<!---------------------------------End Checklist Popup----------------------------------->
</div>

<div class="modal fade" id="inventory-management" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {{__('work_order.inventory.used_item_list')}} |
                    <?=$work_orders_details->work_order_id;?>
                    <?php
                    if(count($work_orders_details->related_wos) > 1)
                    {
                        echo ': '.$work_orders_details->unique_id;
                    }
                    ?>
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <x-akaunting::formtable 
                    :dataProvider="$dataProvider" 
                    :mode="\Akaunting\Api\View\Components\Formtable::MODE_CUSTOM_FORM" 
                    :renderUrl="route('workorder.items.render')"
                    :createFormRenderUrl="route('workorder.items.createFormRender')"
                    :updateFormRenderUrl="route('workorder.items.updateFormRender')"
                    :formRenderData="['work_order_id' => $work_orders_details->id]"
                    :storeUrl="route('workorder.items.store')"
                    :updateUrl="route('workorder.items.update')"
                />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary">{{__('profile.button.save_changes')}}</button>
            </div>
        </div>
    </div>
</div>

                    <!--========End New Design for Multiple Properties=====-->

                    <!--========== Force Close Popup ===========-->
<div class="modal fade" id="force-close" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">{{__('work_order.forms.label.force_close')}}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
         <div class="form-group">
            <div class="alert-icon-big alert alert-warning" role="alert">
                <div class="alert-icon">
                <i class="las la-info-circle"></i>
                </div>
                <div class="alert-content">
                <h6 class="alert-heading d-inline-block mb-0 pb-0">{{__('data_maintanance_request.common.note')}} : </h6>
                <span>{{ trans('work_order.forms.label.there_exists_workers_who_still_not_completed_the_wo', ['count' => $data['totalWorkersofPendingWork']]) }}</span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="text-light text-dark fw-100">{{__('work_order.forms.label.justification_new')}}</label>
            <textarea class="form-control textarea" placeholder="{{__('work_order.forms.label.write_a_justification')}}" id="sp_comment" name="sp_comment"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve" data-dismiss="modal">{{__('work_order.forms.label.wait')}}</button>
        <button type="button" class="btn btn-primary">{{__('work_order.forms.label.force_close')}}</button>
      </div>
    </div>
  </div>
</div>
</div>
<!--========== End Force Close Popup ===========-->

<div class="modal-warranty modal fade" id="view-items-requested" tabindex="-1" role="dialog" aria-modal="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content modal-bg-white ">
            <div class="modal-header">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-title-wraps">
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href=""> {{__('work_order.bread_crumbs.work_order')}} </a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{__('work_order.inventory.items_requested')}}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h6 class="modal-title"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>
            </div>
            <div class="modal-body">
                <div class="col-md-10 offset-md-1 approve-div">
                    <form >
                        <div class="form-group mb-0">
                            <label>{{__('work_order.inventory.items_requested')}}</label>
                        </div>
                        <div class="form-group">
                            <table class="table-responsive w-100 border table d-table table-va-center">
                                <thead>
                                    <tr>
                                        <th>{{__('work_order.inventory.warehouse')}}</th>
                                        <th>{{__('work_order.inventory.items_name')}}</th>
                                        <th>{{__('work_order.inventory.quantity_requested')}}</th>
                                        <th>{{__('work_order.inventory.approval')}}</th>
                                        <th>{{__('work_order.inventory.quantity_missing')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($itemsData as $item)
                                    <tr>
                                        <td>{{$item->warehouse_name}}</td>
                                        <td>{{$item->name}}</td>
                                        <td><span class="text-primary">{{$item->quantity}}</span></td>
                                        <td><span class="text-primary">
                                            @if($item->status == 'accepted' || $item->status == 'fully_given') {{__('work_order.inventory.accepted')}}  @endif 
                                            @if($item->status == 'rejected') {{__('data_maintanance_request.bread_crumbs.rejected')}}@endif
                                            @if($item->status == 'partially_given') {{__('work_order.inventory.partially_given')}} @endif
                                        </span></td>
                                        <td><span class="text-primary">{{$item->missing_quantity}}</span></td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="form-group">
                            <label class="text-light text-dark fw-100">{{__('work_order.inventory.approved_by')}}</label><br>
                            <span class="text-primary">{{ $itemRequestApprovedBy ?? '' }} </span>
                        </div>

                        <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <span  data-dismiss="modal" class="btn btn-outline-danger btn-default  btn-squared text-capitalize radius-md  mh-48 m-5px">{{__('work_order.inventory.close')}}</span>
                        </div>
                </form>
                </div>

                <div class="col-md-10 offset-md-1 reject-div">
                    <form>
                        <div class="form-group">
                            <label class="text-light text-dark fw-100">{{__('work_order.inventory.reject_reason')}}</label>
                            <textarea class="form-control textarea m-h-250" placeholder="{{__('work_order.inventory.Write_a_reason')}}" id="workerItemRequestRejectReasonViewItemsRequested" name="note"></textarea>
                        </div>
                        <span class="text-danger reject_reason_error"></span>
                         <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <span class="btn btn-outline-danger btn-default btn-squared text-capitalize radius-md approve-back mh-48 m-5px">{{__('work_order.inventory.back')}}</span>
                            <a href="javascript:void(0);" onClick="rejectItemRequest()" id="" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.inventory.submit')}}</a>
                        </div>
                    </form>
                </div>
            </div>
        
        
        </div>

        
    </div>
</div>

<!-- Requests by worker popup -->
<div class="modal-warranty modal fade" id="requests-by-worker" tabindex="-1" role="dialog" aria-modal="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content modal-bg-white ">
            <div class="modal-header">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-title-wraps">
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href=""> {{__('work_order.bread_crumbs.work_order')}} </a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{__('work_order.inventory.items_requested_by_worker')}}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h6 class="modal-title"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>
            </div>
            <div class="modal-body">
                <div class="col-md-10 offset-md-1 approve-div">
                    <form >
                        <input type="hidden" id="chanceItemRequestByWorkerStatusUrl" value= "{{route('workorder.changeItemRequestByWorkerStatus')}}">
                        <div class="form-group mb-0">
                            <label>{{__('work_order.inventory.items_requested')}}</label>
                        </div>
                        <div class="form-group">
                            <table class="table-responsive w-100 border table d-table table-va-center">
                                <thead>
                                    <tr>
                                        <th>{{__('work_order.inventory.items_name')}}</th>
                                        <th>{{__('work_order.inventory.quantity')}}</th>
                                        <th class="sh-td">{{__('work_order.inventory.missing')}}</th>
                                        <th class="sh-td">{{__('work_order.inventory.quantity_missing')}}</th>
                                        @if(AUTH::user()->user_type == 'admin')
                                        <th>{{__('work_order.inventory.choose_warehouse')}}</th>
                                        @endif
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($workOrderRequestedItems as $item)
                                    <input type="hidden" name="warehouse_id" id="warehouse_id_{{$item->id}}" value="{{$item->warehouse_id}}" />
                                    <tr>
                                        <td>{{$item->name}}</td>
                                        <td><span class="text-primary">{{$item->quantity}}</span></td>
                                        <td class="sh-td">
                                            <input class="checkbox missing_quantity" type="checkbox" value="1" name="missing_quantity" id="missing_quantity_{{$item->id}}" data-item-id="{{$item->id}}">
                                        </td>
                                        <td class="sh-td">
                                            <div class="input-group number-snippet align-items-center border rounded p-1" style="display:none;"  id="quantity-div-{{$item->id}}">
                                                <span class="input-group-btn">
                                                    <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="minus" data-field="quant[{{$item->id}}]"><i class="las la-minus"></i></button>
                                                </span>
                                                <input type="text" id="quant-{{$item->id}}" name="quant[{{$item->id}}]" class="form-control input-number" value="{{$item->quantity}}" min="1" max="{{$item->quantity}}">
                                                <span class="input-group-btn">
                                                    <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="plus" data-field="quant[{{$item->id}}]" disabled="disabled"><i class="las la-plus"></i></button>
                                                </span>
                                            </div>
                                        </td>
                                        @if(AUTH::user()->user_type == 'admin')
                                        <td>
                                            <div class="atbd-select">
                                                <select class="form-control select2  select2"  placeholder="{{__('work_order.forms.label.Select_response_action')}}" id="po-w-selected-warehouse-{{$item->id}}"  style= "height:auto;">
                                                @foreach($allWarehousesList as $warehouse)
                                                    <option value="{{$warehouse->id}}" data-value="{{$warehouse->id}}">{{$warehouse->name}}</option>
                                                @endforeach
                                                </select>
                                            </div>
                                        </td>
                                        @endif
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="form-group">
                            <label class="text-light text-dark fw-100">{{__('work_order.inventory.note_from_the_worker')}}</label>
                            <textarea class="form-control textarea" placeholder="" id="workerItemRequestNoteBysp" name="note" readonly>{{ $workOrderItemRequest->request_note ?? '' }}</textarea>
                        </div>
                        <div class="checkbox-theme-default custom-checkbox mb-3  custom-radio">
                            <input class="checkbox" type="checkbox" id="missing-parts" value="yes" name="missing-parts">
                            <label for="missing-parts">
                                <span class="checkbox-text text-dark">
                                {{__('work_order.inventory.do_you_have_a_missing_spare_part')}}
                                </span>
                            </label>
                        </div>            

                        <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <span class="btn btn-outline-danger btn-default btn-squared text-capitalize radius-md reject-btn mh-48 m-5px">{{__('work_order.inventory.reject')}}</span>

                            @if((Auth::user()->user_type == 'sp_admin' && $warehouse_owner == 'service_provider') || Auth::user()->user_type != 'sp_admin')
                                <a href="javascript:void(0);" onClick="approoveItemRequest()" id="" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.inventory.approve')}}</a>
                            @endif

                            @if(!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor') && $warehouse_owner != 'service_provider')
                            <!-- Conditions should be added here when the concept of owner is ready. Also hide the missing checkbox. -->
                            <a href="javascript:void(0);" id="send-to-project-owner" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.inventory.send_to_project_owner')}}</a>
                            @endif
                        </div>
                </form>
                </div>

                <div class="col-md-10 offset-md-1 reject-div">
                    <form>
                        <div class="form-group">
                            <label class="text-light text-dark fw-100">{{__('work_order.inventory.reject_reason')}}</label>
                            <textarea class="form-control textarea m-h-250" placeholder="{{__('work_order.inventory.Write_a_reason')}}" id="workerItemRequestRejectReason" name="note"></textarea>
                        </div>
                        <span class="text-danger reject_reason_error"></span>
                         <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <span class="btn btn-outline-danger btn-default btn-squared text-capitalize radius-md approve-back mh-48 m-5px">{{__('work_order.inventory.back')}}</span>
                            <a href="javascript:void(0);" onClick="rejectItemRequest()" id="" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.inventory.submit')}}</a>
                        </div>
                    </form>
                </div>
            </div>
        
        
        </div>

        
    </div>
</div>
<!-- Modal -->


<!-- Modal  US 7: Click in Select items and pick them from the Warehouse-->
<div class="modal fade" id="spare-parts" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{__('work_order.inventory.spare_parts_items')}}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
            <div class="col-md-7">
                <div class="input-group search-group properties-search p-0 pb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text" id="basic-addon1"><i class="las la-search"></i></span>
                    </div>
                    <input type="text" class="form-control rounded search_items" placeholder="{{__('work_order.inventory.search_by_item_name')}}" aria-label="Username" aria-describedby="basic-addon1">
                </div>
            </div>
            <div class="col-12 properties-list">
                <div class="radio-horizontal-list d-sm-flex align-items-center  border-top pt-3 mb-3">
                    <div class="d-sm-flex align-items-center border p-1 rounded tabs-glide">
                            <div class="radio-theme-default custom-radio flex-fill">
                                <input class="radio show_items_filter" type="radio" name="show_items_filter-01" checked="" value="show_all" id="all-items" />
                                <label for="all-items">
                                    <span class="radio-text">{{__('work_order.inventory.all_items')}}</span>
                                </label>
                            </div>
                            <div class="radio-theme-default custom-radio flex-fill">
                                <input class="radio show_items_filter" type="radio" name="show_items_filter-01" value="selected_only" id="selected-missing-items" />
                                <label for="selected-missing-items">
                                    <span class="radio-text">{{__('work_order.inventory.selected_items')}}</span>
                                </label>
                            </div>
                        </div>
                    <div class="radio-theme-default custom-radio d-flex justify-content-end flex-fill mr-0">
                        <button class="btn btn-outline-danger clear_item_selection"><i class="las la-times"></i> {{__('user_management_module.common.clear_selection')}}</button>
                    </div>
                </div>

                <table class="table-responsive w-100 border table d-table table-va-center">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="check_all_items" class="mr-2 check_all_items"> <label for="check_all_items" class="mb-0" >{{__('work_order.inventory.items_name')}}</label></th>
                            <th>{{__('work_order.inventory.category')}}</th>
                            <th>{{__('work_order.inventory.used_quantity')}}</th>
                            <th>{{__('work_order.inventory.price')}}</th>
                            <th>{{__('work_order.inventory.price_with_vat')}}</th>
                            <th>{{__('work_order.inventory.orgin')}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($allItems as $item)
                        <tr class="list-item">
                            <td><input type="checkbox" name="multi_items" id="item1" class="mr-2 item_checkbox" value="{{ $item['id'] }}"> <label for="item1"> {{ $item['name'] }}</label></td>                            
                            <td><span>{{ $item['category']->name }}</span></td>
                            <td>
                                <div class="input-group number-snippet align-items-center border rounded p-1" id="sp-quantity-div-{{$item['id']}}">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="minus" data-field="quantity[{{$item['id']}}]"><i class="las la-minus"></i></button>
                                    </span>
                                    <input type="text" id="quantity-{{$item['id']}}" name="quantity[{{$item['id']}}]" class="form-control input-number" value="{{$item['stock']}}" min="1" max="{{$item['stock']}}">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="plus" data-field="quantity[{{$item['id']}}]" disabled="disabled"><i class="las la-plus"></i></button>
                                    </span>
                                </div>
                            </td>
                            <td>
                                {{ $item['sale_price_formatted'] }}
                            </td>
                            <td>
                                {{ $item['sale_price_vat_formatted'] }}
                            </td>
                            <td>
                                {{ $item['country_of_origin_formatted']}}
                            </td>
                        </tr>
                        @endforeach                   
                    </tbody>
                </table> 
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve" data-dismiss="modal">{{__('work_order.inventory.close')}}</button>
        <button type="button" class="btn btn-primary" id="save-workorder-items">{{__('work_order.inventory.save')}}</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="used-spare-parts" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{__('work_order.inventory.spare_parts_items')}}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
            <div class="col-12 properties-list">
                
                <table class="table-responsive w-100 border table d-table table-va-center">
                    <thead>
                        <tr>
                            <th>{{__('work_order.inventory.warehouse')}}</th>
                            <th>{{__('work_order.inventory.items_name')}}</th>
                            <th>{{__('work_order.inventory.category')}}</th>
                            <th>{{__('work_order.inventory.used_quantity')}}</th>
                            <th>{{__('work_order.inventory.price')}}</th>
                            <th>{{__('work_order.inventory.price_with_vat')}}</th>
                            <th>{{__('work_order.inventory.orgin')}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($usedItems as $item)
                        <tr class="list-item">
                            <td>{{ $item->warehouse_name }}</td> 
                            <td>{{ $item->name }}</td>                            
                            <td><span>{{ $item->category_name }}</span></td>
                            <td>
                                {{ $item->quantity }}
                            </td>
                            <td>
                                {{ $item->sale_price_formatted }}
                            </td>
                            <td>
                                {{ $item->sale_price_vat_formatted }}
                            </td>
                            <td>
                                {{ $item->country_of_origin_formatted}}
                            </td>
                        </tr>
                        @endforeach                   
                    </tbody>
                </table> 
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve" data-dismiss="modal">{{__('work_order.inventory.close')}}</button>
        <button type="button" class="btn btn-primary" id="save-workorder-items">{{__('work_order.inventory.save')}}</button>
      </div>
    </div>
  </div>
</div>


<!-- Modal  US 7: Click in Select items and pick them from the Warehouse-->
<div class="modal fade" id="missing-spare-parts" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{__('work_order.inventory.spare_parts_items')}}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
            <div class="col-md-7">
                <div class="input-group search-group properties-search p-0 pb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text" id="basic-addon1"><i class="las la-search"></i></span>
                    </div>
                    <input type="text" class="form-control rounded search_items" placeholder="{{__('work_order.inventory.search_by_item_name')}}" aria-label="Username" aria-describedby="basic-addon1">
                </div>
            </div>
            <div class="col-12 properties-list">
                <div class="radio-horizontal-list d-sm-flex align-items-center  border-top pt-3 mb-3">
                    <div class="d-sm-flex align-items-center border p-1 rounded tabs-glide">
                            <div class="radio-theme-default custom-radio flex-fill">
                                <input class="radio show_items_filter_missing" type="radio" name="show_items_filter" checked="" value="show_all" id="all-spare-parts-missing-items" />
                                <label for="all-spare-parts-missing-items">
                                    <span class="radio-text">{{__('work_order.inventory.all_items')}}</span>
                                </label>
                            </div>
                            <div class="radio-theme-default custom-radio flex-fill">
                                <input class="radio show_items_filter_missing" type="radio" name="show_items_filter" value="selected_only" id="selected-spare-parts-missing-items" />
                                <label for="selected-spare-parts-missing-items">
                                    <span class="radio-text">{{__('work_order.inventory.selected_items')}}</span>
                                </label>
                            </div>
                        </div>
                    <div class="radio-theme-default custom-radio d-flex justify-content-end flex-fill mr-0">
                        <button class="btn btn-outline-danger clear_item_selection"><i class="las la-times"></i> {{__('user_management_module.common.clear_selection')}}</button>
                    </div>
                </div>

                <table class="table-responsive w-100 border table d-table table-va-center">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="check_all_items" class="mr-2 check_all_items_missing"> <label for="check_all_items" class="mb-0" >{{__('work_order.inventory.items_name')}}</label></th>
                            <th>{{__('work_order.inventory.category')}}</th>
                            <th>{{__('work_order.inventory.quantity_needed')}}</th>
                            <th>{{__('work_order.inventory.quantity_available')}}</th>
                            <th>{{__('work_order.inventory.orgin')}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($allItems as $item)
                        <tr class="list-item">
                            <td><input type="checkbox" name="multi_items" id="item1" class="mr-2 missing_item_checkbox" value="{{ $item['id'] }}"> <label for="item1"> {{ $item['name'] }}</label></td>
                            <td><span>{{ $item['category']->name }}</span></td>
                            <td>
                                <div class="input-group number-snippet align-items-center border rounded p-1" id="sp-quantity-div-{{$item['id']}}">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="minus" data-field="missing-quantity[{{$item['id']}}]" disabled="disabled"><i class="las la-minus"></i></button>
                                    </span>
                                    <input type="text" id="missing-quantity-{{$item['id']}}" name="missing-quantity[{{$item['id']}}]" class="form-control input-number" value="0" min="0" max="{{$item['stock']}}">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="plus" data-field="missing-quantity[{{$item['id']}}]"><i class="las la-plus"></i></button>
                                    </span>
                                </div>
                            </td>
                            <td>
                                {{ $item['stock'] }}
                            </td>
                            <td>
                                {{ $item['country_of_origin_formatted']}}
                            </td>
                        </tr>
                        @endforeach                   
                    </tbody>
                </table> 
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve" data-dismiss="modal">{{__('work_order.inventory.close')}}</button>
        <button type="button" class="btn btn-primary" id="save-missing-workorder-items">{{__('work_order.inventory.save')}}</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="sp-request-spare-parts" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{__('work_order.inventory.spare_parts_items')}}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
            <div class="col-md-7">
                <div class="input-group search-group properties-search p-0 pb-3">
                <div class="input-group-prepend">
                <span class="input-group-text" id="basic-addon1"><i class="las la-search"></i></span>
                </div>
                <input type="text" id="search-sp-missing-items-list" class="form-control rounded search_building" placeholder="{{__('work_order.inventory.search_by_item_name')}}" aria-label="Username" aria-describedby="basic-addon1">
                </div>
            </div>
            <div class="col-12 properties-list">
                <div class="radio-horizontal-list d-sm-flex align-items-center  border-top pt-3 mb-3">
                                            <div class="d-sm-flex align-items-center border p-1 rounded tabs-glide">
                                            <div class="radio-theme-default custom-radio flex-fill"  onclick="showSelectedMissingItems(0)">
                                                <input class="radio"  type="radio" name="show_properties_filter" checked="" value="show_all" id="all-missing-items">
                                                <label for="all-missing-items">
                                                    <span class="radio-text">{{__('work_order.inventory.all_items')}}</span>
                                                </label>
                                            </div>
                                            <div class="radio-theme-default custom-radio flex-fill" onclick="showSelectedMissingItems(1)" >
                                                <input class="radio"  type="radio" name="show_properties_filter" value="selected_only" id="selected-items">
                                                <label for="selected-items">
                                                    <span class="radio-text">{{__('work_order.inventory.selected_items')}}</span>
                                                </label>
                                            </div>
                                            </div>
                                            <div class="radio-theme-default custom-radio d-flex justify-content-end flex-fill mr-0">
                                                <button class="btn btn-outline-danger clear_selection" id="clear-missing-items-selection"><i class="las la-times"></i> {{__('user_management_module.common.clear_selection')}}</button>
                                            </div>
                                        </div>


                                        <table  class="table-responsive w-100 border table d-table table-va-center">
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="all-missing-parts" class="mr-2"> <label for="all-parts" class="mb-0" 
                                            >{{__('work_order.inventory.items_name')}}</label></th>
                                        <th>{{__('work_order.inventory.quantity_needed')}}</th>
                                        <th>{{__('work_order.inventory.quantity_available')}}</th>
                                        <th>{{__('work_order.inventory.orgin')}}</th>
                                    </tr>
                                </thead>
                                <tbody id="sp-missing-items-list">
                                @foreach($allItems as $item)

                                    <tr class="item-row ">
                                        <td><input onchange="updateSpMissingItemsSelectionCount(this)" class="sp-missing-item-checkbox" data-id="{{$item['id']}}" type="checkbox" id="sp-missing-item-checkbox-{{$item['id']}}" class="mr-2"> <label for="{{$item['id']}}" 
                                            >{{ $item['name']}}</label></td>
                                    <td>
                                        <div class="input-group number-snippet align-items-center border rounded p-1">
                                          <span class="input-group-btn">
                                              <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center"  data-type="minus" data-field="quant[{{$item['id']}}]">
                                                  <i class="las la-minus"></i>
                                              </button>
                                          </span>
                                          <input type="text" id="sp-missing-item-quantity-{{$item['id']}}" name="quant[{{$item['id']}}]" class="form-control input-number" value="0" min="0" max="{{$item['stock'] }}">
                                          <span class="input-group-btn">
                                              <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="plus" data-field="quant[{{$item['id']}}]">
                                                  <i class="las la-plus"></i>
                                              </button>
                                          </span>
                                      </div>
                                    </td>
                                    <td>{{ $item['stock']}}</td>
                                    <td>{{ $item['country_of_origin_formatted']}}</td>
                                 @endforeach
                                </tr>
                                </tbody>
                            </table> 
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve" data-dismiss="modal">{{__('work_order.inventory.close')}}</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal">{{__('work_order.inventory.save')}}</button>
      </div>
    </div>
  </div>
</div>


<!-- End Modal  US 7: Click in Select items and pick them from the Warehouse-->




<!-- Missing Item request by SP For PO -->
<div class="modal-warranty modal fade" id="requests-by-sp" tabindex="-1" role="dialog" aria-modal="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content modal-bg-white ">
            <div class="modal-header">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="page-title-wraps">
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href=""> {{__('work_order.bread_crumbs.work_order')}} </a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{__('work_order.inventory.items_requested_by_worker')}}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{__('work_order.button.work_order')}} #<?=$work_orders_details->work_order_id;?></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h6 class="modal-title"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>
            </div>
            <div class="modal-body">
                <div class="col-md-10 offset-md-1 approve-div">
                    <form >
                        <input type="hidden" id="chanceItemRequestBySpStatusUrl" value= "{{route('workorder.changeItemRequestBySpStatus')}}">
                        <div class="form-group mb-0">
                            <label>{{__('work_order.inventory.items_requested')}}</label>
                        </div>
                        <div class="form-group">
                            <table class="table-responsive w-100 border table d-table table-va-center">
                                <thead>
                                    <tr>
                                        <th>{{__('work_order.inventory.items_name')}}</th>
                                        <th>{{__('work_order.inventory.quantity')}}</th>
                                        <th class="sh-td">{{__('work_order.inventory.missing')}}</th>
                                        <th class="sh-td">{{__('work_order.inventory.quantity_missing')}}</th>
                                        <th>{{__('work_order.inventory.choose_warehouse')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($requestedItemsBySp as $item)
                                    <tr>
                                        <td>{{$item->name}}</td>
                                        <td><span class="text-primary">{{$item->quantity}}</span></td>
                                        <td class="sh-td">
                                            <input class="checkbox sp_missing_quantity" type="checkbox" value="1" name="sp_missing_quantity" id="sp_missing_quantity_{{$item->id}}" data-item-id="{{$item->id}}">
                                        </td>
                                        <td class="sh-td">
                                            <div class="input-group number-snippet align-items-center border rounded p-1" style="display:none;"  id="sp-missing-quantity-div-{{$item->id}}">
                                                <span class="input-group-btn">
                                                    <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="minus" data-field="sp-quant[{{$item->id}}]"><i class="las la-minus"></i></button>
                                                </span>
                                                <input type="text" id="sp-quant-{{$item->id}}" name="sp-quant[{{$item->id}}]" class="form-control input-number" value="{{$item->quantity}}" min="1" max="{{$item->quantity}}">
                                                <span class="input-group-btn">
                                                    <button type="button" class="btn btn-default btn-number bg-grey p-0 rounded-circle text-center" data-type="plus" data-field="sp-quant[{{$item->id}}]" disabled="disabled"><i class="las la-plus"></i></button>
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                        <div class="atbd-select">
                                            <select class="form-control select2  select2"  placeholder="{{__('work_order.forms.label.Select_response_action')}}" id="selected-warehouse-{{$item->id}}"  style= "height:auto;">
                                            @foreach($allWarehousesList as $warehouse)
                                                <option value="{{$warehouse->id}}" data-value="{{$warehouse->id}}">{{$warehouse->name}}</option>
                                            @endforeach
                                            </select>
                                        </div>
                                    
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="checkbox-theme-default custom-checkbox mb-3  custom-radio">
                            <input class="checkbox" type="checkbox" id="missing-parts-by-sp" value="yes" name="missing-parts-by-sp">
                            <label for="missing-parts-by-sp">
                                <span class="checkbox-text text-dark">
                                {{__('work_order.inventory.do_you_have_a_missing_spare_part')}}
                                </span>
                            </label>
                        </div>            

                        <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <span class="btn btn-outline-danger btn-default btn-squared text-capitalize radius-md reject-btn mh-48 m-5px">{{__('work_order.inventory.reject')}}</span>
                            <a href="javascript:void(0);" onClick="approoveItemRequestFromPo()" id="" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.inventory.approve')}}</a>

                            @if(!empty(Auth::user()->user_type == 'sp_admin') || !empty(Auth::user()->user_type == 'supervisor'))
                            <!-- Conditions should be added here when the concept of owner is ready. Also hide the missing checkbox. -->
                            <a href="javascript:void(0);" id="send-to-project-owner" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.inventory.send_to_project_owner')}}</a>
                            @endif
                        </div>
                </form>
                </div>

                <div class="col-md-10 offset-md-1 reject-div">
                    <form>
                        <div class="form-group">
                            <label class="text-light text-dark fw-100">{{__('work_order.inventory.reject_reason')}}</label>
                            <textarea class="form-control textarea m-h-250" placeholder="{{__('work_order.inventory.Write_a_reason')}}" id="poItemRequestRejectReason" name="note"></textarea>
                        </div>
                        <span class="text-danger po_reject_reason_error"></span>
                         <div class="d-flex pt-40 justify-content-md-end justify-content-center button-group d-flex pt-25 justify-content-end">
                            <span class="btn btn-outline-danger btn-default btn-squared text-capitalize radius-md approve-back mh-48 m-5px">{{__('work_order.inventory.back')}}</span>
                            <a href="javascript:void(0);" onClick="rejectItemRequestFromPo()" id="" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('work_order.inventory.submit')}}</a>
                        </div>
                    </form>
                </div>
            </div>
        
        
        </div>

        
    </div>
</div>


<!-- Results Feedback -->
<div class="modal fade" id="results-feedback" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{__('work_order.forms.label.workers_feedback')}}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body checklist-data">
        <form>
        </form>
      </div>
      <div class="modal-footer">
        <!-- <button type="button" class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light " data-dismiss="modal">Close</button> -->
        <button type="button" class="btn btn-primary" data-dismiss="modal" aria-label="Close">{{__('work_order.bread_crumbs.OK')}}</button>
      </div>
    </div>
  </div>
</div>

@if($work_orders_details->assign_type && isset($criteria))
<!--Criteria Modal -->
<div class = "modal fade" id = "criteria-modal" tabindex = "-1" role = "dialog" aria-labelledby = "exampleModalLabel" aria-hidden = "true">
    <div class = "modal-dialog modal-sm" role = "document">
        <div class = "modal-content">
            <div class = "modal-header">
                <h5 class = "modal-title" id = "exampleModalLabel"> @lang('work_order.forms.label.smart_assign_criteria')</h5>
                <button type = "button" class = "close" data-dismiss = "modal" aria-label = "Close">
                    <span aria-hidden = "true">&times;</span>
                </button>
            </div>
            <div class = "modal-body">
                <p>@lang('work_order.forms.label.smart_assign_desc')</p>
                <div>
                    <div class = "d-flex justify-content-between align-items-center border rounded p-3 mb-3 criteria-li @if($criteria->availability) active @endif">
                        <div class = "mb-0 d-flex align-items-center">
                            <i class = "las la-business-time icon mr-3"></i> 
                            <div>
                                <label class = "mb-0">@lang('configration_general_settings.gen_settings_forms.label.availability')</label>
                                <p class = "mb-0">
                                    @if($criteria->availability)
                                        @lang('work_order.forms.label.active_availability_desc')
                                    @else
                                        @lang('work_order.forms.label.inactive_availability_desc')
                                    @endif
                                </p>
                            </div>
                        </div> 
                        <i class = "las la-check"></i>
                    </div>
                    <div class = "d-flex justify-content-between align-items-center border rounded p-3 mb-3 criteria-li @if($criteria->rating) active @endif">
                        <div class = "mb-0 d-flex align-items-center">
                            <i class = "las la-medal icon mr-3"></i>
                            <div>
                                <label class = "mb-0">@lang('configration_general_settings.gen_settings_forms.label.rating')</label>
                                <p class = "mb-0">
                                    @if($criteria->rating)
                                        @lang('work_order.forms.label.active_rating_desc')
                                        {{ $criteria->rating_value ?? 5.00 }}
                                    @else
                                        @lang('work_order.forms.label.inactive_rating_desc')
                                    @endif
                                </p>
                            </div>
                        </div> 
                        <i class = "las la-check"></i>
                    </div>
                    <div class = "d-flex justify-content-between align-items-center border rounded p-3 mb-3 criteria-li @if($criteria->location) active @endif">
                        <div class = "mb-0 d-flex align-items-center">
                            <i class = "las la-map-marked-alt icon mr-3"></i> 
                            <div>
                                <label class = "mb-0">@lang('configration_general_settings.gen_settings_forms.label.location')</label>
                                <p class = "mb-0">
                                    @if($criteria->location)
                                        @lang('work_order.forms.label.active_location_desc')
                                    @else
                                        @lang('work_order.forms.label.inactive_location_desc')
                                    @endif
                                </p>
                            </div>
                        </div> 
                        <i class = "las la-check"></i>
                    </div>
                    <div class = "d-flex justify-content-between align-items-center border rounded p-3 criteria-li @if($criteria->experience) active @endif">
                        <div class = "mb-0 d-flex align-items-center">
                            <i class = "las la-user-cog icon mr-3"></i> 
                            <div>
                                <label class = "mb-0">@lang('configration_general_settings.gen_settings_forms.label.experience')</label>
                                <p class  = "mb-0">
                                    @if($criteria->experience)
                                        @lang('work_order.forms.label.active_experience_desc')
                                    @else
                                        @lang('work_order.forms.label.inactive_experience_desc')
                                    @endif
                                </p>
                            </div>
                        </div> 
                        <i class = "las la-check"></i>
                    </div>
                </div>
            </div>
            <div class = "modal-footer">
                <button type = "button" class = "btn btn-default btn-light" data-dismiss = "modal">@lang('work_order.inventory.close')</button>
            </div>
        </div>
    </div>
</div>
@endif

@endsection

@section('script-header')
    
@endsection

@section('scripts')
@include('applications.admin.workorder.script.show_script')
<script>    
    var url = "{{url('/')}}";
    var work_order_id = '<?=$work_orders_details->id;?>';
    function ignoreError(){ return true }
    window.onerror=ignoreError();
</script>
 <script src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/130527/qrcode.js"></script>
 <script src="https://cdn.jsdelivr.net/gh/davidshimjs/qrcodejs@gh-pages/qrcode.min.js"></script>
 <script type="text/javascript" src="{{ asset('js/admin/workorder/show.js').'?v='.time() }}"></script>

{{-- initial chat script --}}
{{-- Axios cdn --}}

<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.0.0-alpha.1/axios.min.js" integrity="sha512-xIPqqrfvUAc/Cspuj7Bq0UtHNo/5qkdyngx6Vwt+tmbvTLDszzXM0G6c91LXmGrRx8KEPulT+AfOOez+TeVylg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script>
    $('#cost').keyup(function(){
        let res = /^[0-9]+$/.test($(this).val());
        if(!res)
        {
            $(this).val('');
        } else {
                //alert($(this).val().charAt(0));
                if($(this).val().charAt(0) != '@') {
                    $(this).val( $(this).val()  );
                }
        }
    });
    $('#cost_main').keyup(function(){
            let res = /^[0-9]+$/.test($(this).val());
            if(!res)
            {
                $(this).val('');
            } else {
                    //alert($(this).val().charAt(0));
                    if($(this).val().charAt(0) != '@') {
                        $(this).val($(this).val()  );
                    }
            }
        });
        $('#estimated_price').keyup(function(){
            let res = /^[0-9]+$/.test($(this).val());
            if(!res)
            {
                $(this).val('');
            } else {
                    //alert($(this).val().charAt(0));
                    if($(this).val().charAt(0) != '@') {
                        $(this).val($(this).val()  );
                    }
            }
        });
</script>
<script type="text/javascript">
    var update_assigned_to = function () {
    if ($("#assigned_to").is(":checked")) {
        $('#worker_id').prop('disabled', 'disabled');
        $('#worker_id option').removeAttr("selected");
        $("#worker_id").val(null).trigger("change");
    }
    else {        
        $('#worker_id').prop('disabled', false);
    }
  };
  $(update_assigned_to);
  $("#assigned_to").change(update_assigned_to);

  var update_assigned_to_user = function () {
    if ($("#assigned_to_user").is(":checked")) {
        $('#reject_wo_worker_id').prop('disabled', 'disabled');
        $('#reject_wo_worker_id option').removeAttr("selected");
        $("#reject_wo_worker_id").val(null).trigger("change");
    }
    else {        
        $('#reject_wo_worker_id').prop('disabled', false);
    }
  };
  $(update_assigned_to_user);
  $("#assigned_to_user").change(update_assigned_to_user);

  var update_assigned_to_user_type = function () {
    if ($("#assigned_to_user_type").is(":checked")) {
        $('#worker_id6').prop('disabled', 'disabled');
        $('#worker_id6 option').removeAttr("selected");
        $("#worker_id6").val(null).trigger("change");
    }
    else {        
        $('#worker_id6').prop('disabled', false);
    }
  };
  $(update_assigned_to_user_type);
  $("#assigned_to_user_type").change(update_assigned_to_user_type);
  
        $(function() {
        $('#select-action').change(function(){
            $('.show-hide').hide();
            $('#' + $(this).val()).show();
        });
    });
</script>
<script>

function setChatHistoryToBottom(){
        let chatBox=document.getElementsByClassName('chat-box')[0]
        chatBox.scrollTop=chatBox.scrollHeight
    }

    window.onload=(e)=>{
        setChatHistoryToBottom()
    }



    // last chat id
    var chats=JSON.parse('{!! json_encode($chats) !!}')


    // console.log('chats',chats);

    var lastChatObj=chats[chats.length-1]
    var w_id ='<?=$work_orders_details->id;?>';


   let checkReceiverChatsInterval=setInterval(()=>{
    //    console.log('checkReceiverInterval');
    //    let last_chat_obj_id=''
    // if(lastChatObj && typeof lastChatObj!= undefined){
    //     last_chat_obj_id=lastChatObj.id
    // }


      axios.post("{{route('workorder.check_receiver_message')}}",{last_chat_id:(lastChatObj?lastChatObj.id:'') ,w_id:(w_id?w_id:'') })
        .then(res=>{
            // console.log(res.data);
            if(res.data.renderHtml){
                let chat_div=$("#chatLoad").html();
                $("#chatLoad").html(chat_div+res.data.renderHtml)
                setChatHistoryToBottom()
            }


            // if(res.data.new_chats){
            //     let new_chats=res.data.new_chats
            // }
            // console.log('new_chats_len',res.data.new_chats.length);
            // if(res.data.new_chats.length){
            //     // console.log('new_chats',new_chats);
            //     // console.log('typeof new_chats',typeof new_chats);

            //     // let receiverChatString=''
            //     // for (let i = 0; i < n_chats.length; i++) {
            //     //     const nc = n_chats[i];
            //     //     lastChatObj=nc;
            //     //     let temp_str= getReceiverMessageTemplate(nc)
            //     //     log('temp_receiv_str',temp_str)
            //     //     receiverChatString+=temp_str

            //     // }
            //     // new_chats.forEach((nc)=>{
            //     //     lastChatObj=nc;
            //     //     let temp_str= getReceiverMessageTemplate(nc)
            //     //     log('temp_receiv_str',temp_str)
            //     //     receiverChatString+=temp_str
            //     // })

            //     // receiverChatString=n_chats.map((nc)=>{
            //     //     // @set lastChatObj to last received message
            //     //     lastChatObj=nc;
            //     //     let temp_str= getReceiverMessageTemplate(nc)
            //     //     log('temp_receiv_str',temp_str)
            //     //     return temp_str
            //     // }).join('')

            //     // console.log('receiverChatString',receiverChatString);

            //     // let chat_div=$("#chatLoad").html();
            //     // $("#chatLoad").html(chat_div+receiverChatString)
            //     // setChatHistoryToBottom()
            // }
        })
        .catch(err=>{})

   },3000)


   function getReceiverMessageTemplate(data){
    //    console.log('inside getReceiverMessageTemplate',data);

            let file_div=''
        if(data.attach_file_mime_type){
            let base_url='{!! asset("") !!}'
            let file_url=''
            let chat_id=data.id?data.id:null;
            if(data.id){
                file_url=data.file_encrypted_link
            }

            if(["image/png","image/jpeg"].includes(data.attach_file_mime_type)){
            file_type_attributes=`src="${file_url}"`

            }else{
            file_type_attributes=`download href="${file_url}"`

            }


            if(["image/png","image/jpeg"].includes(data.attach_file_mime_type)){

                file_div=`
                <div class="sent-message file-list-57 rounded-pill">
                    <a class="file-name d-flex align-items-center">
                        <div class="image1-block view-img">
                        <img onclick="chatImageClick(this)" ${file_type_attributes} class="mr-2 wh-50">
                        </div>
                        <span title="file_example_PNG_500kB.png" class="name-text "
                            id="file_name"><span>${data.attach_file_name}</span></span>
                    </a>
                </div>
                `

            }else{
                file_div=`
                <div class="sent-message file-list-57 rounded-pill">
                    <a class="file-name" ${anchor_attributes}>
                        <i class="las la-download rounded-circle mr-2"></i>
                        <span title="sample.pdf" class="name-text " id="file_name"><span>${data.attach_file_name}</span></span>
                    </a>
                </div>
                `
            }
        }

        // chat div template

        var cu_la = window.current_locale;
                    if(data.user_type === "sp_worker"){
                        usertype =  cu_la === "ar" ? 'عامل' : 'Worker';
                    }
                    else if(data.user_type === "super_admin"){
                        //usertype = 'Super Admin';
                        usertype =  cu_la === "ar" ? 'Super Admin' : 'Super Admin';
                    }
                    else if(data.user_type === "osool_admin"){
                        usertype =  cu_la === "ar" ? 'Osool Admin' : 'Osool Admin';
                    }
                    else if(data.user_type === "admin"){
                        usertype =  cu_la === "ar" ? 'مالك المشروع' : 'Project Owner Admin';
                    }
                    else if(data.user_type === "admin_employee"){
                        //usertype = 'Admin Employee';
                        usertype =  cu_la === "ar" ? 'نائب مالك المشروع' : 'Project Owner Employee';
                    }
                    else if(data.user_type === "building_manager"){
                        usertype =  cu_la === "ar" ? 'مدير المبنى' : 'Building Manager Admin';
                    }
                    else if(data.user_type === "building_manager_employee"){
                        usertype =  cu_la === "ar" ? 'نائب مدير المبنى' : 'Building Manager Employee';

                    }
                    else if(data.user_type === "sp_admin"){
                        usertype =  cu_la === "ar" ? 'مسؤول مقدم الخدمة' : 'Service Provider Admin';

                    }
                    else if(data.user_type === "supervisor"){
                        usertype =  cu_la === "ar" ? 'مشرف مقدم الخدمة' : 'Service Provider Supervisor';

                    }
        let div=`
        <div class=" flex-1 justify-content-start d-flex incoming-chat mb-20">
            <div class="chat-text-box w-100">
                <div class="media">
                    <div class="media-body d-flex justify-content-end">
                        <div class="chat-text-box__content">
                            <div class="chat-text-box__title d-flex align-items-center justify-content-end mb-2">
                                <h6 class="fs-14">${data.receiver.name} </h6>
                                <span class="fa fa-dot-circle"></span><span class="text-light">${usertype}</span>

                                <span class="chat-text-box__time fs-12 color-light fw-400 ml-15">${data.created_at_str}</span>
                            </div>
                            <div class="align-items-center justify-content-end d-flex">
                                <div class="chat-text-box__subtitle p-20 bg-deep">
                                    <p class="color-grey ">${data.message??''}</p>
                                    ${file_div}
                                </div>
                            </div>
                        </div>
                        <div class="chat-text-box__photo">
                            <img src="http://localhost:8000/uploads/profile_images/dummy_profile_image.png"
                                class="align-self-start ml-15 wh-46" alt="">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        `
        return div

   }


 </script>


<script src="{{ asset('vendor_assets\js\datetime\js\bootstrap-datetimepicker.js') }}"></script>

<script type="text/javascript">
    $("#worker_id4,#worker_id6,#worker_id,#reason,#worker_id2,#worker_id3,#worker_id5,.select2,#select-action , #select-warehouse").select2({
        language: {
            noResults: function () {
                 return translations.general_sentence.validation.No_results_found;
            }
        }
    });

    $('#select-action').on('select2:open', function () {
    setTimeout(() => {
      $('#select-action option:disabled').each(function () {
        const val = $(this).val();
        const tooltipText = $(this).data('tooltip');

        if (tooltipText) {
          // Find the matching Select2 rendered option by data-select2-id
          const renderedOption = $(`.select2-results__option[aria-disabled="true"][id*="-${val}"]`);
          renderedOption.attr('title', tooltipText);
        }
      });
    }, 0);
  });
    
</script>

<script type="text/javascript">
    

    
$('#message').keypress(function (e) {
  if (e.which == 13) {
    submit_message();
    return false;    //< Add this line
  }
});
$('.other_reason').hide();

$("#upload_receipt").on("change", function (event) {
  
    $('.dynamic_switch').html('');

    $("#img_output").show();
    $(this).parent().parent().parent().find(".upload-prev").addClass("show-img");
    var files = document.getElementById("upload_receipt").files;
    if(!files[0].size || files[0].size == 'undefined'){
    return false
    }
    //console.log(files);
    var file_size_error = false;
    var file_type_error = false;

    var file_size_in_kb = files[0].size / 2048;
    var file_type = files[0].type;

    if (file_size_in_kb > 2048) {
        file_size_error = true;
    }

    console.log(file_type)
    var supported_types = ["image/jpeg", "image/png", "image/jpg","application/pdf"];

    if (!supported_types.includes(file_type)) {
        file_type_error = true;
    }
    var image_block = '';
    var doc_block ='';

    if (file_size_error == true || file_type_error == true) {
        // reset($("#upload_receipt"));

        var error_message = "";

        if (file_size_error == true && file_type_error == true) {
            error_message=translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_pdf_image_of_max_size_1mb;
        } else if (file_size_error == true && file_type_error == false) {
            error_message=translations.general_sentence.validation.File_size_should_not_be_more_than_1_mb;
        } else {
            error_message=translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_pdf_image;
        }

        swal({
        title: error_message,
        // text: error_message,
        icon: "warning",
        buttons: true,
        dangerMode: true,
        confirmButtonText:  translations.general_sentence.swal_buttons.ok,

    })    } else {
        var work_order_id = '<?=$work_orders_details->id;?>';
        //console.log(work_order_id);
        var reader = new FileReader();
        reader.onload = function () {
            var output = document.getElementById("img_output");
            var output_link = reader.result;
        };
        reader.readAsDataURL(event.target.files[0]);

        var formdata = new FormData($("#img_upload_form")[0]);
        var fileInput = document.getElementById("upload_receipt");
        //console.log(fileInput);
        var file = fileInput.files[0];
        console.log(file);
        formdata.append("upload_receipt", file);
        formdata.append("work_order_id", work_order_id);
        $.ajax({
            url: $("#upload_receipt_action").val(),
            type: "POST",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: formdata,
            dataType: "json",
            async: false,
            processData: false,
            contentType: false,
            success: function (response) {
                $(".loader-overlay").css("display", "block");
                var img_types = ["image/jpeg", "image/png", "image/jpg"];
                if(img_types.includes(file_type)){
                    var image_block = `<div class="upload-prev rounded border p-1">
                    <div class="upload-div"><ul class="d-flex align-items-center">
                    <li class="flex-fill d-flex align-items-center">
                    <img class="ap-img__main rounded-squere rounded" src=`+response.link+` alt="" id="img_output">
                    <a  class="file-name ml-1 d-flex align-items-center"><span class="name-text">`+file.name+`</span></a>
                    <a onclick="removeAttachFile2()" class="btn-delete delete_rec text-danger"><i class="la delete_rec la-trash"></i></a>
                    </li></ul> </div></div>`;
                    $('.dynamic_switch').append(image_block);
                }
                else{
                    var doc_block =`<div class="upload-prev rounded border p-1">
                    <div class="upload-div"><ul class=""> 
                    <li class="flex-fill d-flex align-items-center">
                    <a  class="file-name d-flex align-items-center"><i class="las la-paperclip mr-2 fs-12 text-primary" ></i> <span class="name-text">`+file.name+`<span></span></span></a>
                    <a onclick="removeAttachFile2()" class="btn-delete delete_rec text-danger"><i class="la delete_rec la-trash"></i></a>
                    </li></ul></div></div></div>`;
                    $('.dynamic_switch').append(doc_block);
                }
                $(".loader-overlay").css("display", "none");
                //alert(response);
            },
        });
    }
});

    function removeAttachFile2() {
        $('.dynamic_switch').html('');
        $("#upload_receipt").empty()
        $("#upload_receipt").val('')
        document.getElementById('upload_receipt').value = "";
        var formdata = new FormData();
        var work_order_id = '<?=$work_orders_details->id;?>';
        formdata.append("work_order_id", work_order_id);
    // alert('nww')
    $.ajax({
            url: '<?=route('workorder.workorder_delete_image2')?>',
            type: "POST",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: formdata,
            dataType: "json",
            async: false,
            processData: false,
            contentType: false,
            success: function (response) {
            },
        });




}



$( document ).ready(function() {
        // var refreshId = setInterval(function()
        // {
        //     //container.load('.chat');
        //     $(".loader-overlay").css("display", "none");
        //     $("#chatLoad").load(location.href + " #chatLoad");

        // }, 3000);
        
        $(".download-image").click(function(event) { 
    event.preventDefault(); // Prevent default form submission
    event.stopPropagation(); 

        var imageUrl = $('.image-zoom img').attr("src"); // Get image URL
    
    fetch(imageUrl)
        .then(response => response.blob()) // Convert response to blob
        .then(blob => {
            const filename = extractFilenameFromUrl(imageUrl) || "image.png"; // Extract filename (optional)
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;


            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        })
        .catch(error => console.error(error)); // Handle errors

});

// Optional function to extract filename from URL (can be improved)
function extractFilenameFromUrl(url) {
//   const parts = url.split('/');
//   return parts[parts.length - 1];
// Split the URL by '/' to get the parts
const parts = url.split('/');

// Get the last part of the URL path
let filename = parts[parts.length - 1];

// Remove any query parameters or fragments from the filename
filename = filename.split('?')[0].split('#')[0];

return filename;
}
});

function submit_message()
{
    $('#send_message').prop("disabled", true);
    var message = $('#message').val();
    var work_order_id = '<?=$work_orders_details->id;?>';
    var usertype = '<?php echo auth()->user()->user_type;?>';
    //alert(usertype);
    if(usertype=='building_manager')
    {
        var created_by = '<?=$work_orders_details->service_provider_id;?>';
    }
    else{
        var created_by = '<?=$work_orders_details->created_by;?>';
    }
    //alert(created_by);


    let chat_file=document.getElementById('chat-file')

    //@ check if message is empty and file is not selected
    if(message == '' && !chat_file.files.length && !chat_file.files[0])
    {
        $('#send_message').prop("disabled", false);
        toastr.error(translations.general_sentence.validation.Please_enter_a_message_in_the_chat_box, translations.general_sentence.validation.warning, {timeOut: 2000, positionClass: "toast-top-center",progressBar: true});
        return
    }

    let fd=new FormData()
    let file=chat_file.files[0]
    if(chat_file.files && chat_file.files[0]){
        fd.append('attach_file',file)
    }
    fd.append('_token','{{ csrf_token() }}');
    fd.append('message',message);
    fd.append('work_order_id',work_order_id);
    fd.append('created_by',created_by);
    $('#message').val('');
    let data={ _token:'{{ csrf_token() }}', message: message, work_order_id: work_order_id, created_by: created_by,attach_file:chat_file.files[0]}
    data=JSON.stringify(data)
    $.ajax({
        type: 'POST',
        url: "{{route('workorder.submit_message')}}",
        data: fd,
        contentType:false,
        processData:false,
        // dataType:"json",
        // beforeSend: function( xhr ) {
        //  xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
        //  //$("#wait").css("display", "block");
        // },
        success: function(data) {

            $('#send_message').prop("disabled", false);
            // console.log('after sent message',data);
            lastChatObj=data.chat
            //alert(data);
            //toastr.success(translations.general_sentence.validation.Message_submitted_Successfully, translations.general_sentence.validation.Success, {timeOut: 3000, positionClass: "toast-top-center",progressBar: true});
            $('#message').val('');
            //location.reload();
        //    $("#chatLoad").append(data.renderHtml);
                let chat_div=$("#chatLoad").html();
           $("#chatLoad").html(chat_div+data.renderHtml)
           setChatHistoryToBottom()


        //    @file attachment set to empty
            removeAttachFile(false,false)

        },
        error: function (data){
            $('#send_message').prop("disabled", false);
        }
    });
}

function submit_warranty_comment()
{
    $('.submit_warranty_comment').prop("disabled", true);
    var warranty_comment = $('#warranty_comment').val();
    var work_order_id = '<?=$work_orders_details->id;?>';
    // if(warranty_comment == '')
    // {
    //     toastr.error('Please enter Comment in the chat box', "Warning", {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
    // }
    $.ajax({
        type: 'POST',
        url: "{{route('workorder.submit_warranty_comment')}}",
        data: { _token:'{{ csrf_token() }}', warranty_comment: warranty_comment, work_order_id: work_order_id},
        beforeSend: function( xhr ) {
         xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
         //$("#wait").css("display", "block");
        },
        success: function(data) {
            //alert('test');
            toastr.success(translations.general_sentence.validation.Warranty_Closed_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
            location.reload();
        }
    });
}

function re_open_job()
{
    $('.re_open_job').prop("disabled", true);
    var reopen_reason = $('#reopen_reason').val();
    var work_order_id = '<?=$work_orders_details->id;?>';
    // if(reopen_reason == '')
    // {
    //     $('.re_open_job').prop("disabled", true);
    //     toastr.error(translations.general_sentence.validation.Please_enter_Re_open_reason, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
    //     exit;
    // }
    $.ajax({
        type: 'POST',
        url: "{{route('workorder.re_open_job')}}",
        data: { _token:'{{ csrf_token() }}', reopen_reason: reopen_reason, work_order_id: work_order_id},
        beforeSend: function( xhr ) {
         xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
         //$("#wait").css("display", "block");
        },
        success: function(data) {
            //alert('test');
            toastr.success(translations.general_sentence.validation.Job_Re_opened_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
            location.reload();
        }
    });
}

function update_response_action()
{
    
    var proposed_new_date = $('.proposed_new_date').val();//alert(proposed_new_date);
    var work_order_id = '<?=$work_orders_details->id;?>';
    var start_job = $('input[name="start_job"]:checked').val();
    var worker_id = $('#worker_id2').val();
    var reject_reason = $('#reject_reason').val();
    var reason = $('#reason').val();
    var assigned_to = $('#assigned_to').val();
    var newSelectedWorker = $('#worker_id').val();
    var assignToChecked = $('input[name="assigned_to"]:checked').val() ?? null; // Added for checking started on behalf issue
    
    if(!worker_id)
    {
        var worker_id = $('#worker_id').val();
    }   

    if(start_job == 1 && reason == translations.work_order.forms.label.Other_reason && reject_reason == "")
    {
        toastr.error(translations.general_sentence.validation.Please_enter_reason, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
    }

    
    if(start_job == 1 && !proposed_new_date)
    {
        if(reason != translations.work_order.forms.label.I_don_t_have_the_required_parts){
            toastr.error(translations.general_sentence.validation.Please_select_the_Proposed_Date, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center", progressBar: true});return false;exit;
        }
    }
    
    if(start_job == 2 && worker_id == "")
    {
        if($("#assigned_to").prop('checked') == false){
            toastr.error(translations.general_sentence.validation.Please_select_worker, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
        }
    }

    if(start_job != 1 || reason != translations.work_order.forms.label.Other_reason)
    {
        reject_reason = '';
    }

    // if(start_job == 1 && worker_id == "")
    // {
    //     if(reason != translations.work_order.forms.label.I_don_t_have_the_required_parts){
    //         toastr.error(translations.general_sentence.validation.Please_select_worker, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
    //     }
    // }
    if(start_job == 1 && reason == translations.work_order.forms.label.I_don_t_have_the_required_parts){
        if($('.sp-missing-item-checkbox').filter(':checked').length == 0){
            toastr.error('Please select missing spare parts', translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true}); return false;
        }
        $('.sp-missing-item-checkbox:checkbox:checked').each(function() {
        var id = $(this).data('id');
        var quantity = $('#sp-missing-item-quantity-'+id).val();
        if($('#sp-missing-item-checkbox-'+id).is(':checked')) {
            for(i=0; i < document.missinItems.length; i++){
                if( document.missinItems[i].id == id){
                    document.missinItems[i].isChecked = true;
                    document.missinItems[i].missingQuantity = quantity;
                }
            }
        }
    });

    }

    $(".update_button_wo").attr("disabled", true);
    $.ajax({
        type: 'POST',
        url: "{{route('workorder.update_response_action')}}",
        data: { _token:'{{ csrf_token() }}', start_job: start_job, worker_id: worker_id, work_order_id: work_order_id, reason:reason, proposed_new_date: proposed_new_date, reject_reason: reject_reason, assigned_to: assigned_to,sp_missing_items_list:JSON.stringify(document.missinItems), newSelectedWorker: newSelectedWorker, assignToChecked: assignToChecked},
        beforeSend: function( xhr ) {
         xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
         //$("#wait").css("display", "block");
        },
        success: function(data) { //alert(data);
            //alert('test');
            toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
            location.reload();
        }
    });
}

function approve_reject_sp_date()
{
    $(".approve_reject_sp_date").prop("disabled", true);
    var work_order_id = '<?=$work_orders_details->id;?>';
    var approve_proposed_date = $('input[name="approve_proposed_date"]:checked').val();
    var reason = $('#reason').val();
    var reject_reason = $('#reject_reason_approve_reject_sp').val();
    var invalid = false
    var proposed_date_sp = '<?=$work_orders_details->proposed_new_date;?>';
    var new_suggested_Date = $('.new_suggested_date').val();
    if(reason == "")
    {
        toastr.error(translations.general_sentence.validation.Please_enter_reason, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
    }
    var worker_id3 = $('#worker_id3').val();

    if(approve_proposed_date == 1 && new_suggested_Date == "")
    {       
        toastr.error(translations.general_sentence.validation.Please_Enter_Suggested_Date, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
        invalid = true;
    }
    if(approve_proposed_date == 1 && reject_reason == "")
    {      
        toastr.error(translations.general_sentence.validation.Please_enter_reason, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true}); invalid = true;
    }
    var user_type = '<?=Auth::user()->user_type;?>';
    if(approve_proposed_date == 1 && user_type != "" && worker_id3 == "")
    {
        toastr.error(translations.general_sentence.validation.Please_select_a_Worker, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
        invalid = true;
    }
    if(invalid == true){
        return false;
    }
    else{
        invalid =false;
 
    }
    $.ajax({
        type: 'POST',
        url: "{{route('workorder.approve_reject_sp_date')}}",
        data: { _token:'{{ csrf_token() }}', approve_proposed_date: approve_proposed_date, work_order_id: work_order_id, reason:reason, proposed_date_sp:proposed_date_sp, reject_reason:reject_reason, worker_id3:worker_id3, new_suggested_Date:new_suggested_Date},
        beforeSend: function( xhr ) {
         xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
         //$("#wait").css("display", "block");
        },
        success: function(data) {
            //alert('test');
            toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
            location.reload();
        }
    });
}

function complete_job_by_sp_call_hold()
{


    var work_order_id = '<?=$work_orders_details->id;?>'; //alert(work_order_id);
    var complete_job = $('input[name="complete_job"]:checked').val();
    var proposed_new_date = $('.proposed_new_date_sp').val(); //alert(proposed_new_date);
    var worker_id = $('#worker_id4').val();
    var reason = $('#reason4').val();
    $.ajax({
        type: 'POST',
        url: "{{route('workorder.complete_job_by_sp')}}",
        data: { _token:'{{ csrf_token() }}', work_order_id: work_order_id, complete_job: complete_job, worker_id: worker_id, reason:reason, proposed_new_date:proposed_new_date},
        beforeSend: function( xhr ) {
         xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
         //$("#wait").css("display", "block");
        },
        success: function(data) {
            //alert('test');
            toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
            location.reload();
        }
    });
}

// function set_sp_action(value) {
//     if(value == 2) {
//         $('.bm_respond_sp').hide()
//     } else {
//         $('.bm_rate_block').show()
//     }

// }



// function set_bm_action(value) {
//     if(value == 1) {
//         $('.bm_rate_block').hide()
//     } else {
//         $('.bm_respond_sp').show()
//     }

// }
window.filesForUpload = [];

  $(document).ready(function() {
    $.ajaxSetup({
        headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    @if(Session::has('message'))
    toastr.options =
    {
        "closeButton" : true,
        "progressBar" : true
    }
    toastr.success("{{ session('message') }}");
    @endif
    $("#supervisor_id_form").validate({
        ignore: "input[type=hidden]",
        rules: {               
            supervisor_id: {
                required: true
            }
        },
        messages: {            
            supervisor_id:
            {
                required: translations.general_sentence.validation.This_field_is_required,
            }
        },
        errorPlacement: function (error, element) {
            error.addClass("invalid-feedback");
            if (element.attr("name") == "supervisor_id") {
                error.appendTo($("#supervisor_id_error"));
            }
            else {
                error.insertAfter(element);
            }
        },
        highlight: function (element, errorClass, validClass) {
            $(element).addClass("is-invalid");
        },
        unhighlight: function (element, errorClass, validClass) {
            $(element).removeClass("is-invalid");
            var elem = $(element);
            if (elem.hasClass("select2-offscreen")) {
                $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
            } else {
                elem.removeClass(errorClass);
            }
        },
        submitHandler: function (form) {
            //$.LoadingOverlay("show");
            //alert('ad');
            form.submit();
        },
    });


    console.log(<?=$work_orders_details;?>);
    $(".date_change_request_form").validate({
        ignore: "input[type=hidden]",
        rules: {
            // 'pictures[]': {
            //     required: {
            //         depends: function (element) {
            //             return $('.complete_job').find(':selected').val() == 'show1'; //require if job is complete
            //         }
            //     },
            // },        
            sp_comment: {
                required: true,
                required: {
                    depends: function (element) {
                        return $('.complete_job').find(':selected').val() == 'show1'; //require if job is complete
                    }
                },
            },
            proposed_new_date: {
                required: {
                    depends: function (element) {
                        return $('.complete_job').find(':selected').val() == 'show2'; //require if propose_new_date
                    }
                },
            }, 
            reason4: {
                required: {
                    depends: function (element) {
                        return $('.complete_job').find(':selected').val() == 'show2'; //require if propose_new_date
                    }
                },
            },       
            worker_id4: {
                required: {
                    depends: function (element) {
                        return $('.complete_job').find(':selected').val() == 'show3'; //require if reassign_worker
                    }
                },
            },
        },
        messages: {
            'pictures[]': {
                required: translations.general_sentence.validation.This_field_is_required
            },
            sp_comment:
            {
                required: translations.general_sentence.validation.This_field_is_required,
            },
            proposed_new_date:
            {
                required: translations.general_sentence.validation.This_field_is_required,
            },
            reason4:
            {
                required: translations.general_sentence.validation.This_field_is_required,
            },
            worker_id4:
            {
                required: translations.general_sentence.validation.This_field_is_required,
            }
        },
        errorPlacement: function (error, element) {
            error.addClass("invalid-feedback");
            if (element.attr("id") == "file_upload") {
                error.appendTo($("#complete_job_error1"));
            } else if (element.attr("name") == "sp_comment") {
                error.appendTo($("#complete_job_error2"));
            } 
            else if (element.attr("name") == "proposed_new_date") {
                error.appendTo($("#complete_job_error3"));
            } 
            else if (element.attr("name") == "reason4") {
                error.appendTo($("#complete_job_error4"));
            }
            else if (element.attr("id") == "worker_id4") {
                error.appendTo($("#complete_job_error5"));
            }
            else {
                error.insertAfter(element);
            }
        },
        highlight: function (element, errorClass, validClass) {
            $(element).addClass("is-invalid");
        },
        unhighlight: function (element, errorClass, validClass) {
            $(element).removeClass("is-invalid");
            var elem = $(element);
            if (elem.hasClass("select2-offscreen")) {
                $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
            } else {
                elem.removeClass(errorClass);
            }
        },
        submitHandler: function (form) {
            //$.LoadingOverlay("show");
            //alert('ad');
            form.submit();
        },
    });

    $('#complete_job_by_sp_call').click(function(e){

        
        if($(".date_change_request_form").valid() == false)
        {
            return false;
        }
        $('#complete_job_by_sp_call').prop("disabled", true);
        e.preventDefault();
        var formData = new FormData();

        let TotalFiles = window.filesForUpload.length; //Total files
        let files = window.filesForUpload;
        for (let i = 0; i < TotalFiles; i++) {
            formData.append('file_upload' + i, files[i]);
        }
        var work_order_id = '<?=$work_orders_details->id;?>'; //alert(work_order_id);
        var bm_approove = '<?=$work_orders_details->bm_approove;?>'; //alert(work_order_id);
        var sp_approove = '<?=$work_orders_details->sp_approove;?>'; //alert(work_order_id);

       // var complete_job = $('input[name="complete_job"]:checked').val();
       var complete_job = ($('select.complete_job').find(':selected').data('value') == "undefined" || $('select.complete_job').find(':selected').data('value') == undefined) ? 'job_completed': $('select.complete_job').find(':selected').data('value');
       
        var proposed_new_date = $('.proposed_new_date_sp').val(); //alert(proposed_new_date);
        var worker_id = $('#worker_id4').val();
        var reason = $('#reason4').val();
        var sp_comment = $('#sp_comment').val();
        var cost = $('#cost').val();
        var stored_item_id = $('#stored-item-id').val();
        var stored_quantity = $('#stored-quantity').val();
        var stored_missing_item_id = $('#stored-missing-item-id').val();
        var stored_missing_quantity = $('#stored-missing-quantity').val();
        formData.append('work_order_id', work_order_id);
        formData.append('complete_job', complete_job);
        formData.append('worker_id', worker_id);
        formData.append('reason', reason);
        formData.append('proposed_new_date', proposed_new_date);
        formData.append('sp_approove', sp_approove);
        formData.append('bm_approove', bm_approove);
        formData.append('sp_comment', sp_comment);
        formData.append('TotalFiles', TotalFiles);
        formData.append('cost', cost);
        formData.append('approve_job', 2);
        formData.append('stored_item_id', stored_item_id);
        formData.append('stored_quantity', stored_quantity);
        formData.append('stored_missing_item_id', stored_missing_item_id);
        formData.append('stored_missing_quantity', stored_missing_quantity);
        formData.append('mail_triggered', false);
        $.ajax({
            type: 'POST',
            url: "{{route('workorder.complete_job_by_sp')}}",
            data: formData,
            contentType: false,
            processData: false,
            beforeSend: function( xhr ) {
             xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
             //$("#wait").css("display", "block");
            },
            success: function(data) {
                if(data == "job_completed")
                {
                    formData.append('mail_triggered', true);
                    $.ajax({
                        type: 'POST',
                        url: "{{route('workorder.approve_job_by_sp')}}",
                        data: formData,
                        contentType: false,
                        processData: false,
                        beforeSend: function( xhr ) {
                        xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
                        //$("#wait").css("display", "block");
                        },
                        success: function(data) {
                            // alert(data);
                            // alert('test');
                            toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});

                            location.reload();
                        }
                    });
                }
                else
                {
                    toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});

                    location.reload();
                }
            }
        });
    });


$("#rejection_reason").on("keyup",function() {
    $('#rejection_reason').removeClass("is-invalid");

    $("#reject_reason_error").html('');

});


    $('#approve_job_by_sp').click(function(e){
        $('#approve_job_by_sp').prop("disabled", true);
        e.preventDefault();
        //alert();
        $("#reject_reason_error").html('');
        var bm_approove = '<?=$work_orders_details->bm_approove;?>';
        var sp_approove = '<?=$work_orders_details->sp_approove;?>';

        var work_order_id = '<?=$work_orders_details->id;?>';
        var approve_job = $('input[name="approve_job"]:checked').val();
        var proposed_new_date = $('#proposed_new_date_sp').val();
        var worker_id = $('#worker_id5').val();
        var reason = $('#rejection_reason').val();
        var rating = $('.rate-count').html();
        var uniform_specified_by_authority = $('.rate-count2').html();
        var extent_of_cleanliness = $('.rate-count3').html();
        var safety_procedure = $('.rate-count4').html();
        var estimated_price = $('#estimated_price').val();
        var old_worker_id = $('#old_worker_id').val();
        var building_manager_feedback= $('#building_manager_feedback').val();
        var cost = $('#cost').val();
        if(!cost)
        {
            var cost = $('#cost_main').val();
        }
        if(!cost)
        {
            var cost = $('#cost_readonly').val();
        }
        $('#building_manager_feedback').keypress(function(e) {
            var tval = $('#building_manager_feedback').val(),
                tlength = tval.length,
                set = 100,
                remain = parseInt(set - tlength);
            $('#building_manager_feedbackHelp').text(remain);
            if (remain <= 0 && e.which !== 0 && e.charCode !== 0) {
                $('textarea').val((tval).substring(0, tlength - 1))
            }
        });
        if(!cost)
        {
            var cost = '<?=$work_orders_details->cost;?>';
        }
        if(approve_job == 1 && reason == "")
        {
            $('#approve_job_by_sp').prop("disabled", false);
            console.log('error')
            $('#rejection_reason').addClass("is-invalid");
            $("#reject_reason_error").append( `<a class="text-danger"> ${translations.general_sentence.validation.Please_enter_reason} </a>` );

            toastr.error(translations.general_sentence.validation.Please_enter_reason, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
        }
        var user_type = '<?=Auth::user()->user_type;?>';
        if(approve_job == 1 && user_type == "sp_admin" && worker_id == "")
        {
            $('#approve_job_by_sp').prop("disabled", false);
            toastr.error(translations.general_sentence.validation.Please_select_a_Worker, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
        }        
        if(approve_job == 1 && user_type == "supervisor" && worker_id == "")
        {
            $('#approve_job_by_sp').prop("disabled", false);
            toastr.error(translations.general_sentence.validation.Please_select_a_Worker, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
        }
        $.ajax({
            type: 'POST',
            url: "{{route('workorder.approve_job_by_sp')}}",
            data: { _token:'{{ csrf_token() }}', work_order_id: work_order_id, approve_job: approve_job, worker_id: worker_id, reason:reason, proposed_new_date:proposed_new_date, rating:rating, estimated_price:estimated_price, old_worker_id:old_worker_id,cost:cost,building_manager_feedback:building_manager_feedback, bm_approove:bm_approove,sp_approove:sp_approove, uniform_specified_by_authority:uniform_specified_by_authority, extent_of_cleanliness:extent_of_cleanliness, safety_procedure:safety_procedure},
            beforeSend: function( xhr ) {
             xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
             //$("#wait").css("display", "block");
            },
            success: function(data) {
                // alert(data);
                // alert('test');                
                toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});

                location.reload();
            }
        });
    });

    $('#sp_reopen_job').click(function(e){
        $('#sp_reopen_job').prop("disabled", true);
        e.preventDefault();

        var work_order_id = '<?=$work_orders_details->id;?>'; //alert(work_order_id);
        var reopen_option = $('#bm-ro').val();
        var worker_id = $('#worker_id6').val();
        var assigned_to = $('input[name="assigned_to_user_type"]:checked').val();
        var reason = $('#rejection_reason2').val();
        if(!reason)
        {
            var reason = $('#reopen_reason1').val();
        }
        if(reopen_option == 2 && (worker_id == '' && assigned_to == undefined))
        {
            $('#sp_reopen_job').prop("disabled", false);
            toastr.error(translations.general_sentence.validation.Please_select_a_Worker, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
        }
        if(reopen_option == 2 && reason == '')
        {
            $('#sp_reopen_job').prop("disabled", false);
            toastr.error(translations.general_sentence.validation.Please_enter_a_reason, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
        }
        if(reopen_option == 1 && reason == '')
        {
            $('#sp_reopen_job').prop("disabled", false);
            toastr.error(translations.general_sentence.validation.Please_enter_a_reason, translations.general_sentence.validation.warning, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});exit;
        }
        $.ajax({
            type: 'POST',
            url: "{{route('workorder.sp_reopen_job')}}",
            data: { _token:'{{ csrf_token() }}', work_order_id: work_order_id, reopen_option: reopen_option, worker_id: worker_id, reason:reason, assigned_to:assigned_to},
            beforeSend: function( xhr ) {
             xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
             //$("#wait").css("display", "block");
            },
            success: function(data) {
                //alert('test');
                toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
                location.reload();
            }
        });
    });

    $('#bm_accept_reject_reopen').click(function(e){
        e.preventDefault();
        $('#bm_accept_reject_reopen').prop("disabled", true);
        var work_order_id = '<?=$work_orders_details->id;?>'; //alert(work_order_id);
        var accept_reject_bm = $('input[name="accept_reject_bm"]:checked').val();
        // @flip1@ couse reason is going to null || 17-BM rejects SP reopening WO
        var reason = $('#rejection_reason3_bm').val();
        $.ajax({
            type: 'POST',
            url: "{{route('workorder.bm_accept_reject_reopen')}}",
            data: { _token:'{{ csrf_token() }}', work_order_id: work_order_id, accept_reject_bm: accept_reject_bm, reason:reason},
            beforeSend: function( xhr ) {
             xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
             //$("#wait").css("display", "block");
            },
            success: function(data) {
                //alert('test');
                toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
                location.reload();
            }
        });
    });



    $('#approve_job1').click(function(e){
        $('.building_manager_feedback').show()
    });



    $('#approve_job2').click(function(e){
        $('.building_manager_feedback').hide()
    });

    $('#sp_action_on_rejects_wo_by_bm').click(function(e){
        e.preventDefault();

        var work_order_id = '<?=$work_orders_details->id;?>'; //alert(work_order_id);
        var reject_bm = $('#bm-radio-02').val();

        if(reject_bm == 1){
            var reason = $('#rejection_reason6').val();

        }
        else{
            var reason = $('#rejection_reason3').val();

        }

        var worker_id = $('#reject_wo_worker_id').val();
        var assigned_to = $('#assigned_to_user').val();
        $.ajax({
            type: 'POST',
            url: "{{route('workorder.sp_action_on_rejects_wo_by_bm')}}",
            data: { _token:'{{ csrf_token() }}', worker_id:worker_id, work_order_id: work_order_id, reject_bm: reject_bm, reason:reason, assigned_to:assigned_to},
            beforeSend: function( xhr ) {
             xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
             //$("#wait").css("display", "block");
            },
            success: function(data) {
                //alert('test');
                toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
                location.reload();
            }
        });
    });



    $('#bm_action_on_rejects_wo_by_sp').click(function(e){
        e.preventDefault();

        var work_order_id = '<?=$work_orders_details->id;?>'; //alert(work_order_id);
        var reject_bm = $('#bm-radio-04').val();
        var reason = $('#rejection_reason4').val();
        var estimated_price = $('#estimated_price_bm').val();
        var rating = $('#rate-count-bm').html();
        //console.log(rating);

        $.ajax({
            type: 'POST',
            url: "{{route('workorder.bm_action_on_rejects_wo_by_sp')}}",
            data: { _token:'{{ csrf_token() }}', rating:rating, estimated_price:estimated_price, work_order_id: work_order_id, reject_bm: reject_bm, reason:reason},
            beforeSend: function( xhr ) {
             xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
             //$("#wait").css("display", "block");
            },
            success: function(data) {
                //alert('test');
                toastr.success(translations.general_sentence.validation.Status_updated_Successfully, translations.general_sentence.validation.Success, {timeOut: 5000, positionClass: "toast-top-center",progressBar: true});
                location.reload();
            }
        });

    });


});

$(document).ready(function() {
    //$('.reason').hide();
    if($('input[name="approve_proposed_date"]:checked').val() == 1)
    {
        $(".suggested_date").show();
        $(".reason").show();
        $(".worker").show();

    }
    $("input[name='approve_proposed_date']").click(function() {
        var val = $(this).val();
        if(val == 1)
        {
            $(".suggested_date").show();
            $(".reason").show();
            $(".worker").show();
            
        }
        else
        {
            $('.approve_reject_sp_date').prop('disabled', false)
            $(".suggested_date").hide();
            $(".reason").hide();
            $(".worker").hide();
        }

        
    });

    // if($('input[name="bm_action"]:checked').val() == 1)
    // {

    //     $(".reason").show();
    //     $(".rating").hide();
    // }
    $("#bm-radio-04").change(function() {
        var val = $(this).val();
        if(val == 1)
        {
            $(".reason_sp").show();
            $(".rating").hide();
        }
        else
        {
           $(".reason_sp").hide();
            $(".rating").show();
        }
    });

    $("#bm-ro").change(function() {
        var val = $(this).val();
        if(val == 1)
        {
            $("#send_back").hide();
            $("#respond_bm").show();
        }
        else
        {
            $("#send_back").show();
            $("#respond_bm").hide();
        }
    });



    if($('input[name="approve_job"]:checked').val() == 1)
    {
        $("#cost_receipt").hide();
        $(".rejection_reason5").show();
        $(".worker_id5").show();
    }
    else
    {
        $("#cost_receipt").show();
        $(".rating").show();
        $(".estimated_price").show();
    }
    $("input[name='approve_job']").click(function() {
        var val = $(this).val();
        if(val == 1)
        {
            $("#cost_receipt").hide();
            $(".rejection_reason5").show();
            $(".worker_id5").show();
            $(".rating").hide();
            $(".estimated_price").hide();
        }
        else
        {
            $("#cost_receipt").show();
            $(".rejection_reason5").hide();
            $(".worker_id5").hide();
            $(".rating").show();
            $(".estimated_price").show();
        }
    });
    
});
</script>

<script>
// Set the date we're counting down to




function addDays(date, days) {
  var result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

var date = new Date();
@if($show_counter != 'no')


$(function() {
    var wo_reminder_sent_on = '<?=$work_orders_details->wo_reminder_sent_on_formatted;?>';
    var workorder_reminder_periods = '<?= $workorder_reminder_periods;?>';
    var existing_reminder_period = '<?=$work_orders_details->wo_reminder_period;?>';
    if(existing_reminder_period){
        var testtargetDate  = new Date(wo_reminder_sent_on);

        var d2 = new Date();
        var local1 = d2.getTime();
        var offset1 = d2.getTimezoneOffset() * (60 * 1000);
        var utc1 = new Date(local1 + offset1);
        var testnow = new Date(utc1.getTime() + (3 * 60 * 60 * 1000));
        var testtomorrow = addDays(testtargetDate,parseInt(existing_reminder_period));
        if(testtomorrow < testnow){
            var workorder_reminder_periods = existing_reminder_period;
        }
        else{
            var workorder_reminder_periods = '<?= $workorder_reminder_periods;?>';
        }

    }
    else{
        var workorder_reminder_periods = '<?= $workorder_reminder_periods;?>';
    }
    wo_reminder_sent_on = wo_reminder_sent_on.replace(/-/g, "/");
     var targetDate  = new Date(wo_reminder_sent_on);
     var d1 = new Date();
     var local = d1.getTime();
     var offset = d1.getTimezoneOffset() * (60 * 1000);
     var utc = new Date(local + offset);
     var now = new Date(utc.getTime() + (3 * 60 * 60 * 1000));//riyadh current time
     var now1 = new Date(utc.getTime() + (3 * 60 * 60 * 1000));//riyadh current time
     var tomorrow1 = addDays(targetDate,parseInt(workorder_reminder_periods));

    //  var now   = new Date();
    if(targetDate){
    window_days_cal = workorder_reminder_periods == 1 ? 00 : daysBetween(now, tomorrow1)-1;

    window.days = window_days_cal > 0 ? window_days_cal : 0;
    }

    var secondsLeft = secondsDifference(now, targetDate);
    window.hours = Math.floor(secondsLeft / 60 / 60);
    secondsLeft = secondsLeft - (window.hours * 60 * 60);
    window.minutes = Math.floor(secondsLeft / 60 );
    secondsLeft = secondsLeft - (window.minutes * 60);
      if( tomorrow1 < now)
      {
       $('#counter_div').addClass('d-none');
       $('#button_div').removeClass('d-none');

      }
      else{
        $('#button_div').addClass('d-none');
        $('#counter_div').removeClass('d-none');
      }

  console.log(targetDate);
  console.log(now);
  console.log(window.days);
  console.log(window.hours);
  console.log(window.minutes);
  window.seconds = Math.floor(secondsLeft);
//   alert(workorder_reminder_periods);
//   alert(targetDate)
//   alert(tomorrow1);
  if(new Date(tomorrow1)  > new Date() ){
    startCountdown(targetDate,workorder_reminder_periods,now);
}

});

@else
$('#counter_div').addClass('d-none');
       $('#button_div').removeClass('d-none');
@endif
var interval;

function daysBetween( date1, date2 ) {
  //Get 1 day in milliseconds
  var one_day=1000*60*60*24;

  // Convert both dates to milliseconds
  var date1_ms = date1.getTime();
  var date2_ms = date2.getTime();

  // Calculate the difference in milliseconds
  var difference_ms = date2_ms - date1_ms;
    
  // Convert back to days and return
  return Math.round(difference_ms/one_day); 
}

function secondsDifference( date1, date2 ) {
  //Get 1 day in milliseconds
  var one_day=1000*60*60*24;

  // Convert both dates to milliseconds
  var date1_ms = date1.getTime();
  var date2_ms = date2.getTime();
  var difference_ms = date2_ms - date1_ms;
  var difference = difference_ms / one_day; 
  var offset = difference - Math.floor(difference);
  return offset * (60*60*24);
}



function startCountdown(targetDate,workorder_reminder_periods,now) {
  $('#input-container').hide();
  $('#countdown-container').show();
  var tomorrow = new Date();
    
  tomorrow.setDate(targetDate.getDate() + workorder_reminder_periods );

  
    if(new Date(tomorrow)  > new Date() )
    {
        displayValue('#js-days', window.days);
        displayValue('#js-hours', window.hours);
        displayValue('#js-minutes', window.minutes);
        displayValue('#js-seconds', window.seconds);

        interval = setInterval(function() {
            if (window.seconds > 0) {
            window.seconds--;
            displayValue('#js-seconds', window.seconds);
            } else {
            // Seconds is zero - check the minutes
            if (window.minutes > 0) {
                if(window.minutes != 0){
                window.minutes--;
                }
                window.seconds = 59;
                updateValues('minutes');
            } else {
                // Minutes is zero, check the hours
                if (window.hours > 0)  {
                if(window.hours != 0){
                window.hours--;
                }
                window.minutes = 59;
                window.seconds = 59;
                updateValues('hours');
                } else {
                // Hours is zero
                if(window.days > 0){
                    window.days--;
                }
                window.hours = 23;
                window.minutes = 59;
                window.seconds = 59;
                updateValues('days');
                }
                // $('#js-countdown').addClass('remove');
                // $('#js-next-container').addClass('bigger');
            }
            }
        }, 1000);
    }
    else{
    window.hours = 0;
    window.minutes =0;
    window.days = 0;
    displayValue('#js-days', window.days);
    displayValue('#js-hours', window.hours);
    displayValue('#js-minutes', window.minutes);
    displayValue('#js-seconds', window.seconds);
    }
}


function updateValues(context) {
  if (context === 'days') {
    displayValue('#js-days', window.days);
    displayValue('#js-hours', window.hours);
    displayValue('#js-minutes', window.minutes);
    displayValue('#js-seconds', window.seconds);
  } else if (context === 'hours') {
    displayValue('#js-hours', window.hours);
    displayValue('#js-minutes', window.minutes);
    displayValue('#js-seconds', window.seconds);
  } else if (context === 'minutes') {
    displayValue('#js-minutes', window.minutes);
    displayValue('#js-seconds', window.seconds);
  }
}

function displayValue(target, value) {
  var newDigit = $('<span></span>');
  if(target == '#js-days' && value != undefined)
  {
      if(value.toString().length>2)
      {
        $(newDigit).text(value).addClass('new');        
      }
      else
      {
        $(newDigit).text(pad(value)).addClass('new');
      }
  }
  else
  {
    $(newDigit).text(pad(value))
    .addClass('new');
  }
  
  $(target).prepend(newDigit);
  $(target).find('.current').addClass('old').removeClass('current');
  setTimeout(function() {
    $(target).find('.old').remove();
    $(target).find('.new').addClass('current').removeClass('new');
  }, 900);
}

function pad(number) {
  return ("0" + number).slice(-2);
}
    //$('#modal-approve-date').modal("show");
    $('#datetimepicker1, #datepicker16, #datepicker15, #sbn_calender_01').datetimepicker({
        //language:  'fr',
        dateFormat: 'dd-mm-yyyy HH:ii P',
        format: 'dd-mm-yyyy HH:ii P',
        weekStart: 1,
        todayBtn:  1,
        autoclose: 1,
        todayHighlight: 1,
        startView: 2,
        forceParse: 0,
        showMeridian: 1,
        startDate: new Date()
    });

    /**Delete work order */
    $(document).on("click", ".delete_workorder_bm", function () {
        var $ele = $(this).parent().parent();
        var id = $(this).data("value");
        var url = "delete-work-order-bm";
        var dltUrl = url + "/" + id;
        //alert(dltUrl);
        swal({
            title: translations.general_sentence.modal.warning_title,
            text: translations.general_sentence.modal.delete_warning,
            icon: "warning",
            buttons: true,
            dangerMode: true,
            showCancelButton: true,

            confirmButtonColor: '#ff4d4f',
            confirmButtonText: translations.general_sentence.swal_buttons.work_order_delete,
            cancelButtonText: translations.general_sentence.swal_buttons.work_order_cancel,

            /*buttons: [
                translations.general_sentence.swal_buttons.cancel,
                translations.general_sentence.swal_buttons.confirm,
            ],*/
        },function(willDelete) {

            if (willDelete) {
                //$.LoadingOverlay("show");
                $.ajax({
                    url: dltUrl,
                    type: "GET",

                    data: {
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },
                    success: function (data) {
                       // getPosts();
                        toastr.success(
                            "",
                            translations.general_sentence.tostr_lang
                                .success_deleted,
                            // translations.general_sentence.tostr_lang.success,
                            {
                                timeOut: 2000,
                                positionClass: "toast-top-center",
                                // progressBar: true,
                            }
                        );
                        setTimeout(function () {
                            //history.go(-1);
                            location.reload();
                        }, 2000);
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        //$.LoadingOverlay("hide");
                        var response = jqXHR.responseJSON;
                        var status = jqXHR.status;
                        if (status == "404") {
                            toastr.error(
                                translations.general_sentence.tostr_lang
                                    .invalid_url,
                                translations.general_sentence.tostr_lang.error,
                                { timeOut: 2000 }
                            );
                        } else {
                            toastr.error(
                                translations.general_sentence.tostr_lang
                                    .internal_server_error,
                                translations.general_sentence.tostr_lang.error,
                                { timeOut: 2000 }
                            );
                        }
                    },
                });
            }
        });
    });

    // send wo reminder to sp
    $(document).on("click", ".send_reminder_to_sp", function () {

        var id = $(this).data("value");
        var url = '<?=substr(url()->full(), 0, strpos(url()->full(), "/work-order"));?>'+"/work-order/send-reminder";
        
        var dltUrl = url + "/" + id;
        $.ajax({
                    url: dltUrl,
                    type: "GET",

                    data: {
                        _token: $('meta[name="csrf-token"]').attr("content"),
                        id: id,
                    },
                    success: function (data) {
                        if(data.status == true || data.status == 'true')
                        {
                            toastr.success(
                                "",
                                translations.work_order.button.Email_and_Notification_has_been_sent_to_service_provider,
                                // translations.general_sentence.tostr_lang.success,
                                {
                                    timeOut: 2000,
                                    positionClass: "toast-top-center",
                                    // progressBar: true,
                                }
                            );
                            setTimeout(function () {
                            //history.go(-1);
                            location.reload();
                        }, 1000);
                        }
                        else
                        {
                            toastr.error("",
                            data.message,
                                { timeOut: 2000 }
                            );
                        }
                        
                        setTimeout(function () {
                            //history.go(-1);
                            location.reload();
                        }, 1000);
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        //$.LoadingOverlay("hide");
                        var response = jqXHR.responseJSON;
                        var status = jqXHR.status;
                        if (status == "404") {
                            toastr.error(
                                translations.general_sentence.tostr_lang
                                    .invalid_url,
                                translations.general_sentence.tostr_lang.error,
                                { timeOut: 2000 }
                            );
                        } else {
                            toastr.error(
                                translations.general_sentence.tostr_lang
                                    .internal_server_error,
                                translations.general_sentence.tostr_lang.error,
                                { timeOut: 2000 }
                            );
                        }
                    },
                });
    });
    </script>
    <style type="text/css">
        .datetimepicker
        {
            display: none;
        }
        .new_suggested_date
        {
            color:black !important;
        }
    </style>


    <script type="text/javascript">
        function chatImageClick(elm){
            console.log(this);
            console.log('image click');

            $('.close-img').show();
			$('.image-zoom').fadeIn().addClass('d-flex');
			// var $img = $(this).clone();
			var $img = elm.cloneNode();
			$('.image-zoom').append($img);
			$('.image-zoom').find('.view-img').removeClass('view-img');
            $('.image-zoom img').removeAttr("onclick"); 

        }
        $(document).ready(function(){ 
             $(document).on("click", ".rotate-img", function(){
                $('.image-zoom img').toggleClass("rotate1");
                //$(this).toggleClass("rotate-again");
                $(this).removeClass("rotate-img").addClass("rotate-img-1");   
            });
            $(document).on("click", ".rotate-img-1", function(){
                $('.image-zoom img').removeClass("rotate1").addClass("rotate2");
                $(this).removeClass("rotate-img-1").addClass("rotate-img-2");
            });
            $(document).on("click", ".rotate-img-2", function(){
                $('.image-zoom img').removeClass("rotate2").addClass("rotate3");
                $(this).removeClass("rotate-img-2").addClass("rotate-img-3");;
            });
            $(document).on("click", ".rotate-img-3", function(){
                $('.image-zoom img').removeClass("rotate3").addClass("rotate4");
                $(this).removeClass("rotate-img-3").addClass("rotate-img-4");
            });
            $(document).on("click", ".rotate-img-4", function(){
                $('.image-zoom img').removeClass("rotate4").addClass("rotate1");
                $(this).removeClass("rotate-img-4").addClass("rotate-img-1");
            });
            $('.close-img').click(function () {
                $(this).parent().fadeOut().removeClass('d-flex');
                $('.image-zoom').find('img').remove();
            });
            });

	$(document).ready(function () {
		$('input[type="radio"]').click(function () {
			if ($(this).attr('name') == 'start_job') {
                var id = $(this).attr('id');
				$('.show-radio').removeClass('active');
				$('.show-radio').hide();
				$("." + id).addClass('active');
				$("." + id).show();
			}
			
		});
        // $('input[type="radio"]').click(function () {
        //     if ($(this).attr('name') == 'start_job') {
        //         $('.show-radio').removeClass('active');
        //         $('.show-radio').hide();
        //         $(this)
        //             .parent()
        //             .parent()
        //             .parent()
        //             .find('.show-radio')
        //             .addClass('active');
        //         $(this).parent().parent().parent().find('.show-radio').show();
        //     }
        //     // else {
        //     //      $('#show-me').hide();
        //     // }
        // });
		
        $("#bm-radio-02").change(function () {
			if ($(this).val() == '1') {
				$('#worker_block')
					.removeClass('active');
                $('#resend_to_BM')
					.addClass('active');
            }else{
				$('#worker_block')
					.addClass('active');
                $('#resend_to_BM')
					.removeClass('active');
				//$(this).parent().parent().parent().find('.show-radio-div').show();
			}
		});

		$('.change-worker').hide();
		$("input[name$='complete_job']").click(function () {
			var test = $(this).val();
			if (test == 'other_options') {
				$('.change-worker').show();
				$('.complete-job-by-sp').hide();
			} else {
				$('.change-worker').hide();
				$('.complete-job-by-sp').show();
			}
		});
        
        $("#sp-work-order2 form").submit(function (event) {
            event.preventDefault(); // Prevent default form submission

            var workOrderId = "{{ $work_orders_details->id }}";
            var url = "{{ route('workorder.assign-worker-for-scheduled', ':id') }}";
            url = url.replace(':id', workOrderId);

            var formData = new FormData(this); // Get all form data
            formData.append('_token', '{{ csrf_token() }}');
           // formData.append('start_job',2);
            formData.append('work_order_id', "{{$work_orders_details->id}}");

            // Disable the update button to prevent multiple submissions
            $(".update_button_wo").attr("disabled", true);

            // Send the AJAX request
            $.ajax({
                type: 'POST',
                url: url,
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                    toastr.success(
                        translations.general_sentence.validation.Status_updated_Successfully,
                        translations.general_sentence.validation.Success,
                        { timeOut: 5000, positionClass: "toast-top-center", progressBar: true }
                    );
                    location.reload();
                },
                error: function (xhr, status, error) {
                    toastr.error(
                        translations.general_sentence.validation.Something_went_wrong,
                        translations.general_sentence.validation.Error,
                        { timeOut: 5000, positionClass: "toast-top-center", progressBar: true }
                    );
                    $(".update_button_wo").attr("disabled", false);
                }
            });
        });
        

	});	//end of document ready

      
    $(".view-img div.wo-img").click(function(){ //alert();
    $(".close-img").show();
    $(".image-zoom").fadeIn().addClass("d-flex");
    $(".image-zoom div").removeClass("w-100");
    
  $('.image-zoom').append('<div id="testzoom"></div>');
  let qrcode6 = new QRCode(document.getElementById("testzoom"),
    {
    text: document.getElementById("previousAssetQr").dataset.url
    ,
    width: 250,
    height: 250,
    colorDark : "#000000",
    colorLight : "#ffffff",
    correctLevel : QRCode.CorrectLevel.H
    });  
  
  $('.image-zoom').find(".view-img").removeClass("view-img");
});



	$('#file_upload').on('change', function (event) {
		//alert();
        $("#complete_job_error1").html('');
		var error_message = '';
		var file_type_error = false;
		var fileInput = document.getElementById('file_upload');

		var filePath = fileInput.value;
		//console.log(filePath);
		// Allowing file type
		var allowedExtensions = /(\.jpg|\.jpeg|\.png|\.pdf)$/i;

		if (!allowedExtensions.exec(filePath)) {
			//alert('Invalid file type');
			file_type_error = true;
			if (file_type_error == true) {
				error_message =
					translations.general_sentence.validation
						.Please_upload_only_jpg_jpeg_png_pdf_image;
				swal({
					title: '',
					title: error_message,
					// text: error_message,
					buttons: true,
					dangerMode: true,
                    confirmButtonText:  translations.general_sentence.swal_buttons.ok,
				});
			}
			fileInput.value = '';
			return false;
		}

		var files = document.getElementById('file_upload').files;
		var file_size_error = false;
		var file_type_error = false;

		//console.log(files)
		var files = event.target.files;
		var output = document.getElementById('preview');
		var supported_types = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
		var li_count = $('.atbd-upload__file ul li').length; //alert(li_count);
		var total_files = files.length + li_count -1;
        //alert(files.length);
		if (total_files > 3) {
			//document.getElementById('file_upload').value = '';
			var error_message = '';
			error_message =
				translations.general_sentence.validation
					.File_max_upload_not_more_than_3;
			swal({
				title: error_message,
            // text: error_message,
            icon: "warning",
            buttons: true,
            confirmButtonText: translations.general_sentence.button_and_links.ok,
            dangerMode: true,
			});
			return false;
		} else {
			// Loop through the FileList and render image files as thumbnails.
			for (var i = 0, f; (f = files[i]); i++) {
				var file_type = files[i].type;
				var reader = new FileReader();
				if (!supported_types.includes(file_type)) {
					file_type_error = true;
				}
				if (file_size_error == true || file_type_error == true) {
					document.getElementById('file_upload').value = '';
					var error_message = '';

					if (file_size_error == true && file_type_error == true) {
						error_message =
							translations.general_sentence.validation
								.Please_upload_only_jpg_jpeg_png_pdf_image_of_max_size_1mb;
					} else if (
						file_size_error == true &&
						file_type_error == false
					) {
						error_message =
							translations.general_sentence.validation
								.File_size_should_not_be_more_than_1_mb;
					} else {
						error_message =
							translations.general_sentence.validation
								.Please_upload_only_jpg_jpeg_png_pdf_image;
					}
					swal({
						title: error_message,
						// text: error_message,
						icon: 'warning',
						buttons: true,
						dangerMode: true,
                        confirmButtonText:  translations.general_sentence.swal_buttons.ok,
					});
				}

				// Closure to capture the file information.
				reader.onload = (function (theFile) {
					return function (e) {
						// Render thumbnail.
                        if(theFile.type == "application/pdf")
                        {
                            var src = "{{ asset('img/pdf.png')}}"                            
                        }
                        else
                        {
                            var src = URL.createObjectURL(theFile)
                        }                      
                        console.log(src)
						var imgThumbnailElem =
							"<li class='mb-0'><a class='file-name'><i class='las la-paperclip'></i> <img src="+ src +" height='50' width='50'> <span class='name-text' id='file_name'><span>" +
							theFile.name +
							"</span></span></a><a class='btn-delete'></i><div class='' onclick='removeThumbnailIMG(this,`" +
							theFile.name +
							"`)'  ><i class='la la-trash'></i></a></li>";

						output.innerHTML = output.innerHTML + imgThumbnailElem;
					};
				})(f);

				// Read in the image file as a data URL.
				reader.readAsDataURL(f);
				window.filesForUpload.push(f);
			}
			//  document.getElementById("file_upload").value = filesForUpload;

			console.log(window.filesForUpload);
			checkcountfilesforupload();
		}
	});

	function removeThumbnailIMG(elm, filename) {
		//    elm.parentNode.outerHTML='';
		filesForUpload = window.filesForUpload;
		var fileName = filename;
		// loop through the files array and check if the name of that file matches FileName
		// and get the index of the match
		for (i = 0; i < filesForUpload.length; ++i) {
			if (filesForUpload[i].name == fileName) {
				//console.log("match at: " + i);
				// remove the one element at the index where we get a match
				filesForUpload.splice(i, 1);
			}
		}
		console.log(filesForUpload);
		window.filesForUpload = filesForUpload;
		//console.log(filesToUpload);
		// remove the <li> element of the removed file from the page DOM
		elm.parentNode.outerHTML = '';

		checkcountfilesforupload();
	}

	function checkcountfilesforupload() {
		if (window.filesForUpload.length >= 4) {
			$('.atbd-upload__button').hide();
		} else {
			$('.atbd-upload__button').show();
		}
	}
    $('#openpopup').click(function () {
        $("#previousAssetQr").html('');
        var qrcode5 = new QRCode(document.getElementById("previousAssetQr"),
        {
            text: document.getElementById("previousAssetQr").dataset.url
            ,
            width: 100,
            height: 100,
            colorDark : "#000000",
            colorLight : "#ffffff",
            correctLevel : QRCode.CorrectLevel.H
        });
    });
  
</script>

<script type="text/javascript">
        console.log('testing1234')
    $(document).ready(function() {
        $("#asset_category_id1").select2({
            placeholder:translations.data_properties.property_forms.place_holder.Choose_Category,
            dropdownCssClass: "tag",
            language: { noResults: () => translations.general_sentence.validation.No_results_found,},
        });
    });
    $('input[type=radio][name=file-type]').change(function() {
    if (this.value == 'csv') {
        $('#pdfTypeDiv').hide();
    }
    else {
        $('#pdfTypeDiv').show();
    }
    });

    /* Star Rating Read Only */
    $(".rater2").starRating({
        emptyColor: "#C6D0DC",
        hoverColor: "#FA8B0C",
        ratedColor: "#FA8B0C",
        activeColor: "#FA8B0C",
        useFullStars: true,
        initialRating: 5,
        minRating:1,
        starSize: 12,
        strokeWidth: 6,
        disableAfterRate: false,
        onHover: function (currentIndex, currentRating, $el) {
           $(".rate-count2").text(currentIndex);
        },
        onLeave: function (currentIndex, currentRating, $el) {
            $(".rate-count2").text(currentRating);
        },
    });
    /* Star Rating Read Only */
    $(".rater3").starRating({
        emptyColor: "#C6D0DC",
        hoverColor: "#FA8B0C",
        ratedColor: "#FA8B0C",
        activeColor: "#FA8B0C",
        useFullStars: true,
        initialRating: 5,
        minRating:1,
        starSize: 12,
        strokeWidth: 6,
        disableAfterRate: false,
        onHover: function (currentIndex, currentRating, $el) {
           $(".rate-count3").text(currentIndex);
        },
        onLeave: function (currentIndex, currentRating, $el) {
            $(".rate-count3").text(currentRating);
        },
    });
    /* Star Rating Read Only */
    $(".rater4").starRating({
        emptyColor: "#C6D0DC",
        hoverColor: "#FA8B0C",
        ratedColor: "#FA8B0C",
        activeColor: "#FA8B0C",
        useFullStars: true,
        initialRating: 5,
        minRating:1,
        starSize: 12,
        strokeWidth: 6,
        disableAfterRate: false,
        onHover: function (currentIndex, currentRating, $el) {
           $(".rate-count4").text(currentIndex);
        },
        onLeave: function (currentIndex, currentRating, $el) {
            $(".rate-count4").text(currentRating);
        },
    });
    document.allRequestedItems = '<?php echo json_encode($workOrderRequestedItems); ?>';
    document.missinItems = '<?php echo json_encode($allItems); ?>';
    document.missinItems = JSON.parse(document.missinItems);
    document.allRequestedItemsBySp = '<?php echo json_encode($requestedItemsBySp ?? [],JSON_HEX_APOS); ?>';
    document.user_type = '{{AUTH::user()->user_type}}';
    @if(isset($workOrderItemRequestBySp) && isset($workOrderItemRequestBySp->status))
     $('#missing-item-selection-button-div').hide() 
    @endif
</script>

@endsection
