@extends('layouts.app')
@section('content')
<div class="contents">
   <div class="container-fluid">
      <div class="row">
         <div class="col-lg-12">
            <div class="page-title-wrap">
               <div class="page-title d-flex justify-content-between">
                  <div class="page-title__left">
                     <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                        <h4 class="text-capitalize fw-500 breadcrumb-title">
                           <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a> {{__('user_management_module.bread_crumbs.add_user')}}
                        </h4>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="container-fluid">
      <div class=" checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
         <div class="row justify-content-center">
            <div class="col-xl-8">
               <div class="checkout-progress-indicator content-center">
                  <div class="checkout-progress">
                     <div class="step completed" id="1">
                        <span class="las la-check"></span>
                        <span>{{__('user_management_module.common.user_info')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/green.svg') }}" alt="img" class="svg"></div>
                     <div class="step current" id="2">
                        <span>2</span>
                        <span>{{__('user_management_module.common.user_previleges')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="3">
                        <span>3</span>
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>
                  </div>
               </div>
               <!-- checkout -->
               <div class="justify-content-center">
                  <div class="">
                     <div class="card checkout-shipping-form pt-2 pb-30 px-sm-30 border-0">
                        <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0">
                           <h4 class="fw-400">{{__('user_management_module.common.user_previleges')}}</h4>
                        </div>
                        <div class="card-body px-0 pb-0">
                           <div class="edit-profile__body">
                              <form method="post" id="workspace_privileges" action="{{ route('workspace.admin.store')}}" enctype="multipart/form-data">
                                 @csrf
                                 <table class="table table-social">
                                    <tbody>
                                       <tr>
                                          <td></td>
                                          <td>{{__('user_management_module.user_previleges.show')}}</td>
                                          <td>{{__('user_management_module.user_previleges.hide')}}</td>
                                       </tr>
                                       <tr>
                                          <td>
                                             {{__('user_management_module.user_previleges.public_service_providers')}}
                                          </td>
                                          <div class="public_service_providers_check require">                           
                                             <td>
                                                <div class="checkbox-theme-default custom-checkbox ">
                                                   <input class="checkbox" type="checkbox" id="public_service_providers_show" name="public_service_providers[]" value="show">
                                                   <label for="public_service_providers_show">
                                                   <span class="checkbox-text">
                                                   </span>
                                                   </label>
                                                </div>
                                             </td>
                                             <td>
                                                <div class="checkbox-theme-default custom-checkbox ">
                                                   <input class="checkbox noview" type="checkbox" id="public_service_providers_hide"  name="public_service_providers[]" value="hide" >
                                                   <label for="public_service_providers_hide">
                                                   <span class="checkbox-text">
                                                   </span>
                                                   </label>
                                                </div>
                                             </td>
                                          </div>
                                       </tr>

                                       <tr>
                                          <td>
                                             {{__('user_management_module.user_previleges.client_requests')}}
                                          </td>                                          
                                          <td>
                                             <div class="checkbox-theme-default custom-checkbox ">
                                                <input class="checkbox" type="checkbox" id="client_requests_show" name="client_requests[]" value="show">
                                                <label for="client_requests_show">
                                                <span class="checkbox-text">
                                                </span>
                                                </label>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="checkbox-theme-default custom-checkbox ">
                                                <input class="checkbox noview" type="checkbox" id="client_requests_hide" name="client_requests[]" value="hide">
                                                <label for="client_requests_hide">
                                                <span class="checkbox-text">
                                                </span>
                                                </label>
                                             </div>
                                          </td>
                                       </tr>

                                       <tr>
                                          <td>
                                             {{__('user_management_module.user_previleges.edit_project')}}
                                          </td>                                          
                                          <td>
                                             <div class="checkbox-theme-default custom-checkbox ">
                                                <input class="checkbox" type="checkbox" id="edit_project_show" name="edit_project[]" value="show">
                                                <label for="edit_project_show">
                                                <span class="checkbox-text">
                                                </span>
                                                </label>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="checkbox-theme-default custom-checkbox ">
                                                <input class="checkbox noview" type="checkbox" id="edit_project_hide" name="edit_project[]" value="hide">
                                                <label for="edit_project_hide">
                                                <span class="checkbox-text">
                                                </span>
                                                </label>
                                             </div>
                                          </td>
                                       </tr>

                                       <tr class="mt-0 mb-0">
                                          <td></td>
                                          <td colspan="4">
                                          </td>
                                       </tr>
                                    </tbody>
                                 </table>
                                 <div><span id="service_provider_error"></span></div>
                                 <div class="button-group d-flex pt-25 justify-content-end">
                                    <a href="javascript:history.back()" class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md">{{__('user_management_module.user_button.back')}}</a>
                                    <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 previleg_btn">{{__('user_management_module.user_button.save_next')}}
                                    </button>
                                 </div>
                              </form>
                           </div>
                        </div>
                     </div>
                     <!-- ends: card -->
                  </div>
                  <!-- ends: col -->
               </div>
            </div>
            <!-- ends: col -->
         </div>
      </div>
      <!-- End: .global-shadow-->
   </div>
</div>
@endsection
@section('scripts')
<script type="text/javascript" src="{{asset('js/admin/users/create-privileges.js')}}"></script>
<script type="text/javascript">
   $("#emp_region,#emp_city,#emp_asset_cat").select2({
        placeholder:translations.configration_checklist.checklist_forms.place_holder.please_choose,
        dropdownCssClass: "tag",
        allowClear: true,
        language: {
            noResults: function () {
                return translations.general_sentence.validation.No_results_found;
            }
        }
    });

    $("#workspace_privileges").validate({
        ignore: "input[type=hidden]",
        rules: {
            "public_service_providers[]": {
                required: true,
                minlength: 1,
            },
            "client_requests[]": {
                required: true,
                minlength: 1,
            },
            "edit_project[]": {
                required: true,
                minlength: 1,
            }
        },
        messages: {
            "public_service_providers[]":translations.general_sentence.validation.please_select_at_least_one_checkbox_of_public_service_provider,
            "client_requests[]": translations.general_sentence.validation.please_select_at_least_one_checkbox_of_client_requests,
            "edit_project[]": translations.general_sentence.validation.please_select_at_least_one_checkbox_of_edit_project
        },
        errorPlacement: function (error, element) {
            error.addClass("invalid-feedback");
            if (element.attr("name") == "public_service_providers[]") {
                error.appendTo($("#service_provider_error"));
            } else if (element.attr("name") == "client_requests[]") {
                error.appendTo($("#service_provider_error"));
            } else if (element.attr("name") == "edit_project[]") {
                error.appendTo($("#service_provider_error"));
            } else {
                error.insertAfter(element);
            }
        },
        highlight: function (element, errorClass, validClass) {},
        unhighlight: function (element, errorClass, validClass) {},
        submitHandler: function (form) {
            form.submit();
        },
    });

    $("#public_service_providers_show").click(function () {
        $("input:checkbox#public_service_providers_hide").prop("checked", false);
    });
    $("#public_service_providers_hide").click(function () {
        $("input:checkbox#public_service_providers_show").prop("checked", false);
    });
    $("#client_requests_show").click(function () {
        $("input:checkbox#client_requests_hide").prop("checked", false);
    });
    $("#client_requests_hide").click(function () {
        $("input:checkbox#client_requests_show").prop("checked", false);
    });

   $("#edit_project_show").click(function () {
      $("input:checkbox#edit_project_hide").prop("checked", false);
   });
   $("#edit_project_hide").click(function () {
      $("input:checkbox#edit_project_show").prop("checked", false);
   });
</script>
@endsection