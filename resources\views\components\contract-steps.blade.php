@php
    $steps = [
        1 => __('advance_contracts.contract_steps.step_one'),
        2 => __('advance_contracts.contract_steps.step_two'),
        3 => __('advance_contracts.contract_steps.step_three'),
        4 => __('advance_contracts.contract_steps.step_four'),
        5 => __('advance_contracts.contract_steps.step_five'),
        6 => __('advance_contracts.contract_steps.step_six'),
    ];
@endphp

<div class="contents crm">
    <div class="container-fluid">
        <div class="col-lg-12">
            <div class="row justify-content-sm-between align-items-center justify-content-center flex-sm-row flex-column">
                <div class="page-title-wrap d-flex align-items-center gap-10">
                <a href="Javascript:history.back()" class="btn btn-white btn-default text-center svg-20 wh-40 px-0"><i class="iconsax m-0" icon-name="chevron-left-square"></i></a>
                <div>
                    <div class="page-title d-flex justify-content-between">
                        <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                            <div class="user-member__title mr-sm-25 ml-0">
                                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                    {{$title}}
                                </h4>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="checkout wizard10 global-shadow mb-30 radius-xl w-100">
            <div class="row">
                <div class="col-lg-4 prperty_steps_container">
                    <div class="border-0 p-0 card">
                        <div class="checkout-progress3 property_progress card-body">
                        @foreach ($steps as $step => $label)
                            <div class="step {{ $step == $currentStep ? 'current' : '' }} {{ $currentStep > $step ? 'completed' : '' }}" id="{{ $step }}">
                                    <span>
                                        @if ($currentStep > $step)
                                            <i class="las la-check"></i>
                                        @else
                                            {{ $step }}
                                        @endif
                                    </span>
                                <span class="property_steps">{{ $label }}</span>
                            </div>
                        @endforeach
                            
                        </div>
                    </div>
                </div>