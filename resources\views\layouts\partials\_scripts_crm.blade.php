@php
$lang_path = resource_path('lang/' . App::getLocale());
$translations = collect(File::allFiles($lang_path))->flatMap(function ($file) use ($lang_path) {
return [
($translation = $file->getBasename('.php')) => trans($translation),
];
})->toJson();
@endphp
<script type="text/javascript">
    window.baseUrl = "{{URL::to('/')}}";
    window.current_locale = "{{App::getLocale()}}";
    window.translations = {!!$translations!!};
    //console.log(window.current_locale) ;
</script>

<script src="{{ asset('vendor_assets/js/jquery/jquery-3.5.1.min.js') }}"></script>
<script src="{{ asset('vendor_assets/js/jquery/jquery-ui.js') }}"></script>
<script src="{{ asset('vendor_assets/js/bootstrap/popper.js') }}"></script>
<script src="{{ asset('vendor_assets/js/bootstrap/bootstrap.min.js') }}"></script>
{{-- <script src="{{ asset('vendor_assets/js/accordion.js') }}"></script> --}}
<script src="{{ asset('vendor_assets/js/autoComplete.js') }}"></script>
{{-- <script src="{{ asset('vendor_assets/js/moment/moment.min.js') }}"></script> --}}
<script src="{{ asset('vendor_assets/js/daterangepicker.js') }}"></script>
<script src="{{ asset('vendor_assets/js/drawer.js') }}"></script>
{{-- <script src="{{ asset('vendor_assets/js/dynamicBadge.js') }}"></script> --}}
<script src="{{ asset('vendor_assets/js/dynamicCheckbox.js') }}"></script>
<script src="{{ asset('vendor_assets/js/feather.min.js') }}"></script>
{{-- <script src="{{ asset('vendor_assets/js/footable.min.js') }}"></script> --}}
{{-- <!-- <script src="{{ asset('vendor_assets/js/<EMAIL>') }}"></script> --> --}}
{{-- <script src="{{ asset('vendor_assets/js/google-chart.js') }}"></script> --}}
{{-- <script src="{{ asset('vendor_assets/js/jquery-jvectormap-2.0.5.min.js') }}"></script>
<script src="{{ asset('vendor_assets/js/jquery-jvectormap-world-mill-en.js') }}"></script>
<script src="{{ asset('vendor_assets/js/jquery.countdown.min.js') }}"></script>
<script src="{{ asset('vendor_assets/js/jquery.filterizr.min.js') }}"></script>
<script src="{{ asset('vendor_assets/js/jquery.magnific-popup.min.js') }}"></script>--}}
<script src="{{ asset('vendor_assets/js/jquery.mCustomScrollbar.min.js') }}"></script>
{{-- <script src="{{ asset('vendor_assets/js/jquery.peity.min.js') }}"></script> --}}
{{-- <script src="{{ asset('vendor_assets/js/jquery.star-rating-svg.min.js') }}"></script> --}}
{{-- <script src="{{ asset('vendor_assets/js/leaflet.js') }}"></script> --}}
{{-- <script src="{{ asset('vendor_assets/js/leaflet.markercluster.js') }}"></script> --}}
<script src="{{ asset('vendor_assets/js/loader.js') }}"></script>
{{-- <script src="{{ asset('vendor_assets/js/message.js') }}"></script> --}}
{{-- <script src="{{ asset('vendor_assets/js/moment.js') }}"></script> --}}
{{-- <script src="{{ asset('vendor_assets/js/muuri.min.js') }}"></script> --}}
<script src="{{ asset('vendor_assets/js/notification.js') }}"></script>
<script src="{{ asset('vendor_assets/js/popover.js') }}"></script>
<script src="{{ asset('vendor_assets/js/select2.full.min.js') }}"></script>
{{-- <script src="{{ asset('vendor_assets/js/slick.min.js') }}"></script> --}}
{{-- <script src="{{ asset('vendor_assets/js/trumbowyg.min.js') }}"></script>
<script src="{{ asset('vendor_assets/js/trumbowyg.upload64.min.js') }}"></script> --}}
<script src="{{ asset('vendor_assets/js/wickedpicker.min.js') }}"></script>
<script src="{{ asset('js/drag-drop.js') }}"></script>
{{--
<script src="{{ asset('js/full-calendar.js') }}"></script> --}}
{{-- <script src="{{ asset('js/googlemap-init.js') }}"></script>  --}}
{{-- <script src="{{ asset('js/icon-loader.js') }}"></script> --}}
{{-- <script src="{{ asset('js/jvectormap-init.js') }}"></script> --}}
<script src="{{ asset('js/footable.js') }}"></script>
{{--<script src="{{ asset('js/leaflet-init.js') }}"></script>
<script src="{{ asset('js/jquery-barcode.min.js') }}"></script> --}}
<script src="{{ asset('js/main.js') }}"></script>
<!-- In scripts its not there <script src="{{ asset('js/warehouse-modal-button.js') }}"></script> -->
<script src="{{ asset('js/new-main.js') }}"></script>


<!-- datatables -->
<!-- <script src="https://cdn.datatables.net/1.10.16/js/jquery.dataTables.min.js"></script> -->
{{-- <script src="{{asset('assets/plugins/datatables/jquery.dataTables.min.js')}}"></script>
<script src="{{asset('assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js')}}"></script>
<!-- <script src="{{asset('assets/plugins/datatables-responsive/js/dataTables.responsive.min.js')}}"></script> -->
<script src="{{asset('assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js')}}"></script> --}}
<!-- validation -->
<script src="{{asset('js/jquery.validate.js')}}"></script>
{{-- <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script> --}}
{{-- <script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script> --}}
{{-- <script src="{{asset('js/admin/jquery-validate-method-add.js')}}"></script> --}}

{{-- <script src="{{asset('js/validator_additional.js')}}"></script>

<script src="https://cdn.jsdelivr.net/jsbarcode/3.5.1/JsBarcode.all.min.js"></script> --}}

{{-- <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.9.0/main.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.9.0/main.min.js'></script> --}}
{{--
<script src="{{asset('assets/plugins/fullcalendar/main.js')}}"></script> --}}
{{-- <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.9.0/locales-all.js'></script> --}}

<script src="{{asset('js/admin/user-switch-system.js')}}"></script>

{{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css"> --}}
{{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script> --}}
{{-- <script src="{{asset('js/intlTelInputWithUtils.min.js')}}"></script>
<script src="{{asset('js/intlutils.js')}}"></script>
<script src="{{ asset('home/plugins/aos/aos.min.js') }}"></script>--}}
{{-- <script src="{{asset('js/image-gallery.js')}}"></script>  --}}
@livewireScripts



<script>
    /*$(window).on('load', function() {
        //console.log('Page is loaded')
            $.ajax({
                    type: 'GET',
                    url: "{{route('platform.notifications')}}",
    data: { _token: '{{ csrf_token() }}' },
    dataType: "json",
        beforeSend: function(xhr) {
            xhr.overrideMimeType("text/plain; charset=x-user-defined");

        },
    success: function(data) {
        //console.log(data);
        //$('#header_notifications').html(data.output)
        if (data.total_unread_notifications > 0) {
            $('#show_unread_marker').addClass('unread');
        } else {
            $('#show_unread_marker').removeClass('unread');
        }
        if (data.list_count > 0) {
            $('#unread_notification').text(data.total_unread_notifications)
            $('#display-empty').hide();
            $('#display-list').html(data.output);
            $('#display-list').show();
        } else {
            $('#display-list').hide();
            $('#display-empty').show();

        }
    }
                });
        });*/
</script>
<!--script src="https://www.gstatic.com/firebasejs/7.23.0/firebase.js"></script>
<script>

    var firebaseConfig = {
        apiKey: "AAAA3Dkx3qU:APA91bH9lvLlTMH8I42V-Lg6ID1NUcYLcPh08SIRKGlXxqSvW9tkDnlkRNOsu9Jt9Etg_Jr6diOVG6l9iWsO2G6ZHOcwulxXJcibD0daP0Ic4PuD6-HeWliQ1qdDEDSCfKJlpPJsoNj7",
        authDomain: "XXXX.firebaseapp.com",
        databaseURL: "https://XXXX.firebaseio.com",
        projectId: "osool-cf12e",
        storageBucket: "osool-cf12e.appspot.com",
        messagingSenderId: "945852374693",
        appId: "1:945852374693:android:b1049df0a8a2cc206f774c",
        measurementId: "XXX"
    };

    firebase.initializeApp(firebaseConfig);
    const messaging = firebase.messaging();

    messaging.onMessage(function(payload) {
        const noteTitle = payload.notification.title;
        const noteOptions = {
            body: payload.notification.body,
            icon: payload.notification.icon,
        };
        new Notification(noteTitle, noteOptions);
    });

</script-->
<!--  New Sweet Alert -->
<!-- <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
  /> -->
<!--  New Sweet Alert End-->
<script>
    function onLanguageChange(lang) {
        window.location.href = baseUrl + '/language/' + lang;
        // location.reload();
    }
    /*$(document).on({
        ajaxStart: function(){
            $("#overlayer").css("display", "block");
            $(".loader-overlay").css("display", "block");
        },
        ajaxStop: function(){
            $("#overlayer").css("display", "none")
            $(".loader-overlay").css("display", "none")
        }
    });*/
    $("#osool_admin_select_project").select2({});
    $(document).ready(function() {
        $(".add-wo-btn").click(function() {
            $(this).find(".wo-dropdown").toggle();
            $(this).toggleClass("active");
        });
    });

    // Function to be executed before page reload
    function handleBeforeUnload(event) {
        // Perform actions you want before the page reloads
        console.log("Page is about to reload!");
        $("#overlayer").css("display", "block");
        $(".loader-overlay").css("display", "block");
    }

    // Add event listener for beforeunload event
    window.addEventListener("beforeunload", handleBeforeUnload);
</script>
<script type="text/javascript">
    $(".close-module").click(function() {
        //$(".erp-modules").slideToggle();
        $(".notes-overlay").removeClass("active");
        $(".module-btn + .tool-module").addClass("active");
        $('.module-btn + .tool-module').fadeIn('slow');
        setTimeout(function() {
            $('.module-btn + .tool-new').fadeOut('slow');
        }, 5100);

    });
    $(".module-btn").click(function() {
        //$(".erp-modules").slideToggle();
        $(".module-btn + .tool-module").removeClass("active");
    });
</script>
<script>
    $(document).ready(function() {
        $(".datepicker").datepicker();
        $("#select-status").select2();
        // Initialize intlTelInput for all phone inputs
        $(".phone-input").each(function() {
            var input = this;
            var errorMsg = $(this).siblings("#error-msg");
            var iti = window.intlTelInput(input, {
                initialCountry: "SA",
                geoIpLookup: function(callback) {
                    $.getJSON('https://ipapi.co/json')
                        .done(function(data) {
                            callback(data.country_code);
                        })
                        .fail(function() {
                            callback("us"); // Default to US if location lookup fails
                        });
                },
                separateDialCode: true,
                utilsScript: "/js/utils.js",
            });

            // Restrict input to numbers only
            $(input).on("input", function() {
                this.value = this.value.replace(/[^0-9]/g, "");
            });

            // Validate on form submit
            $(input).closest("form").on("submit", function(e) {
                e.preventDefault();
                // if (iti.isValidNumber()) {
                //     alert("Valid phone number: " + iti.getNumber());
                // } else {
                //     errorMsg.removeClass("d-none");
                // }
            });

            // Hide error message on keyup
            $(input).on("keyup", function() {
                errorMsg.addClass("d-none");
            });
        });
    });
</script>
<!-- Crm Scripts -->
<script type="text/javascript">
    $(function() {
        $(".sort-table").sortable({
            axis: "y",
            containment: "parent",
            cursor: "move",
            opacity: 0.7
        }).disableSelection();
    });
    //$("#deals-stages").select2();
    $('.select2-new').select2();
    $('.datapicker').datepicker();

</script>
<!--script>
$( document ).ready(function() {
        var refreshNotification = setInterval(function()
        {
            $.ajax({
                type: 'GET',
                url: "{{route('workorder.fetch_unread_message')}}",
                data: { _token:'{{ csrf_token() }}'},
                dataType:"json",
                beforeSend: function( xhr ) {
                xhr.overrideMimeType( "text/plain; charset=x-user-defined" );

                },
                success: function(data) {
                    //console.log(data);
                    //alert(data.countUnread);
                    $("#unreadMsgDisplay").empty();
                    $("#unreadMsgDisplay").append(data.renderHtml);
                    if(data.countUnread==0)
                    {
                        $('#yellowSign').removeClass('displayDot');
                    }
                    else
                    {
                        $('#yellowSign').addClass('displayDot');
                    }
                }
            });

        }, 5000);
    });
</script-->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const overlayer = document.getElementById('overlayer');
        const loaderOverlay = document.querySelector('.loader-overlay');

        if (overlayer) overlayer.style.display = 'none';
        if (loaderOverlay) loaderOverlay.style.display = 'none';
    });
</script>
