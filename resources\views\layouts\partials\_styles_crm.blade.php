<link rel="stylesheet" href="{{ asset('vendor_assets/css/bootstrap/'.(Session::get('locale')=='en' ? 'bootstrap.css' : 'bootstrap-rtl.css')) }}">
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/daterangepicker.css') }}"> --}}
<link rel="stylesheet" href="{{ asset('vendor_assets/css/fontawesome.css') }}">
<link rel="stylesheet" href="{{ asset('vendor_assets/css/footable.standalone.min.css') }}">
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/<EMAIL>') }}"> --}}
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/jquery-jvectormap-2.0.5.css') }}"> --}}
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/jquery.mCustomScrollbar.min.css') }}"> --}}
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/leaflet.css') }}"> --}}
<link rel="stylesheet" href="{{ asset('vendor_assets/css/line-awesome.min.css') }}">
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/magnific-popup.css') }}"> --}}
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/MarkerCluster.css') }}">
<link rel="stylesheet" href="{{ asset('vendor_assets/css/MarkerCluster.Default.css') }}"> --}}
<link rel="stylesheet" href="{{ asset('vendor_assets/css/select2.min.css') }}">
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/slick.css') }}"> --}}
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/star-rating-svg.css') }}"> --}}
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/trumbowyg.min.css') }}"> --}}
{{-- <link rel="stylesheet" href="{{ asset('vendor_assets/css/wickedpicker.min.css') }}"> --}}
{{-- <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css"> --}}
<link rel="stylesheet" href="{{ asset('css/'.(Session::get('locale')=='en' ? 'style.css' : 'style-rtl.css')) }}">
<link rel="stylesheet" href="{{ asset('css/scss/new-style.css') }}">
<link rel="stylesheet" href="{{ asset('css/custom.css') }}">
{{-- <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet" /> --}}
<link rel="stylesheet" href="https://iconsax.gitlab.io/i/icons.css">
{{-- <link rel="stylesheet" href="{{ asset('home/plugins/aos/aos.min.css') }}"> --}}
{{-- <link rel="stylesheet" href="{{ asset('css/intlTelInput.css') }}"> --}}