<div>
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            {{ __('income.navigation.manage_revenue') }}
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>{{ __('income.navigation.dashboard') }}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>{{ __('income.navigation.manage_revenue') }}</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                <button class="btn btn-default btn-primary w-100 no-wrap" type="button" wire:click="openCreateModal" aria-expanded="false"><i class="las la-plus fs-16"></i>{{ __('income.buttons.create') }}</button>
            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
            </div>

            <div class="table-responsive">
                <div class="card mb-3" data-select2-id="108">
                    <div class="card-body" data-select2-id="107">
                        <form wire:submit.prevent="applyFilters" class="fs-14">
                            <div class="d-flex flex-wrap gap-10">
                                <div class="flex-fill max-w-180">
                                    <label for="" class="text-osool">{{ __('income.form.date') }}</label>
                                    <div class="position-relative">
                                        <input type="text" class="form-control datepicker" id="start_date" wire:model="filterDate" aria-describedby="emailHelp" placeholder="{{ __('income.form.enter_start_date') }}" />
                                        <i class="iconsax field-icon" icon-name="calendar-search"></i>
                                    </div>
                                </div>
                                <div class="flex-fill max-w-150">
                                    <label for="" class="text-osool">{{ __('income.form.account') }}</label>
                                    <select class="form-control select2-new" wire:model="filterAccount">
                                        <option value="">{{ __('income.form.select_account') }}</option>
                                        @foreach($bankAccounts as $account)
                                            <option value="{{ $account['id'] }}">{{ $account['holder_name'] }} - {{ $account['bank_name'] }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="flex-fill max-w-180" data-select2-id="106">
                                    <label for="" class="text-osool">{{ __('income.form.customer') }}</label>
                                    <select class="form-control select2-new" wire:model="filterCustomer">
                                        <option value="">{{ __('income.form.select_customer') }}</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer['id'] }}">{{ $customer['name'] }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="flex-fill max-w-150" data-select2-id="106">
                                    <label for="" class="text-osool">{{ __('income.form.category') }}</label>
                                    <select class="form-control select2-new" wire:model="filterCategory">
                                        <option value="">{{ __('income.form.select_category') }}</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category['id'] }}">{{ $category['name'] }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="flex-fill">
                                    <label for="" class="d-md-block d-none">&nbsp;</label>
                                    <div class="d-flex gap-10">
                                        <button type="submit" class="btn bg-opacity-new-primary btn-sm text-new-primary radius-md px-5">
                                            {{ __('income.buttons.apply') }}
                                        </button>
                                        <button type="button" class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md" wire:click="resetFilters">
                                            <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                                            <!-- Reset -->
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="">
                        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('income.common.income_transactions') }}</h6>

                            <div class="d-flex gap-10 table-search">
                                <div class="position-relative">
                                    <input type="text" class="form-control" placeholder="{{ __('income.search.placeholder') }}" wire:model.debounce.300ms="search">
                                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                                </div>
                                <button wire:click="export" wire:loading.attr="disabled"
                                    class="btn bg-grey text-dark"><i class="iconsax icon fs-22 mr-0"
                                        icon-name="upload-1"></i>{{ __('income.buttons.export') }} </button>
                            </div>
                        </div>
                    </div>

                    <div class="card-body px-0 pt-0">
                        @include('livewire.accounting.finance-revenue.list-view')
                    </div>

                    <div class="card-body pt-0">
                        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                            <div class="">
                                <ul class="atbd-pagination d-flex justify-content-between">
                                    <li>
                                        <div class="paging-option">
                                            <div class="dataTables_length d-flex">
                                                <label class="d-flex align-items-center mb-0">
                                                    <select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                        <option value="5">5</option>
                                                        <option value="10">10</option>
                                                        <option value="25">25</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                    <span class="no-wrap"> {{ __('income.pagination.entries_per_page') }} </span>
                                                </label>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>

                            @if($total > 0)
                            <div class="">
                                <div class="user-pagination">
                                    <div class="user-pagination new-pagination">
                                        <div class="d-flex justify-content-sm-end justify-content-end">
                                            <nav>
                                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                                    @if($pagination->current_page > 1)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="previousPage">
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                            </button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button class="border-0 disabled" disabled>
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                            </button>
                                                        </span>
                                                    @endif

                                                    @php
                                                        $start = max(1, $pagination->current_page - 2);
                                                        $end = min($pagination->last_page, $pagination->current_page + 2);
                                                    @endphp

                                                    @if($start > 1)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
                                                        </span>
                                                        @if($start > 2)
                                                            <span>
                                                                <button class="border-0 disabled" disabled>...</button>
                                                            </span>
                                                        @endif
                                                    @endif

                                                    @for($i = $start; $i <= $end; $i++)
                                                        @if($i == $pagination->current_page)
                                                            <span>
                                                                <button class="border-0 current-page" disabled>{{ $i }}</button>
                                                            </span>
                                                        @else
                                                            <span>
                                                                <button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
                                                            </span>
                                                        @endif
                                                    @endfor

                                                    @if($end < $pagination->last_page)
                                                        @if($end < $pagination->last_page - 1)
                                                            <span>
                                                                <button class="border-0 disabled" disabled>...</button>
                                                            </span>
                                                        @endif
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
                                                        </span>
                                                    @endif

                                                    @if($pagination->current_page < $pagination->last_page)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="nextPage">
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                            </button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button class="border-0 disabled" disabled>
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                            </button>
                                                        </span>
                                                    @endif
                                                </span>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <p class="text-sm text-gray-700 leading-5 mb-0">
                                    <span>{{ __('income.pagination.showing') }}</span>
                                    <span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
                                    <span>{{ __('income.pagination.to') }}</span>
                                    <span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
                                    <span>{{ __('income.pagination.of') }}</span>
                                    <span class="font-medium">{{ $pagination->total }}</span>
                                    <span>{{ __('income.pagination.results') }}</span>
                                </p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Delete Confirmation Modal --}}
    @livewire('common.delete-confirm')

    {{-- Create/Edit Revenue Modal --}}
    @include('livewire.common.super-modal-v1', [
        'component' => 'accounting.finance-revenue.modals.create',
        'modalId' => 'createRevenueModal',
    ])


</div>

@push('scripts')
<script src="{{ asset('js/livewire/manage-revenue-modal.js') }}"></script>
<script src="{{ asset('js/livewire/manage-select2.js') }}"></script>
<script src="{{ asset('js/livewire/manage-datepicker.js') }}"></script>
<script>
    document.addEventListener('livewire:load', function () {
        // Initialize any JavaScript components if needed
    });

    // Toast notification handler
    window.addEventListener('show-toastr', event => {
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        if (event.detail.type === 'success') {
            toastr.success(event.detail.message);
        } else if (event.detail.type === 'error') {
            toastr.error(event.detail.message);
        }
    });

    window.addEventListener('export-start', event => {
        showLoader();
    });

    window.addEventListener('export-end', event => {
        hideLoader();
    });

    window.addEventListener('redirect-to-export', event => {
        window.open(event.detail.url, '_blank');
    });

    // Modal show/hide handlers
    window.addEventListener('show-modal', event => {
        $('#' + event.detail.modalId).modal('show');
    });

    window.addEventListener('close-modal', event => {
        $('#' + event.detail.modalId).modal('hide');
    });

    function showLoader() {
        if (document.getElementById("overlayer")) {
            document.getElementById("overlayer").style.display = "block";
        }
        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "block";
        }
    }

    function hideLoader() {
        if (document.getElementById("overlayer")) {
            document.getElementById("overlayer").style.display = "none";
        }
        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "none";
        }
    }
</script>
@endpush
