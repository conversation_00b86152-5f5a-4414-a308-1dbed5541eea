<div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
    @if($error)
        <div class="alert alert-danger mx-3 mt-3" role="alert">
            <div class="d-flex align-items-center">
                <i class="iconsax icon fs-22 mr-2" icon-name="warning-2"></i>
                <div>
                    <strong>{{ __('income.status.error') }}</strong> {{ $error }}
                    <button class="btn btn-sm btn-outline-danger ml-2" wire:click="fetchIncomeTransactions">
                        <i class="iconsax icon fs-16" icon-name="refresh-2"></i> {{ __('income.buttons.retry') }}
                    </button>
                </div>
            </div>
        </div>
    @endif

    <div class="table-responsive">
        <table class="table mb-0 radius-0 th-osool">
            <thead>
                <tr class="userDatatable-header">
                    <th wire:click="sortBy('date')" style="cursor: pointer;" title="{{ __('income.table.sort_by_date') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'date' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('income.common.date') }}
                    </th>
                    <th wire:click="sortBy('amount')" style="cursor: pointer;" title="{{ __('income.table.sort_by_amount') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'amount' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('income.common.amount') }}
                    </th>
                    <th wire:click="sortBy('account')" style="cursor: pointer;" title="{{ __('income.table.sort_by_account') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'account' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('income.common.account') }}
                    </th>
                    <th wire:click="sortBy('customer')" style="cursor: pointer;" title="{{ __('income.table.sort_by_customer') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'customer' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('income.common.customer') }}
                    </th>
                    <th wire:click="sortBy('category')" style="cursor: pointer;" title="{{ __('income.table.sort_by_category') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'category' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('income.common.category') }}
                    </th>
                    <th wire:click="sortBy('reference')" style="cursor: pointer;" title="{{ __('income.table.sort_by_reference') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'reference' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('income.common.reference') }}
                    </th>
                    <th wire:click="sortBy('description')" style="cursor: pointer;" title="{{ __('income.table.sort_by_description') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'description' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('income.common.description') }}
                    </th>
                    <th>
                        {{ __('income.common.payment_receipt') }}
                    </th>
                    <th>
                        {{ __('income.common.action') }}
                    </th>
                </tr>
            </thead>
            <tbody class="sort-table ui-sortable">
                @if($loading)
                    <tr>
                        <td colspan="9" class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="sr-only">{{ __('income.status.loading') }}</span>
                                </div>
                                <h6 class="text-muted">{{ __('income.common.loading_income') }}</h6>
                            </div>
                        </td>
                    </tr>
                @else
                    @forelse($incomeTransactions as $transaction)
                    <tr class="ui-sortable-handle">
                        <!-- Date -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span>{{ $transaction['formatted_date'] }}</span>
                            </div>
                        </td>
                        <!-- Amount -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span class="fw-500 text-success">{{ $transaction['display_amount'] }}</span>
                            </div>
                        </td>
                        <!-- Account -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span>{{ $transaction['account'] ?? '-' }}</span>
                            </div>
                        </td>
                        <!-- Customer -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span>{{ $transaction['contact_name'] }}</span>
                            </div>
                        </td>
                        <!-- Category -->
                        <td>
                            <div class="d-flex align-items-center gap-10">
                                <span class="">{{ $transaction['category_name'] }}</span>
                            </div>
                        </td>
                        <!-- Reference -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span>{{ $transaction['reference'] ?? '-' }}</span>
                            </div>
                        </td>
                        <!-- Description -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <div>
                                    <span>{{ $transaction['description'] ?? '-' }}</span>
                                </div>
                            </div>
                        </td>
                        <!-- Payment Receipt -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                @if(isset($transaction['receipt']) && $transaction['receipt'])
                                    <div class="d-flex gap-2">
                                        <a href="{{ $transaction['receipt'] }}" target="_blank" class="btn btn-sm btn-outline-primary" title="{{ __('income.common.view_receipt') }}">
                                            <i class="iconsax icon fs-14" icon-name="eye"></i> {{ __('income.common.view_receipt') }}
                                        </a>
                                    </div>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </div>
                        </td>
                        <!-- Actions -->
                        <td>
                            <div class="d-inline-block">
                                <ul class="mb-0 d-flex gap-10">
                                    <li>
                                        <a href="javascript:void(0);" wire:click="viewRevenue({{ $transaction['id'] }})" title="{{ __('income.common.view') }}">
                                            <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" wire:click="openEditModal({{ $transaction['id'] }})" title="{{ __('income.common.edit') }}">
                                            <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" wire:click="openDeleteModal({{ $transaction['id'] }}, '{{ $transaction['description'] }}')" title="{{ __('income.common.delete') }}">
                                            <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="iconsax icon fs-48 text-muted mb-3" icon-name="money-recive"></i>
                                    <h6 class="text-muted">{{ __('income.common.no_income_found') }}</h6>
                                    @if($search)
                                        <p class="text-muted">{{ __('income.messages.try_adjusting_search') }}</p>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforelse
                @endif
            </tbody>
        </table>
    </div>
</div>
