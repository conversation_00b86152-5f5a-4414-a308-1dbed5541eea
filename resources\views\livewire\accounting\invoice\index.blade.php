<div>
<div class="contents crm">
    <div class="container-fluid">
        <div class="col-lg-12">
            <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
               <div class="page-title-wrap p-0">
                <div class="page-title d-flex justify-content-between">
                    <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                        <div class="user-member__title mr-sm-25 ml-0">
                            <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                @lang('accounting.manageInvoices')
                            </h4>
                        </div>
                    </div>
                </div>
                <div>
                    <ul class="atbd-breadcrumb nav">
                        <li class="atbd-breadcrumb__item">
                            <a href="">@lang('accounting.dashboard')</a>
                            <span class="breadcrumb__seperator">
                                <span class="la la-angle-right"></span>
                            </span>
                        </li>
                        <li class="atbd-breadcrumb__item">
                            <a>@lang('accounting.invoice')</a>
                        </li>
                    </ul>
                </div>
            </div>
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <div class="d-flex gap-10 breadcrumb_right_icons">
                       

                   <!--      <button class="btn btn-white btn-default text-center svg-20 wh-45" wire:click="switchView('cards')">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="layout-3"></i>
                        </button> -->

                        <button class="btn btn-default btn-primary w-100 no-wrap" wire:click="goToCreatePage" type="button"><i class="las la-plus fs-16"></i> {{ __('accounting.create') }}</button>

                    </div>
                </div>
                <!--====End Design for Export PDF===-->
            </div>
        </div>


        <div class="table-responsive">
            <div class="card">
               <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('accounting.invoice') }}</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
<input type="text" class="form-control" placeholder="{{ __('customers.search.placeholder') }}" wire:model.debounce.500ms="search">

                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                      <button class="btn btn-export text-dark"  wire:click="export"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> {{ __('accounting.export') }}</button>
                </div>
            </div>
        </div>
                <div class="card-body px-0 pt-0">
                    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                        <div class="table-responsive">
                            <table class="table mb-0 radius-0 th-osool">
                                <thead>
                                    <tr class="userDatatable-header">

<th wire:click="sortBy('invoice_id')" class="cursor-pointer">
<i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
@lang('accounting.invoice_number')
</th>
<th wire:click="sortBy('issue_date')" class="cursor-pointer">
<i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
@lang('accounting.issue_date')
</th>
<th wire:click="sortBy('due_date')" class="cursor-pointer">
<i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
@lang('accounting.due_date')
</th>
<th wire:click="sortBy('due_amount')" class="cursor-pointer">
<i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
@lang('accounting.due_amount')
</th>
<th wire:click="sortBy('status')" class="cursor-pointer">
<i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
@lang('accounting.status')
</th>
                                        <th>
                                            @lang('accounting.action')
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="sort-table ui-sortable">

@forelse($data as $this_data)

<tr class="ui-sortable-handle">
<td>
<div class="d-flex userDatatable-content mb-0 align-items-center">
<span>{{$this_data['invoice_id']}}</span>
</div>
</td>
<td>
<div class="d-flex align-items-center gap-10">
{{$this_data['issue_date']}}
</div>
</td>
<td>
<div class="d-flex align-items-center gap-10">
{{$this_data['due_date']}}
</div>
</td>
<td>
<div class="d-flex align-items-center gap-10">
<img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15"><span class="text-new-primary">{{$this_data['due_amount']}}</span>
</div>
</td>
<td>
@php
$statusInfo = \App\Http\Traits\CRMProjects\FinanceManagment::getStatusInfo(data_get($this_data, 'status', '---'));
@endphp
<span class="py-1 px-2 {{ $statusInfo['color'] }} rounded text-white">
{{ $statusInfo['label'] }}
</span>
 </td>
<td>
<div class="d-inline-block">
<ul class="mb-0 d-flex gap-10">
<li>
<a href="javascript:void(0);" wire:click="goToView('{{ Crypt::encrypt($this_data['id']) }}')">
<i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
</a>
</li>
<li>
<a href="javascript:void(0);" wire:click="goToEdit('{{ Crypt::encrypt($this_data['id']) }}')">
<i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
</a>
</li>
<li>
<a href="javascript:void(0);" wire:click="openDeleteModal({{ $this_data['id'] }}, '{{ $this_data['invoice_id'] }}')">
<i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
</a>

</li>
</ul>
</div>
</td>
</tr>
@empty
<tr>
<td colspan="6" class="text-center py-4">
<div class="d-flex flex-column align-items-center">
<i class="iconsax icon fs-48 text-muted mb-3" icon-name="user-search"></i>
<h6 class="text-muted">{{ __('accounting.no_data_found') }}</h6>
@if($search)
<p class="text-muted">{{ __('customers.messages.try_adjusting_search') }}</p>
@endif
</div>
</td>
</tr>
@endforelse

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>




                <div class="card-body pt-0">
                        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                            <div class="">
                                <ul class="atbd-pagination d-flex justify-content-between">
                                    <li>
                                        <div class="paging-option">
                                            <div class="dataTables_length d-flex">
                                                <label class="d-flex align-items-center mb-0">
                                                    <select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                        <option value="5">5</option>
                                                        <option value="10">10</option>
                                                        <option value="25">25</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                    <span class="no-wrap"> {{ __('customers.pagination.entries_per_page') }} </span>
                                                </label>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>

                            @if($total > 0)
                            <div class="">
                                <div class="user-pagination">
                                    <div class="user-pagination new-pagination">
                                        <div class="d-flex justify-content-sm-end justify-content-end">
                                            <nav>
                                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                                    @if($pagination->current_page > 1)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="previousPage">
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                            </button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button class="border-0 disabled" disabled>
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                            </button>
                                                        </span>
                                                    @endif

                                                    @php
                                                        $start = max(1, $pagination->current_page - 2);
                                                        $end = min($pagination->last_page, $pagination->current_page + 2);
                                                    @endphp

                                                    @if($start > 1)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
                                                        </span>
                                                        @if($start > 2)
                                                            <span>
                                                                <button class="border-0 disabled" disabled>...</button>
                                                            </span>
                                                        @endif
                                                    @endif

                                                    @for($i = $start; $i <= $end; $i++)
                                                        @if($i == $pagination->current_page)
                                                            <span>
                                                                <button class="border-0 current-page" disabled>{{ $i }}</button>
                                                            </span>
                                                        @else
                                                            <span>
                                                                <button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
                                                            </span>
                                                        @endif
                                                    @endfor

                                                    @if($end < $pagination->last_page)
                                                        @if($end < $pagination->last_page - 1)
                                                            <span>
                                                                <button class="border-0 disabled" disabled>...</button>
                                                            </span>
                                                        @endif
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
                                                        </span>
                                                    @endif

                                                    @if($pagination->current_page < $pagination->last_page)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="nextPage">
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                            </button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button class="border-0 disabled" disabled>
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                            </button>
                                                        </span>
                                                    @endif
                                                </span>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <p class="text-sm text-gray-700 leading-5 mb-0">
                                    <span>{{ __('customers.pagination.showing') }}</span>
                                    <span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
                                    <span>{{ __('customers.pagination.to') }}</span>
                                    <span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
                                    <span>{{ __('customers.pagination.of') }}</span>
                                    <span class="font-medium">{{ $pagination->total }}</span>
                                    <span>{{ __('customers.pagination.results') }}</span>
                                </p>
                            </div>
                            @endif
                        </div>
                    </div>
            </div>


        </div>



    </div>

</div>
</div>

@livewire('common.delete-confirm')


</div>

@push('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
    $(document).ready(function() {
        // Toggle active class and manage dropdown on button click
        $('.filter-button').on('click', function(e) {
            e.stopPropagation(); // Prevent the click from bubbling up to the document
            $(this).toggleClass('active'); // Toggle the 'active' class
            $(this).closest('.dropdown').find(".dropdown-menu").toggleClass('show'); // Toggle the Bootstrap dropdown
        });

        // Remove active class and close dropdown when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                // If the click is outside the dropdown, remove the 'active' class and close the dropdown
                $('.filter-button').removeClass('active');
                $('.dropdown-menu').removeClass('show');
                $('.filter-button').attr('aria-expanded', 'false');
            }
        });

        // Prevent dropdown from closing when clicking inside the dropdown menu
        $('.dropdown-menu').on('click', function(e) {
            e.stopPropagation();
        });
    });
</script>

<script>
    window.addEventListener('download-blob', event => {
        const { base64, fileName, mime } = event.detail;

        // Convert base64 to binary data
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: mime });

        // Create object URL and download
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
    });
</script>

@endpush
