<div>
      <div class="contents crm">
           <div class="container-fluid">
@if($showFullView)
<div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            {{ __('accounting.managePayments') }}
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>{{ __('customers.navigation.dashboard') }}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a> {{ __('accounting.managePayments') }}</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-default btn-primary w-100 no-wrap" data-toggle="modal" data-target="#create-account" type="button" aria-expanded="false"><i class="las la-plus fs-16"></i> {{ __('accounting.create') }}</button>

            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>
@endif

<div class="table-responsive">
    @if($showFullView)
    <div class="card mb-3" data-select2-id="108">
            <div class="card-body" data-select2-id="107">
                <form wire:submit.prevent="applyFilters" class="fs-14">
                <div class="row">
                <!-- Date Field -->
                <div class="col-md-2 col-6">
                <label for="start_date" class="text-osool">@lang('accounting.fromDate')</label>
                <div class="position-relative">
               <input type="text" class="form-control" wire:ignore id="from_date" placeholder="@lang('accounting.enterfromDate')" />
                <i class="iconsax field-icon" icon-name="calendar-search"></i>
                </div>
                </div>

                <div class="col-md-2 col-6">
                <label for="start_date" class="text-osool">@lang('accounting.toDate')</label>
                <div class="position-relative">
               <input type="text" class="form-control" wire:ignore id="to_date" placeholder="@lang('accounting.entertoDate')" />
                <i class="iconsax field-icon" icon-name="calendar-search"></i>
                </div>
                </div>

                <!-- From Account -->
                <div class="col-md-2 col-6">
                <label for="searchFromAccount" class="text-osool">@lang('accounting.account')</label>
                <select class="form-control select2-new" wire:ignore id="searchAccount" name="searchAccount">
                <option value="">{{ __('accounting.select_account') }}</option>
                @foreach($bankAccounts as $account)
                <option value="{{ $account['id'] }}">{{ $account['holder_name'] }}</option>
                @endforeach
                </select>
                </div>

                <!-- To Account -->
                <div class="col-md-2 col-6">
                <label for="searchToAccount" class="text-osool">@lang('accounting.vendor')</label>
                <select class="form-control select2-new" wire:ignore id="searchVendor" name="searchVendor">
                <option value="">{{ __('accounting.select_vendor') }}</option>
                @foreach($vendors as $this_data)
                <option value="{{ $this_data['id'] }}">{{ $this_data['name'] }}</option>
                @endforeach
                </select>
                </div>

                <div class="col-md-2 col-6">
                <label for="searchToAccount" class="text-osool">@lang('accounting.category')</label>
                <select class="form-control select2-new" wire:ignore id="searchCategory" name="searchCategory">
                <option value="">{{ __('accounting.select_category') }}</option>
                @foreach($categories as $this_data)
                <option value="{{ $this_data['id'] }}">{{ $this_data['name'] }}</option>
                @endforeach
                </select>
                </div>

                <!-- Buttons -->
                <div class="col-md-2 mt-md-0 mt-3">
                <label class="d-md-block d-none">&nbsp;</label>
                <div class="d-flex gap-10">
                <button type="submit" class="btn bg-opacity-new-primary btn-sm text-new-primary radius-md px-5">
                @lang('accounting.apply')
                </button>
                <button type="button" class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md" wire:click="refresh">
                <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                </button>
                </div>
                </div>
                </div>
                </form>

            </div>
        </div>
        @endif
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0"> {{ __('accounting.managePayments') }}</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                   <input type="text" class="form-control" placeholder="{{ __('customers.search.placeholder') }}" wire:model="search">

                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"  wire:click="export"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> {{ __('accounting.export') }}</button>

                      <button class="btn btn-export text-dark"  wire:click="refresh"  ><i class="iconsax icon fs-22 mr-0" icon-name="refresh"></i></button>

                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">
                        <thead>
                            <tr class="userDatatable-header">
                            <th wire:click="sortBy('id')" style="cursor: pointer;">
                            <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'id' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                            @lang('accounting.no')
                            </th>
                            <th wire:click="sortBy('date')" style="cursor: pointer;">
                            <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'date' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                            @lang('accounting.date')
                        </th>

                        <th wire:click="sortBy('amount')" style="cursor: pointer;">
                            <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'amount' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                            @lang('accounting.amount')
                        </th>

                        <th wire:click="sortBy('account')" style="cursor: pointer;">
                            <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'account' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                            @lang('accounting.account')
                        </th>

                        <th wire:click="sortBy('vendor')" style="cursor: pointer;">
                            <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'vendor' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                            @lang('accounting.vendor')
                        </th>

                        <th wire:click="sortBy('category')" style="cursor: pointer;">
                            <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'category' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                            @lang('accounting.category')
                        </th>

                        <th wire:click="sortBy('reference')" style="cursor: pointer;">
                            <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'reference' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                            @lang('accounting.reference')
                        </th>

                        <th wire:click="sortBy('description')" style="cursor: pointer;">
                            <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'description' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                            @lang('accounting.description')
                        </th>

                                <th >
                                @lang('accounting.paymentReceipt')
                                </th>
                            <th>
                                @lang('accounting.action')
                            </th>

                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                             @php $i=1; @endphp
                             @forelse($data as $this_data)
                            <tr class="ui-sortable-handle">
                                <td >
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                            <span>{{$i++}}</span>
                            </div>
                            </td>
                                <td >
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>{{$this_data['date']}}</span>
                                    </div>
                                </td>
                                 <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"> <span>{{$this_data['amount']}}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                   <span>{{$this_data['account']}}</span>
                                </td>
                              
                                <td>
                                    <div class="d-flex align-items-center gap-10"><span>{{$this_data['vendor']}}</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <span>{{$this_data['category']}}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <span>{{$this_data['reference']}}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <span>{{$this_data['description']}}</span>
                                    </div>
                                </td>
                                <td>
                                    @if($this_data['payment_receipt'])
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>
                                    <a target="_blank" href="{{$this_data['payment_receipt']}}">
                                            <i class="iconsax icon text-osool fs-18" icon-name="document-download"></i>
                                        </a>
                                    </span>
                                    </div>
                                    @endif
                                </td>
                               
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex gap-10">
                                            <li>
                                                <a href="javascript:void(0);" wire:click="openViewModal({{ $this_data['id'] }})">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" wire:click="openEditModal({{ $this_data['id'] }})">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" wire:click="openDeleteModal({{ $this_data['id'] }}, '{{ $this_data['account'] }}')">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                                
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>


                             @empty
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="iconsax icon fs-48 text-muted mb-3" icon-name="user-search"></i>
                                    <h6 class="text-muted">{{ __('accounting.no_data_found') }}</h6>
                                    @if($search)
                                        <p class="text-muted">{{ __('customers.messages.try_adjusting_search') }}</p>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>




         <div class="card-body pt-0">
                        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                            <div class="">
                                <ul class="atbd-pagination d-flex justify-content-between">
                                    <li>
                                        <div class="paging-option">
                                            <div class="dataTables_length d-flex">
                                                <label class="d-flex align-items-center mb-0">
                                                    <select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                        <option value="5">5</option>
                                                        <option value="10">10</option>
                                                        <option value="25">25</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                    <span class="no-wrap"> {{ __('customers.pagination.entries_per_page') }} </span>
                                                </label>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>

                            @if($total > 0)
                            <div class="">
                                <div class="user-pagination">
                                    <div class="user-pagination new-pagination">
                                        <div class="d-flex justify-content-sm-end justify-content-end">
                                            <nav>
                                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                                    @if($pagination->current_page > 1)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="previousPage">
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                            </button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button class="border-0 disabled" disabled>
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                            </button>
                                                        </span>
                                                    @endif

                                                    @php
                                                        $start = max(1, $pagination->current_page - 2);
                                                        $end = min($pagination->last_page, $pagination->current_page + 2);
                                                    @endphp

                                                    @if($start > 1)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
                                                        </span>
                                                        @if($start > 2)
                                                            <span>
                                                                <button class="border-0 disabled" disabled>...</button>
                                                            </span>
                                                        @endif
                                                    @endif

                                                    @for($i = $start; $i <= $end; $i++)
                                                        @if($i == $pagination->current_page)
                                                            <span>
                                                                <button class="border-0 current-page" disabled>{{ $i }}</button>
                                                            </span>
                                                        @else
                                                            <span>
                                                                <button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
                                                            </span>
                                                        @endif
                                                    @endfor

                                                    @if($end < $pagination->last_page)
                                                        @if($end < $pagination->last_page - 1)
                                                            <span>
                                                                <button class="border-0 disabled" disabled>...</button>
                                                            </span>
                                                        @endif
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
                                                        </span>
                                                    @endif

                                                    @if($pagination->current_page < $pagination->last_page)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="nextPage">
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                            </button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button class="border-0 disabled" disabled>
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                            </button>
                                                        </span>
                                                    @endif
                                                </span>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <p class="text-sm text-gray-700 leading-5 mb-0">
                                    <span>{{ __('customers.pagination.showing') }}</span>
                                    <span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
                                    <span>{{ __('customers.pagination.to') }}</span>
                                    <span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
                                    <span>{{ __('customers.pagination.of') }}</span>
                                    <span class="font-medium">{{ $pagination->total }}</span>
                                    <span>{{ __('customers.pagination.results') }}</span>
                                </p>
                            </div>
                            @endif
                        </div>
                    </div>
    </div>

    
</div>
</div>



@include('livewire.accounting.payment.modals.create')
@livewire('common.delete-confirm')
@include('livewire.accounting.payment.modals.edit')
@include('livewire.accounting.payment.modals.view')


</div>


</div>

@push('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#bank_type, #account_type, #wallet_type").select2();
    $(document).ready(function() {
      // Toggle active class and manage dropdown on button click
      $('.filter-button').on('click', function (e) {
        e.stopPropagation(); // Prevent the click from bubbling up to the document
        $(this).toggleClass('active'); // Toggle the 'active' class
        $(this).closest('.dropdown').find(".dropdown-menu").toggleClass('show'); // Toggle the Bootstrap dropdown
      });

      // Remove active class and close dropdown when clicking outside
      $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length) {
          // If the click is outside the dropdown, remove the 'active' class and close the dropdown
          $('.filter-button').removeClass('active');
          $('.dropdown-menu').removeClass('show');
          $('.filter-button').attr('aria-expanded', 'false');
        }
      });

      // Prevent dropdown from closing when clicking inside the dropdown menu
      $('.dropdown-menu').on('click', function (e) {
        e.stopPropagation();
      });
    });
</script>

<script>
    window.addEventListener('show-edit-modal', () => {
        $('#edit_data').modal('show');
    });

    window.addEventListener('hide-edit-modal', () => {
        $('#edit_data').modal('hide');
    });

    window.addEventListener('show-view-modal', () => {
        $('#view_data').modal('show');
    });
    
</script>

<script>

    document.addEventListener('livewire:load', function () {

        $('#from_date').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true
        });

        $('#to_date').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true
        });

        // Init select2 and bind change
        $('#searchAccount').select2().on('change', function () {
            @this.set('searchAccount', $(this).val());
        });

        $('#searchVendor').select2().on('change', function () {
            @this.set('searchVendor', $(this).val());
        });

        $('#searchCategory').select2().on('change', function () {
            @this.set('searchCategory', $(this).val());
        });

        // Reinitialize select2 after Livewire DOM update
        Livewire.hook('message.processed', () => {
            $('#searchAccount, #searchVendor, #searchCategory').select2();
        });
    });



    $('#from_date').on('change', function () {
    const raw = new Date($(this).val());
    const formatted = raw.getFullYear() + '-' + String(raw.getMonth() + 1).padStart(2, '0') + '-' + String(raw.getDate()).padStart(2, '0');
    Livewire.find('{{ $this->id }}').set('from_date', formatted);
    });
    $('#to_date').on('change', function () {
    const raw = new Date($(this).val());
    const formatted = raw.getFullYear() + '-' + String(raw.getMonth() + 1).padStart(2, '0') + '-' + String(raw.getDate()).padStart(2, '0');
    Livewire.find('{{ $this->id }}').set('to_date', formatted);
    });  
</script>
<script>
    window.addEventListener('download-blob', event => {
        const { base64, fileName, mime } = event.detail;

        // Convert base64 to binary data
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: mime });

        // Create object URL and download
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
    });
</script>

@endpush
