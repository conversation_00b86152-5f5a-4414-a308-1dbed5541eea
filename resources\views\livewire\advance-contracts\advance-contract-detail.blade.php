<div>
    <!-- Content Start --> 
    <div class="contents contract-show">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="align-items-center d-flex flex-sm-row justify-content-sm-between py-2 user-member">
                        <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper mb-0">
                            <div class="">
                                <div class="page-title d-flex justify-content-between">
                                    <div class="page-title__left">
                                        <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                                            <h4 class="text-capitalize breadcrumb-title d-inline-flex align-items-center gap-10 no-wrap text-osool"><a href="Javascript:history.back()" class="btn btn-white btn-default text-center svg-20 wh-40 px-0"><i class="iconsax m-0" icon-name="chevron-left-square"></i></a>{{$contract->contract_number}}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="action-btn flex-fill justify-content-end d-flex">
                            <button class="btn px-15 btn-outline-gray mr-2" href="#"><i class="iconsax mr-1" icon-name="download-1"></i> {{ __('advance_contracts.details_page.download') }}</button>
                            @if(in_array(Auth::user()->user_type,$allowedRolesForVariationOrder) )
                                <button wire:click="createVariationOrder"  type="button" class="btn btn-outline-primary" > {{ __('advance_contracts.variation_order.create_variation_order') }} </button>
                             @endif
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                @if (!empty($variation_order_history) && count($variation_order_history) > 0)
                    <div class="col-12 mb-3">
                        <livewire:advance-contracts.alerts.variation-order :message="$alertMessage" :button="true" />
                    </div>
                    <div class="col-12 mb-3">
                        <livewire:advance-contracts.details.variation-history :history="$variation_order_history"  />
                    </div>
                @endif

                <div class="col-md-6 pr-md-0 mb-md-0 mb-3">
                    <div class="card card-overview-progress border-0 max-w-100 min-h-0 h-100">
                        <div class="card-progress h-100">
                            <div class="card-progress__summery d-flex justify-content-between">
                                <div>
                                    <div class="text_info_new mb-1 fs-18 fw-700">{{$startDate}}</div>
                                    <span class="fw-600">{{__('data_contract.contract_forms.label.start_date')}}</span>
                                </div>
                                <div>
                                    <div class="color-dark mb-1 fs-18 fw-700">{{$endDate}}</div>
                                    <span class="text-right fw-600">{{__('data_contract.contract_forms.label.end_date')}}</span>
                                </div>
                            </div>
                            <div class="card-progress__bar">
                                <div class="progress">
                                    <div class="progress-bar text_info_bg_new border-radius-10" role="progressbar" style="width: {{$percentage}}%" aria-valuenow="{{$percentage}}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="progress-excerpt">
                                    <p class="color-dark">{{__('data_contract.contract_forms.label.remaining_time')}}</p>
                                    <span class="progress-total">{{100-round($percentage)}}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card h-100 border-0 max-w-100 min-h-0">
                        <div class="card-progress d-flex gap-20 h-100">
                            <div class="p-3 w-50">
                                <div>
                                    <div class="card-header border-0 mb-3 px-0 min-h-0">
                                        <h6 class="fw-700 fs-18 text-osool">{{__('admin.akaunting::kpi.overall_kpi')}}</h6>
                                    </div>
                                    <div class="text-light-1 mt-2 mb-3 fs-12 h-40">{{ __('advance_contracts.details_page.kpi_points_description') }}</div>
                                </div>
                                <div class="card-progress__bar">
                                    <div class="progress">
                                        <div class="progress-bar bgGreen border-radius-10" role="progressbar" style="width: {{ $overallPerformance }}%" aria-valuenow="{{ number_format($percentage, 2) }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="progress-excerpt justify-content-end">
                                        <span class="progress-total fw-500 fs-16">{{ number_format($overallPerformance, 0) }}%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="p-3 w-50">
                                <div>
                                    <div class="card-header border-0 mb-3 px-0 min-h-0">
                                    <h6 class="fw-700 fs-18 text-osool">{{__('admin.akaunting::kpi.penality')}}</h6>
                                    </div>
                                    <div class="text-light-1 mt-2 mb-3 fs-12 h-40">{{ __('advance_contracts.details_page.penality_points_description') }} <br><br></div>
                                </div>
                                <div class="card-progress__bar">
                                    <div class="progress">
                                        <div class="progress-bar bg-loss w-0 border-radius-10" role="progressbar"  aria-valuenow="{{ number_format($percentage, 2) }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="progress-excerpt justify-content-end">
                                        <span class="progress-total fw-500 fs-16">0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 pr-md-0 pt-3">

                    <div class="card border-0 max-w-100 mb-3">
                        <div class="card-progress h-100 d-flex">
                            <div class="card-progress__summery">
                                <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.details_page.service_provider_details') }}</h6>
                                </div>
                                <div class="d-flex align-items-center gap-20 card-body pt-0">
                                    <div>
                                        <img src="{{ asset($serviceProvider->getStorageLogoAttribute()) }}" alt="{{$serviceProvider->name}}" alt="Dummy_logo" class="radius-xl" width="84px" height="84px">
                                    </div>
                                    <div class="color-dark fs-12 fw-600">
                                        <p  class="mb-2">{{ __('advance_contracts.details_page.service_provider_name') }}:</p>
                                        <p  class="mb-2">{{ __('advance_contracts.details_page.service_provider_id') }}:</p>
                                        <p  class="mb-0">{{ __('advance_contracts.details_page.business_field') }}:</p>
                                    </div>
                                    <div class="color-dark fw-700 fs-12">
                                        <p>{{$serviceProvider->name}}</p>
                                        <p>{{$serviceProvider->service_provider_id}}</p>
                                        <p class="mb-0">{{$serviceProvider->contact_name ?? '-'}}</p>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>

                    <div class="card card_content">
                        <div class="card-header border-0">
                            <h6 class="fw-700 fs-16">{{__('data_contract.contract_forms.label.contact_details')}}</h6>
                        </div>

                        <div class="card-body card-progress pt-0">
                            <div class="about-projects">
                                <div class="landing-pages-table table-responsive pb-0">
                                    <table class="table table--default align-left fw-600">

                                        <thead>
                                            <tr>
                                                <th class="fw-800">{{ __('advance_contracts.details_page.title_of_table') }}</th>
                                                <th>
                                                    <div class="ml-10 fw-800">
                                                        {{ __('advance_contracts.details_page.information') }}
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>

                                        <tbody>
                                            <tr>
                                                <td width="30%">
                                                    <div>
                                                        {{__('data_contract.table.contract_id')}}
                                                    </div>
                                                </td>
                                                <td width="70%">
                                                    <div class="ml-10 fs-14 fw-700">
                                                        {{$contract->contract_id}}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    {{ __('advance_contracts.details_page.contract_name') }}
                                                </td>
                                                <td>
                                                    <div class="ml-10 fs-14 fw-700">
                                                        {{$contract->contract_number}}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                {{ __('advance_contracts.details_page.warehouse_ownership') }}
                                                </td>
                                                <td>
                                                    <div class="ml-10 fs-14 fw-600">
                                                        {{$contract->getWarehouseOwnerDescriptionAttribute()}}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>{{__('data_contract.contract_forms.place_holder.region')}}</td>
                                                <td>
                                                    <div class="userDatatable-content d-flex flex-wrap d-inline-block align-items-stretch gap-10">
                                                        @foreach (array_slice($regionNames, 0, 10) as $name)
                                                            <span class="bg_gray_badge border-radius-8 userDatatable-content-status active"> {{ $name }}</span>
                                                        @endforeach
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>{{__('data_contract.contract_forms.place_holder.city')}}</td>
                                                <td>
                                                    @php
                                                        $visibleCity = array_slice($cityNames, 0, 5);
                                                        $hiddenCity = array_slice($cityNames, 5);
                                                    @endphp
                                                    <div class="userDatatable-content d-flex d-inline-block gap-10">
                                                    @foreach ($visibleCity as $name)
                                                            <span class="bg_gray_badge border-radius-8 userDatatable-content-status active">{{ $name }}</span>
                                                        @endforeach
                                                        @foreach ($hiddenCity as $name)
                                                            <span class="extra-badge-city bg_gray_badge border-radius-8 userDatatable-content-status active" style="display: none;" >{{ $name }}</span>
                                                        @endforeach
                                                        @if(count($cityNames) > 5)
                                                            <span 
                                                                class="rounded bg-osool-blue text-white cursor-pointer ml-10 expand_all_btn mt-2"
                                                                id="toggle-btn-city"
                                                                onclick="toggleBadges('extra-badge-city','toggle-btn-city')"
                                                            >
                                                                <i id="toggle-icon" class="iconsax fs-22" icon-name="chevron-down"></i>
                                                            </span>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="d-flex">
                                                    <div>
                                                        {{__('data_contract.contract_forms.label.property_name')}}
                                                    </div>
                                                </td>

                                                <td>
                                                    @php
                                                        $visible = array_slice($propertyNamesLimited, 0, 5);
                                                        $hidden = array_slice($propertyNamesLimited, 5);
                                                    @endphp
                                                    <div class="userDatatable-content d-flex flex-wrap d-inline-block">
                                                    @foreach ($visible as $building)
                                                            <span class="bg_gray_badge border-radius-8 userDatatable-content-status active ml-10 mb-2">
                                                                {{ $building['building_tag'] }}
                                                            </span>
                                                        @endforeach
                                                        @foreach ($hidden as $building)
                                                            <span class="extra-badge bg_gray_badge border-radius-8 userDatatable-content-status active ml-10 mb-2"  style="display: none;">
                                                                {{ $building['building_tag'] }}
                                                            </span>
                                                        @endforeach

                                                        @if(count($propertyNamesLimited) > 5)
                                                            <span 
                                                                class="rounded bg-osool-blue text-white cursor-pointer ml-10 expand_all_btn"
                                                                id="toggle-btn"
                                                                onclick="toggleBadges('extra-badge','toggle-btn')"
                                                            >
                                                                <i id="toggle-icon" class="iconsax fs-22" icon-name="chevron-down"></i>
                                                            </span>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="card mt-3 card_content">
                        <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.details_page.contract_feature') }}</h6>
                        </div>

                        <div class="card-body card-progress pt-0">
                            <div class="about-projects">
                                <div class="landing-pages-table table-responsive pb-0">
                                    <table class="table table--default align-left fw-600 fs-12">

                                    <tbody>
                                            <tr>
                                                <td width="50%">
                                                    <div>
                                                    {{ __('advance_contracts.details_page.unit_recieval') }}
                                                    </div>
                                                </td>
                                                <td width="50%">
                                                    <div class="ml-10">
                                                        {{ $contract->useFormOfUnitReceivalText() }} 
                                                        @if($contract->use_form_of_unit_receival == 1)
                                                            ({{ $assetCategories }})
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    {{ __('advance_contracts.details_page.subcontract') }}
                                                </td>
                                                <td>
                                                    <div class="ml-10">
                                                    {{ $contract->allowSubcontractText() }}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    {{ __('advance_contracts.details_page.smart_Assign') }}
                                                </td>
                                                <td>
                                                    <div class="ml-10">
                                                        {{ $contract->useSmartAssigningText() }}
                                                        @if($contract->use_smart_assigning == 1)
                                                            ({{ $smartAssignedServices }})
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">
                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.details_page.items_stock') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.assets_ppm.stocks') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.open_stock') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.low_stock') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.details_page.consumption') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.mandatory') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.approval') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.price') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.assets_ppm.penalty') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach ($items as $item)
                                                <tr>
                                                    <td>{{ $item['name'] }}</td>
                                                    <td>{{ $item['open_stock'] }}</td>
                                                    <td>{{ $item['low_stock']}}</td>
                                                    <td>0</td>
                                                    <td>{{ $item['mandatory'] ? 'Yes' : 'No' }}</td>
                                                    <td>{{ $item['approval'] ? 'Yes' : 'No' }}</td>
                                                    <td>{{ $item['price'] }}</td>
                                                    <td>
                                                        {{ $item['penalty'] }}
                                                        @if ($item['penalty_type'] == 1)
                                                            %
                                                        @elseif ($item['penalty_type'] == 2)
                                                            <img src="{{ asset('currency.svg') }}" alt="Currency" width="12" height="12">
                                                        @else
                                                            —
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach

                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                <th class="b-b-l-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="b-b-r-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">
                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.sla.title') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.details_page.priority_name') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.sla.response_time') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.details_page.service_time') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($contract->contractPriorities as $contractPriority)
                                                <tr>
                                                    <td>{{ $contractPriority->exportPriority->priority_level ?? 'N/A' }}</td>
                                                    <td>{{ ($contractPriority->response_time. ' ' .  $contractPriority->response_time_type ) ?? 'N/A' }}</td>
                                                    <td>{{  ($contractPriority->service_window. ' ' .  $contractPriority->service_window_type ) }}</td>
                                                    
                                                </tr>
                                            @endforeach

                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                <th class="b-b-l-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool b-b-r-r">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-md-6 pt-3">
                    <div class="border-0 card max-w-100 min-h-0">
                        <div class="card-progress text-dark m-0">
                            <div class="d-flex justify-content-between align-items-center">
                                @php
                                    $paymentIntervals = [
                                        '' =>  "Please select payment interval",
                                        'monthly' => __('data_contract.contract_forms.options.payment_interval.monthly'),
                                        'quarterly' => __('data_contract.contract_forms.options.payment_interval.quarterly'),
                                        'semi_annually' => __('data_contract.contract_forms.options.payment_interval.semi_annually'),
                                        'annually' => __('data_contract.contract_forms.options.payment_interval.annually'),
                                    ];
                                @endphp
                                <div class="card-header border-0 px-4">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.details_page.monthly_payment_schedule') }}</h6>
                                </div>
                                <p class="fw-600 mr-3 mb-0">{{ __('advance_contracts.details_page.total_amount') }} : <img  src="{{ asset('currency.svg') }}" style="height: 1em;" alt="Currency" /> {{ $contract->contract_value }}</p>
                            </div>
                            <div class="d-flex justify-content-between fw-700 fs-12 px-4">
                                <p>{{ __('advance_contracts.details_page.interval_of_payments') }}</p>
                                <p>{{ $paymentIntervals[$contract->payment_interval] ?? 'N/A' }}</p>
                            </div>


                            <div class="card-body pt-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable mb-0 ">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.details_page.number_of_payments') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.details_page.total_amount_per_time') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>{{$installmentsCount}}</td>
                                                <td><img  src="{{ asset('currency.svg') }}" style="height: 1em;" alt="Currency" /> {{$perMonthAmount}}</td>
                                            </tr>
                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                <th class="b-b-l-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="b-b-r-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">
                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.property.selected_properties') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary properties_table">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.property.building') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.property.zone') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.property.unit') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.property.each_building') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($buildings as $prop)
                                                <tr class="warehouse-table-tr table-cell position-relative {{ $loop->odd ? '' : 'bg-grey' }}">
                                                    <td>  
                                                        @if(isset($prop['property']->property_type) && $prop['property']->property_type == 'complex')
                                                            {{ $prop['property']['complex_name'] }} - {{ $prop['building_name'] }}
                                                        @else
                                                            {{ $prop['building_name'] }}
                                                        @endif
                                                    </td>
                                                    <td>{{ $prop['zones_count'] }}</td>
                                                    <td>{{ $prop['units_count'] }}</td>
                                                    <td>
                                                        <small class="lh-15 d-inline-block">
                                                        {{ __('advance_contracts.property.has_zones_units', ['zones' => $prop['zones_count'], 'units' => $prop['units_count'] ]) }}
                                                        </small>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                <th class="b-b-l-r text-osool">{{ __('advance_contracts.property.total_properties') }}</th>
                                                <th class="text-osool">{{ $totalZones }} {{ __('advance_contracts.property.zones') }}</th>
                                                <th class="text-osool">{{ $totalUnits }} {{ __('advance_contracts.property.units') }}</th>
                                                <th class="b-b-r-r text-osool"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>

                    @livewire('advance-contracts.kpi-section', ['contract' => $contract, 'kpi' => $groupedIndicators])

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">

                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.general.services') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                            <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.data_agreements.services') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.data_agreements.activated_kpi') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.data_agreements.price') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.data_agreements.description') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($contract->contractServiceKpis as $value)
                                                @php
                                                
                                                    $indicators = json_decode($value->performance_indicator ?? '[]', true);
                                                
                                                    $indicatorNames = collect($indicators)
                                                        ->map(function($indicator) {
                                                            try {
                                                                return (new $indicator)->getBarName();
                                                            } catch (\Throwable $e) {
                                                                return null; // skip if class not found or error
                                                            }
                                                        })
                                                        ->filter()
                                                        ->implode(', ');
                                                @endphp

                                                <tr>
                                                    <td>{{ $value->assetCategory->asset_category ?? 'N/A' }}</td>
                                                    <td>{{$indicatorNames}}</td>
                                                    <td>{{ $value->price }}</td>
                                                    <td>{{ $value->description ?? '' }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                <th class="b-b-l-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="b-b-r-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>

                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">

                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16"> {{ __('advance_contracts.assets_ppm.assets') }}</h6>
                            </div>
                            <div class="userDatatable-content d-flex flex-wrap d-inline-block card-body pt-0">
                                @foreach ($contract->asset_name_labels as $name)
                                    <span class="bg_gray_badge_light border-radius-8 userDatatable-content-status active mr-10 mb-2">
                                        {{ $name }}
                                    </span>
                                @endforeach
                            </div>
                        </div>

                    </div>


                    <div class="border-0 card max-w-100 min-h-0 mt-3 crm">
                        <div class="text-dark m-0">

                            <div class="card-header border-0 d-flex justify-content-between">
                                <h6 class="mb-3 fs-16 fw-700">{{ __('advance_contracts.variation_order.attachment_files') }}</h6>
                            </div>

                            <div class="card-body pt-0 col-12">
                                <div class="item-inner">
                                    <div class="item-content">
                                        @if (!empty($attachment))
                                            <img src="{{ $attachment }}" alt="Preview Image" class="rounded mb-2" style="width: 100%;">
                                        @else
                                            <div class="image-upload border-0 radius-xl">
                                                <label class="mb-2 mt-4">
                                                    <div class="h-100">
                                                        <div class="dplay-tbl">
                                                        
                                                                <div class="dplay-tbl-cell">
                                                                    <div class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                                                        <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative" icon-name="emoji-sad"></i>
                                                                    </div>
                                                                    <p class="drag_drop_txt mt-3">
                                                                       {{ __('advance_contracts.variation_order.no_data_found') }}
                                                                    </p>
                                                                </div>
                                                            
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3 crm">
                        <div class="text-dark m-0">

                            <div class="card-header border-0 d-flex justify-content-between">
                                <h6 class="mb-3 fs-16 fw-700">{{ __('advance_contracts.variation_order.comments') }}</h6>
                            </div>

                            <div class="form-group col-12">
                                <div class="item-inner">
                                    <div class="item-content">
                                        @if (!empty($contract->comment))
                                              <p> {{ $contract->comment }} </p>
                                        @else
                                            <div class="image-upload border-0 radius-xl">
                                                <label class="mb-2 mt-4">
                                                    <div class="h-100">
                                                        <div class="dplay-tbl">
                                                            <div class="dplay-tbl-cell">
                                                                <div class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                                                    <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative" icon-name="emoji-sad"></i>
                                                                </div>
                                                                <p class="drag_drop_txt mt-3">
                                                                    {{ __('advance_contracts.variation_order.no_comments_found') }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-md-12 pr-md-0 pt-3">
                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">
                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.workforce.team_distribute') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.workforce.role') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.proficiency') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.quantity') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.deduction_rate') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.working_days') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.localization_target') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.working_hours_weekly') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.attendance_mandatory') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.details_page.minimum_wage') }}</th>
                                                    <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.details_page.uniform_tool') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($contract->workforceAndTeams as $team)
                                                <tr>
                                                    <td>{{ \App\Enums\Role::label($team->role) }}</td>
                                                    <td>{{ \App\Enums\Proficiency::label($team->proficiency) }}</td>
                                                    <td>{{$team->quantity}}</td>
                                                    <td>{{$team->deduction_rate}}%</td>
                                                    <td>{{$team->working_days}}</td>
                                                    <td>{{$team->localization_target}}%</td>
                                                    <td>{{$team->working_hours}}</td>
                                                    <td>{{$team->attendance_mandatory_text}}</td>
                                                    <td><img  src="{{ asset('currency.svg') }}" style="height: 1em;" alt="Currency" /> {{$team->minimum_wage}}</td>
                                                    <td>{{$team->uniform_and_tools_mandatory_text}}</td>
                                                </tr>
                                            @endforeach

                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                <th class="b-b-l-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                                <th class="b-b-r-r text-osool">
                                                    <div class="invisible">dummy text</div>
                                                </th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <!-- Content -->  
    @push('scripts')
        <script>
            function toggleBadges(eleCls, btnId) {
                const extraBadges = document.querySelectorAll(`.${eleCls}`);
                const icon = document.getElementById(btnId)?.querySelector('i');
            
                extraBadges.forEach(el => {
                    el.style.display = (el.style.display === 'inline') ? 'none' : 'inline';
                });
                const isExpanded = extraBadges[0].style.display === 'inline';
                icon.setAttribute('icon-name', isExpanded ? 'chevron-up' : 'chevron-down');
            }
       
            window.addEventListener('show-toastr', event => {
                    toastr.options = {
                        "closeButton": true,
                        "progressBar": true,
                        "positionClass": "toast-top-center",
                        "timeOut": "3000"
                    };
                    if (event.detail.type === 'success') {
                        toastr.success(event.detail.message);
                    } else if (event.detail.type === 'error') {
                        toastr.error(event.detail.message);
                    }
                });
        </script>
    @endpush
</div>
