<div>
    @if ($isVariation)
        <div class="mb-3">
          <livewire:advance-contracts.alerts.variation-order />
        </div>
    @endif
    <div class="checkout-shipping-form pt-20 card">
        <div class="px-0 pt-0 pb-0 mt-lg-0 border-0">
            <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-3 px-0 mb-0 pt-0 color-dark fw-500">
                {{ __('advance_contracts.data_agreements.agreements_kpis') }}
            </div>
            <div class="card-body px-0 pb-0 pt-0">
                <div class="edit-profile__body property_form_container">
                    <form wire:submit.prevent="submit">
                        <!-- Select Region -->
                        <div class="form-group">
                            <h6 class="mb-3 fw-500"> {{ __('advance_contracts.data_agreements.title') }}</h6>
                            <label>{{ __('advance_contracts.general.regions') }} <small class="required">*</small></label>
                            <div class="atbd-select">
                                <select class="form-control select2-select" data-placeholder="{{ __('advance_contracts.general.please_choose') }}" id="c_region"  multiple  wire:model="region_id">
                                    @foreach($filter_regions as $region)
                                        @if(App::getLocale() == 'en')
                                            <option value="{{ $region['id'] }}">
                                                {{ $region['code'] }} - {{ $region['name'] }}
                                            </option>
                                        @else
                                            <option value="{{ $region['id'] }}">
                                                {{ $region['code'] }} - {{ $region['name_ar'] }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                            @error('region_id') 
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Select City -->
                        <div class="form-group">
                            <label>{{ __('advance_contracts.general.cities') }} <small class="required">*</small></label>
                            <div class="atbd-select">
                                <select class="form-control select2-select" data-placeholder="{{ __('advance_contracts.general.please_choose') }}" id="selected_cities" multiple wire:model="selected_cities">
                                    @foreach($cities as $city)
                                        @if(App::getLocale() == 'en')
                                            <option value="{{ $city['id'] }}"> {{ $city['name_en'] }}</option>
                                        @else
                                            <option value="{{ $city['id'] }}"> {{ $city['name_ar'] }}</option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                            @error('selected_cities') 
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <!-- Select City End -->
                        <!-- Select Properties -->
                        <div class="form-group">
                            <label for="c_property_names">{{ __('advance_contracts.general.properties') }} <small class="required">*</small></label>
                            <div class="atbd-select">
                                <select 
                                    data-placeholder="{{ __('advance_contracts.general.please_choose') }}"
                                    class="form-control select2-select" 
                                    id="c_property_names"
                                    multiple="multiple"
                                    wire:model="selected_properties"
                                >
                                    @foreach($properties as $property)
                                        <option value="{{ $property['id'] }}">
                                            @if($property['property_type'] == 'complex')
                                                {{ $property['complex_name'] }} - {{ $property['building_name'] }}
                                            @else
                                                {{ $property['building_name'] }}
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            @error('selected_properties') 
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <!-- Properties list table -->
                        @if(!empty($paginatedProperties))
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 fw-500 fs-14">{{ __('advance_contracts.property.selected_properties') }}</h6>
                                <div class="d-flex gap-10 table-search">
                                    <div class="position-relative">
                                        <input type="text" class="form-control" placeholder="{{ __('advance_contracts.property.search') }}" wire:model.debounce.500ms="searchProperty">
                                        <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool">{{ __('advance_contracts.property.building') }}</th>
                                                <th class="text-osool">{{ __('advance_contracts.property.zone') }}</th>
                                                <th class="text-osool">{{ __('advance_contracts.property.unit') }}</th>
                                                <th class="b-t-r-r text-osool">{{ __('advance_contracts.property.each_building') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($paginatedProperties as $prop)
                                                <tr class="warehouse-table-tr table-cell position-relative {{ $loop->odd ? '' : 'bg-grey' }}">
                                                <td>  
                                                    @if(isset($prop['property']->property_type) && $prop['property']->property_type == 'complex')
                                                        {{ $prop['property']['complex_name'] }} - {{ $prop['building_name'] }}
                                                    @else
                                                        {{ $prop['building_name'] }}
                                                    @endif
                                                </td>
                                                <td>{{ $prop['zones_count'] }}</td>
                                                <td>{{ $prop['units_count'] }}</td>
                                                <td>
                                                    <small class="lh-15 d-inline-block">
                                                    {{ __('advance_contracts.property.has_zones_units', ['zones' => $prop['zones_count'], 'units' => $prop['units_count'] ]) }}
                                                    </small>
                                                </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                <th class="b-b-l-r text-osool">{{ __('advance_contracts.property.total_properties') }}</th>
                                                <th class="text-osool">{{ $totalZones }} {{ __('advance_contracts.property.zones') }}</th>
                                                <th class="text-osool">{{ $totalUnits }} {{ __('advance_contracts.property.units') }}</th>
                                                <th class="b-b-r-r text-osool"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>

                            <div class="d-flex justify-content-center mt-3">
                            <!-- Numbered pagination -->
                                @for ($i = 1; $i <= $totalPages; $i++)
                                    <button type="button" wire:click="setPage({{ $i }})" class="btn btn-sm {{ $propertyPage == $i ? 'btn-primary' : 'btn-light' }}">
                                        {{ $i }}
                                    </button>
                                @endfor
                            </div>
                        @endif

                        <!-- SLA -->                
                        <div class="d-flex justify-content-between">
                            <h6 class="mb-3 fw-500 fs-14">{{ __('advance_contracts.sla.title') }} <small class="required">*</small></h6>
                        </div>

                        <div class="form-group mb-0">
                            <div class="table-responsive">
                                <table class="table table--default rackshelvestable">
                                    <thead class="userDatatable-header">
                                        <tr class="warehouse-table-tr">
                                            <th class="b-t-l-r text-osool">{{ __('advance_contracts.sla.priorities') }}</th>
                                            <th class="text-osool">{{ __('advance_contracts.sla.service_window') }}</th>
                                            <th class="text-osool">{{ __('advance_contracts.sla.response_time') }}</th>
                                            <th class="b-t-r-r text-osool">{{ __('advance_contracts.sla.actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($slaRows as $index => $row)
                                            <tr class="warehouse-table-tr table-cell position-relative">
                                                <td>
                                                    <div>
                                                        <select
                                                            id="priority_select_{{ $index }}"
                                                            class="form-control select2-select"
                                                            wire:model="slaRows.{{ $index }}.priority_id"
                                                            data-placeholder="{{ __('advance_contracts.general.please_choose') }}"
                                                        >
                                                            <option></option>
                                                            @foreach($priorities as $priority)
                                                                <option
                                                                    value="{{ $priority->id }}"
                                                                    @if(
                                                                        in_array($priority->id, $selectedPriorities)
                                                                        && $priority->id != ($slaRows[$index]['priority_id'] ?? null)
                                                                    )
                                                                        disabled
                                                                    @endif
                                                                >
                                                                    {{ $priority->priority_level }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    @error("slaRows.$index.priority_id")
                                                            <small class="text-danger">{{ $message }}</small>
                                                        @enderror
                                                </td>
                                                <td>
                                                    <div class="min-w-130">
                                                        <div class="border rounded p-2 d-flex gap-10 select-borderless">
                                                            <input type="text"
                                                                class="form-control min-w-130 border-0"
                                                                placeholder="{{ __('advance_contracts.general.enter_value') }}"
                                                                wire:model.defer="slaRows.{{ $index }}.service_window_input">
                                                            <div class="min-w-150">
                                                                <select id="service_window_select_{{ $index }}"  data-placeholder="{{ __('advance_contracts.general.please_choose') }}" class="form-control select2-select" wire:model.defer="slaRows.{{ $index }}.service_window_select">
                                                                        <option></option>
                                                                        <option value="Days">{{ __('advance_contracts.time.days') }}</option>
                                                                        <option value="Hours">{{ __('advance_contracts.time.hours') }}</option>
                                                                        <option value="Minutes">{{ __('advance_contracts.time.minutes') }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                            <div class="d-flex gap-50 mt-1">
                                                                @error("slaRows.$index.service_window_input")
                                                                    <div class="text-danger"><small>{{ $message }}</small></div>
                                                                @enderror

                                                                @error("slaRows.$index.service_window_select")
                                                                    <div class="text-danger"><small>{{ $message }}</small></div>
                                                                @enderror
                                                            </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="min-w-130">
                                                        <div class="border rounded p-2 d-flex gap-10 select-borderless">
                                                            <input type="text"
                                                                class="form-control min-w-130 border-0"
                                                                placeholder="{{ __('advance_contracts.general.enter_value') }}"
                                                                wire:model.defer="slaRows.{{ $index }}.response_time_input">
                                                            

                                                            <div class="min-w-150">
                                                                <select id="response_time_select_{{ $index }}" class="form-control select2-select" data-placeholder="{{ __('advance_contracts.general.please_choose') }}"  wire:model.defer="slaRows.{{ $index }}.response_time_select">
                                                                        <option></option>   
                                                                        <option value="Days">{{ __('advance_contracts.time.days') }}</option>
                                                                        <option value="Hours">{{ __('advance_contracts.time.hours') }}</option>
                                                                        <option value="Minutes">{{ __('advance_contracts.time.minutes') }}</option>
                                                                </select>
                                                            </div>                                                       
                                                        </div>
                                                        <div class="mt-1 d-flex gap-50 mt-1">
                                                            @error("slaRows.$index.response_time_input")
                                                                <div class="text-danger"><small>{{ $message }}</small></div>
                                                            @enderror

                                                            @error("slaRows.$index.response_time_select")
                                                                <div class="text-danger"><small>{{ $message }}</small></div>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                </td>

                                                <td>
                                                    <ul class=" mb-0 d-flex flex-wrap">
                                                        <li wire:click="removeSlaRow({{ $index }})">
                                                            <a href="javascript:void(0);"
                                                                class="remove">
                                                                <i class="iconsax icon text-delete fs-18 mr-0"
                                                                    icon-name="trash"></i>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot class="userDatatable-header text-osool">
                                        <tr>
                                            <td colspan="4" class="text-right p-0">
                                                <div class="action-btn asset_creation_btn_container col-md-12 d-center p-2 b-b-l-r b-b-r-r flex-column align-items-center">
                                                    <button type="button" class="btn px-15 py-1 text-white no-border w-100 bg-osool-new w-auto btn-xs" wire:click="addSlaRow">+ {{ __('advance_contracts.sla.add_sla') }}</button>
                                                      @if ($errors->has('slaRows'))
                                                        <span class="text-danger mt-1">{{ $errors->first('slaRows') }}</span>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        <!-- SLA ENd-->      
                        <!-- KPI -->  
                        <div class="d-flex justify-content-between mt-4">
                            <h6 class="mb-3 fw-500 fs-14">{{ __('advance_contracts.data_agreements.key_performance_indicators') }} <small class="">{{ __('advance_contracts.data_agreements.kpi_limit') }}</small></h6>
                        </div>
                        <div class="form-group">
                            <div class="table-responsive">
                                <table x-data="rackShelfData" class="table table--default rackshelvestable">
                                    <thead class="userDatatable-header">
                                        <tr class="warehouse-table-tr">
                                            <th class="b-t-l-r text-osool" style="width: 200px">{{ __('advance_contracts.data_agreements.performance_indicator') }}</th>
                                            <th class="text-osool b-t-r-r">{{ __('advance_contracts.penalty.ranges') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td colspan="2" class="px-0">
                                            
                                    <div class="accordion mt-3" id="accordionExample">
                                        <!-- Selected KPI loop Started -->
                                        @foreach($selectedKpis as $index => $kpiData)
                                        <div class="card border-0">
                                        <!-- Accordion Header -->
                                        <div class="card-header px-0" id="headingOne">
                                            <h2 class="mb-0 w-100">
                                            <button class="btn btn-link btn-block text-left warehouse-table-tr d-flex align-items-center justify-content-between accordion_header p-1 collapsed" type="button" data-toggle="collapse" data-target="#collapse{{ $index }}" aria-expanded="true" aria-controls="collapse{{ $index }}">
                                                    <span> {{ (new $kpiData['class'])->getName() }}</span>
                                                    <i class="iconsax ml-2" icon-name="chevron-down"></i>
                                            </button>
                                        </h2>
                                        </div>

                                        <!-- Accordion Collapsible Body -->
                                        <div wire:ignore.self id="collapse{{ $index }}" class="collapse" data-parent="#accordionExample">  
                                            <table>
                                                <tr>
                                            <td>
                                            <span> {{ (new $kpiData['class'])->getName() }}</span>
                                            </td>                           
                                            <td>
                                                <div class="border radius-xl p-2">
                                                    <table
                                                        class="table valign-middle table-ranges mb-0">
                                                        <thead >
                                                            <tr>
                                                                <th><span class="b-b-l-r b-t-l-r border-left border-right-0 b-b-r-r-0 b-t-r-r-0">{{ __('advance_contracts.penalty.range') }}</span></th>
                                                                <th class="penalty"><span>{{ __('advance_contracts.penalty.points') }}</span></th>
                                                                <th class=""><span>=</span></th>
                                                                <th class="percentage">
                                                                     <span class="b-b-r-r b-b-l-r-0 b-t-r-r b-t-l-r-0 border-left-0 border-right">
                                                                        <select id="percentage_type_select_{{ $index }}" class="form-control select2-select"
                                                                            wire:model="selectedKpis.{{ $index }}.percentage_type" data-placeholder="{{ __('advance_contracts.general.please_choose') }}">
                                                                            <option value="percentage">{{ __('advance_contracts.penalty.percentage') }}</option>
                                                                            <option value="fixed">{{ __('advance_contracts.penalty.fixed_number') }}</option>
                                                                        </select>
                                                                    </span>
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach(['100%-95%', '95%-90%', '90%-85%', '85%-80%', '80%-75%', '75%-70%', '70%-65%'] as $range)
                                                            <tr>
                                                                <td>
                                                                    <span class="projectDatatable-title no-wrap">{{ $range }}</span>
                                                                </td>
                                                                <td>
                                                                    <span class="ml-3"> 
                                                                        <input type="text" class="form-control px-2 penalty" wire:model.defer="selectedKpis.{{ $index }}.ranges.{{ $range }}.penalty" placeholder="{{ __('advance_contracts.penalty.enter_points') }}" />
                                                                    </span>
                                                                </td>
                                                                <td class=""></td>
                                                                <td class="">
                                                                    <span class="position-relative"> 
                                                                        <input type="text" class="form-control p-0 pr-3 h-100 performance_indicator_0_placeholder deduction_value"
                                                                            wire:model.defer="selectedKpis.{{ $index }}.ranges.{{ $range }}.deduction" placeholder="{{ __('advance_contracts.penalty.percentage') }}" />
                                                                        @if(data_get($selectedKpis, $index.'.percentage_type') === 'percentage')
                                                                                <i class="las la-percent percent_icon fs-16"></i>
                                                                            @elseif(data_get($selectedKpis, $index.'.percentage_type') === 'fixed')
                                                                            <span class="percent_icon">   <img  src="{{ asset('currency.svg') }}" style="height: 1em;vertical-align: top;" alt="Currency" /></span>
                                                                            
                                                                            @endif
                                                                            
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                        </table>
                                        </div>
                                        </div>
                                        @endforeach
                                    </div>
                                        </td>
                                    </tr>
                                        <!-- Selected KPI loop Ended -->
                                    </tbody>
                                    <tfoot class="userDatatable-header text-osool">
                                        <tr>
                                            <td colspan="5" class="text-right p-0">
                                                <div class="action-btn asset_creation_btn_container col-md-12 d-center p-2 mt-2 b-b-l-r b-b-r-r">
                                                <button type="button" class="btn px-15 py-1 text-white no-border w-100 bg-osool-new w-auto btn-xs" wire:click.prevent="openModal">+ {{ __('advance_contracts.data_agreements.add_new_kpi') }}</button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <!-- KPI END -->  
                        <!-- Services -->  
                        <div class="d-flex justify-content-between">
                            <h6 class="mb-3 fw-500 fs-14">{{ __('advance_contracts.general.services') }} <small
                                    class="required">*</small></h6>
                        </div>
                        <div class="form-group">
                            <div class="table-responsive">
                                <table x-data="rackShelfData"
                                    class="table table--default rackshelvestable workforce_table">
                                    <thead class="userDatatable-header">
                                        <tr class="warehouse-table-tr">
                                            <th class="b-t-l-r text-osool">{{ __('advance_contracts.data_agreements.services') }}</th>
                                            <th class="text-osool">{{ __('advance_contracts.data_agreements.activated_kpi') }}</th>
                                            <th class="text-osool">{{ __('advance_contracts.data_agreements.price') }}</th>
                                            <th class="text-osool">{{ __('advance_contracts.data_agreements.description') }}</th>
                                            <th class="b-t-r-r text-osool">{{ __('advance_contracts.sla.actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tableBody1">
                                        @foreach($servicesRows as $index => $row)
                                        <tr class="warehouse-table-tr table-cell position-relative" wire:key="service-row-{{ $row['id'] ?? $index }}">
                                        <td >
                                                <div class="min-w-150">
                                            <select 
                                                id="services_select_{{ $index }}" 
                                                class="form-control select2-select" 
                                                data-placeholder="{{ __('advance_contracts.general.please_choose') }}"
                                                wire:model="servicesRows.{{ $index }}.service_id"
                                            >
                                                <option></option>
                                                @foreach($serviceTypes as $service)
                                                    <option  value="{{ $service->id }}"   
                                                        @if(
                                                            in_array($service->id, $selectedServices)
                                                            && $service->id != ($servicesRows[$index]['service_id'] ?? null)
                                                        )
                                                            disabled
                                                        @endif
                                                        @if($service->id == ($servicesRows[$index]['service_id'] ?? null)) selected @endif
                                                    >
                                                        {{ $service->asset_category }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error("servicesRows.$index.service_id")
                                                <small class="text-danger">{{ $message }}</small>
                                            @enderror
                                        </div>
                                        </td>

                                            <td class="">
                                                <div class="min-w-180 atbd-select">
                                                <select class="form-control select2-select"  id="kpi_ids_{{ $index }}" wire:model.defer="servicesRows.{{ $index }}.kpi_ids" data-placeholder="{{ __('advance_contracts.general.please_choose') }}" multiple>
                                                    @foreach($selectedKpis as $i => $kpiData)
                                                        @if(in_array($kpiData['class'], $allowedKpiClasses))
                                                            <option value="{{ $kpiData['class'] }}">
                                                                {{ (new $kpiData['class'])->getBarName() }}
                                                            </option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                            </td>
                                            <td class="min-w-150">
                                                <input type="text" class="form-control min-w-100px" placeholder="{{ __('advance_contracts.general.enter_value') }}" wire:model.defer="servicesRows.{{ $index }}.price">
                                                @error("servicesRows.$index.price")
                                                    <small class="text-danger">{{ $message }}</small>
                                                @enderror
                                            </td>
                                            <td class="min-w-150">
                                                <input type="text" class="form-control min-w-100px" placeholder="{{ __('advance_contracts.general.enter_text') }}" wire:model.defer="servicesRows.{{ $index }}.description">
                                            </td>
                                            
                                            <td class="align-content-around">
                                                <ul class=" mb-0 d-flex flex-wrap">
                                                    <li>
                                                        <a href="javascript:void(0);" class="remove" wire:click="removeServiceRow({{ $index }})">
                                                            <i class="iconsax icon text-delete fs-18 mr-0" icon-name="trash"></i>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </td>
                                        </tr>
                                        @endforeach   
                                    </tbody>

                                    <tfoot class="userDatatable-header text-osool">
                                        <tr>
                                            <td colspan="5" class="text-right p-0">
                                                <div
                                                    class="action-btn asset_creation_btn_container col-md-12 d-center p-2 b-b-l-r b-b-r-r flex-column align-items-center" >
                                                    <button type="button" class="btn px-15 py-1 text-white no-border w-100 bg-osool-new w-auto btn-xs" id="addService" wire:click="addServiceRow">+ {{ __('advance_contracts.data_agreements.add_service') }}</button>
                                                    @if ($errors->has('servicesRows'))
                                                        <span class="text-danger mt-1">{{ $errors->first('servicesRows') }}</span>
                                                    @endif
                                                </div>
                                            
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <!-- Services End -->  
                        <!-- Side Features -->  
                        <div class="form-group">
                            <h6 class="mb-3 fw-500 fs-14">{{ __('advance_contracts.data_agreements.side_features') }}</h6>
                            <div class="chekbox-horizontal-list">
                            @foreach ([
                                        'allowSub' => __('advance_contracts.data_agreements.allow_sub_contract'),
                                        'smartAssign' => __('advance_contracts.data_agreements.enable_smart_assign'),
                                        'tenant' => __('advance_contracts.data_agreements.use_unit_receival_form')
                                    ] as $id => $label)

                                    <div class="checkbox-theme-default custom-checkbox mr-3">
                                        <input 
                                            class="chekbox" 
                                            type="checkbox" 
                                            name="type"
                                            id="{{ $id }}"
                                            wire:model="{{ 'sideFeatures.' . $id }}" 
                                            @if ($id === 'allowSub' && $isSubcontract) disabled @endif
                                            value="1"
                                        >
                                        <label for="{{ $id }}">
                                            <span class="text-dark">{{ $label }}</span>
                                        </label>
                                    </div>

                                    @if($sideFeatures['tenant'] && $id == 'tenant')
                                        <div class="form-group mt-3">
                                            <label for="selectedTenantCategories"> @lang('data_contract.contract_forms.label.tenants_services_categories') <small class="required">*</small></label>
                                            <select wire:model="selectedTenantCategories" multiple onchange = "selectAllAssetCategory(this, 'selectedTenantCategories')" data-placeholder="{{ __('advance_contracts.general.please_choose') }}" class="form-control" id="selectedTenantCategories">
                                                @if(count($tenantCategories) > 1)
                                                    <option value="all">{{ __('general_sentence.All') }}</option>
                                                @endif
                                                @foreach($tenantCategories as $tenantCategory)
                                                    <option value="{{ $tenantCategory['id'] }}">
                                                        {{ $tenantCategory['asset_category'] }}
                                                        ({{ $tenantCategory['service_type'] == 'hard' ? __('configration_assets.maintanence_type.hard_service') : __('configration_assets.maintanence_type.soft_service') }})
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('selectedTenantCategories')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    @endif
                                    @if($sideFeatures['smartAssign'] && $id == 'smartAssign')
                                        <div class="form-group mt-3">
                                            <label for="selectedSmartAssignCategories">@lang('data_contract.contract_forms.label.select_services_for_smart_assigning') <small class="required">*</small></label>
                                            <select  wire:model="selectedSmartAssignCategories" onchange = "selectAllAssetCategory(this, 'selectedSmartAssignCategories')" data-placeholder="{{ __('advance_contracts.general.please_choose') }}" multiple class="form-control" id="selectedSmartAssignCategories">
                                                @if(count($smartAssignCategories) > 1)
                                                    <option value="all">{{ __('general_sentence.All') }}</option>
                                                @endif
                                                @foreach($smartAssignCategories as $category)
                                                    @if(in_array($category->id, collect($servicesRows)->pluck('service_id')->toArray())) 
                                                    <option value="{{ $category->id }}">
                                                        {{ $category->asset_category }}
                                                        ({{ __('configration_assets.maintanence_type.' . ($category->service_type == 'hard' ? 'hard_service' : 'soft_service')) }})
                                                    </option>
                                                    @endif
                                                @endforeach
                                            </select>
                                            @error('selectedSmartAssignCategories')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror

                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>

                        <!-- Side Features End -->  
                        <div
                            class="d-flex pt-40 justify-content-md-end button-group d-flex pt-25 justify-content-end">
                            <a class="btn btn-outline-lighten fw-400 text-capitalize radius-md" href="{{ route($route_prefix.'create', ['uuid' => $uuid]) }}" >{{ __('advance_contracts.buttons.previous') }} </a>
                            <button id="create" type="button"  wire:click="submit" class="btn bg-primary border-0 btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{ __('advance_contracts.buttons.save_next') }}</button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- ends: card -->
        </div>
        <!-- Modal for KPI Selection -->
        @if($isOpenModal)
            <div class="modal fade show d-block" role="dialog" style="background: rgba(0,0,0,0.5);" aria-modal="true" id="kpiModal">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <!-- Modal Header -->
                        <div class="modal-header kpi_close_btn">
                            <h4 class="modal-title">{{ __('advance_contracts.data_agreements.add_kpi') }}1</h4>
                            <button type="button" class="close border-0" wire:click.prevent="closeModal">
                                <span data-feather="x"></span>
                            </button>
                        </div>

                        <!-- Modal body -->
                        <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
                            <div class="Kpi_popup_checkbox">
                                <div class="form-check">
                                    <input type="hidden" id="selectedKpisJson" name="selectedKpisJson">
                                    <input type="checkbox" id="checkAll" class="form-check-input" >
                                    <label class="form-check-label" for="checkAll">{{ __('advance_contracts.data_agreements.all') }}</label>
                                </div>

                                @php $kpi_number = 1; @endphp
                                @foreach ($availableKpis as $kpiClass)
                                    @php $kpiId = 'kpi' . $kpi_number++ . 'a'; @endphp
                                    <div class="form-check">
                                        <input type="checkbox"
                                            class="form-check-input kpi-checkbox"
                                            id="{{ $kpiId }}"
                                            value="{{ $kpiClass }}"
                                            {{ in_array($kpiClass, $tempSelectedKpis) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="{{ $kpiId }}">
                                            {{ (new $kpiClass)->getName() }}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                            <div class="modal-footer mt-4">
                                <button type="button" wire:click.prevent="closeModal" class="btn btn-default">{{ __('advance_contracts.buttons.close') }}</button>
                                <button type="button" onclick="submitSelectedKpisToLivewire()" class="btn btn-primary">{{ __('advance_contracts.buttons.submit') }}</button>
                            </div>
                    </div>
                </div>
            </div>
        @endif
        <!-- Modal for KPI Selection End -->
    </div>


    @push('scripts')
    <script>   

        document.addEventListener('livewire:load', function () {
            initializeSelect2();
        });

        Livewire.hook('message.processed', (message, component) => {
            initializeSelect2();
        });

        function initializeSelect2() {
            // Initialize select2
            $('select').select2({
                allowClear: true, // Enable clear button
                placeholder: function() {
                    return $(this).data('placeholder') || 'Select an option'; // Default placeholder if none is provided
                }
            });

            $('.select2-select').each(function () {
                let inputId = $(this).attr('id');

                // Check if inputId exists
                if (!inputId) {
                    console.warn('No id found for this select element');
                    return; // Skip processing if there's no id
                }

                let index = inputId.split('_').pop();

                // Percentage Fixed dropdown
                if (inputId.startsWith('percentage_type_select_')) {
                    $(this).on('change', function () {
                        @this.set(`selectedKpis.${index}.percentage_type`, $(this).val());
                    });
                }

                    // Services
                    if (inputId.startsWith('services_select_')) {
                        $(this).on('change', function () {
                            @this.set(`servicesRows.${index}.service_id`, $(this).val());
                        });
                    }

                if (inputId.startsWith('kpi_ids_')) {
                    $(this).on('change', function () {
                        @this.set(`servicesRows.${index}.kpi_ids`, $(this).val());
                    });
                }

                // Priority
                if (inputId.startsWith('priority_select_')) {
                    $(this).on('change', function () {
                        @this.set(`slaRows.${index}.priority_id`, $(this).val());
                    });
                }

                // Service Window
                if (inputId.startsWith('service_window_select_')) {
                    $(this).on('change', function () {
                        @this.set(`slaRows.${index}.service_window_select`, $(this).val());
                    });
                }

                // Response Time
                if (inputId.startsWith('response_time_select_')) {
                    $(this).on('change', function () {
                        @this.set(`slaRows.${index}.response_time_select`, $(this).val());
                    });
                }

                
            });
        

            
            // When value changes via Select2, tell Livewire
            $('#c_region').on('change', function () {
                @this.set('region_id', $(this).val());
            });

            $('#selected_cities').on('change', function () {
                @this.set('selected_cities', $(this).val());
            });

            $('#c_property_names').on('change', function () {
                @this.set('selected_properties', $(this).val());
            });

            $('#selectedTenantCategories').on('change', function () {
                @this.set('selectedTenantCategories', $(this).val());
            });

            $('#selectedSmartAssignCategories').on('change', function () {
                @this.set('selectedSmartAssignCategories', $(this).val());
            });
        }

        // @flip1@ when user select all then all the avail. data would be selected
        function selectAllAssetCategory(e,select_tag_id){
            const select=document.getElementById(select_tag_id)
            if(e.value=='all'){
                console.log('option','all');
            let ids=[]
            Object.keys(select.options).forEach(ind=>{
                if(select.options[ind].value!='all' && select.options[ind].value!=''){
                    ids.push(select.options[ind].value)
                }
            })
            if($(select).val().length == select.options.length){
                ids = [];
            }
            $(select).val(ids);
            $(select).trigger('change');
            }
        }

        window.addEventListener('show-toastr', event => {
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-center",
                "timeOut": "3000"
            };
            if (event.detail.type === 'success') {
                toastr.success(event.detail.message);
            } else if (event.detail.type === 'error') {
                toastr.error(event.detail.message);
            }
        });

       $(document).ready(function () {
            // Delegated event for both 'Check All' and individual checkboxes
            $(document).on('change', '#checkAll, .kpi-checkbox', function () {
                const isCheckAll = $(this).is('#checkAll');
                const isChecked = $(this).is(':checked');

                if (isCheckAll) {
                    // When 'Check All' is toggled
                    $('.kpi-checkbox').prop('checked', isChecked);
                } else {
                    // When any checkbox is toggled, update 'Check All' state
                    const total = $('.kpi-checkbox').length;
                    const checked = $('.kpi-checkbox:checked').length;
                    $('#checkAll').prop('checked', total === checked);
                }
            });
        });
    function submitSelectedKpisToLivewire() {
            const selected = $('.kpi-checkbox:checked').map(function () {
                return $(this).val();
            }).get();

            @this.call('submitKpiForm', selected);
        }

    </script>
    @endpush

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/line-awesome/1.3.0/line-awesome/css/line-awesome.min.css">
</div>
