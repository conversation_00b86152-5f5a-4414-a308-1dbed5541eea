<div>
   <div class="contents contract-show">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcrumb-main user-member justify-content-sm-between ">
                        <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                            <div class="">
                                <div class="page-title d-flex justify-content-between">
                                    <div class="page-title__left">
                                        <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                                            <h4 class="text-capitalize breadcrumb-title d-inline-flex align-items-center gap-10 no-wrap text-osool"><a href="{{route('advance-contracts.view', ['id' => base64_encode($draft->contract_id)] ) }}" class="btn btn-white btn-default text-center svg-20 wh-40 px-0"><i class="iconsax m-0" icon-name="chevron-left-square"></i></a>{{$contractNumberData['value']}}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                       
                            <div class="action-btn flex-fill justify-content-end d-flex gap-10">
                                @if($showCancelButton)
                                    <button class="btn bg-osool-blue radius-xl text-white" data-toggle="modal" data-target="#cancel-modal">{{ __('advance_contracts.variation_order.cancel') }}</button>
                                @endif
                                 @if($showApprovalActions)
                                    <button class="btn bg-osool-blue radius-xl text-white" data-toggle="modal" data-target="#reject-modal">
                                        {{ __('advance_contracts.variation_order.reject') }}
                                    </button>
                                    <button class="btn bg-new-primary radius-xl text-white" data-toggle="modal" data-target="#approve-modal">{{ __('advance_contracts.variation_order.approve') }}</button>
                                @endif
                            </div>
                      
                            @php
                                $statusClassMap = [
                                    'approved' => 'btn px-15 btn-success mr-2 text-white',
                                    'rejected' => 'btn px-15 bg-loss mr-2 text-white',
                                    'cancelled' => 'btn bg-osool-blue radius-xl text-white',
                                ];

                                $statusClass = $statusClassMap[$userApprovalStatus] ?? 'bg-info text-white';

                                // Fetch label from language file
                                $statusLabel = __('advance_contracts.variation_order.' . $userApprovalStatus);
                            @endphp

                            @if(!$showApprovalActions && $userApprovalStatus)
                                <button class="{{ $statusClass }}"> {{ $statusLabel }}</button>
                            @endif


                    </div>
                </div>
            </div>


            <div class="row">
                
                <div class="col-12 mb-3">
                    <div class="alert bg-white d-flex flex-sm-row flex-column align-items-center justify-content-sm-between justify-content-center text-osool  radius-xl gap-10" role="alert">
                        <div class="d-flex align-items-center gap-10">
                            <span class="bg-opacity-primary d-center wh-40 radius-xl"><i class="iconsax fs-22 text-osool" icon-name="info-circle"></i></span> <div><h6 class="text-osool">{{ __('advance_contracts.variation_order.total_changes') }} : {{$totalDifference}}</h6> </div>
                        </div>
                        <div class="d-flex gap-10">
                            <span class="btn bg-opacity-warning radius-xl text-warning cursor-auto">
                                <span class="wh-10 rounded-circle bg-warning mr-2"></span> {{ __('advance_contracts.variation_order.changed_add') }} </span>
                            <span class="btn bg-opacity-loss radius-xl text-loss cursor-auto">
                                <span class="wh-10 rounded-circle bg-loss mr-2"></span> {{ __('advance_contracts.variation_order.removed') }}</span>
                    </div>
                    </div>
                </div>
                
                 <div class="col-md-6 pr-md-0 mb-md-0 mb-3">
                    <div class="card card-overview-progress border-0 max-w-100 min-h-0 h-100">
                        <div class="card-progress h-100">
                            <div class="card-progress__summery d-flex justify-content-between">
                                <div>
                                    <div class="text_info_new mb-1 fs-24 fw-700 {{ $startDateData['class'] }}">{{ $startDateData['value'] }}</div>
                                    <span class="fs-16 fw-600">{{__('data_contract.contract_forms.label.start_date')}}</span>
                                </div>
                                <div>
                    
                                    <div class="color-dark mb-1 fs-24 fw-700 {{ $endDateData['class'] }}">{{ $endDateData['value'] }}</div>
                                    <span class="text-right fs-16 fw-600">{{__('data_contract.contract_forms.label.end_date')}}</span>
                                </div>
                            </div>
                            <div class="card-progress__bar">
                                <div class="progress">
                                    <div class="progress-bar text_info_bg_new border-radius-10" role="progressbar" style="width: {{$percentageData['value']}}%" aria-valuenow="{{$percentageData['value']}}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="progress-excerpt">
                                    <p class="color-dark">{{__('data_contract.contract_forms.label.remaining_time')}}</p>
                                    <span class="progress-total">{{100-round($percentageData['value'])}}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card h-100 border-0 max-w-100 min-h-0">
                        <div class="card-progress d-flex gap-20 h-100">
                            <div class="p-3 w-50">
                                <div>
                                    <div class="card-header border-0 mb-3 px-0 min-h-0">
                                        <h6 class="fw-700 fs-18 text-osool">{{__('admin.akaunting::kpi.overall_kpi')}}</h6>
                                    </div>
                                    <div class="text-light-1 mt-2 mb-3 fs-12 h-40">{{ __('advance_contracts.details_page.kpi_points_description') }}</div>
                                </div>
                                <div class="card-progress__bar">
                                    <div class="progress">
                                        <div class="progress-bar bgGreen border-radius-10" role="progressbar" style="width: 12%" aria-valuenow="" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="progress-excerpt justify-content-end d-flex">
                                        <span class="progress-total fw-500 fs-12 text-dark">12%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="p-3 w-50">
                                <div>
                                    <div class="card-header border-0 mb-3 px-0 min-h-0">
                                    <h6 class="fw-700 fs-18 text-osool">{{__('admin.akaunting::kpi.penality')}}</h6>
                                    </div>
                                    <div class="text-light-1 mt-2 mb-3 fs-12 h-40">{{ __('advance_contracts.details_page.penality_points_description') }} <br><br></div>
                                </div>
                                <div class="card-progress__bar">
                                    <div class="progress">
                                        <div class="progress-bar bg-loss w-0 border-radius-10" role="progressbar" style="width: 52%" aria-valuenow="" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="progress-excerpt justify-content-end d-flex">
                                        <span class="progress-total fw-500 fs-12 text-dark">52%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 pr-0 pt-3">

                    <div class="card border-0 max-w-100 mb-3">
                        <div class="card-progress h-100 d-flex">
                            <div class="card-progress__summery">
                                <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.details_page.service_provider_details') }}</h6>
                                </div>
                                <div class="d-flex align-items-center gap-20 card-body pt-0">
                                    <div>
                                        <img src="{{ asset($serviceProvider->getStorageLogoAttribute()) }}" alt="{{$serviceProvider->name}}" alt="Dummy_logo" class="radius-xl" width="84px" height="84px">
                                    </div>
                                    <div class="color-dark fs-12 fw-600">
                                        <p  class="mb-2">{{ __('advance_contracts.details_page.service_provider_name') }}:</p>
                                        <p  class="mb-2">{{ __('advance_contracts.details_page.service_provider_id') }}:</p>
                                        <p  class="mb-0">{{ __('advance_contracts.details_page.business_field') }}:</p>
                                    </div>
                                    <div class="color-dark fw-700 fs-12">
                                        <p class="mb-2">{{$serviceProvider->name}}</p>
                                        <p class="mb-2">{{$serviceProvider->service_provider_id}}</p>
                                        <p class="mb-0">-</p>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>

                    <div class="card card_content">
                        <div class="card-header border-0">
                            <h6 class="fw-700 fs-16">{{__('data_contract.contract_forms.label.contact_details')}}</h6>
                        </div>

                        <div class="card-body card-progress pt-0">
                            <div class="about-projects">
                                <div class="landing-pages-table table-responsive pb-0">
                                    <table class="table table--default align-left fw-600">

                                        <thead>
                                            <tr>
                                                <th class="fw-800">{{ __('advance_contracts.details_page.title_of_table') }}</th>
                                                <th>
                                                    <div class="ml-10 fw-800">
                                                        {{ __('advance_contracts.details_page.information') }}
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>

                                        <tbody>
                                            <tr>
                                                <td width="30%">
                                                    <div>
                                                        {{__('data_contract.table.contract_id')}}
                                                    </div>
                                                </td>
                                                <td width="70%">
                                                    <div class="ml-10 fs-14 fw-700">
                                                        {{$contract->contract_id}}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    {{ __('advance_contracts.details_page.contract_name') }}
                                                </td>
                                                <td>
                                                    <div class="ml-10 fs-14 fw-700 {{$contractNumberData['class']}}">
                                                        {{$contractNumberData['value']}}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                {{ __('advance_contracts.details_page.warehouse_ownership') }}
                                                </td>
                                                <td>
                                                    <div class="ml-10 fs-14 fw-600">
                                                        {{$contract->getWarehouseOwnerDescriptionAttribute()}}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>{{__('data_contract.contract_forms.place_holder.region')}}</td>
                                                <td>
                                                    <div class="userDatatable-content d-flex flex-wrap d-inline-block">
                                                        {{-- Visible --}}
                                                        @foreach ($visibleRegions as $region)
                                                            @php
                                                                $class = match($region['status']) {
                                                                    'added' => 'bg-opacity-warning text-warning',
                                                                    'deleted' => 'bg-opacity-loss text-loss',
                                                                    default => 'bg_gray_badge',
                                                                };
                                                            @endphp
                                                            <span class="border-radius-8 userDatatable-content-status active ml-10 mt-2 {{ $class }}">
                                                                {{ $region['name'] }}
                                                            </span>
                                                        @endforeach

                                                        {{-- Hidden --}}
                                                        @foreach ($hiddenRegions as $region)
                                                            @php
                                                                $class = match($region['status']) {
                                                                    'added' => 'bg-opacity-warning text-warning',
                                                                    'deleted' => 'bg-opacity-loss text-loss',
                                                                    default => 'bg_gray_badge',
                                                                };
                                                            @endphp
                                                            <span class="extra-badge-region border-radius-8 userDatatable-content-status active ml-10 mt-2 {{ $class }}" style="display: none;">
                                                                {{ $region['name'] }}
                                                            </span>
                                                        @endforeach

                                                        {{-- Toggle --}}
                                                        @if(count($visibleRegions) + count($hiddenRegions) > 5)
                                                            <span 
                                                                class="rounded bg-osool-blue text-white cursor-pointer ml-10 expand_all_btn mt-2"
                                                                id="toggle-btn-region"
                                                                onclick="toggleBadges('extra-badge-region','toggle-btn-region')"
                                                            >
                                                                <i id="toggle-icon" class="iconsax fs-22" icon-name="chevron-down"></i>
                                                            </span>
                                                        @endif

                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>{{__('data_contract.contract_forms.place_holder.city')}}</td>
                                                <td>
                                                    <div class="userDatatable-content d-flex d-inline-block">
                                                    {{-- Visible Cities --}}
                                                    @foreach ($visibleCities as $city)
                                                        @php
                                                            $class = match($city['status']) {
                                                                'added' => 'bg-opacity-warning text-warning',
                                                                'deleted' => 'bg-opacity-loss text-loss',
                                                                default => 'bg_gray_badge',
                                                            };
                                                        @endphp
                                                        <span class="border-radius-8 userDatatable-content-status active ml-10 mt-2 {{ $class }}">
                                                            {{ $city['name'] }}
                                                        </span>
                                                    @endforeach

                                                    {{-- Hidden Cities --}}
                                                    @foreach ($hiddenCities as $city)
                                                        @php
                                                            $class = match($city['status']) {
                                                                'added' => 'bg-opacity-warning text-warning',
                                                                'deleted' => 'bg-opacity-loss text-loss',
                                                                default => 'bg_gray_badge',
                                                            };
                                                        @endphp
                                                        <span class="extra-badge-city border-radius-8 userDatatable-content-status active ml-10 mt-2 {{ $class }}" style="display: none;">
                                                            {{ $city['name'] }}
                                                        </span>
                                                    @endforeach

                                                    {{-- Toggle Button --}}
                                                    @if(count($visibleCities) + count($hiddenCities) > 5)
                                                        <span 
                                                            class="rounded bg-osool-blue text-white cursor-pointer ml-10 expand_all_btn mt-2"
                                                            id="toggle-btn-city"
                                                            onclick="toggleBadges('extra-badge-city','toggle-btn-city')"
                                                        >
                                                            <i id="toggle-icon" class="iconsax fs-22" icon-name="chevron-down"></i>
                                                        </span>
                                                    @endif

                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="d-flex">
                                                    <div>
                                                        {{__('data_contract.contract_forms.label.property_name')}}
                                                    </div>
                                                </td>

                                                <td>
                                                    <div class="userDatatable-content d-flex flex-wrap d-inline-block">
                                                    {{-- Visible Properties --}}
                                                    @foreach ($visibleProperties as $building)
                                                        @php
                                                            $class = match($building['status']) {
                                                                'added' => 'bg-opacity-warning text-warning',
                                                                'deleted' => 'bg-opacity-loss text-loss',
                                                                default => 'bg_gray_badge',
                                                            };
                                                        @endphp
                                                        <span class="border-radius-8 userDatatable-content-status active ml-10 mb-2 {{ $class }}">
                                                            {{ $building['name']}}
                                                        </span>
                                                    @endforeach

                                                    {{-- Hidden Properties --}}
                                                    @foreach ($hiddenProperties as $building)
                                                        @php
                                                            $class = match($building['status']) {
                                                                'added' => 'bg-opacity-warning text-warning',
                                                                'deleted' => 'bg-opacity-loss text-loss',
                                                                default => 'bg_gray_badge',
                                                            };
                                                        @endphp
                                                        <span class="extra-badge border-radius-8 userDatatable-content-status active ml-10 mb-2 {{ $class }}" style="display: none;">
                                                            {{ $building['name']}}
                                                        </span>
                                                    @endforeach

                                                    {{-- Toggle Button --}}
                                                    @if(count($visibleProperties) + count($hiddenProperties) > 5)
                                                        <span 
                                                            class="rounded bg-osool-blue text-white cursor-pointer ml-10 expand_all_btn"
                                                            id="toggle-btn"
                                                            onclick="toggleBadges('extra-badge','toggle-btn')"
                                                        >
                                                            <i id="toggle-icon" class="iconsax fs-22" icon-name="chevron-down"></i>
                                                        </span>
                                                    @endif

                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="card mt-3 card_content">
                        <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.details_page.contract_feature') }}</h6>
                        </div>

                        <div class="card-body card-progress pt-0">
                            <div class="about-projects">
                                <div class="landing-pages-table table-responsive pb-0">
                                    <table class="table table--default align-left fw-600">

                                    <tbody>
                                            <tr>
                                                <td width="50%">
                                                    <div>
                                                    {{ __('advance_contracts.details_page.unit_recieval') }}
                                                    </div>
                                                </td>
                                                <td width="50%">
                                                    <div class="ml-10 {{ $unitRecievalData['class'] }} ">
                                                        {{ $unitRecievalData['value'] ? __('advance_contracts.general.yes') : __('advance_contracts.general.no') }}
                                                        @if($unitRecievalData['value'] && !empty($assetCategoriesWithDiff))
                                                            (
                                                                @foreach($assetCategoriesWithDiff as $category)
                                                                    <span class="{{ $category['class'] }}">{{ $category['name'] }}</span>{{ !$loop->last ? ',' : '' }}
                                                                @endforeach
                                                            )
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    {{ __('advance_contracts.details_page.subcontract') }}
                                                </td>
                                                <td>
                                                    <div class="ml-10 {{ $subContractData['class'] }} ">
                                                   {{ $subContractData['value'] ? __('advance_contracts.general.yes') : __('advance_contracts.general.no') }}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    {{ __('advance_contracts.details_page.smart_Assign') }}
                                                </td>
                                                <td>
                                                    <div class="ml-10 {{ $smartAssignData['class'] }}">
                                                       {{ $smartAssignData['value'] ? __('advance_contracts.general.yes') : __('advance_contracts.general.no') }}

                                                        @if($smartAssignData['value'] && !empty($smartAssignCategoriesWithDiff))
                                                            (
                                                                @foreach($smartAssignCategoriesWithDiff as $category)
                                                                    <span class="{{ $category['class'] }}">{{ $category['name'] }}</span>{{ !$loop->last ? ',' : '' }}
                                                                @endforeach
                                                            )
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">
                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.details_page.items_stock') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.assets_ppm.stocks') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.open_stock') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.low_stock') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.details_page.consumption') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.mandatory') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.approval') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.assets_ppm.price') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.assets_ppm.penalty') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach ($comparedItems as $item)
                                                <tr>
                                                    <td class="{{ $item['fields']['name']['class'] }}" >{{ $item['fields']['name']['variation'] }}</td>
                                                    <td class="{{ $item['fields']['open_stock']['class'] }}" >{{ $item['fields']['open_stock']['variation'] }}</td>
                                                    <td class="{{ $item['fields']['low_stock']['class'] }}" >{{ $item['fields']['low_stock']['variation'] }}</td>
                                                    <td>0</td>
                                                    <td class="{{ $item['fields']['mandatory']['class'] }}" >{{ $item['fields']['mandatory']['variation']  ? 'Yes' : 'No' }}</td>
                                                    <td class="{{ $item['fields']['approval']['class'] }}" >{{ $item['fields']['approval']['variation']  ? 'Yes' : 'No' }}</td>
                                                    <td class="{{ $item['fields']['price']['class'] }}" >{{ $item['fields']['price']['variation'] }}</td>
                                                    <td class="{{ $item['fields']['penalty']['class'] }}" >
                                                        {{ $item['fields']['penalty']['variation'] }}
                                                         @if ( $item['fields']['penalty_type']['variation'] == 1)
                                                           <span class=" $item['fields']['penalty_type']['class']">%</span>
                                                        @elseif ( $item['fields']['penalty_type']['variation'] == 2)
                                                            <img src="{{ asset('currency.svg') }}" alt="Currency" width="12" height="12">
                                                        @else
                                                            —
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach

                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                @for ($i = 0; $i < 8; $i++)
                                                    <th class="b-b-l-r text-osool">
                                                        <div class="invisible">dummy text</div>
                                                    </th>
                                                @endfor
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">
                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.sla.title') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.details_page.priority_name') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.sla.response_time') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.details_page.service_time') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                           @foreach ($comparedPriorities as $priority)
                                            <tr>
                                                {{-- Priority Level --}}
                                                <td class="{{ $priority['fields']['priority_level']['class'] ?? '' }}">
                                                    {{ $priority['fields']['priority_level']['variation'] ?? $priority['fields']['priority_level']['original'] ?? 'N/A' }}
                                                </td>

                                                {{-- Response Time + Type --}}
                                                <td>
                                                    <span class="{{ $priority['fields']['response_time']['class'] ?? '' }}">
                                                        {{ $priority['fields']['response_time']['variation'] ?? $priority['fields']['response_time']['original'] ?? 'N/A' }}
                                                    </span>
                                                    <span class="{{ $priority['fields']['response_time_type']['class'] ?? '' }}">
                                                        {{ $priority['fields']['response_time_type']['variation'] ?? $priority['fields']['response_time_type']['original'] ?? '' }}
                                                    </span>
                                                </td>

                                                {{-- Service Window + Type --}}
                                                <td>
                                                    <span class="{{ $priority['fields']['service_window']['class'] ?? '' }}">
                                                        {{ $priority['fields']['service_window']['variation'] ?? $priority['fields']['service_window']['original'] ?? 'N/A' }}
                                                    </span>
                                                    <span class="{{ $priority['fields']['service_window_type']['class'] ?? '' }}">
                                                        {{ $priority['fields']['service_window_type']['variation'] ?? $priority['fields']['service_window_type']['original'] ?? '' }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach


                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                @for ($i = 0; $i < 3; $i++)
                                                    <th class="b-b-l-r text-osool">
                                                        <div class="invisible">dummy text</div>
                                                    </th>
                                                @endfor
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-md-6 pt-3">
                    <div class="border-0 card max-w-100 min-h-0">
                        <div class="card-progress text-dark m-0">
                            <div class="d-flex justify-content-between align-items-center">
                                @php
                                    $paymentIntervals = [
                                        '' =>  "Please select payment interval",
                                        'monthly' => __('data_contract.contract_forms.options.payment_interval.monthly'),
                                        'quarterly' => __('data_contract.contract_forms.options.payment_interval.quarterly'),
                                        'semi_annually' => __('data_contract.contract_forms.options.payment_interval.semi_annually'),
                                        'annually' => __('data_contract.contract_forms.options.payment_interval.annually'),
                                    ];
                                @endphp
                                <div class="card-header border-0 px-4">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.details_page.monthly_payment_schedule') }}</h6>
                                </div>
                                <p class="fw-600 mr-3 mb-0">{{ __('advance_contracts.details_page.total_amount') }} : <img  src="{{ asset('currency.svg') }}" style="height: 1em;" alt="Currency" /> <span class="{{ $contractValueData['class'] }}">{{ $contractValueData['value'] }}</span></p>
                            </div>
                            <div class="d-flex justify-content-between fw-700 fs-12 px-4">
                                <p>{{ __('advance_contracts.details_page.interval_of_payments') }}</p>
                                <p class="{{$intervalTypeData['class']}}">{{ $paymentIntervals[$intervalTypeData['value']] ?? 'N/A' }}</p>
                            </div>


                            <div class="card-body pt-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable mb-0 ">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.details_page.number_of_payments') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.details_page.total_amount_per_time') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="{{$numberOfPaymentData['class']}}" >{{$numberOfPaymentData['value']}}</td>
                                                <td class="{{$totalAmountPerTimeData['class']}}" ><img  src="{{ asset('currency.svg') }}" style="height: 1em;" alt="Currency" /> {{$totalAmountPerTimeData['value']}}</td>
                                            </tr>
                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                 @for ($i = 0; $i < 2; $i++)
                                                    <th class="b-b-l-r text-osool">
                                                        <div class="invisible">dummy text</div>
                                                    </th>
                                                @endfor
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">
                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.property.selected_properties') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary properties_table">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.property.building') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.property.zone') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.property.unit') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.property.each_building') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                           @foreach($comparedBuildings as $prop)
                                            <tr class="warehouse-table-tr table-cell position-relative {{ $loop->odd ? '' : 'bg-grey' }} ">
                                                <td class="{{ $prop['fields']['building_name']['class'] ?? '' }}">
                                                    @if(isset($prop['fields']['property_type']['variation']) && $prop['fields']['property_type']['variation'] === 'complex')
                                                        {{ $prop['fields']['complex_name']['variation'] ?? '' }} - 
                                                    @endif
                                                 
                                                    {{ $prop['fields']['building_name']['variation'] }}
                                                </td>

                                                <td class="{{ $prop['fields']['zones_count']['class'] ?? '' }}">
                                                   {{ $prop['fields']['zones_count']['variation'] }}
                                                </td>

                                                <td class="{{ $prop['fields']['units_count']['class'] ?? '' }}">
                                                   {{ $prop['fields']['units_count']['variation'] }}
                                                </td>

                                                <td>
                                                    <small class="lh-15 d-inline-block">
                                                        {{ __('advance_contracts.property.has_zones_units', [
                                                            'zones' => $prop['fields']['zones_count']['variation'],
                                                            'units' => $prop['fields']['units_count']['variation']
                                                        ]) }}
                                                    </small>
                                                </td>
                                            </tr>
                                        @endforeach

                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                <th class="b-b-l-r text-osool">{{ __('advance_contracts.property.total_properties') }}</th>
                                                <th class="{{ $totalZonesData['class'] ?? 'text-osool' }}">{{ $totalZonesData['value'] }} {{ __('advance_contracts.property.zones') }}</th>
                                                <th class="{{ $totalUnitData['class'] ?? 'text-osool' }}">{{ $totalUnitData['value'] }} {{ __('advance_contracts.property.units') }}</th>
                                                <th class="b-b-r-r text-osool"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>

                    @livewire('advance-contracts.kpi-diff-section', ['contract' => $contract, 'kpi' => $comparedIndicators])

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">

                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.general.services') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                            <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.data_agreements.services') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.data_agreements.activated_kpi') }}</th>
                                                <th class="text-osool pl-15">{{ __('advance_contracts.data_agreements.price') }}</th>
                                                <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.data_agreements.description') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($contractServiceKpisDiff as $kpi)
                                                @php
                                                    $fields = $kpi['fields'];
                                                 
                                                    $indicatorClassList = explode(',', $fields['kpi_ids']['variation'] ?? '');

                                                    $indicatorNames = collect($indicatorClassList)
                                                        ->map(function ($indicator) {
                                                            try {
                                                                return (new $indicator)->getBarName();
                                                            } catch (\Throwable $e) {
                                                                return null;
                                                            }
                                                        })
                                                        ->filter()
                                                        ->implode(', ');
                                                @endphp

                                                <tr>
                                                    <td class="{{ $fields['service_id']['class'] ?? '' }}">
                                                        {{ $fields['service_name']['variation'] ?? '--' }}
                                                    </td>
                                                    <td class="{{ $fields['kpi_ids']['class'] ?? '' }}">
                                                        {{ $indicatorNames ?: '--' }}
                                                    </td>
                                                    <td class="{{ $fields['price']['class'] ?? '' }}">
                                                        {{ number_format((float) ($fields['price']['variation'] ?? 0), 2) }}
                                                    </td>
                                                    <td class="{{ $fields['description']['class'] ?? '' }}">
                                                        {{ $fields['description']['variation'] ?? '--' }}
                                                    </td>
                                                </tr>
                                            @endforeach

                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                 @for ($i = 0; $i < 4; $i++)
                                                    <th class="b-b-l-r text-osool">
                                                        <div class="invisible">dummy text</div>
                                                    </th>
                                                @endfor
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">

                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16"> {{ __('advance_contracts.assets_ppm.assets') }}</h6>
                            </div>
                            <div class="userDatatable-content d-flex flex-wrap d-inline-block card-body pt-0">
                               @foreach ($assetNameDiffs as $item)
                                    @php
                                        $class = match($item['status']) {
                                            'added' => 'bg-opacity-warning text-warning',
                                            'deleted' => 'bg-opacity-loss text-loss',
                                            default => 'bg_gray_badge',
                                        };
                                    @endphp
                                    <span class="border-radius-8 userDatatable-content-status active ml-10 mt-2 {{ $class }}">
                                        {{ $item['name'] }}
                                    </span>
                                @endforeach
                            </div>
                        </div>

                    </div>


                    <div class="border-0 card max-w-100 min-h-0 mt-3 crm">
                        <div class="text-dark m-0">

                            <div class="card-header border-0 d-flex justify-content-between">
                                <h6 class="mb-3 fs-16 fw-700">{{ __('advance_contracts.variation_order.attachment_files') }}</h6>
                            </div>

                            <div class="card-body pt-0 col-12">
                                <div class="item-inner">
                                    <div class="item-content {{ $pictureData['class'] }}">
                                        @if (!empty($pictureData['url']))
                                            <img src="{{ $pictureData['url'] }}" alt="Preview Image" class="rounded mb-2" style="width: 100%;">
                                        @else
                                            <div class="image-upload border-0 radius-xl">
                                                <label class="mb-2 mt-4">
                                                    <div class="h-100">
                                                        <div class="dplay-tbl">
                                                        
                                                                <div class="dplay-tbl-cell">
                                                                    <div class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                                                        <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative" icon-name="emoji-sad"></i>
                                                                    </div>
                                                                    <p class="drag_drop_txt mt-3">
                                                                       {{ __('advance_contracts.variation_order.no_data_found') }}
                                                                    </p>
                                                                </div>
                                                            
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        @endif
                                                      
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-0 card max-w-100 min-h-0 mt-3 crm">
                        <div class="text-dark m-0">

                            <div class="card-header border-0 d-flex justify-content-between">
                                <h6 class="mb-3 fs-16 fw-700">  {{ __('advance_contracts.variation_order.comments') }}</h6>
                            </div>

                            <div class="form-group col-12">
                                <div class="item-inner">
                                    <div class="item-content">
                                        @if (!empty($commentData['value']))
                                              <p class="{{ $commentData['class'] }} "> {{ $commentData['value'] }} </p>
                                        @else
                                            <div class="image-upload border-0 radius-xl">
                                                <label class="mb-2 mt-4">
                                                    <div class="h-100">
                                                        <div class="dplay-tbl">
                                                            <div class="dplay-tbl-cell">
                                                                <div class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                                                    <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative" icon-name="emoji-sad"></i>
                                                                </div>
                                                                <p class="drag_drop_txt mt-3">
                                                                    {{ __('advance_contracts.variation_order.no_comments_found') }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-md-12 pr-0 pt-3">
                    <div class="border-0 card max-w-100 min-h-0 mt-3">
                        <div class="card-progress text-dark m-0">
                            <div class="card-header border-0">
                                    <h6 class="fw-700 fs-16">{{ __('advance_contracts.workforce.team_distribute') }}</h6>
                            </div>

                            <div class="card-body pt-0 mb-0">
                                <div class="table-responsive">
                                    <table class="table table--default rackshelvestable table_text_primary">
                                        <thead class="userDatatable-header">
                                            <tr class="warehouse-table-tr">
                                                <th class="b-t-l-r text-osool pl-15">{{ __('advance_contracts.workforce.role') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.proficiency') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.quantity') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.deduction_rate') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.working_days') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.localization_target') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.working_hours_weekly') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.workforce.attendance_mandatory') }}</th>
                                                    <th class="text-osool pl-15">{{ __('advance_contracts.details_page.minimum_wage') }}</th>
                                                    <th class="b-t-r-r text-osool pl-15">{{ __('advance_contracts.details_page.uniform_tool') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                           @foreach ($comparedWorkforce as $team)
                                            <tr class="{{ $team['status'] === 'deleted' ? 'bg-danger-subtle' : ($team['status'] === 'added' ? 'bg-warning-subtle' : '') }}">
                                                <td class="{{ $team['fields']['role']['class'] ?? '' }}">
                                                    {{ \App\Enums\Role::label($team['fields']['role']['variation'] ?? '') ?: '--' }}
                                                </td>

                                                <td class="{{ $team['fields']['proficiency']['class'] ?? '' }}">
                                                    {{ \App\Enums\Proficiency::label($team['fields']['proficiency']['variation'] ?? '') ?: '--' }}
                                                </td>
                                                <td class="{{ $team['fields']['quantity']['class'] ?? '' }}">{{ $team['fields']['quantity']['variation'] ?? '--' }}</td>
                                                <td class="{{ $team['fields']['deduction_rate']['class'] ?? '' }}">{{ $team['fields']['deduction_rate']['variation'] ?? '--' }}%</td>
                                                <td class="{{ $team['fields']['working_days']['class'] ?? '' }}">{{ $team['fields']['working_days']['variation'] ?? '--' }}</td>
                                                <td class="{{ $team['fields']['localization_target']['class'] ?? '' }}">{{ $team['fields']['localization_target']['variation'] ?? '--' }}%</td>
                                                <td class="{{ $team['fields']['working_hours']['class'] ?? '' }}">{{ $team['fields']['working_hours']['variation'] ?? '--' }}</td>
                                                <td class="{{ $team['fields']['attendance_mandatory']['class'] ?? '' }}">
                                                    {{ ($team['fields']['attendance_mandatory']['variation'] ?? '--') == 1 ? 'Yes' : 'No' }}
                                                </td>
                                                <td class="{{ $team['fields']['minimum_wage']['class'] ?? '' }}" ><img src="{{ asset('currency.svg') }}" style="height: 1em;" alt="Currency" /> {{ $team['fields']['minimum_wage']['variation'] ?? '--' }}</td>
                                                <td class="{{ $team['fields']['uniform_and_tools_mandatory']['class'] ?? '' }}"> {{ ($team['fields']['uniform_and_tools_mandatory']['variation'] ?? '--') == 1 ? 'Yes' : 'No' }}
                                                </td>
                                            </tr>
                                        @endforeach


                                        </tbody>
                                        <tfoot class="userDatatable-header text-osool">
                                            <tr>
                                                 @for ($i = 0; $i < 10; $i++)
                                                    <th class="b-b-l-r text-osool">
                                                        <div class="invisible">dummy text</div>
                                                    </th>
                                                @endfor
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Approval Modal -->
        <div wire:ignore.self class="modal fade" id="approve-modal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content radius-xxl">
                    <div class="modal-body pb-0">
                        <div class="text-center mb-4">
                            <h1 class="text-new-primary mb-3"><i class="iconsax fs-60" icon-name="tick-circle"></i></h1>
                            <h5 class="mb-3">Approval Confirmation</h5>
                            <p>{{ __('advance_contracts.variation_order.about_to') }} <span class="text-new-primary fw-600">{{ __('advance_contracts.variation_order.approve') }} </span> {{ __('advance_contracts.variation_order.variation_order') }}</p>
                        </div>

                        <div class="form-group">
                            <label>{{ __('advance_contracts.variation_order.comment') }} <small>{{ __('advance_contracts.variation_order.optional') }}</small></label>
                            <textarea class="textarea form-control" wire:model.defer="approveComment" placeholder="{{ __('advance_contracts.variation_order.enter_comments') }}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-between border-0 px-4 pb-4">
                        <button type="button" class="btn bg-gray-light text-white flex-fill radius-xl" data-dismiss="modal">
                             {{ __('advance_contracts.variation_order.cancel') }}
                        </button>
                        <button type="button" class="btn bg-new-primary text-white flex-fill radius-xl"
                            wire:click="performAction('approved')">{{ __('advance_contracts.variation_order.yes_approve_it') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reject  Modal -->
       <div class="modal fade" id="reject-modal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content radius-xxl">
                    <div class="modal-body pb-0">
                        <div class="text-center mb-4">
                            <h1 class="text-loss mb-3"><i class="iconsax fs-60" icon-name="warning-triangle"></i></h1>
                            <h5 class="mb-3">@lang('lead.modal.delete_title')</h5>
                            <p>{{ __('advance_contracts.variation_order.about_to') }} <span class="text-loss fw-600">{{ __('advance_contracts.variation_order.reject') }}</span> {{ __('advance_contracts.variation_order.variation_order') }}</p>
                        </div>
                        <div class="form-group">
                            <label>{{ __('advance_contracts.variation_order.rejected_reason') }} <small class="required">*</small></label>
                            <textarea wire:model.defer="rejectComment"
                                    class="textarea form-control"
                                    placeholder="{{ __('advance_contracts.variation_order.enter_rejected_reason') }}"></textarea>
                            @error('rejectComment') <small class="text-danger">{{ $message }}</small> @enderror
                        </div>
                    </div>
                    <div class="modal-footer justify-content-between border-0 px-4 pb-4">
                        <button type="button" class="btn bg-gray-light text-white flex-fill radius-xl"
                                data-dismiss="modal">@lang('lead.modal.no_keep')</button>
                        <button type="button" wire:click.prevent="performAction('rejected')"
                                class="btn bg-loss text-white flex-fill radius-xl">
                            @lang('lead.modal.yes_delete')
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <!-- Cancel  Modal -->
        <div class="modal fade" id="cancel-modal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content radius-xxl">
                    <div class="modal-body pb-0">
                        <div class="text-center mb-4">
                            <h1 class="text-osool mb-3"><i class="iconsax fs-60" icon-name="not-allowed-3"></i></h1>
                            <h5 class="mb-3">@lang('lead.modal.delete_title')</h5>
                            <p>{{ __('advance_contracts.variation_order.about_to') }} <span class="text-osool fw-600">{{ __('advance_contracts.variation_order.cancel') }}</span> {{ __('advance_contracts.variation_order.variation_order') }}</p>
                        </div>
                        <div class="form-group">
                            <label>{{ __('advance_contracts.variation_order.cancel_reason') }} <small class="required">*</small></label>
                            <textarea wire:model.defer="cancelComment"
                                    class="textarea form-control"
                                    placeholder="{{ __('advance_contracts.variation_order.enter_Cancel_reason') }}"></textarea>
                            @error('cancelComment') <small class="text-danger">{{ $message }}</small> @enderror
                        </div>
                    </div>
                    <div class="modal-footer justify-content-between border-0 px-4 pb-4">
                        <button type="button" class="btn bg-gray-light text-white flex-fill radius-xl"
                                data-dismiss="modal">@lang('lead.modal.no_keep')</button>
                        <button type="button" wire:click.prevent="performAction('cancelled')"
                                class="btn bg-osool-blue text-white flex-fill radius-xl">
                            @lang('lead.modal.yes_delete')
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>
    @push('scripts')
        <script>
            function toggleBadges(eleCls, btnId) {
                const extraBadges = document.querySelectorAll(`.${eleCls}`);
                const icon = document.getElementById(btnId)?.querySelector('i');
            
                extraBadges.forEach(el => {
                    el.style.display = (el.style.display === 'inline') ? 'none' : 'inline';
                });
                const isExpanded = extraBadges[0].style.display === 'inline';
                icon.setAttribute('icon-name', isExpanded ? 'chevron-up' : 'chevron-down');
            }
       
            window.addEventListener('show-toastr', event => {
                    toastr.options = {
                        "closeButton": true,
                        "progressBar": true,
                        "positionClass": "toast-top-center",
                        "timeOut": "3000"
                    };
                    if (event.detail.type === 'success') {
                        toastr.success(event.detail.message);
                    } else if (event.detail.type === 'error') {
                        toastr.error(event.detail.message);
                    }
                });
   
            window.addEventListener('close-modals', () => {
                $('#approve-modal').modal('hide');
                $('#reject-modal').modal('hide');
                $('#cancel-modal').modal('hide');
            });
        </script>
    @endpush
</div>
