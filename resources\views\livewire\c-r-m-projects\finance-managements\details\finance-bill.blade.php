<div>
    <div class = "contents crm">
        <div class = "container-fluid">
            <div class = "col-lg-12">
                <div class = "row justify-content-sm-between align-items-center justify-content-center flex-sm-row flex-column">
                    <div class = "page-title-wrap">
                        <div class = "page-title d-flex justify-content-between">
                            <div class = "page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class = "user-member__title mr-sm-25 ml-0">
                                    <h4 class = "text-capitalize fw-500 breadcrumb-title fs-16">@lang('finace_manage.common.details_bill')</h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class = "atbd-breadcrumb nav">
                                <li class = "atbd-breadcrumb__item">
                                    <a>@lang('finace_manage.common.dashboard')</a>
                                    <span class = "breadcrumb__seperator">
                                        <span class = "la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class = "atbd-breadcrumb__item">
                                    <a href="{{ route('CRMProjects.finance-manage.bill', ['id' => Crypt::encrypt($projectID)]) }}">@lang('finace_manage.common.bill')</a>
                                    <span class = "breadcrumb__seperator">
                                        <span class = "la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class = "atbd-breadcrumb__item">
                                    <a>@lang('finace_manage.common.details_bill')</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class = "d-flex gap-10 breadcrumb_right_icons flex-wrap"> 
                        <a data-toggle = "tooltip" title = "@lang('finace_manage.common.copy')" href = "javascript:void(0);" class = "btn btn-white btn-default text-center svg-20 wh-45" wire:click = "copyURL">
                            <i class = "iconsax icon text-primary" icon-name = "document-copy"></i>
                        </a>
                    
                    </div>
                </div>
            </div>
            @php
//echo '<pre>';print_r($invoiceDetails);die;
            @endphp

@if($invoiceDetails['data']['bill']['status'] != "Paid")

            <div class = "card mb-3 {{ isset($invoiceDetails) && $invoiceDetails['data']['bill']['status'] == "success" && strtolower($invoiceDetails['data']['bill']['status']) == '1' ? 'd-none' : '' }}">
                <div class = "card-body">
                    <div class = "row text-center proposal-timeline">
                        <div class = "col-md-6 col-lg-4 col-xl-4">
                            <div class = "p-3 bg-grey radius-xl h-100">
                                <div class = "timeline-icons text-center primary active">
                                    <span class = "timeline-dots"></span>
                                    <i class = "iconsax text-primary fs-22 d-center wh-50 bg-white rounded-circle shadow border-primary mx-auto" icon-name = "add"></i>
                                </div>
                                <h6 class = "text-primary my-3">@lang('finace_manage.common.create_bill')</h6>
                                <p class = "text-muted text-sm mb-3">
                                    <i class = "fa fa-clock"></i>
                                    @lang('finace_manage.common.created_on')
                                    {{  $invoiceDetails['data']['bill']['due_date']}}
                                </p>
                                <a href = "{{ route('CRMProjects.finance-manage.edit.bill', ['projectId' => \Crypt::encrypt($projectID),'id' => $documentId]) }}" class = "btn btn-sm btn-primary mx-auto" data-bs-toggle = "tooltip" title = "@lang('finace_manage.common.edit') ">
                                    <i class = "iconsax" icon-name = "edit-1"></i>
                                    @lang('finace_manage.common.edit') 
                                </a>
                            </div>
                        </div>
                        <div class = "col-md-6 col-lg-4 col-xl-4">
                            <div class = "p-3 border radius-xl h-100">
                                <div class = "timeline-icons warning active">
                                    <span class = "timeline-dots"></span>
                                    <i class = "iconsax text-warning fs-22 d-center wh-50 bg-white rounded-circle shadow border-warning mx-auto" icon-name = "mail"></i>
                                </div>
                                <h6 class = "text-warning my-3">@lang('finace_manage.common.send_bill')</h6>
                                @if(isset($invoiceDetails) && $invoiceDetails['data']['bill']['status'] == "1")
                                    @if(is_null($invoiceDetails['data']['bill']['send_date']))
                                        <p class = "text-muted text-sm mb-3">
                                            <small>
                                                @lang('finace_manage.common.status') : 
                                                <span class = "d-inline-block">@lang('finace_manage.common.not_sent')</span>
                                            </small>
                                        </p>
                                    @endif
                                @else
                                    -
                                @endif
                                @if(isset($invoiceDetails))
                                    @if(is_null($invoiceDetails['data']['bill']['send_date']))
                                        <div>
                                            <div wire:loading class = "text-center" wire:target = "sendDocument">
                                                <button type = "button" class = "btn btn-sm btn-warning mx-auto" wire:loading.attr = "disabled">
                                                    <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span> 
                                                    @lang('work_order.common.loading')
                                                </button>
                                            </div>
                                            <button type = "button" class = "btn btn-sm btn-warning mx-auto" wire:loading.class = "hide" wire:target = "sendDocument" wire:click = "sendDocument()" data-bs-toggle = "tooltip" title = "@lang('finace_manage.common.send')">
                                                <i class = "iconsax" icon-name = "send-1"></i>
                                                @lang('finace_manage.common.send') 
                                            </button>
                                        </div>
                                    @else
                                        <i class = "fa fa-clock"></i>
                                        @lang('finace_manage.common.sent_on')
                                        {{ $invoiceDetails['data']['bill']['send_date'] ?? '-' }}
                                    @endif
                                @else
                                    -
                                @endif
                            </div>
                        </div>
                        <div class = "col-md-6 col-lg-4 col-xl-4">
                            <div class = "p-3 border radius-xl h-100">
                                <div class = "timeline-icons osool active">
                                    <span class = "timeline-dots"></span>
                                    <i class = "iconsax text-osool fs-22 d-center wh-50 bg-white rounded-circle shadow border-osool mx-auto" icon-name = "dollar-circle"></i>
                                </div>
                                <h6 class = "text-info my-3">@lang('finace_manage.common.pay_bill')</h6>
                                <small class = "mx-auto">
                                    @lang('finace_manage.common.status'): {{$invoiceDetails['data']['bill']['status']}}
                                </small>



                                        <button type = "button" class = "btn btn-sm btn-primary mx-auto mt-3  @if(is_null($invoiceDetails['data']['bill']['send_date'])) d-none   @endif" id = "add-payment-button">
                                            <i class = "iconsax" icon-name = "money-add"></i>
                                            @lang('finace_manage.common.add_payment') 
                                        </button>
                                
                            
                            </div>
                        </div>
                    </div>
                </div>
            </div>

@endif

            <div class = "mb-3">


                <div class = "d-flex justify-content-sm-between mb-3">
                    @if(isset($invoiceDetails['data']['bill']))
                        <ul class = "nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id = "myTab" role = "tablist">
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded active" id = "home-tab" data-toggle = "tab" data-target = "#invoice-tab" type = "button" role = "tab" aria-controls = "home" aria-selected = "true">@lang('finace_manage.common.bill')</button>
                            </li>
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded" id = "profile-tab" data-toggle = "tab" data-target = "#payment_summary" type = "button" role = "tab" aria-controls = "profile" aria-selected = "false">@lang('finace_manage.common.payment_summary')</button>
                            </li>
                             <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded" id = "profile-tab" data-toggle = "tab" data-target = "#payment_summary2" type = "button" role = "tab" aria-controls = "profile" aria-selected = "false">@lang('finace_manage.common.debit_note_summary')</button>
                            </li>
                       
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded" id = "profile-tab" data-toggle = "tab" data-target = "#attachments-tab" type = "button" role = "tab" aria-controls = "profile2" aria-selected = "false">@lang('finace_manage.common.attachment')</button>
                            </li>
                        </ul>
                    @endif
                    <div class = "d-flex gap-10">
                        @if(session()->has('success'))
                            <div class = "alert alert-success" role = "alert">
                                {{ session()->get('success') }}
                            </div>
                        @elseif(session()->has('error'))
                            <div class = "alert alert-danger" role = "alert">
                                {{ session()->get('error') }}
                            </div>
                        @endif
                        @php 
                            $this->clearDataSession('success');
                            $this->clearDataSession('error');
                        @endphp
                    </div>
                    <div class = "d-flex gap-10">
                      
                        <div>
                                <div wire:loading class = "text-center" wire:target = "resendBill">
                                    <button type = "button" class = "btn bg-new-primary">
                                        <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span> 
                                        @lang('work_order.common.loading')
                                    </button>
                                </div>


@if(isset($invoiceDetails['data']['bill']['send_date']) && strtolower($invoiceDetails['data']['bill']['send_date']) != 'draft')
<button class = "btn bg-new-primary" wire:loading.class = "hide" wire:target = "resendBill" wire:click = "resendBill()">@lang('finace_manage.common.resend_bill')</button>
@endif

                            </div>
                        <div>
                           
                            <button class = "btn bg-new-primary"  onclick="downloadPDF()">@lang('finace_manage.common.download')</button>
                        </div>
                    </div>
                </div>
                <div class = "tab-content" id = "myTabContent">
                    <div class = "tab-pane fade show active" id = "invoice-tab" role = "tabpanel" aria-labelledby = "home-tab">
                        @include('livewire.c-r-m-projects.finance-managements.details.composants.bill_invoice', ['projectID' => "$projectID", 'documentId' => "$documentId", 'documentType' => $documentType,'invoiceDetails'=>$invoiceDetails,'invoiceDeliveryDetails'=>$invoiceDeliveryDetails])
                    </div>
                    <div class = "tab-pane fade  " id = "payment_summary" role = "tabpanel" aria-labelledby = "home-tab">
                        @include('livewire.c-r-m-projects.finance-managements.details.composants.bill_payment', ['projectID' => "$projectID", 'documentId' => "$documentId", 'documentType' => $documentType,'invoiceDetails'=>$invoiceDetails])
                    </div>

                     <div class = "tab-pane fade  " id = "payment_summary2" role = "tabpanel" aria-labelledby = "home-tab">
                        @include('livewire.c-r-m-projects.finance-managements.details.composants.bill_debit_note', ['projectID' => "$projectID", 'documentId' => "$documentId", 'documentType' => $documentType,'invoiceDetails'=>$invoiceDetails])
                    </div>
                 


                    @if(isset($invoiceDetails) && $invoiceDetails['status'] == "success" && !is_null($invoiceDetails['data']['bill']['send_date']))
                        <div class = "tab-pane fade" id = "attachments-tab" role = "tabpanel" aria-labelledby = "profile-tab">
                            @livewire('c-r-m-projects.finance-managements.details.composants.attachment', ['projectID' => "$projectID", 'documentId' => "$documentId", 'documentType' => $documentType])
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class = "modal fade delete" id = "invoice-delivery-modal" tabindex = "-1" role = "dialog" aria-labelledby = "deleteModalLabel" aria-hidden = "true" wire:ignore.self>
        <div class = "modal-dialog modal-lg modal-dialog-centered" role = "document">
            <div class = "modal-content radius-xl">
                <div class = "modal-header">
                    <h5 class = "modal-title" id = "exampleModalLabel">@lang('finace_manage.common.invoice_delivery_form')</h5>
                    <button type = "button" class = "close" data-dismiss = "modal" aria-label = "Close">
                        <span aria-hidden = "true">
                            <i class = "iconsax" icon-name = "x"></i>
                        </span>
                    </button>
                </div>
                @if(isset($invoiceDeliveryDetails) && $invoiceDeliveryDetails['status'] == "success")

                    <div class = "modal-body">
                        <div class = "d-flex justify-content-between align-items-center pb-3 border-bottom mb-3">
                            <img src = "{{asset('img/OSOOL_logo_svg.svg')}}" class = "max-w-130" />
                            <div>
                                <div wire:loading class = "text-center" wire:target = "downloadDeliveryInvoice">
                                    <button type = "button" class = "btn btn-primary btn-xs px-10" wire:loading.attr = "disabled">
                                        <i class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></i> 
                                        @lang('work_order.common.loading')
                                    </button>
                                </div>
                                <button type = "button" class = "btn btn-primary btn-xs px-10" wire:loading.class = "hide" wire:target = "downloadDeliveryInvoice" wire:click = "downloadDeliveryInvoice()">
                                    <i class = "iconsax m-0 py-10 fs-18 mx-1" icon-name = "download-1"></i>
                                    @lang('finace_manage.common.download') 
                                </button>
                            </div>
                        </div>
                        <div id = "printableArea">
                            <div>
                                <div class = "mb-2">
                                    <strong class = "mt-2">
  @php
//echo '<pre>';print_r($invoiceDeliveryDetails['data']['invoice']['invoice_number']);
                @endphp
                                    @lang('finace_manage.common.invoice_id')  :</strong>
                                    {{ $invoiceDeliveryDetails['data']['invoice']['invoice_number'] ?? '-' }}
                                </div>
                                <div class = "mb-2">
                                    <strong>@lang('finace_manage.common.invoice_date')  :</strong>
                                    {{ $invoiceDeliveryDetails['data']['invoice']['issue_date'] ?? '-' }}<br />
                                </div>
                                <div class = "mb-2">
                                    <div class = "d-flex align-items-center">
                                        <span class = "fw-600">@lang('finace_manage.common.invoice') : </span>
                                        @if(isset($invoiceDeliveryDetails) && $invoiceDeliveryDetails['status'] == "success")
                                            @switch($invoiceDeliveryDetails['data']['invoice']['status'])
                                                @case('Paid')
                                                    <span class = "bg-opacity-danger text-danger rounded-pill userDatatable-content-status active ml-2 py-3 px-4">@lang('finace_manage.common.paid')</span>
                                                @break

                                                @case('Sent')
                                                    <span class = "bg-opacity-warning text-warning rounded-pill userDatatable-content-status active ml-2 py-3 px-4">@lang('finace_manage.common.sent')</span>  
                                                @break
                                                
                                                @case('Draft')
                                                    <span class = "bg-opacity-primary text-primary rounded-pill userDatatable-content-status active ml-2 py-3 px-4">@lang('finace_manage.common.draft')</span>  
                                                @break

                                                @default
                                                    -
                                                @break 
                                            @endswitch
                                        @else
                                            -
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class = "table-responsive mt-4">
                                <table class = "table table--default">
                                    <thead class = "userDatatable-header">
                                        <tr class = "thead-default">
                                            <th class = "text-dark">@lang('finace_manage.common.project')</th>
                                            <th class = "text-dark">@lang('finace_manage.common.description')</th>
                                            <th class = "text-dark">@lang('finace_manage.common.quantity')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(isset($invoiceDeliveryDetails['data']['items']) && count($invoiceDeliveryDetails['data']['items']) > 0)
                                            @foreach ($invoiceDeliveryDetails['data']['items'] as $data)
                                                <tr>
                                                    <td>{{ $data['project_name'] ?? '-' }}</td>
                                                    <td>
                                                        <span>{{ !empty($data['description']) ? $data['description'] : '-' }}</span>
                                                    </td>
                                                    <td>{{ $data['quantity'] ?? '-' }}</td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan = "3" class = "text-center">@lang('CRMProjects.no_items')</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                            <div class = "bg-grey radius-xl p-3 d-flex align-items-end flex-column justify-content-end" style = "min-height: 100px;">
                                <h6 class = " m-r-10 fs-16">@lang('finace_manage.common.customer_signature')</h6>
                            </div>
                        </div>
                    </div>
                @else
                    <div class = "modal-body">
                        <div class = "d-flex justify-content-center align-items-center pb-3 border-bottom mb-3 mx-auto">
                            <div class = "row">
                                <div class = "PropertyListEmpty">
                                    <img src = "{{asset('empty-icon/Building_amico.svg')}}" class = "fourth_img">
                                    <h4 class = "first_title">@lang('finace_manage.common.no_data_found')</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <div class = "modal fade delete" id = "add-payment-modal" tabindex = "-1" role = "dialog" aria-labelledby = "deleteModalLabel" aria-hidden = "true" wire:ignore.self>
        <div class = "modal-dialog modal-lg modal-dialog-centered" role = "document">
            <div class = "modal-content radius-xl">
                <div class = "modal-header">
                    <h5 class = "modal-title" id = "exampleModalLabel">@lang('finace_manage.common.add_payment')</h5>
                    <button type = "button" class = "close" data-dismiss = "modal" aria-label = "Close">
                        <span aria-hidden = "true">
                            <i class = "iconsax" icon-name = "x"></i>
                        </span>
                    </button>
                </div>
                <div class = "modal-body">
                    <form name = "creation-form" id = "creation-form" enctype = "multipart/form-data" wire:submit.prevent = "submitPaymentForm">
                        @csrf
                        <div class = "row">
                            <div class = "col-md-6">
                                <div class = "form-group" wire:ignore>
                                    <label for = "date">
                                        @lang('finace_manage.common.date') 
                                        <small class = "required">*</small>
                                    </label>
                                    <div class = "position-relative">
                                        <input type = "text" name = "date" id = "date" datepicker-autohide class = "form-control datepicker" placeholder = "@lang('finace_manage.common.enter_date')">
                                        <i class = "iconsax field-icon text-light" icon-name = "calendar-1"></i>
                                    </div>
                                </div>
                                @error('date')
                                    <span class = "text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class = "col-md-6">
                                <div class = "form-group">
                                    <label for = "amount">
                                        @lang('finace_manage.common.amount') 
                                        <small class = "required">*</small>
                                    </label>
                                    <input type = "text" class = "form-control" name = "amount" id = "amount" wire:model.live.debounce.250ms = "amount" placeholder = "@lang('finace_manage.common.enter_amount')"/>
                                </div>
                                @error('amount')
                                    <span class = "text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class = "row">
                            <div class = "col-md-6">
                                <div class = "form-group" wire:ignore>
                                    <label for = "account">
                                        @lang('finace_manage.common.account')  
                                        <small class = "required">*</small>
                                    </label>
                                    <select class = "form-control select2-select" id = "account" name = "account">
                                        <option value = "" selected disabled>@lang('finace_manage.common.select_account')</option>
                                        <option value = "cach">@lang('finace_manage.common.cach') </option>
                                    </select>
                                </div>
                                @error('account')
                                    <span class = "text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class = "col-md-6">
                                <div class = "form-group">
                                    <label for = "reference">
                                        @lang('finace_manage.common.reference') 
                                        <small class = "required">*</small>
                                    </label>
                                    <input type = "text" class = "form-control" name = "reference" id = "reference" wire:model.live.debounce.250ms = "reference" placeholder = "@lang('finace_manage.common.enter_reference')"/>
                                </div>
                                @error('reference')
                                    <span class = "text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class = "row mb-2">
                            <div class = "col-md-12">
                                <div class = "form-group">
                                    <label for = "description">
                                        @lang('CRMProjects.common.description')
                                        <small class = "required">*</small>
                                    </label>
                                    <textarea id = "description" class = "form-control" placeholder = "@lang('CRMProjects.common.placeholder_enter_description')" wire:model.live.debounce.250ms = "description" rows = "4" name = "description"></textarea>
                                </div>
                                @error('description')
                                    <span class = "text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class = "row mb-2">
                            <div class = "col-md-12">
                                <label for = "payment_receipt">
                                    @lang('finace_manage.common.payment_receipt')
                                    <small class = "required">*</small>
                                </label>
                                <div class = "import-wrapper" wire:ignore>
                                    <label for = "file-upload2" class = "upload-box cursor-pointer radius-xl bg-grey d-flex flex-column align-items-center m-0">
                                        <span class = "wh-40 rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center mb-2">
                                            <i class = "iconsax fs-22" icon-name = "upload-1"></i>
                                        </span>
                                        <p class = "fs-16 text-dark mb-0">@lang('finace_manage.common.drag_drop') 
                                            <span class = "text-primary">@lang('finace_manage.common.choose_file')</span>@lang('finace_manage.common.to_upload')
                                        </p>
                                        <input type = "file" id = "file-upload2" name = "file" wire:model.live.debounce.250ms = "file">
                                    </label>
                                    <div class = "progress-wrapper p-3 border rounded">
                                        <div class = "file-details d-flex justify-content-between mb-2">
                                            <div class = "d-flex align-items-center gap-10">
                                                <img src = "{{ asset('img/file.png') }}" class = "p-1 border rounded" width = "24">
                                                <div>
                                                    <span class = "d-block" id = "file-name2">PPM</span> 
                                                    <span id = "file-size2">0 MB</span>
                                                </div>
                                            </div>
                                            <span class = "cancel-btn">
                                                <i class = "iconsax fs-20 cursor-pointer" icon-name = "x"></i>
                                            </span>
                                        </div>
                                        <div class = "progress-bar">
                                            <div class = "progress-fill"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @error('file')
                            <span class = "text-danger">{{ $message }}</span>
                        @enderror
                        <div class = "modal-footer d-flex justify-content-end">
                            <button type = "button" class = "cancel-btn btn btn-default bg-white btn-light" data-dismiss = "modal" wire:click = "resetPaymentForm">@lang('finace_manage.common.cancel')</button>
                            <div wire:loading class = "text-center" wire:target = "submitPaymentForm">
                                <button type = "submit" class = "btn bg-new-primary radius-xl" wire:loading.attr = "disabled">
                                    <span class = "spinner-border spinner-border-sm text-white" role = "status" aria-hidden = "true"></span> 
                                    @lang('finace_manage.common.create')
                                </button>
                            </div>
                            <button type = "submit" wire:loading.class = "hide" wire:target = "submitPaymentForm" class = "btn bg-new-primary radius-xl">
                                @lang('finace_manage.common.create')
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>