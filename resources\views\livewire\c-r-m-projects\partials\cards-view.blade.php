<div class="">
    @if (count($projects) > 0)
        <div class="cards-view gap-20 projects-list">

            @foreach ($projects as $project)
                <div class="">
                    <div data-lead-id="33" data-stage-id="209"
                        class="card radius-xl p-xxl-4 p-xl-3 p-2 shadow-lg d-flex align-items-start flex-column h-100">
                        <div class="mb-auto w-100">
                            <div class="d-flex justify-content-between align-items-top">
                                <div class="d-flex align-items-top position-relative cursor-pointer"
                                    onclick="window.location.href='{{ route('CRMProjects.details', Crypt::encrypt($project['id'])) }}'">
                                    <div class="user-profile mr-15 ">
                                        <span
                                            class="wh-40 rounded-circle d-center fs-20 {{-- bg-osool-new --}} shadow-lg text-white  FLName_avatar">{{ mb_substr($project['name'], 0, 1, 'UTF-8') }}
                                        </span>
                                    </div>
                                    <div class="text-osool projects_list">
                                        <p class="mb-0 text-new-primary d-flex align-items-center fw-600">
                                            {{ $project['name'] . '-' . $project['id'] }}</p>
                                        @if ($project['status'] == 'Draft')
                                            <small class="bg-draft rounded text-dark status-badge">
                                                @lang('CRMProjects.common.draft')
                                            </small>
                                        @elseif ($project['status'] == 'OnHold')
                                            <small class="bg-warning rounded text-white status-badge">
                                                @lang('CRMProjects.common.onhold')
                                            </small>
                                        @elseif($project['status'] == 'Finished')
                                            <small class="bg-win rounded text-white status-badge">
                                                @lang('CRMProjects.common.finished')
                                            </small>
                                        @else
                                            <small class="bg-hold rounded text-white status-badge">
                                                @lang('CRMProjects.common.ongoing')
                                            </small>
                                        @endif

                                    </div>
                                </div>

                                @if (auth()->user()->user_type != 'building_manager')
                                    <div class="dropdown">
                                        <span
                                            class="d-center rounded border-osool wh-20 dropdown-toggle cursor-pointer text-osool"
                                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i
                                                class="las la-ellipsis-v"></i></span>
                                        <div class="dropdown-menu py-0" aria-labelledby="dropdownMenuButton">

                                            <a class="dropdown-item px-2" href="javascript:void(0);"
                                                wire:click="openInviteUserModal({{ $project['id'] }}, '{{ json_encode($project['users'] ?? []) }}')">
                                                @lang('CRMProjects.common.invite_users')
                                            </a>
                                            <a class="dropdown-item px-2" href="javascript:void(0);"
                                                wire:click="openShareToClientModal({{ $project['id'] }}, '{{ json_encode($project['clients'] ?? []) }}')">
                                                @lang('CRMProjects.common.share_clients')
                                            </a>
                                            <a class="dropdown-item px-2"href="javascript:void(0);"
                                                wire:click="openDuplicateProjectModal({{ $project['id'] }}, '{{ $project['name'] }}')">
                                                @lang('CRMProjects.common.duplicate')
                                            </a>
                                            <a class="dropdown-item px-2" href="javascript:void(0);"
                                                wire:click="openSaveProjectAsTemplateModal({{ $project['id'] }})">
                                                @lang('CRMProjects.common.save_as_template')
                                            </a>
                                            <a class="dropdown-item px-2" href="javascript:void(0);"
                                                wire:click="openEditModal({{ $project['id'] }})">
                                                @lang('CRMProjects.common.edit')
                                            </a>
                                            <a class="dropdown-item px-2" href="javascript:void(0);"
                                                wire:click="openDeleteModal({{ $project['id'] }}, '{{ $project['name'] }}')">
                                                @lang('CRMProjects.common.delete')
                                            </a>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <p class="mt-3 mb-0 description"
                                title="{{ Str::Limit($project['description'], 800, '...') }}">
                                {{ Str::Limit($project['description'], 800, '...') }}
                            </p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="fw-600"> @lang('CRMProjects.project_type') :</span>
                                <span class="fw-600">@if(!empty($project['project_type'])) @lang('CRMProjects.project_type_dropdown.' . $project['project_type']) @endif </span>
                            </div>
                             <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="fw-600"> @lang('CRMProjects.priority_level') :</span>
                                <span class="fw-600">@if(!empty($project['priority_level'])) @lang('CRMProjects.priority_level_dropdown.' . $project['priority_level']) @endif </span>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="fw-600"> @lang('CRMProjects.common.due_date') :</span>
                                <span class="fw-600"> {{ $project['end_date'] }} </span>
                            </div>
                            <div class="mt-2">
                                <span class="text-osool mb-1 d-block fw-600">@lang('CRMProjects.common.members')</span>

                                <div class="profile-group ml-2">
                                    @php
                                        $usersToShow = 4;
                                        $userCount = count($project['users'] ?? []);
                                    @endphp
                                    @foreach ($project['users'] ?? [] as $index => $user)
                                        @if ($index < $usersToShow)
                                            <div class="profile">
                                                <img src="{{ $user['avatar'] }}" data-toggle="tooltip"
                                                    title="{{ $user['name'] }}" alt="{{ $user['name'] }}" />
                                            </div>
                                        @endif
                                    @endforeach
                                    @if ($userCount > $usersToShow)
                                        <div class="profile extra d-center">
                                            <span class="">+{{ $userCount - $usersToShow }}</span>
                                        </div>
                                    @endif

                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-3 w-100">
                            <div>
                                <span class="mr-2 fs-14 fw-500"><i class="iconsax icon text-osool fs-17 mr-0"
                                        icon-name="message-dots"></i> {{ $project['total_comments'] ?? 0 }} </span>
                                <span class="fs-14 fw-500"><i class="iconsax icon text-osool fs-17 mr-0"
                                        icon-name="document-text-1"></i> {{ $project['total_tasks'] ?? 0 }} </span>
                            </div>
                            <a href="{{ route('CRMProjects.task-board-list-view', encrypt($project['id'])) }}"
                                class="py-1 px-2 bg-new-primary rounded text-white"> @lang('CRMProjects.common.task_board') </a>
                        </div>

                    </div>
                </div>
            @endforeach






        </div>
    @else
        <div class="row">
            <div class="PropertyListEmpty">
                <img src="{{ asset('empty-icon/no-projects.svg') }}" class="fourth_img" alt="">
                <h4 class="first_title">@lang('CRMProjects.common.no_projects_yet')</h4>
                <h6 class="second_title">@lang('CRMProjects.common.projects_list_will_appear')</h6>
            </div>
        </div>
    @endif

    @if (count($projects) > 0)
        @php
            $perPageOptions = [8, 12, 16];
        @endphp
        @include('livewire.c-r-m-projects.partials.paginator')
    @endif







</div>
