<div>


    <div class="contents crm crm-dashboard">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center">
                    <div class="page-title-wrap">
                        <div class="page-title d-flex justify-content-between">
                            <div
                                class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        @lang('CRMProjects.common.project_details')
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{ route('admin.dashboard') }}">@lang('CRMProjects.common.dashboard')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('CRMProjects.common.project_details')</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex gap-10 breadcrumb_right_icons mb-sm-0 mb-3">

                        @if(auth()->user()->user_type != 'building_manager')
                        <div class="d-flex gap-10 breadcrumb_right_icons flex-wrap">
                            <a href="javascript:void(0);" wire:click="showListBMA" class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                            title="@lang('CRMProjects.common.assign_building_manager')">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="building-2"></i>
                        </a>
                            {{-- <button class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="Business Mapping" aria-expanded="true">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="flow-chart-1"></i>
                            </button>
                            <button class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="Video Hub">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="video"></i>
                            </button> --}}
                            <button class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="@lang('CRMProjects.common.copy')" wire:click="copyURL">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                    icon-name="document-copy"></i>
                            </button>

                            <button class="btn btn-white btn-default text-center svg-20 wh-45"
                            data-toggle="modal" data-target="#projectSettingModal" title="Share Project Settings">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="setting-1"></i>
                            </button>

                            {{--
                            <button class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="Calendar">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="calendar-1"></i>
                            </button>


                            <button class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="RFx">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="note"></i>
                            </button> --}}
                         
                            <a href="{{ route('CRMProjects.calendar', encrypt($projectData['id'])) }}" class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="@lang('CRMProjects.common.calendar')">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="calendar-1"></i>
                            </a>
                            <a  href="{{ route('CRMProjects.finance-manage.proposol', ['id' => Crypt::encrypt($projectData['id'])]) }}" class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                            title="@lang('CRMProjects.common.finance')">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="money-in"></i>
                        </a>
                            <a href = "{{ route('CRMProjects.charts.openGantChart',  ['projectId' => Crypt::encryptString($projectData['id'])]) }}" class = "btn btn-white btn-default text-center svg-20 wh-45" data-toggle = "tooltip" title = "@lang('CRMProjects.common.gantt_charts')">
                                <i class = "iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name = "bar-graph-1"></i>
                            </a>
                            <a href="{{ route('CRMProjects.incident-report', encrypt($projectData['id'])) }}"
                                class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title=" @lang('CRMProjects.incident-report')">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="info-badge"></i>
                            </a>
                        @endif
                            <a href="{{ route('CRMProjects.task-board-list-view', encrypt($projectData['id'])) }}"
                                class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="@lang('CRMProjects.task-board')">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                    icon-name="clipboard-text-1"></i>
                            </a>

                        </div>
                    </div>
                    <!--====End Design for Export PDF===-->
                </div>
            </div>
            <div class="row">

                <div class="col-lg-12">
                    <div class="row">
                        <div class="col-md-6 col-sm-12 align-items-streched mb-lg-0 mb-md-0 mb-30 d-flex aos-init aos-animate"
                            data-aos="fade-up" data-aos-delay="200" data-aos-duration="1000">
                            <div class="card broder-0 d-flex align-items-streched w-100 h-100 dash-crm-logo">
                                <div class="card-body py-30 d-flex flex-column justify-content-between">
                                    <div class="d-flex justify-content-between">
                                        <div class="text-white pr-3">

                                            <h6 class="text-white mb-2 fs-20">{{ $projectData['title'] . '-' . $projectData['id']}}
                                                @if ($projectData['status'] == 'Draft')
                                                    <small
                                                        class="py-1 px-2 bg-draft rounded text-dark status-dashboard fs-12 d-inline-block">
                                                        @lang('CRMProjects.common.draft')
                                                    </small>
                                                @elseif ($projectData['status'] == 'OnHold')
                                                    <small
                                                        class="py-1 px-2 bg-warning rounded text-white status-dashboard fs-12 d-inline-block">
                                                        @lang('CRMProjects.common.onhold')
                                                    </small>
                                                @elseif($projectData['status'] == 'Finished')
                                                    <small
                                                        class="py-1 px-2 bg-win rounded text-white status-dashboard fs-12 d-inline-block">
                                                        @lang('CRMProjects.common.finished')
                                                    </small>
                                                @else
                                                    <small
                                                        class="py-1 px-2 bg-hold rounded text-white status-dashboard fs-12 d-inline-block">
                                                        @lang('CRMProjects.common.ongoing')
                                                    </small>
                                                @endif

                                            </h6>
                                            <p class="project-description site-scrollbar">{{ $projectData['description'] }} </p>
                                        </div>

                               
                                        <div class="crm-logo position-relative">
                                            <div
                                                class="logo-inner d-flex justify-content-center align-items-center position-relative">
                                                <span
                                                    class="wh-50 rounded-circle d-center fs-20 shadow-lg text-white  FLName_avatar">{{ mb_substr($projectData['title'], 0, 1, 'UTF-8') }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mt-3 px-3 py-2 radius-xl align-items-center"
                                        style="background: rgba(0, 0, 0, .1);">
                                        <div class="d-flex text-white gap-10">
                                            <div>
                                                <small>@lang('CRMProjects.priority_level'):</small>
                                                <p class="mb-0">
                                                @if(!empty($projectData['priority_level'])) @lang('CRMProjects.priority_level_dropdown.' . $projectData['priority_level']) @endif</p>
                                            </div>
                                            <div>
                                                <small>@lang('CRMProjects.project_type'):</small>
                                                <p class="mb-0">
                                                @if(!empty($projectData['project_type'])) @lang('CRMProjects.project_type_dropdown.' . $projectData['project_type']) @endif</p>
                                            </div>
                                            <div>
                                                <small>@lang('CRMProjects.common.start_date'):</small>
                                                <p class="mb-0">{{ $projectData['start_date'] }}</p>
                                            </div>
                                            <div>
                                                <small>@lang('CRMProjects.common.end_date'):</small>
                                                <p class="mb-0">{{ $projectData['end_date'] }}</p>
                                            </div>
                                            <div>
                                                <small>@lang('CRMProjects.common.total_membres'):</small>
                                                <p class="mb-0">
                                                    {{ isset($projectData['users']) && $projectData['users'] ? count($projectData['users']) : 0 }}
                                                </p>
                                            </div>
                                        </div>

                                        @if(auth()->user()->user_type != 'building_manager')
                                        <div class="d-flex gap-10">
                                            <button
                                                class="btn bg-new-primary text-white btn-default text-center svg-20 wh-30 px-0"
                                                data-toggle="tooltip" title="@lang('CRMProjects.common.edit')" wire:click.prevent="editProject({{  $projectData['id']}})">
                                                <i class="iconsax icon fs-18 mr-0" icon-name="message-edit"></i>
                                            </button>
                                  <button
                                                class="btn bg-new-primary text-white btn-default text-center svg-20 wh-30 px-0"
                                                data-toggle="tooltip" title="@lang('CRMProjects.common.delete')" wire:click="openDeleteModal({{ $projectData['id'] }}, '{{ $projectData['title'] }}')">
                                                <i class="iconsax icon fs-18 mr-0" icon-name="trash"></i>
                                            </button>
                                        </div>
                                        @endif
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex align-items-stretch">
                            <div class="row d-flex align-items-stretch flex-grow-1">
                                <div class="col-sm-6 mb-3 d-flex note aos-init aos-animate" data-aos="fade-up"
                                    data-aos-delay="400" data-aos-duration="1000">
                                    <div
                                        class="card broder-0 d-flex align-items-streched w-100 h-100 bgRedLight overflow-hidden py-3">
                                        <div class="bg_circle_shape1 bgRed opacity-7"></div>
                                        <div class="bg_circle_shape2 bgRed opacity-7"></div>
                                        <div class="dash-circle wh-70 bgRed position-absolute rounded-circle"></div>
                                        <div class="d-flex-center px-4 h-100">
                                            <div class="d-flex justify-content-between w-100">
                                                <div>
                                                    <span
                                                        class="wh-40 bg-white rounded d-inline-flex d-flex-center mb-2 position-relative"><i
                                                            class="iconsax icon fs-18 colorRed"
                                                            icon-name="calendar-1"></i></span>
                                                    <p class="fs-16 mb-0 colorRed">@lang('CRMProjects.common.days_left') </p>
                                                </div>
                                                <h3 class="fw-500">{{ abs($daysleft) }}</h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3 d-flex note aos-init aos-animate" data-aos="fade-up"
                                    data-aos-delay="600" data-aos-duration="1000">
                                    <div
                                        class="card broder-0 d-flex align-items-streched w-100  h-100 bgGreenLight overflow-hidden py-3">
                                        <div class="bg_circle_shape1 bgGreen opacity-3"></div>
                                        <div class="bg_circle_shape2 bgGreen opacity-3"></div>
                                        <div class="dash-circle wh-70 bgGreen opacity-5 position-absolute rounded-circle"></div>
                                        <div class="d-flex-center px-4 h-100">
                                            <div class="d-flex justify-content-between w-100">
                                                <div>
                                                    <span
                                                        class="wh-40 bg-white rounded d-inline-flex d-flex-center mb-2 position-relative"><i
                                                            class="iconsax icon fs-18 colorGreen"
                                                            icon-name="money-out"></i></span>
                                                    <p class="fs-16 mb-0 colorGreen">@lang('CRMProjects.common.budget')</p>
                                                </div>
                                                <h3 class="fw-500">{{ $projectData['budget'] ?? 0 }} </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-3 mb-sm-0 d-flex note aos-init aos-animate" data-aos="fade-up"
                                    data-aos-delay="800" data-aos-duration="1000">
                                    <div
                                        class="card broder-0 d-flex align-items-streched w-100  h-100 bgOrangeLight overflow-hidden py-3">
                                        <div class="bg_circle_shape1 bgOrange opacity-3"></div>
                                        <div class="bg_circle_shape2 bgOrange opacity-3"></div>
                                        <div class="dash-circle wh-70 bgOrange opacity-5 position-absolute rounded-circle"></div>
                                        <div class="d-flex-center px-4 h-100">
                                            <div class="d-flex justify-content-between w-100">
                                                <div>
                                                    <span
                                                        class="wh-40 bg-white rounded d-inline-flex d-flex-center mb-2 position-relative"><i
                                                            class="iconsax icon fs-18 colorOrange"
                                                            icon-name="tick-square"></i></span>
                                                    <p class="fs-16 mb-0 colorOrange">@lang('CRMProjects.common.total_task')</p>
                                                </div>
                                                <h3 class="fw-500">{{ $projectData['total_task'] ?? 0 }} </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 d-flex note aos-init aos-animate" data-aos="fade-up"
                                    data-aos-delay="400" data-aos-duration="1000">
                                    <div
                                        class="card broder-0 d-flex align-items-streched w-100 h-100 bgBlueLight overflow-hidden py-3">
                                        <div class="bg_circle_shape1 bgBlue opacity-3"></div>
                                        <div class="bg_circle_shape2 bgBlue opacity-3"></div>
                                        <div class="dash-circle wh-70 bgBlue opacity-5 position-absolute rounded-circle"></div>
                                        <div class="d-flex-center px-4 h-100">
                                            <div class="d-flex justify-content-between w-100">
                                                <div>
                                                    <span
                                                        class="wh-40 bg-white rounded d-inline-flex d-flex-center mb-2 position-relative"><i
                                                            class="iconsax icon fs-18 colorBlue"
                                                            icon-name="message-text"></i></span>
                                                    <p class="fs-16 mb-0 colorBlue">@lang('CRMProjects.common.comments') </p>
                                                </div>
                                                <h3 class="fw-500">{{ $projectData['total_comment'] ?? 0 }} </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-sm-12 mb-lg-0 mb-md-0 mb-30 mt-4">
                            <div class="card broder-0 d-flex align-items-streched w-100 mb-3">
                                <div class="card-header">
                                    <h6>@lang('CRMProjects.common.progress_last_week_tasks')</h6>
                                </div>
                                <div class="card-body py-30 d-flex justify-content-center">
                                    <canvas id="tasksOverviewChart"></canvas>
                                </div>
                            </div>
                        </div>

                        @include('livewire.c-r-m-projects.partials.details.vendor-client-user-share')


                        @include('livewire.c-r-m-projects.modals.delete-confirm')

                        @include('livewire.c-r-m-projects.modals.confirm-delete-file')
                        @include('livewire.c-r-m-projects.partials.details.milestones')

                        @include('livewire.c-r-m-projects.partials.details.files')


                        @include('livewire.c-r-m-projects.partials.details.activity')
                        @include('livewire.c-r-m-projects.partials.details.documents')

                        {{-- <livewire:c-r-m-projects.documents :projectID="$projectID" :projectName="$projectData['title']" /> --}}
                         @include('livewire.c-r-m-projects.partials.details.setting')
                         @include('livewire.c-r-m-projects.partials.details.bma_list')
      
                    </div>
                    @include('livewire.c-r-m-projects.modals.inviteUser')
                    @include('livewire.c-r-m-projects.modals.shareClient')
                    @include('livewire.c-r-m-projects.modals.shareVendors')
                    @include('livewire.c-r-m-projects.modals.assign_BMA')
                    @include('livewire.c-r-m-projects.modals.CancelProjectAccessModal')
                    @include('livewire.c-r-m-projects.modals.delete-confirm-milstone')
                    @include('livewire.c-r-m-projects.modals.deleteAssignBMA')
                </div>
            </div>

        </div>
    </div><div wire:ignore.self data-backdrop="static" class="modal fade new-popup" id="edit-project" tabindex="-1" role="dialog"
    aria-labelledby="editModalLabel" aria-modal="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <h6 class="modal-title" id="editModalLabel">@lang('CRMProjects.common.edit_project')</h6>
                <button  wire:ignore type="button" class="close border-0" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span> 
                  </button>
            </div>

            {{-- <form> --}}
            <div class="modal-body">




 <div class="row">
<div class="col-md-6">
<div class="form-group">
<label for="usersList">@lang('CRMProjects.project_type') <small class="required">*</small></label>
<select class="form-control" name="project_type"wire:model.defer="project_type">
<option value="">{{ __('Select') }}</option>
@foreach ($projectType as $pt)
<option value="{{ $pt['value'] }}">@lang('CRMProjects.project_type_dropdown.' . $pt['value'])</option>
@endforeach
</select>
@error('project_type')
<span class="text-danger">{{ $message }}</span>
@enderror
</div>
</div>


<div class="col-md-6">
<div class="form-group">
<label for="usersList">@lang('CRMProjects.priority_level') <small class="required">*</small></label>
<select class="form-control" name="priority_level"wire:model.defer="priority_level">
<option value="">{{ __('Select') }}</option>
@foreach ($priorityLevel as $pt)
<option value="{{ $pt['value'] }}">@lang('CRMProjects.priority_level_dropdown.' . $pt['value'])</option>
@endforeach
</select>
@error('priority_level')
<span class="text-danger">{{ $message }}</span>
@enderror
</div>
</div>

</div>

                    <div class="form-group">
                        <label for="lead_stage_name">
                            @lang('CRMProjects.common.name') <small class="required">*</small>
                        </label>
                        <input type="text" class="form-control" id="lead_stage_name" wire:model.defer="name"
                            placeholder="@lang('CRMProjects.common.placeholder_enter_project_name')" required="" />
                        @error('name')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>



            <div class="form-group">


                    </div>
                    <div class="form-group ">
                        <label for="pipeline">
                            @lang('CRMProjects.common.description') <small class="required">*</small>
                        </label>
                        <textarea class="form-control {{-- textarea --}}" id="edit_description" placeholder="@lang('CRMProjects.common.placeholder_enter_description')" wire:model.defer="description"
                            rows="4"></textarea>
                        @error('description')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                        <label class="mx-auto" id="counter_edit"></label>
                    </div>
                    <div class="row">



                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lead_stage_budget">
                                    @lang('CRMProjects.common.status')
                                </label>
                                <select class="form-control  " name="status" id="status"  wire:model.defer="statuss">
                                    <option value="Draft"> @lang('CRMProjects.common.draft')</option>
                                    <option value="Ongoing"> @lang('CRMProjects.common.ongoing')</option>
                                    <option value="Finished">  @lang('CRMProjects.common.finished')</option>
                                    <option value="OnHold"> @lang('CRMProjects.common.onhold')</option>
                                </select>


                            </div>
                        </div>



                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="lead_stage_budget">
                                    @lang('CRMProjects.common.budget') <small class="required">*</small>
                                </label>
                                <input type="number" min="1.00" class="form-control" id="project_budget" wire:model.defer="budget"
                                    placeholder="@lang('CRMProjects.common.placeholder_enter_budget')"  />
                                @error('budget')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_date">@lang('CRMProjects.common.start_date') <small class="required">*</small></label>
                                <div class="position-relative">
                                    <input type="text" name="start_date" id="start_date"
                                        class="form-control datepicker"
                                        placeholder="@lang('CRMProjects.common.enter_start_date')"
                                        wire:model.defer="start_date"
                                        autocomplete="off">
                                    <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                                </div>
                                @error('start_date')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="end_date">@lang('CRMProjects.common.end_date') <small class="required">*</small></label>
                                <div class="position-relative">
                                    <input type="text" class="form-control datepicker" name="end_date" id="end_date"
                                        placeholder="@lang('CRMProjects.common.enter_end_date')"
                                        wire:model.defer="end_date"
                                        autocomplete="off">
                                    <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                                </div>
                                @error('end_date')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                            </div>
                        </div>


                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn bg-loss text-white radius-xl"
                        data-dismiss="modal">@lang('CRMProjects.common.close')</button>
                    <button type="button" wire:click="updateProject()" wire:loading.attr="disabled" wire:loading.class="btn-loading"
                        class="btn bg-new-primary radius-xl">@lang('CRMProjects.common.submit')</button>
                </div>
            {{-- </form> --}}
        </div>
    </div>
</div>

</div>






<script src="{{ asset('vendor_assets/js/Chart.min.js') }}"></script>
<script src="{{ asset('js/charts_dashboard.js') }}"></script>

 @include('livewire.c-r-m-projects.partials.details.balde_js_script')
 
</div>



