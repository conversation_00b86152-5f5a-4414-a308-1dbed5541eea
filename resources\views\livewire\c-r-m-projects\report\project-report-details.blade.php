<div class="contents crm">
    <div class="container-fluid">

        <div class="col-lg-12">
            <div
                class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                <div class="page-title-wrap p-0">
                    <div class="page-title d-flex justify-content-between">
                        <div
                            class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                            <div class="user-member__title mr-sm-25 ml-0">
                                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                    @lang('CRMProjects.common.projectReportsDetail')
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div>
                        <ul class="atbd-breadcrumb nav">
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('CRMProjects.common.dashboard')</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('CRMProjects.common.project_reports')</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('CRMProjects.common.projectReportsDetail')</a>
                            </li>
                        </ul>
                    </div>
                </div>

                {{-- <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-white btn-default text-center svg-20 px-2 downloadPdf" data-toggle="dropdown"
                        aria-expanded="true">
                        <i class="iconsax icon text-new-primary fs-18 colorRed" icon-name="download-square"></i>
                        @lang('CRMProjects.common.download')
                    </button>
                </div> --}}
                <!--====End Design for Export PDF===-->
            </div>
        </div>


        <div class="row">
            <div class="col-lg-6 mb-3">
                <div class="card report-card">
                    <div class="card-header">
                        <h5>@lang('CRMProjects.common.overview')</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-7">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr class="table_border">
                                            <th class="table_border">@lang('CRMProjects.common.projectName'):</th>
                                            <td class="table_border">{{ data_get($project, 'name', '-') }}</td>
                                        </tr>

                                        <tr>
                                            <th class="table_border">@lang('CRMProjects.common.projectStatus'):</th>
                                            <td class="table_border">
                                                <small class="py-1 px-2 bg-hold rounded text-white">
                                                    {{ data_get($project, 'status', '-') }}
                                                </small>
                                            </td>
                                        </tr>

                                        <tr role="row">
                                            <th class="table_border">@lang('CRMProjects.common.startDate'):</th>
                                            <td class="table_border">
                                                {{ data_get($project, 'start_date', '-') }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <th class="table_border">@lang('CRMProjects.common.dueDate'):</th>
                                            <td class="table_border">
                                                {{ data_get($project, 'end_date', '-') }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <th class="table_border">@lang('CRMProjects.common.totalMembers'):</th>
                                            <td class="table_border">
                                                {{ is_countable($projectUsers) ? count($projectUsers) : 0 }}

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                            </div>
                            <div class="col-5">
                                <div class="pieChart position-relative d-center">

                                    <div>
                                        <canvas id="overviewChart" width="140" height="140"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>


            <div class="col-lg-6 mb-3">
                <div class="card report-card h-100">
                    <div class="card-header">
                        <h5>@lang('CRMProjects.common.milestoneProgress')</h5>
                    </div>
                    <div class="card-body d-center">
                        <canvas id="milestoneProgress" width="300" height="150"></canvas>
                    </div>
                </div>
            </div>


            <div class="col-lg-6 mb-3">
                <div class="card report-card h-100">
                    <div class="card-header">
                        <h5>@lang('CRMProjects.common.taskPriority')</h5>
                    </div>
                    <div class="card-body d-center">
                        <canvas id="taskPriority" width="300" height="150"></canvas>
                    </div>
                </div>
            </div>


             <div class="col-lg-6 mb-3">
                <div class="card report-card h-100">
                    <div class="card-header">
                        <h5>@lang('CRMProjects.common.taskStatus')</h5>
                    </div>
                    <div class="card-body d-center">
                        <canvas id="taskStatusChart" width="200" height="200"></canvas>
                    </div>
                </div>
            </div>


            <div class="col-lg-5 mb-3">
                <div class="card h-100">
                    <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                        <h6 class="text-capitalize fw-500 mb-3 mb-sm-0">@lang('CRMProjects.common.users')</h6>
                    </div>
                    <div class="card-body px-0 pt-0">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive">
                                <table class="table mb-0 radius-0">
                                    <thead>
                                        <tr class="userDatatable-header">
                                            <th>
                                                @lang('CRMProjects.common.name')
                                            </th>
                                            <th>
                                                @lang('CRMProjects.common.assignedTasks')
                                            </th>
                                            <th>
                                                @lang('CRMProjects.common.completedTasks')
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="">
                                        @foreach ($projectUsers ?? [] as $projectUser)
                                            <tr style="opacity: 1;">
                                                <td>
                                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                        <span
                                                            class="no-wrap">{{ data_get($projectUser, 'name', '-') }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="userDatatable-content mb-0">
                                                        <span>{{ data_get($projectUser, 'totalTasks', 0) }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="userDatatable-content mb-0">
                                                        <span>{{ data_get($projectUser, 'completedTasks', 0) }}</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-lg-7 mb-3">
                <div class="card h-100">
                    <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                        <h6 class="text-capitalize fw-500 mb-3 mb-sm-0"> @lang('CRMProjects.common.milestones')</h6>
                    </div>
                    <div class="card-body px-0 pt-0">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive">
                                @if (count($projectMilestones ?? [] > 0))
                                    <table class="table mb-0 radius-0">
                                        <thead>
                                            <tr class="userDatatable-header">
                                                <th>
                                                    @lang('CRMProjects.common.name')
                                                </th>
                                                <th>
                                                    @lang('CRMProjects.common.progress')
                                                </th>
                                                <th>
                                                    @lang('CRMProjects.common.cost')
                                                </th>
                                                <th>
                                                    @lang('CRMProjects.common.status')
                                                </th>
                                                <th>
                                                    @lang('CRMProjects.common.startDate')
                                                </th>
                                                <th>
                                                    @lang('CRMProjects.common.endDate')
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody class="sort-table ui-sortable">
                                            @foreach ($projectMilestones ?? [] as $projectMilestone)
                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                    <td>
                                                        <div
                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                            <span>{{ data_get($projectMilestone, 'title', '-') }}</span>
                                                        </div>
                                                    </td>

                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar" role="progressbar"
                                                                style="width: {{ data_get($projectMilestone, 'progress', 0) }}%"
                                                                aria-valuenow="{{ data_get($projectMilestone, 'progress', 0) }}"
                                                                aria-valuemin="0" aria-valuemax="100">
                                                            </div>
                                                        </div>
                                                        <span
                                                            class="fs-12">{{ data_get($projectMilestone, 'progress', 0) }}%</span>
                                                    </td>

                                                    <td>
                                                        <small class="py-1 px-2 bg-hold rounded text-white">
                                                            {{ data_get($projectMilestone, 'status', '-') }}
                                                        </small>
                                                    </td>

                                                    <td>
                                                        <div class="d-flex userDatatable-content mb-0">
                                                            <span>{{ number_format(data_get($projectMilestone, 'cost', 0), 2) }}
                                                                SAR</span>
                                                        </div>
                                                    </td>

                                                    <td>
                                                        <div
                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                            <span>{{ data_get($projectMilestone, 'startDate', '-') }}</span>
                                                        </div>
                                                    </td>

                                                    <td>
                                                        <div
                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                            <span>{{ data_get($projectMilestone, 'endDate', '-') }}</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    @else
                                    <div class="row">
                                        <div class="PropertyListEmpty">
                                            <img src="{{ asset('empty-icon/no-projects.svg') }}" class="fourth_img" alt="">
                                            <h4 class="first_title">@lang('CRMProjects.common.no_milestone_yet')</h4>
                                            <h6 class="second_title">@lang('CRMProjects.common.milestone_list_will_appear')</h6>
                                        </div>
                                    </div>

                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{-- START fILTER --}}
            <div class="col-lg-12 mb-3">
                <form  wire:submit.prevent="applyFilters">
                    <div class="mb-3 d-flex align-items-strech justify-content-end flex-wrap gap-10" id="show_filter">
            
                        <div class="col-md-3 col-6 px-0">
                            <select wire:model.defer="filters.all_users" class="form-control">
                                <option selected="selected" value="">@lang('CRMProjects.common.allUsers')</option>
                                @foreach($users as $id=>$name)
                                <option value="{{ $id }}">{{$name}}</option>
                                @endforeach
                            </select>
                        </div>
            
                        <div class="">
                            <select wire:model.defer="filters.milestone_id" class="form-control">
                                <option selected="selected" value="">@lang('CRMProjects.common.allMilestones')</option>
                                @foreach($milestones as $id=>$name)
                                <option value="{{ $id }}">{{$name}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="">
                            <select class="form-control" wire:model.defer="filters.status" id="status">
                                <option value="" class="px-4">@lang('CRMProjects.common.allStatus')</option>
                                <option value="101">@lang('CRMProjects.common.todo')</option>
                                <option value="102">@lang('CRMProjects.common.inProgress')</option>
                                <option value="103">@lang('CRMProjects.common.review')</option>
                                <option value="104">@lang('CRMProjects.common.done')</option>
                            </select>
                        </div>
                        <div class="">
                            <select class="form-control" wire:model.defer="filters.priority" id="priority">
                                <option value="" class="px-4">@lang('CRMProjects.common.allPriority')</option>
                                @foreach($priority as $priority)
                                <option value="{{ $priority['value'] }}">{{$priority['label']}}</option>
                                @endforeach
                            </select>
                        </div>
            
                        <div class="d-flex ms-4 gap-10">
                            <button type="submit" class="btn bg-new-primary text-white px-3"><i class="iconsax mr-0"
                                    icon-name="search-normal-2"></i></button>
                            <button type="button" wire:click="resetFilters" wire:loading.attr="disabled" class="btn bg-loss text-white px-3">
                                <i class="iconsax mr-0" icon-name="x"></i>
                            </button>
                        </div>
            
                    </div>
                </form>
            
                <div class="card">
                    <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                        <h6 class="text-capitalize fw-500 mb-3 mb-sm-0">@lang('CRMProjects.common.projectReportsDetail')</h6>
            
                      {{--   <div class="d-flex gap-10 table-search">
                            <div class="position-relative">
                                <input type="text" class="form-control" placeholder=" @lang('CRMProjects.common.search')">
                                <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                            </div>
                        </div> --}}
                    </div>
            
                    <div class="card-body px-0 pt-0">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive">
                                <table class="table mb-0 radius-0">
                                    <thead>
                                        <tr class="userDatatable-header">
                                            <th>
                                                @lang('CRMProjects.common.taskName')
                                            </th>
                                            <th>
                                                @lang('CRMProjects.common.milestone')
                                            </th>
                                            <th>
                                                @lang('CRMProjects.common.startDate')
                                            </th>
                                            <th>
                                                @lang('CRMProjects.common.dueDate')
                                            </th>
                                            <th>
                                                @lang('CRMProjects.common.assignedTo')
                                            </th>
                                            <th>
                                                @lang('CRMProjects.common.priority')
                                            </th>
                                            <th>
                                                @lang('CRMProjects.common.status')
                                            </th>
                                        </tr>
                                    </thead>
                                 <tbody class="sort-table ui-sortable">
                                    
                                  
                                    @foreach($tasks as $task)
                                    <tr class="" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatable-content mb-0">
                                                <span> {{ data_get($task, 'title', '-') }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ data_get($task, 'milestone', '-') }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ data_get($task, 'startDate', '-') }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ data_get($task, 'dueDate', '-') }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                @foreach(data_get($task, 'userName', []) as $userName)
                                                <span>{{$userName}}</span>
                                                @endforeach
                                            </div>
                                        </td>
                                        <td>
                                            <span class="py-1 px-2 bg-danger rounded text-white">
                                               {{ data_get($task, 'priority', '-') }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="py-1 px-2 bg-new-primary rounded text-white">
                                    
                                                {{ data_get($task, 'status', '-') }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach

                                    </tbody> 
                                </table>
                            </div>
                        </div>
            
                    </div>
                </div>
            </div>


            {{-- END FILTER --}}
           {{--   @livewire('c-r-m-projects.project-report-detail',['details'=>$details]) --}}
            <div class="col-12" id="cashflowDiv">
                <div class="card">
                    <div class="card-header py-4 border-0 d-flex justify-content-between align-items-center">
                        <h6 class="text-capitalize fw-500 mb-3 mb-sm-0">@lang('CRMProjects.common.cashflow')</h6>
                     {{--    <ul class="nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id="pills-tab"
                            role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link rounded active" href="">
                                    <i class="iconsax" icon-name="user-octagon"></i>
                                    @lang('CRMProjects.common.monthly')
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link rounded" href="">
                                    <i class="iconsax" icon-name="swap-horizontal-square"></i>
                                    @lang('CRMProjects.common.quarterly')
                                </a>
                            </li>
                        </ul> --}}

                        <div class="d-flex align-items-center gap-10 table-search">
                            <span class="no-wrap">@lang('CRMProjects.common.year') :</span>
                            <select class="form-control" wire:change="filterYear($event.target.value)">
                                @foreach ($yearList ?? [] as $year)
                                    <option value="{{ $year }}">{{ $year }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <div class="row">
                            <div class="col-sm-5 mb-sm-0 mb-3">
                                <div class="border radius-xl p-3">
                                    <span class="d-block">@lang('CRMProjects.common.reports') :</span>
                                    <strong>@lang('CRMProjects.common.monthlyCashflow')</strong>
                                </div>
                            </div>
                            <div class="col-sm-5 col-8">
                                <div class="border radius-xl p-3">
                                    <span class="d-block">@lang('CRMProjects.common.duration') :</span>
                                       <strong>{{ $startDateRange }} to {{ $endDateRange }}</strong>
                                </div>
                            </div>
                            <div class="col-sm-2 col-4 d-center">
                                <div class="border radius-xl p-3 h-100 d-sm-inline-block">
                                    <button class="btn bg-new-primary h-100 downloadPdf" data-toggle="tooltip"
                                        title="Download"><i class="iconsax mr-0" icon-name="download-1"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 mt-4">
                                <div class="border py-3 radius-xl">
                                    <h6 class="mb-4 ml-4">@lang('CRMProjects.common.income')</h6>
                                    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                        <div class="table-responsive">
                                            <table class="table mb-0 radius-0">
                                                <thead>
                                                    <tr class="userDatatable-header">
                                                        <th>@lang('CRMProjects.common.category')</th>
                                                        <th>@lang('CRMProjects.common.january')</th>
                                                        <th>@lang('CRMProjects.common.february')</th>
                                                        <th>@lang('CRMProjects.common.march')</th>
                                                        <th>@lang('CRMProjects.common.april')</th>
                                                        <th>@lang('CRMProjects.common.may')</th>
                                                        <th>@lang('CRMProjects.common.june')</th>
                                                        <th>@lang('CRMProjects.common.july')</th>
                                                        <th>@lang('CRMProjects.common.august')</th>
                                                        <th>@lang('CRMProjects.common.september')</th>
                                                        <th>@lang('CRMProjects.common.october')</th>
                                                        <th>@lang('CRMProjects.common.november')</th>
                                                        <th>@lang('CRMProjects.common.december')</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="">
                                                    <tr class="">
                                                        <td>
                                                            <div class="userDatatable-content mb-0">
                                                                <span class="no-wrap">@lang('CRMProjects.common.totalIncomeInvoice')</span>
                                                            </div>
                                                        </td>
                                                        @foreach ($chartIncomeArr ?? [] as $income)
                                                            <td>
                                                                <div class="userDatatable-content mb-0">
                                                                    <span class="no-wrap">﷼{{ $income }}</span>
                                                                </div>
                                                            </td>
                                                        @endforeach
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 mt-4">
                                <div class="border py-3 radius-xl">
                                    <h6 class="mb-4 ml-4">@lang('CRMProjects.common.expense')</h6>
                                    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                        <div class="table-responsive">
                                            <table class="table mb-0 radius-0">
                                                <thead>
                                                    <tr class="userDatatable-header">
                                                        <th>@lang('CRMProjects.common.category')</th>
                                                        <th>@lang('CRMProjects.common.january')</th>
                                                        <th>@lang('CRMProjects.common.february')</th>
                                                        <th>@lang('CRMProjects.common.march')</th>
                                                        <th>@lang('CRMProjects.common.april')</th>
                                                        <th>@lang('CRMProjects.common.may')</th>
                                                        <th>@lang('CRMProjects.common.june')</th>
                                                        <th>@lang('CRMProjects.common.july')</th>
                                                        <th>@lang('CRMProjects.common.august')</th>
                                                        <th>@lang('CRMProjects.common.september')</th>
                                                        <th>@lang('CRMProjects.common.october')</th>
                                                        <th>@lang('CRMProjects.common.november')</th>
                                                        <th>@lang('CRMProjects.common.december')</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="">
                                                    <tr class="">
                                                        <td>
                                                            <div class="userDatatable-content mb-0">
                                                                <span class="no-wrap">@lang('CRMProjects.common.totalIncomeBill')</span>
                                                            </div>
                                                        </td>
                                                        @foreach ($chartExpenseArr ?? [] as $expense)
                                                            <td>
                                                                <div class="userDatatable-content mb-0">
                                                                    <span class="no-wrap">﷼{{ $expense  }}</span>
                                                                </div>
                                                            </td>
                                                        @endforeach
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="col-12 mt-4">
                                <div class="border py-3 radius-xl">
                                    <h6 class="mb-4 ml-4">@lang('CRMProjects.common.netProfit') </h6>
                                    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                        <div class="table-responsive">
                                            <table class="table mb-0 radius-0">
                                                <thead>
                                                    <tr class="userDatatable-header">
                                                        <th colspan="13">@lang('CRMProjects.common.netProfitCalc')</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="">
                                                    <tr class="">
                                                        <td>
                                                            <div class="userDatatable-content mb-0">
                                                                <span class="no-wrap">@lang('CRMProjects.common.totalExpenses')</span>
                                                            </div>
                                                        </td>
                                                        @foreach ($ProfitArray ?? [] as $profit)
                                                            <td>
                                                                <div class="userDatatable-content mb-0">
                                                                    <span class="no-wrap">﷼{{ $profit  }}</span>
                                                                </div>
                                                            </td>
                                                        @endforeach
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script type="text/javascript">
        $(".select2-selct").select2();

        // === OVERVIEW PROGRESS DOUGHNUT ===
        const overviewCtx = document.getElementById("overviewChart")?.getContext("2d");
        if (overviewCtx) {
            let progress = parseFloat(
                "{{ is_numeric(data_get($project ?? [], 'progress')) ? data_get($project, 'progress') : 0 }}");
            progress = isNaN(progress) ? 0 : progress;

            new Chart(overviewCtx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [progress, 100 - progress],
                        backgroundColor: ['#74D33C', '#eee'],
                        borderWidth: 0,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: false,
                    cutout: '75%',
                    rotation: -90,
                    circumference: 360,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        },
                        customText: {
                            text: progress + '%'
                        }
                    }
                },
                plugins: [{
                    id: 'customText',
                    beforeDraw: function(chart) {
                        const {
                            ctx,
                            chartArea
                        } = chart;
                        if (!chartArea) return;
                        const {
                            left,
                            right,
                            top,
                            bottom
                        } = chartArea;
                        const centerX = (left + right) / 2;
                        const centerY = (top + bottom) / 2;
                        ctx.save();
                        ctx.font = "18px Arial";
                        ctx.fillStyle = "#222";
                        ctx.textAlign = "center";
                        ctx.textBaseline = "middle";
                        ctx.fillText(chart.options.plugins.customText.text, centerX, centerY);
                        ctx.restore();
                    }
                }]
            });
        }

        // === MILESTONE PROGRESS DOUGHNUT (HALF) ===
        const milestoneCtx = document.getElementById("milestoneProgress")?.getContext("2d");
        if (milestoneCtx) {
            let milestoneProgress = parseFloat("{{ $milestoneProgress ?? 0 }}");
            if (isNaN(milestoneProgress)) milestoneProgress = 0;

            new Chart(milestoneCtx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [milestoneProgress, 100 - milestoneProgress],
                        backgroundColor: ['#74D33C', '#eee'],
                        borderWidth: 0,
                        borderRadius: 10
                    }]
                },
                options: {
                    responsive: false,
                    cutout: '70%',
                    rotation: -90,
                    circumference: 180,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        },
                        customText: {
                            percentage: milestoneProgress + '%',
                            label: "Progress"
                        }
                    }
                },
                plugins: [{
                    id: 'customText',
                    beforeDraw: function(chart) {
                        const {
                            ctx,
                            chartArea
                        } = chart;
                        if (!chartArea) return;
                        const {
                            left,
                            right,
                            bottom
                        } = chartArea;
                        const centerX = (left + right) / 2;
                        const centerY = bottom - 20;
                        ctx.save();
                        ctx.font = "18px Arial";
                        ctx.fillStyle = "#222";
                        ctx.textAlign = "center";
                        ctx.textBaseline = "middle";
                        ctx.fillText(chart.options.plugins.customText.percentage, centerX, centerY -
                        20);
                        ctx.font = "14px Arial";
                        ctx.fillStyle = "blue";
                        ctx.fillText(chart.options.plugins.customText.label, centerX, centerY);
                        ctx.restore();
                    }
                }]
            });
        }

        // === TASK PRIORITY BAR CHART ===
        const taskPriorityCtx = document.getElementById("taskPriority")?.getContext("2d");
        if (taskPriorityCtx) {
            new Chart(taskPriorityCtx, {
                type: 'bar',
                data: {
                   
                    labels: @json($arrProcessLabelPriority),
                    datasets: [{
                        data: @json($arrProcessPerPriority), // Exemple de données
                        backgroundColor: ["#74D33C", "#FFB54A", "#FF6384"],
                        borderWidth: 0,
                        borderRadius: 30
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            ticks: {
                                color: "blue"
                            },
                            grid: {
                                display: false
                            },
                            barPercentage: 0.5,
                            categoryPercentage: 0.6
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: "#ddd"
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        const taskStatusCtx = document.getElementById("taskStatusChart")?.getContext("2d");
    if (taskStatusCtx) {
        const labels = @json($arrProcessLabelStatusTasks);
        const data = @json($arrProcessPerStatusTask);

        new Chart(taskStatusCtx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: ['#74D33C', '#FFCE56', '#36A2EB', '#FF6384', '#eee'],
                    borderWidth: 0,
                    borderRadius: 5
                }]
            },
            options: {
                responsive: false,
                cutout: '60%',
                rotation: -90,
                circumference: 360,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            color: '#444',
                            boxWidth: 15
                        }
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.raw}%`;
                            }
                        }
                    },
                    customText: {
                        percentage: (data[0] ?? 0) + '%'

                    }
                }
            },
            plugins: [{
                id: 'customText',
                beforeDraw: function(chart) {
                    const { ctx, chartArea } = chart;
                    if (!chartArea) return;
                    const { left, right, top, bottom } = chartArea;
                    const centerX = (left + right) / 2;
                    const centerY = (top + bottom) / 2;
                    ctx.save();
                    ctx.font = "18px Arial";
                    ctx.fillStyle = "#222";
                    ctx.textAlign = "center";
                    ctx.textBaseline = "middle";
                    ctx.fillText(chart.options.plugins.customText.percentage, centerX, centerY);
                    ctx.restore();
                }
            }]
        });
    }
    $(function () {
        $(".downloadPdf").click(function () {
            let doc = new jspdf.jsPDF('p', 'mm', 'a4');
            let element = document.getElementById("cashflowDiv");
            html2canvas(element).then((canvas) => {
                let imgData = canvas.toDataURL("image/png");
                let imgWidth = 190; // Fit width to A4
                let imgHeight = (canvas.height * imgWidth) / canvas.width;
                doc.addImage(imgData, 'PNG', 10, 10, imgWidth, imgHeight);
                doc.save("exported-content.pdf");
            });
        });
    });
    </script>
@endpush
