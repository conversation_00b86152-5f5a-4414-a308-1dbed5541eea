{{-- @extends('layouts.app')
@section('styles')
@endsection
@section('content') --}}

<div class="contents crm pb-0">
    <div class="container-fluid">
        <div class="col-lg-12">
            <div class="row justify-content-between align-items-center">
                <div class="page-title-wrap">
                    <div class="page-title d-flex justify-content-between">
                        <div class="page-title__left">
                            <div
                                class="d-flex align-items-center user-member__title justify-content-center mr-sm-25 ml-0">
                                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                    <!-- Javascript:history.back()-->
                                    <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a>
                                    {{ $deal['id'] . ' -  ' . $deal['pipeline'] }}
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="">
                        <ul class="atbd-breadcrumb nav">
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('deal.deal_stage.dashboard')</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('deal.deal_stage.deals')</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('deal.deal_stage.representative')</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <!--====Design for Export PDF===-->
                <div class="d-flex gap-10">
                    <div class="btn btn-white btn-default text-center svg-20 px-3 py-2"
                        wire:click.prevent="$emit('editStage', {{ $deal['id'] }})">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="flow-chart-1"></i>
                    </div>
                    {{--                        <div class="btn btn-white btn-default text-center svg-20 px-3 py-2"> --}}
                    {{--                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="video-2"></i> --}}
                    {{--                        </div> --}}
                    {{--                        <div class="btn btn-white btn-default text-center svg-20 px-3 py-2"> --}}
                    {{--                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="tag-1"></i> --}}
                    {{--                        </div> --}}
                    <div class="btn btn-white btn-default text-center svg-20 px-3 py-2"
                        wire:click.prevent="$emit('editDeal', {{ $deal['id'] }})">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="edit-1"></i>
                    </div>
                </div>

                <!--====End Design for Export PDF===-->
            </div>
        </div>

        <div class="checkout pt-2 w-100">
            <div class="row">
                <div class="col-lg-3 pr-md-0 mb-3 mb-md-0">
                    <div class="card">
                        <ul class="list-group crm nav-stages crm-sidebar">
                            <li class="list-group-item active">
                                <a href="#general-section" class="d-flex justify-content-between align-items-center">
                                    @lang('deal.deal_stage.generals')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="#task-section" class="d-flex justify-content-between align-items-center">
                                    @lang('deal.deal_stage.tasks')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="#users-section" class="d-flex justify-content-between align-items-center">
                                    @lang('deal.deal_stage.user_products')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="#sources-section" class="d-flex justify-content-between align-items-center">
                                    @lang('deal.deal_stage.sources_emails')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="#notes-section" class="d-flex justify-content-between align-items-center">
                                    @lang('deal.deal_stage.discussions_notes')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="#files-section" class="d-flex justify-content-between align-items-center">
                                    @lang('deal.deal_stage.files')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="#calls-section" class="d-flex justify-content-between align-items-center">
                                    @lang('deal.deal_stage.calls')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="#activity-section" class="d-flex justify-content-between align-items-center">
                                    @lang('deal.deal_stage.activity')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="#attachments-section"
                                    class="d-flex justify-content-between align-items-center">
                                    @lang('lead.attachments.title')
                                    <i class="las la-angle-right"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-9 fs-14 deals-details leads-details new-scrollbar-hidden"
                    style="max-height: 70vh;overflow-y: auto;">
                    <div class="card checkout-shipping-form pb-0 mt-lg-0 mb-3">
                        <div class="card-body">
                            <div class="row" id="general-section">
                                <div class="col-md-4 d-flex align-items-center mb-4">
                                    <span
                                        class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="hierarchy-1"></i>
                                    </span>
                                    <div class="flex-fill">
                                        <p class="text-dark mb-0">@lang('deal.deal_stage.price')</p>
                                        <span class="text-new-primary">{{ $deal['price'] }}</span>
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-center mb-4">
                                    <span
                                        class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="mobile"></i>
                                    </span>
                                    <div class="flex-fill">
                                        <p class="text-dark mb-0">@lang('deal.deal_stage.phone')</p>
                                        <span class="text-new-primary">{{ $deal['phone'] }}</span>
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-center mb-4">
                                    <span
                                        class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="hierarchy-1"></i>
                                    </span>
                                    <div class="flex-fill">
                                        <p class="text-dark mb-0">@lang('deal.deal_stage.pipeline')</p>
                                        <span class="text-new-primary">{{ $deal['pipeline'] }}</span>
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-center mb-4">
                                    <span
                                        class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="driver-1"></i>
                                    </span>
                                    <div class="flex-fill">
                                        <p class="text-dark mb-0">@lang('deal.deal_stage.stage')</p>
                                        <span class="text-new-primary">{{ $deal['stage'] }}</span>
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-center mb-4">
                                    <span
                                        class="wh-40 radius-xl bg-new-primary  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="dollar-square"></i>
                                    </span>
                                    <div class="flex-fill">
                                        <p class="text-dark mb-0">{{ $deal['precentage'] ?? 0 . '%' }}</p>
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar"
                                                style="width: {{ $deal['precentage'] ?? 0 }}"
                                                aria-valuenow="{{ $deal['precentage'] ?? 0 }}" aria-valuemin="0"
                                                aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 col-sm-6">
                            <div class="card px-3 py-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="text-osool mb-1">@lang('deal.deal_stage.tasks')</p>
                                        <span class="text-new-primary fw-500">{{ count($deal['tasks']) }}</span>
                                    </div>
                                    <span
                                        class="wh-40 radius-xl bg-osool-new  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0"
                                            icon-name="task-list-square"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card px-3 py-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="text-osool mb-1">@lang('deal.deal_stage.products')</p>
                                        <span class="text-new-primary fw-500">{{ count($deal['products']) }}</span>
                                    </div>
                                    <span
                                        class="wh-40 radius-xl bg-osool-new  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="shopping-cart"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card px-3 py-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="text-osool mb-1">@lang('deal.deal_stage.source')</p>
                                        <span class="text-new-primary fw-500">{{ count($deal['sources']) }}</span>
                                    </div>
                                    <span
                                        class="wh-40 radius-xl bg-osool-new  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="share"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card px-3 py-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="text-osool mb-1">@lang('deal.deal_stage.files')</p>
                                        <span class="text-new-primary fw-500">{{ count($deal['files']) }}</span>
                                    </div>
                                    <span
                                        class="wh-40 radius-xl bg-osool-new  text-white lh-40 text-white d-flex-center mr-2">
                                        <i class="iconsax icon fs-22 text-white mr-0" icon-name="document-1"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3" id="task-section">
                            <livewire:lead-task-list page-type="deal" :sidebar-crm="true"
                                workspace-slug="{{ auth()->user()->workspace }}" leadId="{{ $deal['id'] }}" :leadTaskArray="$deal['tasks']" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 pr-md-2 mb-3" id="users-section">
                            <div class="card h-100">
                                <div
                                    class="card-header border-bottom-0 justify-content-between d-flex align-items-center p-3">
                                    <h3 class="text-osool">@lang('deal.deal_stage.users')</h3>

                                    <button class="btn btn-default bg-new-primary dropdown-toggle" type="button"
                                        id="userModal" data-toggle="modal" aria-haspopup="true"
                                        aria-expanded="false" data-toggle="modal" data-target="#addUserModal"><i
                                            class="las la-plus fs-16"></i>@lang('lead.forms.button.create')</button>
                                </div>
                                <div class="card-body p-0">
                                    @if (isset($deal['users']))
                                        @foreach ($deal['users'] as $deal_user)
                                            <div class="d-flex justify-content-between align-items-center p-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="user-profile mr-2">
                                                        <img src="{{ $deal_user['avatar'] }}" class="wh-50 rounded">
                                                    </div>
                                                    <div class="text-osool">
                                                        <p class="mb-0">{{ $deal_user['name'] }} </p>
                                                    </div>
                                                </div>
                                                <div class="d-flex">
                                                    <a href="#"
                                                        wire:click.prevent="deleteUser({{ $deal_user['id'] }})"><i
                                                            class="iconsax icon fs-22 text-delete fs-22 mr-0"
                                                            icon-name="trash"></i></a>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        </div>
                        <livewire:lead-products-livewire page-type="deal"
                            workspace-slug="{{ auth()->user()->workspace }}" leadId="{{ $deal['id'] }}" :leadProductArray="$deal['products']" />

                        <livewire:lead-sources page-type="deal" workspace-slug="{{ auth()->user()->workspace }}"
                            leadId="{{ $deal['id'] }}" :leadSourcesArray="$deal['sources']"/>
                        {{--                            <div class="col-md-6 pl-md-2 mb-3"> --}}
                        {{--                                    <div class="card h-100"> --}}
                        {{--                                        <div class="card-header border-bottom-0 justify-content-between d-flex align-items-center p-3"> --}}
                        {{--                                            <h3 class="text-osool">@lang('deal.deal_stage.emails')</h3> --}}

                        {{--                                            <button class="btn btn-default bg-new-primary dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="modal" data-target="#creat-dealstage" aria-haspopup="true" aria-expanded="false"><i class="las la-plus fs-16"></i>@lang('lead.forms.button.create')</button> --}}
                        {{--                                        </div> --}}
                        {{--                                        <div class="card-body p-0"> --}}
                        {{--                                            @if (isset($deal['emails'])) --}}
                        {{--                                            @foreach ($deal['emails'] as $email) --}}
                        {{--                                                <div class="d-flex justify-content-between align-items-center p-3"> --}}
                        {{--                                                    <div class="d-flex align-items-center"> --}}
                        {{--                                                        <div class="user-profile mr-2"> --}}
                        {{--                                                            <img src="{{ asset('img/author-nav.jpg') }}" class="wh-50 rounded"> --}}
                        {{--                                                        </div> --}}
                        {{--                                                        <div class="text-osool"> --}}
                        {{--                                                            <p class="mb-0">{{$email['subject']}} <span class=" ml-2 fs-12">{{$email['diff_time']}}</span></p> --}}
                        {{--                                                            <span class="fs-12">{{$email['to']}}</span> --}}
                        {{--                                                        </div> --}}
                        {{--                                                    </div> --}}
                        {{--                                                    <div class="d-flex"> --}}
                        {{--                                                        <a href="#"><i class="iconsax icon fs-22 text-delete fs-22 mr-0" icon-name="trash"></i></a> --}}
                        {{--                                                    </div> --}}
                        {{--                                                </div> --}}
                        {{--                                            @endforeach --}}
                        {{--                                            @endif --}}
                        {{--                                        </div> --}}
                        {{--                                    </div> --}}
                        {{--                                </div> --}}
                        <livewire:leads-discussion page-type="deal" sidebar-crm={{ false }}
                            workspace-slug="{{ auth()->user()->workspace }}" activity-id="{{ $deal['id'] }}"  :leadDiscussionArray="$deal['discussion']"/>
                        <div class="col-md-6 pr-md-2 mb-3" id="notes-section">
                            <div class="card h-100">
                                <div
                                    class="card-header border-bottom-0 justify-content-between d-flex align-items-center p-3">
                                    <h3 class="text-osool">@lang('deal.deal_stage.notes')</h3>

                                    <button class="btn btn-default bg-new-primary" type="button" data-toggle="modal"
                                        data-target="#createNoteModal">
                                        <i class="las la-plus fs-16"></i> @lang('deal.forms.button.create')
                                    </button>
                                </div>

                                <div class="card-body p-0">
                                    <div class="d-flex justify-content-between align-items-center p-3">
                                        <div class="d-flex align-items-start">
                                            <div class="text-osool">
                                                <span class="fs-12">
                                                    @if (isset($deal['notes']))
                                                        @if ($deal['notes'] != null)
                                                            {{ $deal['notes'] }}
                                                        @else
                                                            @lang('lead.no_notes')
                                                        @endif
                                                    @else
                                                        @lang('lead.no_notes')
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                    </div>
                    <div class="row" id="calls-section">
                        <livewire:lead-calls-list type="deals" :sidebar-crm="false"
                            workspace-slug="{{ auth()->user()->workspace }}" activity-id="{{ $deal['id'] }}" :leadCallsArray="$deal['calls']"/>
                    </div>
                    <div class="row" id="attachments-section">
                        <livewire:lead-attachments-list type="deals" :sidebar-crm="false"
                            workspace-slug="{{ auth()->user()->workspace }}" activity-id="{{ $deal['id'] }}" :leadfilesArray="$deal['files']" />
                    </div>
                    <div class="row" id="activity-section">
                        <div class="col-md-6">
                        <livewire:lead-activity-list page-type="deal" :sidebar-crm="false"
                            workspace-slug="{{ auth()->user()->workspace}}" activity-id="{{ $deal['id'] }}"  :leadActivityArray="$deal['activities']" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div wire:ignore.self class="modal fade" id="editStageModal" tabindex="-1"
            aria-labelledby="editStageModalLabel" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editStageModalLabel">@lang('lead.lead_stage.edit_stage')</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form wire:submit.prevent="updateDeadStage">
                            <label for="leadStage">@lang('lead.lead_stage.stage')</label>
                            <select wire:model="selectedStage" class="form-control">
                                <option value="">Select Stage</option>
                                @foreach ($stages as $stage)
                                    <option value="{{ $stage['name'] }}">{{ $stage['name'] }}</option>
                                @endforeach
                            </select>
                            <div class="modal-footer">
                                <button type="button" class="btn bg-hold-light text-white"
                                    data-dismiss="modal">@lang('lead.common.cancel')</button>
                                <button type="submit" class="btn btn-primary">@lang('lead.common.save')</button>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>
        <div wire:ignore.self class="modal fade new-popup" id="creat_lead" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog radius-xl" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="exampleModalLabel">Update Deal</h6>
                        <button type="button" class="close" wire:click="closeModal">
                            <span data-feather="x"></span>
                        </button>
                    </div>
                    <div class="modal-body">
                        @if ($errors->has('form_error'))
                            <div class="alert alert-danger">
                                {{ $errors->first('form_error') }}
                            </div>
                        @endif

                        <form wire:submit.prevent="updateDeal">
                            <div class="row">
                                <!-- Clients (Multiple Selection) -->
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>@lang('lead.forms.label.clients') <small class="required">*</small></label>
                                        <select wire:model.defer="clients" id="clients-dropdown" multiple
                                            class="form-control cc-select2" wire:ignore>
                                            <option value = "" selected>@lang('lead.forms.label.clients')</option>

                                            @foreach ($users['items'] as $user)
                                                <option value="{{ $user['id'] }}">{{ $user['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('clients')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <!-- Name -->
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>@lang('lead.forms.label.name') <small class="required">*</small></label>
                                        <input type="text" wire:model="name" class="form-control"
                                            placeholder="@lang('lead.forms.label.name')">
                                        @error('name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>@lang('lead.forms.label.price') <small class="required">*</small></label>
                                        <input type="text" wire:model.defer="price" class="form-control"
                                            placeholder="@lang('lead.forms.label.price')">
                                        @error('price')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Phone Number -->
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>@lang('lead.forms.label.phone') <small class="required">*</small></label>
                                        <input type="text" wire:model="phone" class="form-control"
                                            placeholder="@lang('lead.forms.label.phone')">
                                        @error('phone')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn bg-hold-light text-white" wire:click="closeModal">
                                    @lang('lead.forms.button.cancel')
                                </button>
                                <button type="submit" class="btn bg-new-primary">
                                    Update
                                </button>
                            </div>
                        </form>
                    </div>



                </div>
            </div>
        </div>
        {{-- @livewireScripts --}}


        <!-- Add User Modal -->
        <div wire:ignore.self class="modal fade" id="addUserModal" tabindex="-1" role="dialog"
            aria-labelledby="addUserModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addUserModalLabel">@lang('lead.add_user')</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        @if (session()->has('error'))
                            <div class="alert alert-danger">
                                {{ session()->get('error') }}
                            </div>
                        @endif
                        <div>
                            <label>@lang('lead.select_user')</label>
                            <select wire:model="selectedUserId" class="form-control">
                                <option value="">@lang('lead.choose_user')</option>
                                @if (isset($users['items']))
                                    @foreach ($users['items'] as $user)
                                        <option value="{{ $user['id'] }}">{{ $user['name'] }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                            data-dismiss="modal">@lang('lead.close')</button>
                        <button type="button" class="btn btn-primary"
                            wire:click="addUser">@lang('lead.add')</button>
                    </div>
                </div>
            </div>
        </div>
        <div wire:ignore.self class="modal fade" id="createNoteModal" tabindex="-1"
            aria-labelledby="createNoteModalLabel" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="createNoteModalLabel">@lang('lead.lead_stage.create_note')</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        @if (session()->has('error'))
                            <div class="alert alert-danger">
                                {{ session()->get('error') }}
                            </div>
                        @endif

                        @if (session()->has('message'))
                            <div class="alert alert-success">
                                {{ session()->get('message') }}
                            </div>
                        @endif
                        <form wire:submit.prevent="createNote">
                            <!-- Note Content Field -->
                            <div class="form-group">
                                <label for="note_content">@lang('lead.lead_stage.note_content')</label>
                                <textarea wire:model="note_content" class="form-control" id="note_content" placeholder="@lang('lead.lead_stage.note_content')"
                                    rows="5"></textarea>
                                @error('note_content')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary"
                                    data-dismiss="modal">@lang('lead.common.cancel')</button>
                                <button type="submit" class="btn btn-primary">@lang('lead.common.save')</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
</div>
</div>

{{-- <div class="contents">
         <div class="container-fluid">
             <div class="row">
                 <div class="col-lg-12">
                     <div class="breadcrumb-main user-member justify-content-sm-between ">
                         <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                             <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25 page-title__left">
                                 <h4 class="text-capitalize fw-500 breadcrumb-title"><a href="{{ url()->previous() }}"><i class="las la-arrow-left"></i></a> {{__('user_management_module.common.users_list')}}</h4>
                             </div>
                         </div>
                     </div>
                     <div class="row">
                         <div class="PropertyListEmpty">
                             <img src="{{asset('empty-icon/Tiredness_amico.svg')}}" class="fourth_img" alt="">
                             <h4 class="first_title">{{__('general_sentence.empty_ui.No_users_yet')}}</h4>
                             <h6 class="second_title">{{__('general_sentence.empty_ui.The_users_list_will_appear_here')}}</h6>
                             <div class="action-btn">
                                 <a href="{{ route('users.create.info') }}" class="btn px-15 btn-primary third_button">
                                     <i class="las la-plus fs-16"></i>{{__('user_management_module.user_button.add_new_user')}}</a>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </div> --}}

<div class="modal-info-delete modal fade show" id="modal-info-delete" tabindex="-1" role="dialog"
    aria-hidden="true">
    <div class="modal-dialog modal-sm modal-info" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <div class="modal-info-body d-flex">
                    <div class="modal-info-icon warning">
                        <span data-feather="info"></span>
                    </div>
                    <div class="modal-info-text">
                        <h6>Do you Want to delete User?</h6>
                        <div class="atbd-select">
                            <select class="form-control select2 h-auto" name="worker_id" id="worker_id">
                                <option value="">{{ __('work_order.forms.label.Choose_Worker') }} </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger btn-outlined btn-sm" data-dismiss="modal">No</button>
                <button type="button" class="btn btn-success btn-outlined btn-sm delete_user">Confirm</button>
            </div>
        </div>
    </div>
</div>


<!--Delete Lead Stage Modal -->
<div class="modal fade delete" id="delete-dealstage" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header d-none">
                <h6 class="modal-title" id="exampleModalLabel">Delete Lead Stage</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span data-feather="x"></span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <h1 class="text-danger mb-4"><i class="las la-exclamation-circle fs-60"></i></h1>
                    <h5 class="mb-3">Are you Sure?</h5>
                    <p>Your are about to delete <b>Open</b> Lead Stage</p>
                </div>
            </div>
            <div class="modal-footer justify-content-between border-0">
                <button type="button" class="btn btn-default btn-light flex-fill" data-dismiss="modal">No, Keep
                    It</button>
                <button type="button" class="btn btn-danger flex-fill">Yes, Delete It</button>
            </div>
        </div>

    </div>
</div>
{{-- @endsection --}}

@section('scripts')
    <script>
        function initSelect2() {
            $('#clients-dropdown, #pipelines').select2();
        }
        initSelect2();
        Livewire.hook('message.processed', (message, component) => {
            initSelect2();
        });
        $('#clients-dropdown').on('change', function() {
            let selectedValues = $(this).val();
            @this.set('clients', selectedValues);
        });
        window.addEventListener('open-modal', event => {
            $('#creat_lead').modal('show');
        });
        window.addEventListener('close-modal', event => {
            $('#creat_lead').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        window.addEventListener('close-modal', event => {
            $('#creat_lead').modal('hide');
        });
        window.addEventListener('reload-page', () => {
            window.location.reload();
        });
        window.addEventListener('open-modal-stage', event => {
            $('#editStageModal').modal('show');
        });
        window.addEventListener('close-modal-note', event => {
            $('#createNoteModal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        window.addEventListener('close-modal-users', event => {
            $('#addUserModal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
        window.addEventListener('close-modal-stage', event => {
            $('#editStageModal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open-stage');
        });
        window.addEventListener('reload-page-stage', () => {
            window.location.reload();
        });
    </script>
    <script type="text/javascript">
        $('#deals-stages').select2();
        $(document).ready(function() {
            $(".crm-sidebar a").click(function(e) {
                $(this).parent().parent().find(".active").removeClass("active");
                $(this).parent().addClass("active")
                e.preventDefault(); // Prevent default anchor behavior

                var target = $(this).attr("href"); // Get the href attribute

                if (target && target.startsWith("#")) {
                    var targetElement = $(target);

                    // Get the offset of the target element and the .leads-details container
                    var targetOffset = targetElement.offset().top;
                    var containerOffset = $(".leads-details").offset().top;

                    // Add a slight offset (e.g., 120px) to make sure it scrolls past
                    var scrollPosition = targetOffset - containerOffset + $(".leads-details").scrollTop() -
                        0; // Adjust this value if needed

                    // Scroll the .leads-details container smoothly to the target section
                    $(".leads-details").animate({
                        scrollTop: scrollPosition
                    }, 500); // Smooth scroll
                }
            });

        });
    </script>
@endsection
