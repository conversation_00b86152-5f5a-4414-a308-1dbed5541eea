<div>
    @if(!is_null($user))
        <aside class = "sidebar">
            <div class = "sidebar__menu-group">
                <ul class = "sidebar_nav">
                    <li class = "menu-title">
                        <span> @lang('slider.lists.main_menu')</span>
                    </li>
                    @if(!empty($user->user_type == 'super_admin') && is_null($projectId))
                        <li>
                            <a href = "{{ route('workspace.project', request('workspace.project')) }}" class = "{{ Route::is('workspace.project', request('workspace.project')) || Route::is('workspace.admin.createproject', request('workspace')) || Route::is('workspace.project.edit', request('workspace')) || Route::is('workspace.project.update', request('workspace')) ? 'active' : ''}}">
                                <span data-feather = "disc" class = "nav-icon"></span>
                                <span class="menu-text">@lang('slider.lists.projects')</span>
                            </a>
                        </li>
                        <li>
                            <a href = "{{ route('workspace.home', request('workspace.home')) }}" class = "@if(Route::is('workspace.home.*') && !Route::is('workspace.project')  && !Route::is('workspace.project.update') && !Route::is('workspace.project.update', request('workspace')) &&  !Route::is('workspace.admin.createproject', request('workspace')) && !Route::is('workspace.project.edit', request('workspace')) || (Route::is('workspace.admin.edit') || Route::is('workspace.admin.privileges.edit') || Route::is('workspace.admin.update') || Route::is('workspace.admin.privileges') || Route::is('workspace.admin.store') ) ) {{ 'active' }} @endif">
                                <span data-feather = "user" class = "nav-icon"></span>
                                <span class = "menu-text">@lang('slider.lists.admin_management')</span>
                            </a>
                        </li>
                        <li>
                            <a href = "{{ route('serviceproviders.mainlist') }}" class = "@if( Route::is('main.serviceproviders.*') || Route::is('users.pspa_users_list')  || Route::is('users.sp_edit_user') || Route::is('users.edit.role') || Route::is('users.edit.privileges') || Route::is('users.edit.edit_worker_password') || Route::is('users.edit.confirm') || Route::is('serviceproviders.mainlist')) active @endif">
                                <span class = "nav-icon">
                                    <img src = "{{asset('img/icons/service_providers.svg')}}">
                                </span>
                               <span class = "menu-text">@lang('slider.lists.service_provider')</span>
                            </a>
                        </li>
                        <li class = "has-child {{ request()->is('contacts/*') ? 'open' : ''}}">
                            <a href = "#" class = "{{ request()->is('contacts/*') ? 'active' : ''}}">
                               <span data-feather = "disc" class = "nav-icon"></span>
                               <span class = "menu-text">@lang('contactus_request.user_forms.label.clients_requests')</span>
                               <span class = "toggle-icon"></span>
                            </a>
                            <ul>
                               <li>
                                    <a href = "{{ route('contacts.list') }}" class = "{{ Route::is('contacts.list')  ? 'active' : '' }}">
                                        <span>@lang('contactus_request.user_forms.label.contact_us_page')</span>
                                    </a>
                               </li>
                               <li>
                                  <a href = "{{ route('contacts.demolist') }}" class = "{{ Route::is('contacts.demolist')  ? 'active' : '' }}">
                                        <span>@lang('contactus_request.user_forms.label.request_a_demo')</span>
                                    </a>
                               </li>
                            </ul>
                        </li>
                        <li>
                            <a href = "{{ route('workspace.manage-releases.list') }}" class = "{{ Route::is('workspace.manage-releases.list') || Route::is('workspace.manage-releases.*')  ? 'active' : ''}}">
                                <span data-feather = "zap" class = "nav-icon"></span>
                                <span class = "menu-text">@lang('manage_releases.manage_releases')</span>
                            </a>
                        </li>
                        @role('super_admin')
                        <li class = "has-child {{ (in_array(Route::currentRouteName(), ['settings.usersroles', 'settings.userspermissions']) || request()->is('settings/roles/*') ) ?'open' : ''}}">
                           <a href="#" class = "{{ (in_array(Route::currentRouteName(), ['settings.usersroles', 'settings.userspermissions'])) ? ' active' : '' }}" >
                            <span data-feather = "check-square" class = "nav-icon"></span>
                               <span class="menu-text">{{__('user_management_module.common.manage_user_permissions')}} <span class="badge badge-warning text-white ml-10" style="border-radius: 10px;">{{ __('purchase_request.common.test') }}</span></span>
                               <span class="toggle-icon"></span>
                           </a>
                           <ul>
                           <li>
                               <a  href="{{ route('settings.usersroles')}}" class = "{{ (Route::is('settings.usersroles') || request()->is('settings/roles/*'))  ? 'active' : '' }}">
                                   <span class="">{{__('user_management_module.common.roles')}} <span class="badge badge-warning text-white ml-10" style="border-radius: 10px;">{{ __('purchase_request.common.test') }}</span>
                                   </span>
                               </a>
                           </li>

                           <li>
                               <a  href="{{ route('settings.userspermissions')}}" class = "{{ Route::is('settings.userspermissions')  ? 'active' : '' }}" >
                                   <span class="">{{__('user_management_module.common.permissions')}} <span class="badge badge-warning text-white ml-10" style="border-radius: 10px;">{{ __('purchase_request.common.test') }}</span>
                                   </span>
                               </a>
                           </li>
                           </ul>
                        </li>
                        @endrole
                        <li>
                            <a class = "{{ (in_array(Route::currentRouteName(), ['settings.openConfigurationList', 'settings.openCreateNewConfiguration'])) ? ' active' : '' }}" href = "{{ route('settings.openConfigurationList') }}">
                                <span data-feather = "settings" class = "nav-icon"></span>
                                <span class = "menu-text">@lang('configuration.application_settings')</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('psp-registration-requests.index') }}"
                               class="{{ (in_array(Route::currentRouteName(), ['psp-registration-requests.index','psp-registration-requests.details'])) ? ' active' : '' }}" >
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                     class="nav-icon" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M16.7274 16.8133L15.2637 15.8556L15.2372 17.4933L16.7274 16.8133ZM21.5175 8.5172L21.9038 7.9248L21.3099 7.53742L20.9235 8.12983L21.5175 8.5172H21.5175ZM3.51789 16.715V17.8359L3.75241 17.6591C3.84199 17.5888 3.97094 17.5844 4.06614 17.6563L4.30446 17.8359V16.715H3.51789ZM4.81971 16.715H6.55657V22.0059H1.26574V16.715H3.00264V18.3522H3.00307C3.00288 18.5583 3.24396 18.6867 3.41524 18.5575L3.91118 18.1837L4.39591 18.5491C4.57844 18.6948 4.81975 18.5599 4.81975 18.3522V16.7151L4.81971 16.715ZM11.7938 7.61162C11.3172 7.13505 10.4986 7.47409 10.4986 8.14811C10.4986 8.82212 11.3172 9.16117 11.7938 8.68459C12.09 8.38834 12.09 7.90787 11.7938 7.61162ZM9.48283 10.9881C10.5346 11.7713 11.98 11.7713 13.0317 10.9881C12.5681 9.83941 11.148 9.42958 10.1448 10.1474C9.85146 10.3574 9.61989 10.6485 9.48283 10.9881ZM13.3601 6.49844C14.4926 7.63089 14.5251 9.45676 13.4334 10.6283C13.1024 9.96025 12.4783 9.47547 11.7445 9.32547C12.5785 8.97991 12.8023 7.89165 12.158 7.24731C11.6605 6.74978 10.854 6.74978 10.3565 7.24731C9.71219 7.89165 9.93597 8.97991 10.77 9.32547C10.0362 9.47547 9.41219 9.9603 9.08116 10.6283C7.98944 9.45672 8.02197 7.63089 9.15443 6.49844C10.3158 5.33706 12.1988 5.33706 13.3602 6.49844H13.3601ZM11.2573 5.1122C13.1842 5.1122 14.7464 6.67445 14.7464 8.60134C14.7464 10.5282 13.1842 12.0905 11.2573 12.0905C9.33039 12.0905 7.76814 10.5282 7.76814 8.60134C7.76814 6.67445 9.33039 5.1122 11.2573 5.1122ZM18.8389 9.99123L20.0321 8.16166L21.8478 9.34576L19.5555 12.8607L17.7398 11.6766L18.816 10.0263C18.8246 10.0153 18.8322 10.0036 18.8389 9.99123H18.8389ZM18.8698 14.8522V20.7911C18.8698 20.9334 18.7545 21.0488 18.6122 21.0488H7.07182V22.2635C7.07182 22.4057 6.95646 22.5211 6.81419 22.5211H1.00811C0.865848 22.5211 0.750488 22.4057 0.750488 22.2635V16.4574C0.750488 16.3151 0.865848 16.1998 1.00811 16.1998H3.64469V5.25883C3.64474 5.19292 3.66986 5.12697 3.72016 5.07667L7.23855 1.55828C7.28533 1.51028 7.35068 1.48047 7.423 1.48047H18.6122C18.7545 1.48047 18.8698 1.59583 18.8698 1.73809V9.00353L19.7419 7.66633C19.8191 7.54741 19.9782 7.51361 20.0972 7.59086L20.4933 7.84923L21.0197 7.04214C21.0969 6.92322 21.256 6.88942 21.3749 6.96667L22.3991 7.63459C22.518 7.71184 22.5518 7.87089 22.4746 7.98986L21.9477 8.79775L22.8551 9.38955C23.2557 9.65078 23.3701 10.19 23.1087 10.5908L21.8016 12.5952C21.7243 12.7141 21.5653 12.7479 21.4463 12.6707C21.3274 12.5934 21.2936 12.4344 21.3708 12.3154L22.678 10.311C22.7839 10.1486 22.7381 9.92631 22.5753 9.82023L22.278 9.62626L18.8698 14.8522ZM18.3856 14.6546L17.1858 16.4943L15.3701 15.3101L17.4592 12.1068L19.2749 13.2909L18.4083 14.6197C18.3998 14.6306 18.3922 14.6423 18.3856 14.6545V14.6546ZM4.52421 5.00125H7.16547V2.35998L4.52421 5.00125ZM7.68072 1.99577H18.3546V9.79366L14.7993 15.2452C14.7702 15.29 14.7569 15.3405 14.758 15.3902L14.7175 17.8853C14.7119 18.0816 14.91 18.2071 15.0807 18.1291L17.3481 17.0944C17.3979 17.0759 17.4424 17.0419 17.4737 16.9941L18.3546 15.6423V20.5335H7.07182V16.4574C7.07182 16.3151 6.95646 16.1997 6.81419 16.1997H4.15994V5.5165H7.42305C7.56532 5.5165 7.68068 5.40114 7.68068 5.25887V1.99577H7.68072ZM7.72868 17.8614H14.0898C14.2321 17.8614 14.3474 17.746 14.3474 17.6038C14.3474 17.4615 14.2321 17.3462 14.0898 17.3462H7.72868C7.58641 17.3462 7.47105 17.4615 7.47105 17.6038C7.47105 17.746 7.58641 17.8614 7.72868 17.8614ZM6.2748 15.1697H6.18311C6.04085 15.1697 5.92549 15.2851 5.92549 15.4273C5.92549 15.5696 6.04085 15.685 6.18311 15.685H6.2748C6.41707 15.685 6.53243 15.5696 6.53243 15.4273C6.53243 15.2851 6.41707 15.1697 6.2748 15.1697ZM7.72868 15.685H14.1691C14.3114 15.685 14.4267 15.5696 14.4267 15.4273C14.4267 15.2851 14.3114 15.1697 14.1691 15.1697H7.72868C7.58641 15.1697 7.47105 15.2851 7.47105 15.4273C7.47105 15.5696 7.58641 15.685 7.72868 15.685ZM6.2748 12.9933H6.18311C6.04085 12.9933 5.92549 13.1087 5.92549 13.2509C5.92549 13.3932 6.04085 13.5085 6.18311 13.5085H6.2748C6.41707 13.5085 6.53243 13.3932 6.53243 13.2509C6.53243 13.1087 6.41707 12.9933 6.2748 12.9933ZM7.72868 13.5085H15.3593C15.5015 13.5085 15.6169 13.3932 15.6169 13.2509C15.6169 13.1087 15.5015 12.9933 15.3593 12.9933H7.72868C7.58641 12.9933 7.47105 13.1087 7.47105 13.2509C7.47105 13.3932 7.58641 13.5085 7.72868 13.5085Z"
                                          fill="#696F79" stroke="#696F79" stroke-width="0.4"/>
                                </svg>
                                <span class="menu-text">@lang('psp_registration.dashboard.sidebar_label')</span>
                                <span class="badge badge-danger ml-10 border-radius-10">{{ $vendorRegistrationApplicationRequests ?? 0 }}</span>
                            </a>
                        </li>

                    @elseif((!empty($user->user_type == 'osool_admin')) && is_null($projectId))
                        <li>
                            <a href = "{{ route('workspace.project', request('workspace')) }}" class = "{{ Route::is('workspace.project',request('workspace'))  ? 'active' : ''}}">
                                <span data-feather = "disc" class = "nav-icon"></span>
                                <span class = "menu-text">@lang('slider.lists.project_list')</span>
                            </a>
                        </li>
                        @if(isset($osoolAdminData['public_service_providers']) && $osoolAdminData['public_service_providers'] === 'show')
                            <li>
                                <a href = "{{ route('serviceproviders.list') }}" class = "{{ Route::is('serviceproviders.*') ? 'active': '' }}">
                                    <span class = "nav-icon">
                                        <img src = "{{asset('img/icons/service_providers.svg')}}">
                                    </span>
                                    <span class = "menu-text">@lang('slider.lists.service_provider')</span>
                                </a>
                            </li>
                        @endif
                        @if(isset($osoolAdminData['client_requests']) && $osoolAdminData['client_requests'] === 'show')
                            <li class = "has-child {{ request()->is('contacts/*') ? 'open' : ''}}">
                                <a href = "#" class = "{{ request()->is('contacts/*') ? 'active' : ''}}">
                                    <span data-feather = "disc" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('contactus_request.user_forms.label.clients_requests')</span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>
                                    <li>
                                        <a href = "{{ route('contacts.list') }}" class = "{{  Route::is('contacts.list')  ? 'active' : '' }}">
                                            <span>@lang('contactus_request.user_forms.label.contact_us_page')</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href = "{{ route('contacts.demolist') }}" class = "{{  Route::is('contacts.demolist')  ? 'active' : '' }}">
                                            <span>@lang('contactus_request.user_forms.label.request_a_demo')</span>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        @endif
                        <li>
                            <a href="{{ route('psp-registration-requests.index') }}"
                               class="{{ (in_array(Route::currentRouteName(), ['psp-registration-requests.index','psp-registration-requests.details'])) ? ' active' : '' }}">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                     class="nav-icon" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M16.7274 16.8133L15.2637 15.8556L15.2372 17.4933L16.7274 16.8133ZM21.5175 8.5172L21.9038 7.9248L21.3099 7.53742L20.9235 8.12983L21.5175 8.5172H21.5175ZM3.51789 16.715V17.8359L3.75241 17.6591C3.84199 17.5888 3.97094 17.5844 4.06614 17.6563L4.30446 17.8359V16.715H3.51789ZM4.81971 16.715H6.55657V22.0059H1.26574V16.715H3.00264V18.3522H3.00307C3.00288 18.5583 3.24396 18.6867 3.41524 18.5575L3.91118 18.1837L4.39591 18.5491C4.57844 18.6948 4.81975 18.5599 4.81975 18.3522V16.7151L4.81971 16.715ZM11.7938 7.61162C11.3172 7.13505 10.4986 7.47409 10.4986 8.14811C10.4986 8.82212 11.3172 9.16117 11.7938 8.68459C12.09 8.38834 12.09 7.90787 11.7938 7.61162ZM9.48283 10.9881C10.5346 11.7713 11.98 11.7713 13.0317 10.9881C12.5681 9.83941 11.148 9.42958 10.1448 10.1474C9.85146 10.3574 9.61989 10.6485 9.48283 10.9881ZM13.3601 6.49844C14.4926 7.63089 14.5251 9.45676 13.4334 10.6283C13.1024 9.96025 12.4783 9.47547 11.7445 9.32547C12.5785 8.97991 12.8023 7.89165 12.158 7.24731C11.6605 6.74978 10.854 6.74978 10.3565 7.24731C9.71219 7.89165 9.93597 8.97991 10.77 9.32547C10.0362 9.47547 9.41219 9.9603 9.08116 10.6283C7.98944 9.45672 8.02197 7.63089 9.15443 6.49844C10.3158 5.33706 12.1988 5.33706 13.3602 6.49844H13.3601ZM11.2573 5.1122C13.1842 5.1122 14.7464 6.67445 14.7464 8.60134C14.7464 10.5282 13.1842 12.0905 11.2573 12.0905C9.33039 12.0905 7.76814 10.5282 7.76814 8.60134C7.76814 6.67445 9.33039 5.1122 11.2573 5.1122ZM18.8389 9.99123L20.0321 8.16166L21.8478 9.34576L19.5555 12.8607L17.7398 11.6766L18.816 10.0263C18.8246 10.0153 18.8322 10.0036 18.8389 9.99123H18.8389ZM18.8698 14.8522V20.7911C18.8698 20.9334 18.7545 21.0488 18.6122 21.0488H7.07182V22.2635C7.07182 22.4057 6.95646 22.5211 6.81419 22.5211H1.00811C0.865848 22.5211 0.750488 22.4057 0.750488 22.2635V16.4574C0.750488 16.3151 0.865848 16.1998 1.00811 16.1998H3.64469V5.25883C3.64474 5.19292 3.66986 5.12697 3.72016 5.07667L7.23855 1.55828C7.28533 1.51028 7.35068 1.48047 7.423 1.48047H18.6122C18.7545 1.48047 18.8698 1.59583 18.8698 1.73809V9.00353L19.7419 7.66633C19.8191 7.54741 19.9782 7.51361 20.0972 7.59086L20.4933 7.84923L21.0197 7.04214C21.0969 6.92322 21.256 6.88942 21.3749 6.96667L22.3991 7.63459C22.518 7.71184 22.5518 7.87089 22.4746 7.98986L21.9477 8.79775L22.8551 9.38955C23.2557 9.65078 23.3701 10.19 23.1087 10.5908L21.8016 12.5952C21.7243 12.7141 21.5653 12.7479 21.4463 12.6707C21.3274 12.5934 21.2936 12.4344 21.3708 12.3154L22.678 10.311C22.7839 10.1486 22.7381 9.92631 22.5753 9.82023L22.278 9.62626L18.8698 14.8522ZM18.3856 14.6546L17.1858 16.4943L15.3701 15.3101L17.4592 12.1068L19.2749 13.2909L18.4083 14.6197C18.3998 14.6306 18.3922 14.6423 18.3856 14.6545V14.6546ZM4.52421 5.00125H7.16547V2.35998L4.52421 5.00125ZM7.68072 1.99577H18.3546V9.79366L14.7993 15.2452C14.7702 15.29 14.7569 15.3405 14.758 15.3902L14.7175 17.8853C14.7119 18.0816 14.91 18.2071 15.0807 18.1291L17.3481 17.0944C17.3979 17.0759 17.4424 17.0419 17.4737 16.9941L18.3546 15.6423V20.5335H7.07182V16.4574C7.07182 16.3151 6.95646 16.1997 6.81419 16.1997H4.15994V5.5165H7.42305C7.56532 5.5165 7.68068 5.40114 7.68068 5.25887V1.99577H7.68072ZM7.72868 17.8614H14.0898C14.2321 17.8614 14.3474 17.746 14.3474 17.6038C14.3474 17.4615 14.2321 17.3462 14.0898 17.3462H7.72868C7.58641 17.3462 7.47105 17.4615 7.47105 17.6038C7.47105 17.746 7.58641 17.8614 7.72868 17.8614ZM6.2748 15.1697H6.18311C6.04085 15.1697 5.92549 15.2851 5.92549 15.4273C5.92549 15.5696 6.04085 15.685 6.18311 15.685H6.2748C6.41707 15.685 6.53243 15.5696 6.53243 15.4273C6.53243 15.2851 6.41707 15.1697 6.2748 15.1697ZM7.72868 15.685H14.1691C14.3114 15.685 14.4267 15.5696 14.4267 15.4273C14.4267 15.2851 14.3114 15.1697 14.1691 15.1697H7.72868C7.58641 15.1697 7.47105 15.2851 7.47105 15.4273C7.47105 15.5696 7.58641 15.685 7.72868 15.685ZM6.2748 12.9933H6.18311C6.04085 12.9933 5.92549 13.1087 5.92549 13.2509C5.92549 13.3932 6.04085 13.5085 6.18311 13.5085H6.2748C6.41707 13.5085 6.53243 13.3932 6.53243 13.2509C6.53243 13.1087 6.41707 12.9933 6.2748 12.9933ZM7.72868 13.5085H15.3593C15.5015 13.5085 15.6169 13.3932 15.6169 13.2509C15.6169 13.1087 15.5015 12.9933 15.3593 12.9933H7.72868C7.58641 12.9933 7.47105 13.1087 7.47105 13.2509C7.47105 13.3932 7.58641 13.5085 7.72868 13.5085Z"
                                          fill="#696F79" stroke="#696F79" stroke-width="0.4"/>
                                </svg>
                                <span class="menu-text">@lang('psp_registration.dashboard.sidebar_label')</span>
                                <span class="badge badge-danger ml-10 border-radius-10">{{ $vendorRegistrationApplicationRequests ?? 0 }}</span>
                            </a>
                        </li>
                    @else
                        <li>
                            <a href = "{{ route('admin.dashboard') }}" class = "{{ Route::is('admin.blank_dashboard') || Route::is('admin.dashboard')  ? 'active' : '' }}">
                                <span data-feather = "home" class = "nav-icon"></span>
                                <span class = "menu-text">@lang('slider.lists.dashboard')</span>
                            </a>
                        </li>
                        @if(in_array($user->user_type,['building_manager','admin','admin_employee']))
                                    <li>
                                            <a href = "{{ route('payment.dashboard') }}" class = "{{ Route::is('payment.dashboard') ? 'active' : ''}}">
                                            <span class="feather nav-icon"><i class="las la-wallet fs-19"></i></span>
                                <span class = "menu-text">@lang('commercial_contracts.common.payment_tracker')</span>

                                            </a>
                                        </li>
                                    @endif
                        @if(($user->user_type == 'admin' || $user->user_type == 'admin_employee' || $user->user_type == 'superadmin' || $user->user_type == 'osool_admin') && \App\Services\AkauntingService::allow())
                            <li>
                                <a href = "{{ route('performance-indicator.index') }}" class = "{{  Route::is('performance-indicator.index') ? 'active' : '' }}">
                                    <span data-feather = "check-circle" class="nav-icon"></span>
                                    <span class = "menu-text">@lang('admin.akaunting::slider.lists.performance_indicators')</span>
                                </a>
                            </li>
                        @endif
                        @if($hasAdmin)
                            @if(!empty($user->user_type == 'building_manager') || !empty($user->user_type == 'building_manager_employee'))
                                @if($this->checkUserPrivileges('no_view','workorder', false, $user)['success'])
                                    <li>
                                        <a href = "{{ route('calendar') }}" class = "{{  Route::is('calendar')  ? 'active' : '' }}">
                                            <span data-feather = "calendar" class = "nav-icon"></span>
                                            <span class = "menu-text">@lang('slider.lists.calendar')</span>
                                        </a>
                                    </li>
                                @endif
                            @elseif( empty($user->user_type == 'procurement_admin'))
                                <li>
                                    <a href="{{ route('serviceprovider.index') }}" class = "{{  Route::is('serviceprovider.index') || Route::is('calendar')  ? 'active' : '' }} ">
                                        <span data-feather = "calendar" class = "nav-icon"></span>
                                        <span class = "menu-text">@lang('slider.lists.calendar')</span>
                                    </a>
                                </li>
                            @endif
                        @endif
                        @if($hasAdmin)
                            @if( (!empty($user->user_type == 'building_manager') || !empty($user->user_type == 'building_manager_employee') ) && isset($userPrivilegesAside->workorder) && !in_array('no_view',$userPrivilegesAside->workorder))
                                @if($flagWorkorderSidebarMenu)
                                    <li class = "has-child {{ request()->is('work-order/*') ? 'open' : ''}}">
                                        <a href = "#" class = "{{ request()->is('work-order/*') ? 'active' : ''}}">
                                            <span data-feather = "tool" class = "nav-icon"></span>
                                            <span class = "menu-text">@lang('slider.lists.work_order')</span>
                                            <span class = "toggle-icon"></span>
                                        </a>
                                        <ul>
                                            <li>
                                                <a id = "listWOByAllStatus" href = "{{ route('workorder.openWorkOrdersList', ['all', $this->encryptDecryptedString(1)]) }}" class = "{{ ( request()->is('work-order/new-work-orders-list/all/*') )  ? 'active' : '' }}">
                                                    <span>@lang('slider.lists.all')</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href = "{{ route('workorder.openWorkOrdersList', ['open', $this->encryptDecryptedString(1)]) }}" id = "listWOByOpenStatus" class = "{{ ( request()->is('work-order/list/open') || request()->is('work-order/open-workorders/*') || request()->is('work-order/new-work-orders-list/open/*') ) ? 'active' : ''}}">
                                                    <span>@lang('work_order.bread_crumbs.open_tab')</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href = "{{ route('workorder.openWorkOrdersList', ['in-progress', $this->encryptDecryptedString(1)]) }}" id = "listWOByInProgressStatus" class = "{{ ( request()->is('work-order/list/in-progress') || request()->is('work-order/in-progress-workorders/*') || request()->is('work-order/new-work-orders-list/in-progress/*') ) ? 'active' : ''}}">
                                                    <span>@lang('work_order.bread_crumbs.in_progress')</span>
                                                </a>
                                            </li>
                                            @if(!empty($user->user_type != 'building_manager') && !empty($user->user_type != 'building_manager_employee'))
                                                <li>
                                                    <a href = "{{ route('workorder.openServiceProvidersList','spare-parts') }}" id = "listWOBySparePartsStatus" class = "{{ ( request()->is('work-order/list/spare-parts') || request()->is('work-order/spare-parts-workorders/*') || request()->is('work-order/new-work-orders-list/spare-parts/*') ) ? 'active' : ''}}">
                                                        <span>@lang('work_order.bread_crumbs.spare_parts')</span>
                                                    </a>
                                                </li>
                                            @endif
                                            <li>
                                                <a href = "{{ route('workorder.openWorkOrdersList', ['under-evaluation', $this->encryptDecryptedString(1)]) }}" id = "listWOByUnderEvaluationStatus" class = "{{ ( request()->is('work-order/list/under-evaluation') || request()->is('work-order/under-evaluation-workorders/*') || request()->is('work-order/new-work-orders-list/under-evaluation/*') ) ? 'active' : ''}}">
                                                    <span>@lang('work_order.bread_crumbs.Under_Evaluation')</span>
                                                </a>
                                            </li>
                                            <li class = "@if(($user->user_type == 'building_manager'|| $user->user_type == 'building_manager_employee') && Helper::checkSubPrivilege($user->id,'work_order_approve')==false) d-none @endif">
                                                <a href = "{{ route('workorder.openWorkOrdersList', ['pending', $this->encryptDecryptedString(1)]) }}" class = "{{ request()->is('work-order/new-work-orders-list/pending/*') ? 'active' : ''}}">
                                                    <span>@lang('slider.pending_text_for_bm_bme.pending')</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href = "{{ route('workorder.openWorkOrdersList', ['closed', $this->encryptDecryptedString(1)]) }}" id = "listWOByClosedStatus" class = "{{ request()->is('work-order/new-work-orders-list/closed/*')  ? 'active' : '' }}">
                                                    <span>@lang('slider.lists.work_order_closed')</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                @endif
                            @elseif(!empty($user->user_type == 'sp_admin') || !empty($user->user_type == 'supervisor'))
                                <li class = "has-child {{ request()->is('work-order/*') ? 'open' : ''}}">
                                    <a href = "#" class = "{{ request()->is('work-order/*') ? 'active' : ''}}">
                                        <span data-feather = "tool" class = "nav-icon"></span>
                                        <span style = "color: {{$workOrderMenuItemColor}}" class = "menu-text">@lang('slider.lists.work_order')</span>
                                        <span class = "toggle-icon"></span>
                                    </a>
                                    <ul>
                                        <li>
                                            <a id = "listWOByAllStatus" href = "{{ route('workorder.openWorkOrdersList', ['all', $this->encryptDecryptedString(1)]) }}" class = "{{ request()->is('work-order/new-work-orders-list/all/*')  ? 'active' : '' }}">
                                                <span>@lang('slider.lists.all')</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('workorder.openWorkOrdersList', ['open', $this->encryptDecryptedString(1)]) }}" id = "listWOByOpenStatus" class = "{{ ( request()->is('work-order/list/open') || request()->is('work-order/open-workorders/*') || request()->is('work-order/new-work-orders-list/open/*') ) ? 'active' : ''}}">
                                                <span>@lang('work_order.bread_crumbs.open_tab')</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('workorder.openWorkOrdersList', ['in-progress', $this->encryptDecryptedString(1)]) }}" id = "listWOByInProgressStatus" class = "{{ ( request()->is('work-order/list/in-progress') || request()->is('work-order/in-progress-workorders/*') || request()->is('work-order/new-work-orders-list/in-progress/*') ) ? 'active' : ''}}">
                                                <span>@lang('work_order.bread_crumbs.in_progress')</span>
                                            </a>
                                        </li>
                                        @if(!empty($user->user_type != 'building_manager') && !empty($user->user_type != 'building_manager_employee'))
                                            <li>
                                                <a href = "{{ route('workorder.openWorkOrdersList', ['spare-parts', $this->encryptDecryptedString(1)]) }}" id = "listWOBySparePartsStatus" class = "{{ ( request()->is('work-order/list/spare-parts') || request()->is('work-order/spare-parts-workorders/*') || request()->is('work-order/new-work-orders-list/spare-parts/*') ) ? 'active' : ''}}">
                                                    <span>@lang('work_order.bread_crumbs.spare_parts')</span>
                                                </a>
                                            </li>
                                        @endif
                                        <li>
                                            <a href = "{{ route('workorder.openWorkOrdersList', ['under-evaluation', $this->encryptDecryptedString(1)]) }}" id = "listWOByUnderEvaluationStatus" class = "{{ ( request()->is('work-order/list/under-evaluation') || request()->is('work-order/under-evaluation-workorders/*') || request()->is('work-order/new-work-orders-list/under-evaluation/*') ) ? 'active' : ''}}">
                                                <span>@lang('work_order.bread_crumbs.Under_Evaluation')</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('workorder.openWorkOrdersList', ['pending', $this->encryptDecryptedString(1)]) }}" id = "listWOByWaitingForActionStatus" class = "{{ request()->is('work-order/new-work-orders-list/pending/*') ? 'active' : ''}}">
                                                <span>
                                                    @lang('slider.workorder_text_for_sp_sps_request_pending.Waiting_for_action')
                                                    <span class = "badge badge-danger ml-10 border-radius-10"></span>
                                                </span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('workorder.openWorkOrdersList', ['request', $this->encryptDecryptedString(1)]) }}" id = "listWOByPendingStatus" class = "{{ request()->is('work-order/new-work-orders-list/request/*') ? 'active' : ''}}">
                                                <span>
                                                    @lang('slider.workorder_text_for_sp_sps_request_pending.New_Requests')
                                                </span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('workorder.openWorkOrdersList', ['closed', $this->encryptDecryptedString(1)]) }}" id = "listWOByClosedStatus" class = "{{ request()->is('work-order/new-work-orders-list/closed/*') ? 'active' : '' }}">
                                                <span>
                                                    @lang('slider.lists.work_order_closed')
                                                </span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>

                            @elseif(!empty($user->user_type == 'admin') || !empty($user->user_type == 'admin_employee')
                            || (!empty($user->user_type == 'osool_admin') || !empty($user->user_type == 'super_admin')
                            || (!empty($user->user_type == 'procurement_admin') && ( isset($userPrivilegesAside->workorder) && !in_array('no_view', $userPrivilegesAside->workorder)))
                            ||  ( isset($userPrivilegesAside->service_provider) && !in_array('no_view', $userPrivilegesAside->service_provider))))
                                <li class = "has-child {{ request()->is('work-order/*') ? 'open' : ''}}">
                                    <a href = "#" class = "{{ request()->is('work-order/*') ? 'active' : ''}}">
                                        <span data-feather = "tool" class = "nav-icon"></span>
                                        <span style = "color: {{$workOrderMenuItemColor}}" class = "menu-text">@lang('slider.lists.work_order')</span>
                                        <span class = "toggle-icon"></span>
                                    </a>
                                    <ul>
                                        <li>
                                            <a id = "listWOByAllStatus" href = "{{ route('workorder.openServiceProvidersList','all') }}" class = "{{ ( request()->is('work-order/list/all') || request()->is('work-order/list/deleted') || request()->is('work-order/new-work-orders-list/all/*') ) ? 'active' : ''}}">
                                                <span>@lang('slider.lists.all')</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('workorder.openServiceProvidersList','open') }}" id = "listWOByOpenStatus" class = "{{ ( request()->is('work-order/list/open') || request()->is('work-order/new-work-orders-list/open/*') ) ? 'active' : ''}}">
                                                <span>@lang('work_order.bread_crumbs.open_tab')</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('workorder.openServiceProvidersList','in-progress') }}" id = "listWOByInProgressStatus" class = "{{ ( request()->is('work-order/list/in-progress') || request()->is('work-order/new-work-orders-list/in-progress/*') ) ? 'active' : ''}}">
                                                <span>@lang('work_order.bread_crumbs.in_progress')</span>
                                            </a>
                                        </li>
                                        @if(!empty($user->user_type != 'building_manager') && !empty($user->user_type != 'building_manager_employee'))
                                        <li>
                                            <a href = "{{ route('workorder.openServiceProvidersList','spare-parts') }}" id = "listWOBySparePartsStatus" class = "{{ ( request()->is('work-order/list/spare-parts') || request()->is('work-order/new-work-orders-list/spare-parts/*') ) ? 'active' : ''}}">
                                                <span>@lang('work_order.bread_crumbs.spare_parts')</span>
                                            </a>
                                        </li>
                                        @endif
                                        <li>
                                            <a href = "{{ route('workorder.openServiceProvidersList','under-evaluation') }}" id = "listWOByUnderEvaluationStatus" class = "{{ ( request()->is('work-order/list/under-evaluation') || request()->is('work-order/new-work-orders-list/under-evaluation/*') ) ? 'active' : ''}}">
                                                <span>@lang('work_order.bread_crumbs.Under_Evaluation')</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('workorder.openServiceProvidersList','closed') }}" id = "listWOByClosedStatus" class = "{{ ( request()->is('work-order/work-order-closed-list/*') || request()->is('work-order/new-work-orders-list/closed/*') )  ? 'active' : '' }}">
                                                <span>
                                                    @lang('slider.lists.work_order_closed')
                                                </span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            @else
                                @if(!empty($user->user_type == 'building_manager_employee') && ( isset($userPrivilegesAside->workorder) && !in_array('no_view' ,$userPrivilegesAside->workorder)))
                                    <li>
                                        <a id = "listWOByAllStatus" href = "{{ route('workorder.openWorkOrdersList', ['all', $this->encryptDecryptedString(1)]) }}" class = "{{ request()->is('data/*') ? 'active' : (request()->is('serviceproviders') ? 'active' : (request()->is('serviceproviders/*') ? 'active':''))}}">
                                            <span data-feather = "package" class = "nav-icon"></span>
                                            <span class = "menu-text">@lang('slider.lists.work_order')</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href = "{{ route('workorder.openWorkOrdersList', ['closed', $this->encryptDecryptedString(1)]) }}" id = "listWOByClosedStatus" class = "{{ request()->is('work-order/work-order-closed-list/*') ? 'active' : '' }}">
                                            <span class = "menu-text">
                                                @lang('slider.lists.work_order_closed')
                                            </span>
                                        </a>
                                    </li>
                                @endif
                            @endif
                        @endif
                        @if(!empty($user->user_type <> 'building_manager_employee'))
                            @if($user->user_type == 'sp_admin' || $user->user_type == 'supervisor')
                                <li class = "has-child {{ request()->is('user/*') ? 'open' : ''}}">
                                    <a href = "#" class = "{{ request()->is('user/*') ? 'active' : ''}}">
                                        <span data-feather = "users" class = "nav-icon"></span>
                                        <span style = "color: {{$workOrderMenuItemColor}}" class = "menu-text">@lang('slider.lists.user_management')</span>
                                        <span class = "badge badge-danger ml-10 border-radius-10">{{$pendingOfflineRequestCount}}</span>
                                        <span class = "toggle-icon"></span>
                                    </a>
                                    <ul>
                                        <li>
                                            <a href = "{{ route('users.list') }}" class = "{{ request()->is('user/*') && !request()->is('user/leave-request-list') && !request()->is('user/worker-list') && !request()->is('user/attendance-list') ? 'active' : ''}}">
                                                <span>@lang('user_management_module.user_forms.label.user_accounts')</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href = "{{ route('users.workerlist') }}" class = "{{ request()->is('user/worker-list') ? 'active' : ''}}">
                                                <span>@lang('user_management_module.user_forms.label.worker_list')</span>
                                            </a>
                                        </li>
                                        @if(\App\Services\AkauntingService::hasErpEnabledProjects())
                                            <li>
                                                <a href="{{ route('users.leaverequest.list') }}" class="{{ request()->is('user/leave-request-list') ? 'active' : ''}}">
                                                <span class="">@lang('user_management_module.user_forms.label.leave_requests')</span>
                                                <span class="badge badge-danger ml-10 border-radius-10">{{$pendingOfflineRequestCount}}</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href = "{{ route('users.attendancelist') }}" class = "{{ request()->is('user/attendance-list') ? 'active' : ''}}">
                                                    <span>@lang('user_management_module.attendance.attendance')</span>
                                                </a>
                                            </li>
                                        @endif
                                    </ul>
                                </li>
                            @elseif(empty($user->user_type == 'procurement_admin'))
                                <li {{ request()->is('user/*') ? 'open' : ''}}>
                                    <a href = "{{ route('users.list') }}" class = "{{ request()->is('user/*') ? 'active' : ''}}">
                                        <span data-feather = "users" class = "nav-icon"></span>
                                        <span class = "menu-text">@lang('slider.lists.user_management')</span>
                                    </a>
                                </li>
                            @endif
                        @endif
                        @if($hasAdmin && empty($user->user_type == 'procurement_admin'))
                            <li class = "has-child {{ request()->is('bulk-import/*') || ( request()->is('data/*') || Route::is('sharedfiles.show') || Route::is('contactinformation.show') ) ? 'open' : (request()->is('serviceproviders')||request()->is('property')  ? 'open':(request()->is('serviceproviders/*')||request()->is('property/*') || request()->is('assets-managements/*') ? 'open':''))}}">
                                <a href = "#" class = "{{ request()->is('data/*') || Route::is('sharedfiles.show') || Route::is('contactinformation.show')  ? 'active' : (request()->is('serviceproviders') ? 'active':(request()->is('serviceproviders/*') ? 'active':''))}}">
                                    <span data-feather = "server" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('slider.lists.data')</span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>
                                    @if(((!empty($user->user_type == 'osool_admin') ||!empty($user->user_type == 'admin_employee') || !empty($user->user_type == 'super_admin')) || !empty($user->user_type == 'admin')) || (!empty($userPrivilegesAside->service_provider) && (!in_array('no_view', $userPrivilegesAside->service_provider))))
                                        <li>
                                            <a href = "{{ route('serviceproviders.list') }}" class = "{{ Route::is('serviceproviders.*') ? 'active': '' }}">
                                                @lang('slider.lists.service_provider')
                                            </a>
                                        </li>

                                    @endif
                                    @if((!empty($user->user_type == 'admin_employee') ) || (!empty($user->user_type == 'osool_admin') || !empty($user->user_type == 'super_admin')) || !empty($user->user_type == 'admin') || !empty($user->user_type == 'sp_admin') || !empty($user->user_type == 'supervisor') || !empty($user->user_type == 'building_manager') ||!empty($user->user_type == 'building_manager_employee') || (!empty($userPrivilegesAside->property) && (!in_array('no_view', $userPrivilegesAside->property))))
                                        <li>
                                            <a href = "{{ route('property.list') }}" class = "{{ Route::is('property.*') ? 'active': '' }}">
                                                @lang('slider.lists.property')
                                            </a>
                                        </li>
                                    @endif
                                    @if((!empty($user->user_type == 'admin_employee') ) || (!empty($user->user_type == 'osool_admin') || !empty($user->user_type == 'super_admin')) || !empty($user->user_type == 'admin') || !empty($user->user_type == 'sp_admin') || !empty($user->user_type == 'supervisor') || !empty($user->user_type == 'building_manager') ||!empty($user->user_type == 'building_manager_employee') || (!empty($userPrivilegesAside->property) && (!in_array('no_view',$userPrivilegesAside->property))))
                                        <li>
                                        <a href="{{ route('asset-management.index') }}"
                                            class="{{ Route::is('asset-management.*') ? 'active': '' }}">{{__('slider.lists.assets')}}                     <span class="badge badge-danger ml-10" style="border-radius: 10px;">{{ __('assets_managements.common.new_module') }}</span></a>

                                        </li>
                                    @endif
                                    @if((!empty($user->user_type == 'admin_employee') ) || (!empty($user->user_type == 'osool_admin') || !empty($user->user_type == 'super_admin')) || !empty($user->user_type == 'admin') || ($this->checkUserPrivileges('no_view', 'contracts', false, $user)['success']))
                                        <li>
                                            <a href = "{{ route('data.contracts.list') }}" class = "{{ Route::is('data.contracts.*') ? 'active' : ''}}">
                                                @lang('slider.lists.contracts')
                                            </a>
                                        </li>
                                    @endif
                                    @if(in_array($user->user_type,['building_manager','admin','admin_employee']))
                                    <li>
                                            <a href = "{{ route('data.operational-contracts.list') }}" class = "{{ Route::is('data.operational-contracts.*') ? 'active' : ''}}">
                                                @lang('slider.lists.operational_contracts')
                                            </a>
                                        </li>
                                    @endif
                                    @if((isset($project) && isset($project->use_beneficiary_module) && isset($project->benificiary_status)) && $project->use_beneficiary_module == 1 && $project->benificiary_status == 1)
                                        @if((!empty($user->user_type == 'admin_employee') ) || (!empty($user->user_type == 'osool_admin') || !empty($user->user_type == 'super_admin')) || !empty($user->user_type == 'admin') || !empty($user->user_type == 'sp_admin') || !empty($user->user_type == 'building_manager') || (!empty($userPrivilegesAside->beneficiary) && (!in_array('no_view', $userPrivilegesAside->beneficiary))))
                                            <li>
                                                <a href = "{{ route('data.beneficiary.openBeneficirayList') }}" class = "{{ Route::is('data.beneficiary.*') ? 'active': '' }}">
                                                    @lang('slider.lists.beneficiary')
                                                </a>
                                            </li>
                                        @endif
                                    @endif
                                    @if(isset($project) && isset($project->use_tenant_module) && $project->use_tenant_module==1 && $project->tenant_status <> 2)
                                        @if(($user->user_type == 'building_manager' || $user->user_type == 'building_manager_employee') && (!empty($userPrivilegesAside->tenant)) && (!in_array('no_view', $userPrivilegesAside->tenant) )|| $user->user_type == 'admin' || $user->user_type == 'admin_employee'  || $user->user_type == 'osool_admin' || $user->user_type == 'super_admin')
                                            <li>
                                                <a href = "{{ route('data.tenants.buildinglist') }}" class = "{{ Route::is('data.tenants.*') || Route::is('data.tenants.building_detail') || Route::is('contactinformation.show') || Route::is('sharedfiles.show') || Route::is('data.tenants.create.info') || Route::is('data.tenants.edit.info') || Route::is('tenant.upload.document')  ? 'active': '' }}">
                                                    @lang('user_management_module.user_forms.label.tenant_menu_label')
                                                </a>
                                            </li>
                                        @endif
                                    @endif
                                    @if(in_array($user->user_type, array('super_admin', 'osool_admin')))
                                        <li>
                                            <a href = "{{ route('bulk-import.openUploadFile') }}" class = "{{ Route::is('bulk-import.openUploadFile') || Route::is('bulk-import.oepnSelectsheets') || Route::is('bulk-import.openFileMapping') || Route::is('bulk-import.openErrorsValidation') || Route::is('bulk-import.openFinishInsertion') ? 'active': '' }}">
                                                @lang('import.import_project_assets')
                                                <span class = "badge badge-danger ml-10" style = "border-radius: 10px;">
                                                    @lang('assets_managements.common.new_module')
                                                </span>
                                            </a>
                                        </li>
                                    @endif
                                </ul>
                            </li>
                            <li class = "has-child @if(Route::is('reports.create') || Route::is('reports.manage_reports') ) open @endif">
                                <a href = "#" class = "@if(Route::is('reports.create') || Route::is('reports.manage_reports') ) active @endif">
                                    <span data-feather = "file" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('reports.labels.reports')</span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>
                                    <li>
                                        <a href = "{{ route('reports.create') }}" class = "{{  Route::is('reports.create')  ? 'active' : '' }}">
                                            @lang('reports.labels.generate_reports')
                                        </a>
                                    </li>
                                    <li>
                                        <a href = "{{ route('reports.manage_reports') }}" class = "@if( Route::is('reports.manage_reports') ) active @endif">
                                            @lang('reports.labels.manage_reports')
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        @endif
                        @if((!empty($user->user_type == 'building_manager')) || (!empty($user->user_type == 'building_manager_employee')))
                            <li>
                                <a href = "{{ route('maintenance_requests.list') }}" class = "{{ Route::is('maintenance_requests.*') ? 'active': '' }}">
                                    <span data-feather = "package" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('slider.lists.maintenance_request')</span>
                                    <span class = "badge badge-danger ml-10 border-radius-10">{{App\Models\WorkOrders::get_pending_bm_maintenance_requests_count($user->id)}}</span>
                                </a>
                            </li>
                        @endif
                        @if($hasAdmin)
                            @if((!empty($user->user_type == 'osool_admin') || !empty($user->user_type == 'super_admin')) || !empty($user->user_type == 'admin') || !empty($user->user_type == 'admin_employee'))
                                <li>
                                    <a href = "{{ route('service_provider.performance') }}" class = "{{  Route::is('service_provider.performance')  ? 'active' : '' }}">
                                        <span data-feather = "bar-chart-2" class = "nav-icon"></span>
                                        <span class = "menu-text">@lang('performance.common.performance')</span>
                                    </a>
                                </li>
                            @endif
                        @endif
                    @endif
                    @if(\App\Services\AkauntingService::allow() &&
                    ( (!empty($user->user_type == 'osool_admin') && !is_null($projectId) ) ||
                    (!empty($user->user_type == 'procurement_admin') ) ||
                    (!empty($user->user_type == 'super_admin') && !is_null($projectId) ) ||
                    !empty($user->user_type == 'admin' ||
                    !empty($user->user_type == 'sp_admin') ||
                    (!empty($user->user_type) && $user->user_type == 'building_manager' && isset($userPrivilegesAside->inventory) && in_array('view', $userPrivilegesAside->inventory))
                     || $hasViewPrivilege  )
                     || !empty($user->user_type == 'admin_employee')  ))

                        @if($hasAdmin)
                        @if((!empty($user->user_type == 'osool_admin') && !is_null($projectId) ) ||
                        (!empty($user->user_type == 'super_admin') && !is_null($projectId) ) ||
                        !empty($user->user_type == 'admin' ||
                        !empty($user->user_type == 'sp_admin')) ||
                        (!empty($user->user_type == 'procurement_admin') && ( isset($userPrivilegesAside->purchases)
                        && !in_array('no_view', $userPrivilegesAside->purchases)) ) || !empty($user->user_type == 'admin_employee'))
                            @if (!empty($user->user_type <> 'building_manager') && !empty($user->user_type <> 'building_manager_employee') )
                            @if($user->user_type == 'sp_admin')
                            @if (\App\Services\AkauntingService::hasErpEnabledProjects())
                            <li class = "has-child {{  request()->is('purchases/*')  ? 'open' : ''}}">
                                <a href = "#" class="{{ request()->is('purchases/*') ? 'active' : ''}}">
                                    <span data-feather = "shopping-cart" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('admin.akaunting::slider.lists.purchases')
                                        <span class="badge badge-danger text-white ml-10" style="border-radius: 10px;">{{ __('assets_managements.common.new_module') }}</span>
                                    </span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>
                                    <li>
                                      <a href = "{{ route('purchase-request.index') }}" class = "{{ ( request()->is('purchases/purchases-requests') || request()->is('purchases/purchases-requests/*') ) ? 'active' : ''}}">
                                          <span>@lang('purchase_request.common.purchase_request') </span>
                                      </a>
                                  </li>
                                  <li>
                                    <a href = "{{ route('quotation.index') }}" class = "{{ ( request()->is('purchases/quotations') || request()->is('purchases/quotations/*') ) ? 'active' : ''}}">
                                        <span>@lang('quotation.common.quotations') </span>
                                    </a>
                                </li>
                                    <li>
                                       <a href = "{{route('purchase-orders.index')}}" class = "{{ ( request()->is('purchases/purchases-orders') || request()->is('purchases/purchases-orders/*') ) ? 'active' : ''}}">
                                          <span>@lang('purchase_order.common.purchases_orders')</span>
                                      </a>
                                  </li>

                                  <li>
                                      <a href = "{{ route('purchases.bills.index') }}" class = "{{ ( request()->is('purchases/bills') || request()->is('purchases/bills/*') ) ? 'active' : ''}}">
                                          <span>@lang('admin.akaunting::slider.lists.bills')</span>
                                      </a>
                                  </li>
								 {{--  <li>
                                      <a href = "{{ route('purchases.vendors.index') }}" class = "{{ ( request()->is('purchases/vendors') || request()->is('purchases/vendors/*') ) ? 'active' : ''}}">
                                          <span>@lang('admin.akaunting::vendors.vendors_title')</span>
                                      </a>
                                  </li> --}}

                                </ul>
                            </li>
                            @endif

                            @else
                            <li class = "has-child {{  request()->is('purchases/*')  ? 'open' : ''}}">
                                <a href = "#" class="{{ request()->is('purchases/*') ? 'active' : ''}}">
                                    <span data-feather = "shopping-cart" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('admin.akaunting::slider.lists.purchases')
                                        <span class="badge badge-danger text-white ml-10" style="border-radius: 10px;">{{ __('assets_managements.common.new_module') }}</span>
                                    </span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>
                                    <li>
                                      <a href = "{{ route('purchase-request.index') }}" class = "{{ ( request()->is('purchases/purchases-requests') || request()->is('purchases/purchases-requests/*') ) ? 'active' : ''}}">
                                          <span>@lang('purchase_request.common.purchase_request') </span>
                                      </a>
                                  </li>
                                  <li>
                                    <a href = "{{ route('quotation.index') }}" class = "{{ ( request()->is('purchases/quotations') || request()->is('purchases/quotations/*') ) ? 'active' : ''}}">
                                        <span>@lang('quotation.common.quotations') </span>
                                    </a>
                                </li>
                                    <li>
                                       <a href = "{{route('purchase-orders.index')}}" class = "{{ ( request()->is('purchases/purchases-orders') || request()->is('purchases/purchases-orders/*') ) ? 'active' : ''}}">
                                          <span>@lang('purchase_order.common.purchases_orders')</span>
                                      </a>
                                  </li>

                                  <li>
                                      <a href = "{{ route('purchases.bills.index') }}" class = "{{ ( request()->is('purchases/bills') || request()->is('purchases/bills/*') ) ? 'active' : ''}}">
                                          <span>@lang('admin.akaunting::slider.lists.bills')</span>
                                      </a>
                                  </li>
								{{--   <li>
                                      <a href = "{{ route('purchases.vendors.index') }}" class = "{{ ( request()->is('purchases/vendors') || request()->is('purchases/vendors/*') ) ? 'active' : ''}}">
                                          <span>@lang('admin.akaunting::vendors.vendors_title')</span>
                                      </a>
                                  </li> --}}

                                </ul>
                            </li>
                            @endif

                            @endif
                            @endif
                            @if (!empty($user->user_type <> 'building_manager') && !empty($user->user_type <> 'building_manager_employee') )
                            @if($user->user_type == 'sp_admin')
                            @if (\App\Services\AkauntingService::hasErpEnabledProjects())
                            <li class = "has-child {{  request()->is('sales/*')  ? 'open' : ''}}">
                                <a href = "#" class = "{{  request()->is('sales/*') ? 'active' : ''}}">
                                    <span data-feather = "trending-up" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('admin.akaunting::slider.lists.sales')

                                        <span class="badge badge-danger text-white ml-10" style="border-radius: 10px;">{{ __('assets_managements.common.new_module') }}</span>
                                    </span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>

                                    <li>
                                        <a href = "{{ route('sales-orders.index') }}" class = "{{ ( request()->is('sales/sales-orders') || request()->is('sales/sales-orders/*') ) ? 'active' : ''}}">
                                            <span>@lang('sales_order.common.sales_orders') </span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href = "{{ route('invoice.index') }}" class = "{{ ( request()->is('sales/invoice-Doc') || request()->is('sales/invoice-Doc/*') ) ? 'active' : ''}}">
                                            <span>@lang('sales_invoice.common.invoices')</span>
                                        </a>
                                    </li>
                                {{--     <li>
                                        <a href = "{{ route('sales.customers.index') }}" class = "{{ ( request()->is('sales/customers') || request()->is('sales/customers/*') ) ? 'active' : ''}}">
                                            <span>@lang('admin.akaunting::slider.lists.customer')</span>
                                        </a>
                                    </li> --}}
                                </ul>
                            </li>
                            @endif
                            @else
                            <li class = "has-child {{  request()->is('sales/*')  ? 'open' : ''}}">
                                <a href = "#" class = "{{  request()->is('sales/*') ? 'active' : ''}}">
                                    <span data-feather = "trending-up" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('admin.akaunting::slider.lists.sales')

                                        <span class="badge badge-danger text-white ml-10" style="border-radius: 10px;">{{ __('assets_managements.common.new_module') }}</span>
                                    </span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>

                                    <li>
                                        <a href = "{{ route('sales-orders.index') }}" class = "{{ ( request()->is('sales/sales-orders') || request()->is('sales/sales-orders/*') ) ? 'active' : ''}}">
                                            <span>@lang('sales_order.common.sales_orders') </span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href = "{{ route('invoice.index') }}" class = "{{ ( request()->is('sales/invoice-Doc') || request()->is('sales/invoice-Doc/*') ) ? 'active' : ''}}">
                                            <span>@lang('sales_invoice.common.invoices')</span>
                                        </a>
                                    </li>
                                {{--     <li>
                                        <a href = "{{ route('sales.customers.index') }}" class = "{{ ( request()->is('sales/customers') || request()->is('sales/customers/*') ) ? 'active' : ''}}">
                                            <span>@lang('admin.akaunting::slider.lists.customer')</span>
                                        </a>
                                    </li> --}}
                                </ul>
                            </li>
                            @endif
                            @endif
                            @if((!empty($user->user_type == 'osool_admin') && !is_null($projectId) ) ||
                            (!empty($user->user_type == 'super_admin') && !is_null($projectId) ) ||
                            !empty($user->user_type == 'admin' ||
                            (!empty($user->user_type) && $user->user_type == 'building_manager' && isset($userPrivilegesAside->inventory) && in_array('view', $userPrivilegesAside->inventory))
                            || $hasViewPrivilege||
                            !empty($user->user_type == 'sp_admin')) ||
                            (!empty($user->user_type == 'procurement_admin') && ( isset($userPrivilegesAside->inventory)
                            && !in_array('no_view', $userPrivilegesAside->inventory))) || !empty($user->user_type == 'admin_employee') )

                            @if($user->user_type == 'sp_admin')
                            @if (\App\Services\AkauntingService::hasErpEnabledProjects())
                            <li class = "has-child {{ request()->is('inventory/*') ? 'open' : ''}}">
                                <a href = "#" class = "{{ request()->is('inventory/*') ? 'active' : ''}}">
                                    <span data-feather = "box" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('admin.akaunting::slider.lists.inventory')</span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>
                                    <li>
                                        <a href = "{{ route('inventory.items.index') }}" class = "{{ ( request()->is('inventory/items') || request()->is('inventory/items/create') || request()->is('inventory/items/*') ) ? 'active' : ''}}">
                                            <span>@lang('admin.akaunting::slider.lists.items')</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href = "{{ route('inventory.history') }}" class = "{{ ( request()->is('inventory/history')) ? 'active' : ''}}">
                                            <span>@lang('admin.akaunting::slider.lists.history')</span>
                                        </a>
                                    </li>
                                    @if((!empty($user->user_type == 'osool_admin') && !is_null($projectId) ) ||
                                    (!empty($user->user_type == 'super_admin') && !is_null($projectId) ) || !empty($user->user_type == 'admin_employee') ||
                                    !empty($user->user_type == 'admin' ||
                                    !empty($user->user_type == 'sp_admin') ||
                                    (!empty($user->user_type) && $user->user_type == 'building_manager' && isset($userPrivilegesAside->inventory) && in_array('view', $userPrivilegesAside->inventory))
                                     || $hasViewPrivilege ) ||
                                    (!empty($user->user_type == 'procurement_admin') && ( isset($userPrivilegesAside->warehouses)
                                    && !in_array('no_view', $userPrivilegesAside->warehouses)) ))
                                   <li>
                                      <a href = "{{ route('inventory.warehouses.index') }}" class = "{{ ( request()->is('inventory/warehouses') || request()->is('inventory/warehouses/create') || request()->is('inventory/warehouses/*') ) ? 'active' : ''}}">
                                        <span>@lang('admin.akaunting::slider.lists.warehouses')</span>
                                      </a>
                                   </li>
                                   @endif
                                </ul>
                            </li>
                            @endif
                            @else
                            <li class = "has-child {{ request()->is('inventory/*') ? 'open' : ''}}">
                                <a href = "#" class = "{{ request()->is('inventory/*') ? 'active' : ''}}">
                                    <span data-feather = "box" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('admin.akaunting::slider.lists.inventory')</span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>
                                    <li>
                                        <a href = "{{ route('inventory.items.index') }}" class = "{{ ( request()->is('inventory/items') || request()->is('inventory/items/create') || request()->is('inventory/items/*') ) ? 'active' : ''}}">
                                            <span>@lang('admin.akaunting::slider.lists.items')</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href = "{{ route('inventory.history') }}" class = "{{ ( request()->is('inventory/history')) ? 'active' : ''}}">
                                            <span>@lang('admin.akaunting::slider.lists.history')</span>
                                        </a>
                                    </li>
                                    @if((!empty($user->user_type == 'osool_admin') && !is_null($projectId) ) ||
                                    (!empty($user->user_type == 'super_admin') && !is_null($projectId) ) ||!empty($user->user_type == 'admin_employee') ||
                                    !empty($user->user_type == 'admin' ||
                                    !empty($user->user_type == 'sp_admin') ||
                                    (!empty($user->user_type) && $user->user_type == 'building_manager' && isset($userPrivilegesAside->inventory) && in_array('view', $userPrivilegesAside->inventory))
                                     || $hasViewPrivilege ) ||
                                    (!empty($user->user_type == 'procurement_admin') && ( isset($userPrivilegesAside->warehouses)
                                    && !in_array('no_view', $userPrivilegesAside->warehouses)) ))
                                   <li>
                                      <a href = "{{ route('inventory.warehouses.index') }}" class = "{{ ( request()->is('inventory/warehouses') || request()->is('inventory/warehouses/create') || request()->is('inventory/warehouses/*') ) ? 'active' : ''}}">
                                        <span>@lang('admin.akaunting::slider.lists.warehouses')</span>
                                      </a>
                                   </li>
                                   @endif
                                </ul>
                            </li>
                            @endif


                            @endif
                        @endif
                    @endif
                    @if (!in_array($user->user_type,['osool_admin','super_admin']))
                    @php
                        $marketplaceRoutes = [
                            'marketplace.providers-list', 'marketplace.provider-details', 'marketplace.my-requests',
                            'marketplace.my-requests-explore', 'marketplace.open-offers', 'marketplace.direct-offers',
                            'marketplace.service-request-detail', 'marketplace.work-orders', 'marketplace.work-orders.view'
                        ];
                        $providerRoutes = [
                            'marketplace.providers-list', 'marketplace.provider-details', 'marketplace.my-requests',
                            'marketplace.my-requests-explore', 'marketplace.open-offers', 'marketplace.direct-offers',
                            'marketplace.service-request-detail'
                        ];

                        $workOrderRoutes = ['marketplace.work-orders', 'marketplace.work-orders.view'];

                        $isMarketplaceActive = in_array(Route::currentRouteName(), $marketplaceRoutes);
                        $isProviderActive = in_array(Route::currentRouteName(), $providerRoutes);
                        $isWorkOrderActive = in_array(Route::currentRouteName(), $workOrderRoutes);
                    @endphp
                    <li class="has-child {{ $isMarketplaceActive ? 'open' : '' }}">
                        <a href="#" class="{{ $isMarketplaceActive ? 'active' : '' }}">
                            <span class="feather nav-icon"><i class="las la-warehouse fs-18"></i></span>
                            <span class="menu-text">@lang('marketplace.heading')</span>
                            <span class="toggle-icon"></span>
                        </a>
                        <ul>
                            <li>
                                <a href="{{ route('marketplace.providers-list') }}" class="{{ $isProviderActive ? 'active' : '' }}">
                                    <span class="menu-text">@lang('marketplace.heading')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('marketplace.work-orders') }}" class="{{ $isWorkOrderActive ? 'active' : '' }}">
                                    <span class="menu-text">@lang('MarketPlace Work Orders')</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    @endif

                    @if(!empty($user->user_type == 'osool_admin') || !empty($user->user_type == 'super_admin'))

                        <li class = "has-child {{ request()->is('subscribers/*') ? 'open' : ''}}">
                            <a href = "#" class = "{{ request()->is('subscribers/*') ? 'active' : ''}}">
                                <span data-feather = "disc" class = "nav-icon"></span>
                                <span class = "menu-text">@lang('NHC.main_title')</span>
                                <span class = "toggle-icon"></span>
                            </a>
                            <ul>
                                <li>
                                    <a href = "{{ route('subscribers.manage-orders') }}" class = "{{  Route::is('subscribers.manage-orders')  ? 'active' : '' }}">
                                        <span>@lang('NHC.manage_orders')</span>
                                    </a>
                                </li>
                                @if(!empty($user->user_type == 'super_admin'))
                                    <li>
                                        <a href = "{{ route('subscribers.manage-packages') }}" class = "{{ (in_array(Route::currentRouteName(), ['subscribers.manage-packages', 'subscribers.create-subscription', 'subscribers.update-subscription'])) ? ' active' : '' }}">
                                            <span>@lang('NHC.manage_packages')</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href = "{{ route('subscribers.payments') }}" class = "{{  Route::is('subscribers.payments')  ? 'active' : '' }}">
                                            <span>@lang('NHC.payments')</span>
                                        </a>
                                    </li>
                                @endif
                                {{-- <li>
                                    <a href = "{{ route('contacts.demolist') }}" class = "{{  Route::is('contacts.demolist')  ? 'active' : '' }}">
                                        <span>@lang('contactus_request.user_forms.label.request_a_demo')</span>
                                    </a>
                                </li> --}}
                            </ul>
                        </li>
                    @endif

                    {{-- PROJECTS --}}
                 {{--    @php
                        $projectRoutes = [
                            'projects.task-board', 'task-board-list-view','incident-report','incident-report-list-view','projects.system-setup.incident-stages','projects.system-setup.task-stages'
                        ];

                        $systemSetup = ['projects.system-setup.incident-stages','projects.system-setup.task-stages'];
                        $isSystemSetupActive = in_array(Route::currentRouteName(), $systemSetup);

                        $isProjectActive = in_array(Route::currentRouteName(), $projectRoutes);
                    @endphp

                    <li class="has-child {{ $isProjectActive ? 'open' : '' }}">
                        <a href="#" class="{{ $isProjectActive ? 'active' : '' }}">
                            <span class="feather nav-icon"><i class="las la-warehouse fs-18"></i></span>
                            <span class="menu-text">@lang('CRMProjects.title')</span>
                            <span class="toggle-icon"></span>
                        </a>
                        <ul>
                            <li>
                                <a href="{{ route('projects.system-setup.task-stages') }}" class="{{ $isSystemSetupActive ? 'active' : '' }}">
                                    <span class="menu-text">@lang('CRMProjects.system-setup')</span>
                                </a>
                            </li>
                        </ul>
                    </li> --}}

                    @if((!empty($user->user_type) && $user->user_type != 'supervisor' && \App\Services\AkauntingService::allow()) ||
                    (($user->user_type == 'osool_admin' || $user->user_type == 'super_admin') && !is_null($projectId)))
                    @if($user->user_type != 'procurement_admin')

                        @if($user->user_type == 'sp_admin')
                            @if (\App\Services\AkauntingService::hasErpEnabledProjects())
                            <li>
                            <a href="{{ route('complaints.list') }}" class="{{ Route::is('complaints.list') ? 'active' : '' }}">
                                <span data-feather="alert-triangle" class="nav-icon"></span>
                                <span class="menu-text">@lang('complaints.complaint_management')</span>
                                @if($user->user_type == 'sp_admin')
                                    <span class="badge badge-danger ml-10 border-radius-10">{{$pendingComplaintsCount}}</span>
                                @endif
                            </a>
                        </li>
                            @endif
                        @else
                        <li>
                            <a href="{{ route('complaints.list') }}" class="{{ Route::is('complaints.list') ? 'active' : '' }}">
                                <span data-feather="alert-triangle" class="nav-icon"></span>
                                <span class="menu-text">@lang('complaints.complaint_management')</span>
                                @if($user->user_type == 'sp_admin')
                                    <span class="badge badge-danger ml-10 border-radius-10">{{$pendingComplaintsCount}}</span>
                                @endif
                            </a>
                        </li>
                        @endif
                    @endif
                @endif

                    @if($hasAdmin)
                        @if(!empty($user->user_type == 'admin') || ( (!empty($user->user_type == 'osool_admin') && !is_null($projectId) ) ) || ( (!empty($user->user_type == 'super_admin') && !is_null($projectId) ) ) || !empty($user->user_type == 'admin_employee'))
                            <li class = "ossol-bottom-navigation fixed-bottom">
                                <a href = "{{ route('configuration.general_settings.index') }}" class = "actives {{ Route::is('asset.*') || Route::is('configuration.general_settings.*') || Route::is('configuration.service_assets.*') ? 'active': '' }}">
                                    <span data-feather = "settings" class = "nav-icon"></span>
                                    <span class = "menu-text">@lang('slider.lists.configuration')</span>
                                </a>
                            </li>
                        @endif
                    @endif
                    @if(!empty($user->user_type == 'admin') || ( (!empty($user->user_type == 'osool_admin') && !is_null($projectId) ) ) || ( (!empty($user->user_type == 'super_admin') && !is_null($projectId) ) ) || !empty($user->user_type == 'admin_employee'))
                        <li class = "ossol-bottom-navigation fixed-bottom">
                            <a href = "{{ route('configuration.general_settings.index') }}" class = "actives {{ Route::is('asset.*') || Route::is('configuration.general_settings.*') || Route::is('configuration.service_assets.*') ? 'active': '' }}">
                                <span data-feather = "settings" class = "nav-icon"></span>
                                <span class = "menu-text">@lang('slider.lists.configuration')</span>
                            </a>
                        </li>
                    @endif
                    @if($user->user_type == "supervisor" || $user->user_type == 'sp_admin')
                        <li>
                            <a href = "{{ route('service_provider.performance_details', \Crypt::encryptString(1)) }}" class = "{{  Route::is('service_provider.performance_details')  ? 'active' : '' }}">
                                <span data-feather = "bar-chart-2" class = "nav-icon"></span>
                                <span class = "menu-text">@lang('performance.common.performance')</span>
                            </a>
                        </li>
                    @endif
                    @if($user->is_crm_user && ($user->user_type <> 'super_admin') )
                        <li>
                            <a href="{{ route('crm.omnichat.omniChat', ['slug'=> 'email']) }}" class="{{ Route::is('crm.omnichat.omniChat', ['slug'=> 'email']) ? 'active' : '' }}">
                                <span data-feather="bar-chart-2" class="nav-icon"></span>
                                <span>@lang('slider.lists.omni_channel')</span>
                            </a>
                        </li>
                    <li class = "has-child @if(Route::is('crm.dashboard')
                    || Route::is('crm.deals_dashboard')
                    || Route::is('crm.leads_dashboard')
                    || Route::is('crm.users.*')
                    || Route::is('crm.leads.*')
                    ||  Route::is('crm.deals.*')
                    || Route::is('crm.leads.stages')
                    || Route::is('crm.pipelines.*')
                    || Route::is('crm.sources.list')) open @endif">
                        <a href = "#" class="@if(Route::is('crm.dashboard')
                        || Route::is('crm.deals_dashboard')
                        || Route::is('crm.leads_dashboard')
                        || Route::is('crm.users.*')
                        || Route::is('crm.leads.*')
                        ||  Route::is('crm.deals.*')
                        || Route::is('crm.leads.stages')
                        || Route::is('crm.pipelines.*')
                        || Route::is('crm.sources.list') ) active @endif">
                            <i class="iconsax nav-icon fs-16" icon-name="group"></i>
                            <span class = "menu-text">CRM</span>
                            <span class = "toggle-icon"></span>
                        </a>
                        <ul>
                            <li>
                                 <a href="{{ route('crm.dashboard') }}" class="{{ Route::is('crm.dashboard') ? 'active' : '' }}">
                                    <span>@lang('slider.lists.dashboard')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('crm.deals_dashboard') }}" class="{{ Route::is('crm.deals_dashboard') ? 'active' : '' }}">
                                    <span>@lang('slider.lists.deals_dashboard')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('crm.leads_dashboard') }}" class="{{ Route::is('crm.leads_dashboard') ? 'active' : '' }}">
                                    <span>@lang('slider.lists.leads_dashboard')</span>
                                </a>
                            </li>
                            <li>
                                <a href = "{{ route('crm.users.list') }}" class = "{{ Route::is('crm.users.list') || Route::is('crm.users.*')  ? 'active' : '' }}">
                                    <span>@lang('slider.lists.users')</span>

                                </a>
                            </li>
                            <li>
                                <a href = "{{ route('crm.leads.list') }}" class = "{{ (Route::is('crm.leads.*') && !Route::is('crm.leads.stages'))  ? 'active' : '' }}">
                                    <span>@lang('slider.lists.leads')</span>

                                </a>
                            </li>

                            <li>
                                <a href = "{{ route('crm.deals.list') }}" class = "{{ (Route::is('crm.deals.*') && !Route::is('crm.deals.stages'))  ? 'active' : '' }}">
                                    <span>@lang('slider.lists.deals')</span>

                                </a>
                            </li>

                            <li>
                                <a href = "{{ route('crm.leads.stages') }}"
                                    class = "{{ Route::is('crm.leads.stages')
                                || Route::is('crm.pipelines.*')
                                || Route::is('crm.sources.list')
                                ||  Route::is('crm.deals.stages')   ? 'active' : '' }}">
                                    <span>@lang('slider.lists.stages')</span>

                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class = "has-child {{ in_array(Route::currentRouteName(), ['sales.accountType', 'sales.accountIndustry', 'sales.opportunitiesStage', 'sales.caseType', 'sales.shippingProvider', 'sales.documentType', 'sales.documentFolder', 'sales.accounts', 'sales.contacts', 'sales.opportunities', 'sales.quotes','sales.invoices','sales.orders','sales.cases','sales.streams','sales.documents','sales.calls','sales.meetings','sales.quoteAnalytics', 'sales.invoiceAnalytics', 'sales.orderAnalytics', 'sales.editAccount', 'sales.editContact', 'sales.editOpportunity', 'sales.editQuote', 'sales.viewQuote', 'sales.editInvoice', 'sales.viewInvoice', 'sales.editOrder', 'sales.viewOrder', 'sales.editCase', 'sales.editDocument', 'sales.editCall', 'sales.editMeeting']) ? 'open' : '' }}">
                        <a href = "#">
                            <span data-feather = "users" class = "nav-icon"></span>
                            <span class = "menu-text">@lang('Sales')</span>
                            <span class = "toggle-icon"></span>
                        </a>
                        <ul>
                            <li>
                                <a href="{{ route('sales.accountType') }}" class="{{ in_array(Route::currentRouteName(), ['sales.accountType', 'sales.accountIndustry', 'sales.opportunitiesStage', 'sales.caseType', 'sales.shippingProvider', 'sales.documentType', 'sales.documentFolder']) ? 'active' : '' }}">
                                    <span>@lang('System Setup')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.accounts') }}" class="{{ in_array(Route::currentRouteName(), ['sales.accounts', 'sales.editAccount']) ? 'active' : '' }}">
                                    <span>@lang('Account')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.contacts') }}" class="{{ in_array(Route::currentRouteName(), ['sales.contacts', 'sales.editContact']) ? 'active' : '' }}">
                                    <span>@lang('Contact')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.opportunities') }}" class="{{ in_array(Route::currentRouteName(), ['sales.opportunities', 'sales.editOpportunity']) ? 'active' : '' }}">
                                    <span>@lang('Opportunities')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.quotes') }}" class="{{ in_array(Route::currentRouteName(), ['sales.quotes', 'sales.viewQuote', 'sales.editQuote']) ? 'active' : '' }}">
                                    <span>@lang('Quotes')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.orders') }}" class="{{ in_array(Route::currentRouteName(), ['sales.orders', 'sales.editOrder', 'sales.viewOrder']) ? 'active' : '' }}">
                                    <span>@lang('Sales Order')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.invoices') }}" class="{{ in_array(Route::currentRouteName(), ['sales.invoices', 'sales.editInvoice', 'sales.viewInvoice']) ? 'active' : '' }}">
                                    <span>@lang('Sales Invoice')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.cases') }}" class="{{ in_array(Route::currentRouteName(), ['sales.cases', 'sales.editCase']) ? 'active' : '' }}">
                                    <span>@lang('Cases')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.streams') }}" class="{{ in_array(Route::currentRouteName(), ['sales.streams']) ? 'active' : '' }}">
                                    <span>@lang('Stream')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.documents') }}" class="{{ in_array(Route::currentRouteName(), ['sales.documents', 'sales.editDocument']) ? 'active' : '' }}">
                                    <span>@lang('Sales Document')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.calls') }}" class="{{ in_array(Route::currentRouteName(), ['sales.calls', 'sales.editCall']) ? 'active' : '' }}">
                                    <span>@lang('Calls')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.meetings') }}" class="{{ in_array(Route::currentRouteName(), ['sales.meetings', 'sales.editMeeting']) ? 'active' : '' }}">
                                    <span>@lang('Meeting')</span>
                                </a>
                            </li>
                            {{-- <li class = "has-child {{ in_array(Route::currentRouteName(), ['sales.quoteAnalytics', 'sales.invoiceAnalytics', 'sales.orderAnalytics']) ? 'open' : '' }}">
                                <a href = "#">
                                    <span class = "menu-text">@lang('Report')</span>
                                    <span class = "toggle-icon"></span>
                                </a>
                                <ul>
                                    <li>
                                        <a href="{{ route('sales.quoteAnalytics') }}" class="{{ Route::is('sales.quoteAnalytics') ? 'active' : '' }}">
                                            <span>@lang('Quote Analytics')</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('sales.invoiceAnalytics') }}" class="{{ Route::is('sales.invoiceAnalytics') ? 'active' : '' }}">
                                            <span>@lang('Sales Invoice Analytics')</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('sales.orderAnalytics') }}" class="{{ Route::is('sales.orderAnalytics') ? 'active' : '' }}">
                                            <span>@lang('Sales Order Analytics')</span>
                                        </a>
                                    </li>
                                </ul>
                            </li> --}}
                            <li>
                                <a href="{{ route('sales.quoteAnalytics') }}" class="{{ Route::is('sales.quoteAnalytics') ? 'active' : '' }}">
                                    <span>@lang('Quote Analytics')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.invoiceAnalytics') }}" class="{{ Route::is('sales.invoiceAnalytics') ? 'active' : '' }}">
                                    <span>@lang('Sales Invoice Analytics')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('sales.orderAnalytics') }}" class="{{ Route::is('sales.orderAnalytics') ? 'active' : '' }}">
                                    <span>@lang('Sales Order Analytics')</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class = "has-child {{ Route::is('finance.*') ? 'open' : '' }}">
                        <a href = "#" class="{{ Route::is('finance.*') ? 'active' : '' }}">
                            <span data-feather = "dollar-sign" class = "nav-icon"></span>
                            <span class = "menu-text">@lang('admin.akaunting::slider.lists.accounting')</span>
                            <span class = "toggle-icon"></span>
                        </a>
                        <ul>
                            <li>
                                <a href="{{ route('finance.customers') }}" class="{{ Route::is('finance.customers') ? 'active' : '' }}">
                                    <span>@lang('admin.akaunting::slider.lists.customers')</span>
                                </a>
                            </li>

                            <li>
                                <a href="{{ route('finance.goals') }}" class="{{ Route::is('finance.goals*') ? 'active' : '' }}">
                                    <span>@lang('goals.common.goals')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('finance.journals') }}" class="{{ Route::is('finance.journals*') ? 'active' : '' }}">
                                    <span>@lang('journals.common.journals')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('finance.income') }}" class="{{ Route::is('finance.income') ? 'active' : '' }}">
                                    <span>@lang('income.common.revenue')</span>
                                </a>
                            </li>
                           
<li>
<a href="{{ route('finance.invoice') }}" class="{{ Route::is('finance.invoice') ? 'active' : '' }}">
<span>@lang('accounting.invoice')</span>
</a>
</li>

<li class = "has-child {{ in_array(Route::currentRouteName(), ['finance.bank-account', 'finance.chart-account', 'finance.transfer']) ? 'open' : '' }}">
<a href = "#">
<span class = "menu-text">@lang('accounting.banking')</span>
<span class = "toggle-icon"></span>
</a>
<ul>
<li>
<a href="{{ route('finance.bank-account') }}" class="{{ Route::is('finance.bank-account') ? 'active' : '' }}">
<span>@lang('accounting.accounts')</span>
</a>
</li>
<li>
<a href="{{ route('finance.chart-account') }}" class="{{ Route::is('finance.chart-account') ? 'active' : '' }}">
<span>@lang('accounting.chartaccount')</span>
</a>
</li>
<li>
<a href="{{ route('finance.transfer') }}" class="{{ Route::is('finance.transfer') ? 'active' : '' }}">
<span>@lang('accounting.transfer')</span>
</a>
</li>
</ul>
</li>


<li class = "has-child {{ in_array(Route::currentRouteName(), ['finance.income', 'finance.credit-note']) ? 'open' : '' }}">
<a href = "#">
<span class = "menu-text">@lang('accounting.income')</span>
<span class = "toggle-icon"></span>
</a>
<ul>
<li>
<a href="{{ route('finance.income') }}" class="{{ Route::is('finance.income') ? 'active' : '' }}">
<span>@lang('income.common.revenue')</span>
</a>
</li>
<li>
<a href="{{ route('finance.credit-note') }}" class="{{ Route::is('finance.credit-note') ? 'active' : '' }}">
<span>@lang('accounting.creditnotes')</span>
</a>
</li>
</ul>
</li>

<li class = "has-child {{ in_array(Route::currentRouteName(), ['finance.bill','finance.payment', 'finance.debit-note']) ? 'open' : '' }}">
<a href = "#">
<span class = "menu-text">@lang('accounting.expense')</span>
<span class = "toggle-icon"></span>
</a>
<ul>

<li>
<a href="{{ route('finance.bill') }}" class="{{ Route::is('finance.bill') ? 'active' : '' }}">
<span>@lang('accounting.bill')</span>
</a>
</li>   
<li>
<a href="{{ route('finance.payment') }}" class="{{ Route::is('finance.payment') ? 'active' : '' }}">
<span>@lang('accounting.payment')</span>
</a>
</li>
<li>
<a href="{{ route('finance.debit-note') }}" class="{{ Route::is('finance.debit-note') ? 'active' : '' }}">
<span>@lang('accounting.debitnotes')</span>
</a>
</li>
</ul>
</li>

<li>
<a href="{{ route('finance.budget-planner') }}" class="{{ Route::is('finance.budget-planner') ? 'active' : '' }}">
<span>@lang('accounting.budgetPlanner')</span>
</a>
</li>
                            

                          
                             

                            
                            
                        </ul>
                    </li>

                    <li class = "has-child {{ in_array(Route::currentRouteName(), ['CRMProjects.dashboard','CRMProjects.list','CRMProjects.details','CRMProjects.task-board','CRMProjects.task-board-list-view','CRMProjects.incident-report','CRMProjects.incident-report-list-view','CRMProjects.system-setup.incident-stages','CRMProjects.system-setup.task-stages','CRMProjects.dashboard','CRMProjects.gantChartDetails','crm.projects.reports.index','crm.projects.reports.token','crm.projects.reports.show','crm.projects.templates.cards','crm.projects.templates.cards','crm.projects.templates.list','crm.projects.templates.view']) ? 'open' : '' }}">
                        <a href = "#">
                            <i class="iconsax nav-icon fs-16" icon-name="task-list-square"></i>
                            <span class = "menu-text">@lang('CRMProjects.common.projects')</span>
                            <span class = "toggle-icon"></span>
                        </a>
                        <ul>
                            <li>
                                <a href="{{ route('CRMProjects.dashboard') }}" class="{{ in_array(Route::currentRouteName(), ['CRMProjects.dashboard']) ? 'active' : '' }}">
                                    <span>@lang('CRMProjects.common.dashboard')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('CRMProjects.list') }}" class="{{ in_array(Route::currentRouteName(), ['CRMProjects.list','CRMProjects.details','CRMProjects.task-board','CRMProjects.task-board-list-view','CRMProjects.incident-report','CRMProjects.incident-report-list-view','CRMProjects.gantChartDetails']) ? 'active' : '' }}">
                                    <span>@lang('CRMProjects.common.project')</span>
                                </a>
                            </li>

                            @if(auth()->user()->user_type != 'building_manager')
                            <li>
                                <a href="{{ route('crm.projects.templates.cards') }}" class="{{ in_array(Route::currentRouteName(), ['crm.projects.templates.cards','crm.projects.templates.list','crm.projects.templates.view']) ? 'active' : '' }}">
                                    <span>@lang('CRMProjects.common.project-template')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('crm.projects.reports.index') }}"
                                    class="{{ in_array(Route::currentRouteName(), ['crm.projects.reports.index','crm.projects.reports.token','crm.projects.reports.show']) ? 'active' : '' }}">
                                    <span> @lang('CRMProjects.common.project_reports')</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('CRMProjects.system-setup.task-stages') }}" class="{{ in_array(Route::currentRouteName(), ['CRMProjects.system-setup.task-stages','CRMProjects.system-setup.incident-stages']) ? 'active' : '' }}">
                                    <span class="menu-text">@lang('CRMProjects.system-setup')</span>
                                </a>
                            </li>
@endif
                        </ul>
                    </li>

                    @endif
                </ul>
            </div>
        </aside>
    @endif
</div>
