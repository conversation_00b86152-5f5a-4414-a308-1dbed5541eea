<div wire:init="loadData">
    <div class = "dropdown-custom">
        <a href = "#" class="nav-item-toggle notification_bell_status {{ isset($totalUnreadNotifications) && $totalUnreadNotifications > 0 ? 'unread' : '' }}">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                 class="feather feather-bell">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
            </svg>
        </a>
        <div class = "dropdown-wrapper">
            <h2 class = "dropdown-wrapper__title">
                @lang('notifications.common.notifications')
            </h2>
            <ul>
                @if(isset($list) && $list->count())
                    <span id = "display-list">
                        @foreach ($list as $key => $data)
                            <li class = "nav-notification__single @if($data->is_read == 'no') unread @endif d-flex flex-wrap" wire:key="{{ $key }}">
                                <div class = "nav-notification__type">
                                    @switch($data->notification_sub_type)
                                        @case('booking_created')
                                            <img src = "{{asset('img/svg/booking-created.svg')}}">
                                        @break
                                        @case('kpi_report_generated')
                                            <img src = "{{asset('img/svg/booking-created.svg')}}">
                                        @break

                                        @case('booking_completed')
                                            <img src = "{{asset('img/svg/booking-completed.svg')}}">
                                        @break

                                        @case('booking_cancelled')
                                            <img src = "{{asset('img/svg/booking-cancelled.svg')}}">
                                        @break

                                        @case('new_work_order_created')
                                            <img src = "{{asset('img/svg/new-work.svg')}}">
                                        @break

                                        @case('scheduled_new_workerorder')
                                            <img src = "{{asset('img/svg/new-work.svg')}}">
                                        @break

                                        @case('new_chat_message')
                                            <img src = "{{asset('img/svg/chat-work.svg')}}">
                                        @break

                                        @case('bm_has_reopend_wo')
                                            <img src = "{{asset('img/svg/repoen-work.svg')}}">
                                        @break

                                        @case('worker_assigned_by_bm')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('sp_has_did_not_agreed_on_workorder')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('sp_has_edited_target_date')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('send_wo_reminder_to_sp')
                                            <img src = "{{asset('img/svg/clock-icon.svg')}}">
                                        @break

                                        @case('overdue_payment')
                                                    <img src = "{{asset('img/svg/clock-icon.svg')}}">
                                        @break

                                        @case('bm_has_did_not_agreed_on_workorder')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('bm_respond_to_sp_rejection')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('sp_respond_to_bm_rejection')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('sp_has_an_issue')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('new_maintenance_request')
                                            <img src = "{{asset('img/svg/main-work.svg')}}">
                                        @break

                                        @case('bm_has_approved_and_evaluated_wo')
                                            <img src = "{{asset('img/svg/closed-work.svg')}}">
                                        @break

                                        @case('wo_completed_wo')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('wo_paused_wo')
                                            <img src = "{{asset('img/svg/pause-icon.svg')}}">
                                        @break

                                        @case('wo_restarted_wo')
                                            <img src = "{{asset('img/svg/resume-icon.svg')}}">
                                        @break

                                        @case('bm_has_automatically_approved_wo')
                                            <img src = "{{asset('img/svg/closed-work.svg')}}">
                                        @break

                                        @case('sp_has_automatically_approved_wo')
                                            <img src="{{asset('img/svg/work-update.svg')}}">
                                            @break

                                        @case('pause_workorder_spa')
                                            <img src="{{asset('img/svg/work-update.svg')}}">
                                            @break
                                        
                                        @case('pause_workorder')
                                            <img src="{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('sps_has_assigned')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('new_availability_change_request')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @case('sent_to_project_owner')
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break

                                        @default
                                            <img src = "{{asset('img/svg/work-update.svg')}}">
                                        @break
                                    @endswitch
                                </div>
                                <div class = "nav-notification__details">
                                    @php
                                        $route = "";
                                        $notificationType = '';

                                        if($data['section_type'] == 'work_order' && $data['notification_sub_type'] == 'new_maintenance_request') {
                                            $notificationType = '';
                                            $route = route('maintenance_requests.list',[$this->encryptDecryptedString($data['section_id']),'notification-read', $this->encryptDecryptedString($data['id'])]);
                                        }

                                        elseif($data['work_order_type'] == 'reactive') {
                                            $notificationType = $data['work_order_id'];
                                            $route = route('workorder.show', [$this->encryptDecryptedString($data['section_id']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                        }

                                        elseif($data['work_order_type'] == 'preventive') {
                                            $notificationType = $data['work_order_id'];
                                            $route = route('workorder.show', [$this->encryptDecryptedString($data['section_id']),'notification-read', $this->encryptDecryptedString($data['id'])]);
                                        }

                                        elseif($data['section_type'] == 'report' ) {
                                            $notificationType = '';
                                            if($data['notification_type'] == 'create_ppm')
                                            {
                                                $route = route('ppmrequests');
                                            }
                                            elseif($data['notification_type'] == 'kpi_report_generated')
                                            {
                                                $route = route('reports.manage_reports');
                                            }
                                            else
                                            {
                                                $route = $ociLink."/osool-bt/reports/".$data->additional_param;
                                            }
                                        }

                                        elseif($data['section_type'] == 'user' && $data['notification_sub_type'] == 'new_availability_change_request') {
                                            $notificationType = '';
                                            $route = route('users.leaverequest.list',[$this->encryptDecryptedString($data['additional_param']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                        }

                                        if(in_array($user->user_type, array('admin', 'admin_employee')) && $data['notification_sub_type'] == "wo_completed_wo"){
                                            $notificationSubType = 'wo_completed_wo_po';
                                        }

                                        else{
                                            $notificationSubType = $data['notification_sub_type'];
                                        }

                                        if($data['section_type'] == "contracts") {
                                            $route = route('data.contracts.contract-documents.list', $this->encryptDecryptedString($data['section_id']));
                                        }
                                        elseif($data['section_type'] == "advance_contracts" || $data['section_type'] == "variation_order") {
                                            $route = route('advance-contracts.view', base64_encode($data['section_id']));
                                        }
                                        elseif($data['section_type'] == "complaints") {
                                            $route = route('complaints.list');
                                        }

                                        elseif($data['notification_sub_type'] == "overdue_payment")
                                        {
                                            $route = route('data.operational-contracts.show', [$this->encryptDecryptedString($data['section_id']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                        }

                                        elseif($data['section_type'] == "bookings") {
                                            $route = route('data.tenants.appointments.list');
                                        }

                                        $notificationFormattedDatetime = $this->setDateTimeFormatByLanguage($selectedLanguage, $data->created_at);
                                    @endphp

                                    @if($data->section_type != 'report')
                                        @if(str_starts_with($data['notification_sub_type'], "marketplace_"))
                                            @php
                                                if($data['notification_sub_type'] === 'marketplace_request_received') {
                                                    $route = route('marketplace.my-requests.explore', ['id' => encrypt($data['section_id'])]);
                                                }
                                                if($data['notification_sub_type'] === 'marketplace_open_request_received') {
                                                    $route = route('marketplace.my-requests.explore', ['id' => encrypt($data['section_id'])]);
                                                }
                                                $text = ($selectedLanguage == "en") ? $data['message'] : $data['message_ar'];
                                            @endphp
                                        @elseif($data['section_type'] == "advance_contracts" || $data['section_type'] == "variation_order") 
                                            @php
                                                $text = ($selectedLanguage == "en") ? $data['message'] : $data['message_ar'];
                                            @endphp
                                        @elseif($data->section_type == "contracts" || $data->section_type == "complaints")
                                            @if($data->section_type == "contracts")
                                                @if($data->notification_sub_type ="payroll_has_been_requested")
                                                    @php
                                                        $route = route('data.contracts.contract-payroll.list', $this->encryptDecryptedString($data->section_id));
                                                    @endphp
                                                @else
                                                    @php
                                                        $route = route('data.contracts.contract-documents.list', $this->encryptDecryptedString($data->section_id));
                                                    @endphp
                                                @endif

                                            @elseif($data->section_type == "complaints")
                                                @php
                                                    $route = route('complaints.list');
                                                @endphp
                                            @endif

                                            @if($selectedLanguage == 'en')
                                                @php
                                                    $text = $data->message;
                                                @endphp
                                            @else
                                                @php
                                                    $text = $data->message_ar;
                                                @endphp
                                            @endif
                                        @elseif($data->section_type == "user" && $data->notification_sub_type == "new_availability_change_request")
                                            @if($selectedLanguage == 'en')
                                                @php
                                                    $text = $data->message;
                                                    $route = route('users.list', [$this->encryptDecryptedString($data->additional_param),'notification-read', $this->encryptDecryptedString($data->id)]);
                                                @endphp
                                            @else
                                                @php
                                                    $text = $data->message_ar;
                                                    $route = route('users.list', [$this->encryptDecryptedString($data->additional_param),'notification-read', $this->encryptDecryptedString($data->id)]);
                                                @endphp
                                            @endif
                                        @elseif($data->section_type == "bookings")
                                            @if($selectedLanguage == 'en')
                                                @php
                                                    $text = $data->message;
                                                    $route = route('data.tenants.appointments.list');
                                                @endphp
                                            @else
                                                @php
                                                    $text = $data->message_ar;
                                                    $route = route('data.tenants.appointments.list');
                                                @endphp
                                            @endif
                                        @elseif($data->section_type == "milestones")
                                            @if($selectedLanguage == 'en')
                                                @php
                                                    $text = $data->message;
                                                    $route = route('CRMProjects.list');
                                                @endphp
                                            @else
                                                @php
                                                    $text = $data->message_ar;
                                                    $route = route('data.tenants.appointments.list');
                                                @endphp
                                            @endif
                                        @elseif($data->section_type == "work_order" && $data->notification_sub_type == "pause_workorder")
                                                @php
                                                    $text = $selectedLanguage == 'en' ? $data->message : $data->message_ar;
                                                @endphp
                                        @elseif($data->notification_sub_type == "overdue_payment")
                                                @php
                                                    $text = $selectedLanguage == 'en' ? $data->message : $data->message_ar;
                                                @endphp   
                                        @else
                                            @php
                                                $text = trans('notifications.lists.'.$notificationSubType, ['done_by' => $data->additional_param, 'wo' => $notificationType]);
                                            @endphp
                                        @endif
                                    @else
                                        @if($selectedLanguage == 'en')
                                            @php
                                                $text = $data->message;
                                            @endphp
                                        @else
                                            @php
                                                $text = $data->message_ar;
                                            @endphp
                                        @endif
                                    @endif
                                    <p>
                                        <a href = "{{$route}}" @if($data->section_type == 'report') target =  "_blank" @endif class = "subject text-truncate" style = "max-width: 240px;">
                                                @if($data->notification_sub_type == "overdue_payment")
                                                @php
                                                $text = str_replace(['<a', '</a>'], ['<span', '</span>'], $text);
                                                @endphp
                                                <span>{!! html_entity_decode($text) !!}</span>
                                                @else
                                        <span>{{$text}}</span>
                                        @endif
                                        </a>
                                    </p>
                                    <p>
                                        <span class = "time-posted">{{$notificationFormattedDatetime}}</span>
                                    </p>
                                </div>
                            </li>
                        @endforeach
                        <a href = "{{ route('openNotificationsList') }}" class = "dropdown-wrapper__more text-primary">@lang('notifications.common.see_all_notifications')</a>
                    </span>
                @else
                    <li class = "nav-notification__single nav-notification__single--unread d-flex flex-wrap">
                        <div class = "nav-notification__details">
                            <div class = "PropertyListEmpty">
                                <img src = "{{asset('empty-icon/freepik_Bell_inject_94.svg')}}" class = "fourth_img mb-2">
                                <h6>@lang('general_sentence.empty_ui.No_notifications_yet')</h6>
                                <p>
                                    @lang('general_sentence.empty_ui.Your_received_notifications_will')
                                    <br>
                                    <span> @lang('general_sentence.empty_ui.appear_here')</span>
                                </p>
                            </div>
                        </div>
                    </li>
                @endif
            </ul>
        </div>
    </div>
</div>
