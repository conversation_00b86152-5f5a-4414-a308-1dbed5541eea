<div>
    @if(isset($list) && $list->count())
        <div class = "contents">
            <div class = "container-fluid">
                <div class = "row">
                    <div class = "col-lg-12">
                        <div class = "breadcrumb-main user-member justify-content-sm-between">
                            <div class = "d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                                <div class = "d-flex align-items-center justify-content-center user-member__title mr-sm-25 page-title__left ml-0">
                                    <h4 class = "text-capitalize fw-500 breadcrumb-title">
                                        <a href = "{{ url()->previous() }}">
                                            <i class = "las la-arrow-left text-dark"></i>
                                        </a>
                                        @lang('notifications.common.notifications')
                                    </h4>
                                    <span class = "sub-title ml-sm-25 pl-sm-25 work_order_count unread_notification_count_number">
                                        {{ $totalUnreadNotifications ?? 0 }}
                                        @lang('notifications.common.unreaded_notifications')
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class = "notification-list">
                <div class = "row">
                    <div class = "col-lg-3"></div>
                    <div class = "col-lg-9">
                        <div class = "d-sm-flex justify-content-sm-between align-items-center">
                            <div class = "d-flex form-group">
                                <div class ="overflow-hidden radius-6 note-section">
                                    <div class ="alert-note align-items-center alert alert-warning alert-dismissible justify-content-between pr-3 fade show" role = "alert">
                                       <h3 class = "mr-2">
                                            <i class = "las la-stopwatch fs-lg text-warning"></i>
                                        </h3>
                                       <span>@lang('notifications.common.notice')</span>
                                       <button type = "button" class = "btn close-note btn-default ml-3 p-0" data-dismiss = "alert" aria-label = "Close">
                                           <i class = "las la-times fs-lg text-warning mr-0"></i>
                                       </button>
                                       <button type = "button" class = "btn show-note btn-default ml-3 p-0" aria-label = "Close">
                                           <h3>
                                                <i class = "las la-stopwatch fs-lg text-warning mr-0"></i>
                                            </h3>
                                       </button>
                                   </div>
                               </div>
                            </div>
                            <div class = "form-group" dir = "rtl" id = "modal_confirm_update">
                                <button type = "button" id = "read_all_notification" class = "btn btn-light">
                                    @lang('notifications.common.Read_all_notification')
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class = "notification-list">
                @foreach ($list as $data)
                    <div class = "row">
                        <div class = "col-lg-2">
                            <p class = "notification_date_container text-right">
                                <span class = "notification-date" @if($selectedLanguage == 'ar') dir = "ltr" @endif>
                                    {{ $this->changeDateFormat('d F Y', $data->created_at) ?? '---' }}
                                </span>
                                <span class = "get_notification_ids">
                                    @php
                                        $readUserArr = !empty($data['is_read_by_users']) ? $this->explodeDataFromField($data['is_read_by_users']) : [];
                                    @endphp

                                    @if(!in_array($user->id, $readUserArr))
                                        <span data-notiid = "{{ $data['id'] ?? 0 }}" class = "notification_circle">
                                            <i class = "fa fa-circle" aria-hidden = "true"></i>
                                        </span>
                                    @else
                                        <span data-notiid = "{{ $data['id'] ?? 0 }}" class = "read_notification_circle">
                                            <i class = "fa fa-circle" aria-hidden = "true"></i>
                                        </span>
                                    @endif
                                </span>
                            </p>
                        </div>
                        <div class = "col-lg-9">
                            <div class = "card card-default notification_right_contentbox">
                                <div class = "card-body">
                                    <div class = "nav-notification__details d-flex">
                                        <div class = "nav-notification__type">
                                            @switch($data->notification_sub_type)
                                                @case('booking_created')
                                                    <img src = "{{asset('img/svg/booking-created.svg')}}">
                                                @break
                                                @case('kpi_report_generated')
                                                    <img src = "{{asset('img/svg/booking-created.svg')}}">
                                                @break

                                                @case('booking_completed')
                                                    <img src = "{{asset('img/svg/booking-completed.svg')}}">
                                                @break

                                                @case('booking_cancelled')
                                                    <img src = "{{asset('img/svg/booking-cancelled.svg')}}">
                                                @break

                                                @case('new_work_order_created')
                                                    <img src = "{{asset('img/svg/new-work.svg')}}">
                                                @break

                                                @case('scheduled_new_workerorder')
                                                    <img src = "{{asset('img/svg/new-work.svg')}}">
                                                @break

                                                @case('new_chat_message')
                                                    <img src = "{{asset('img/svg/chat-work.svg')}}">
                                                @break

                                                @case('bm_has_reopend_wo')
                                                    <img src = "{{asset('img/svg/repoen-work.svg')}}">
                                                @break

                                                @case('worker_assigned_by_bm')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('sp_has_did_not_agreed_on_workorder')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('sp_has_edited_target_date')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('send_wo_reminder_to_sp')
                                                    <img src = "{{asset('img/svg/clock-icon.svg')}}">
                                                @break

                                                @case('overdue_payment')
                                                    <img src = "{{asset('img/svg/clock-icon.svg')}}">
                                                @break

                                                @case('bm_has_did_not_agreed_on_workorder')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('bm_respond_to_sp_rejection')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('sp_respond_to_bm_rejection')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('sp_has_an_issue')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('new_maintenance_request')
                                                    <img src = "{{asset('img/svg/main-work.svg')}}">
                                                @break

                                                @case('bm_has_approved_and_evaluated_wo')
                                                    <img src = "{{asset('img/svg/closed-work.svg')}}">
                                                @break

                                                @case('wo_completed_wo')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('wo_paused_wo')
                                                    <img src = "{{asset('img/svg/pause-icon.svg')}}">
                                                @break

                                                @case('wo_restarted_wo')
                                                    <img src = "{{asset('img/svg/resume-icon.svg')}}">
                                                @break

                                                @case('bm_has_automatically_approved_wo')
                                                    <img src = "{{asset('img/svg/closed-work.svg')}}">
                                                @break

                                                @case('sp_has_automatically_approved_wo')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('sps_has_assigned')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('new_availability_change_request')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @case('sent_to_project_owner')
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break

                                                @default
                                                    <img src = "{{asset('img/svg/work-update.svg')}}">
                                                @break
                                            @endswitch
                                        </div>
                                        <p class = "notification_text ml-2 mb-0 d-flex align-items-center">
                                            @php
                                            // dd($data);
                                                $route = "";
                                                $notificationType = '';

                                                if($data['section_type'] == 'marketplace' && $data['notification_sub_type'] == 'marketplace_request_received') {
                                                    $notificationType = '';
                                                    $route = route('marketplace.my-requests.explore',[$this->encryptDecryptedString($data['section_id']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                }

                                                if($data['section_type'] == 'work_order' && $data['notification_sub_type'] == 'new_maintenance_request') {
                                                    $notificationType = '';
                                                    $route = route('maintenance_requests.list',[$this->encryptDecryptedString($data['section_id']),'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                }
                                                elseif($data['section_type'] == 'work_order' && $data['notification_sub_type'] == 'new_reschedule_request') {
                                                    $notificationType = '';
                                                    $route = route('workorder.show', [$this->encryptDecryptedString($data['section_id']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                    //$route = route('maintenance_requests.list',[$this->encryptDecryptedString($data['section_id']),'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                }
                                                elseif($data['section_type'] == 'user' && $data['notification_sub_type'] == 'scheduled_new_workerorder') {

                                                    $notificationType = $data['work_order_id'];
                                                    $route = route('workorder.show', [$this->encryptDecryptedString($data['section_id']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                    //$route = route('maintenance_requests.list',[$this->encryptDecryptedString($data['section_id']),'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                }
                                                elseif($data['work_order_type'] == 'reactive') {
                                                    $notificationType = $data['work_order_id'];
                                                    $route = route('workorder.show', [$this->encryptDecryptedString($data['section_id']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                }

                                                elseif($data['work_order_type'] == 'preventive') {
                                                    $notificationType = $data['work_order_id'];
                                                    $route = route('workorder.show', [$this->encryptDecryptedString($data['section_id']),'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                }

                                                elseif($data['section_type'] == 'report' ) {
                                                    $notificationType = '';
                                                    if($data['notification_type'] == 'create_ppm')
                                                    {
                                                        $route = route('ppmrequests');
                                                    }
                                                    elseif($data['notification_type'] == 'kpi_report_generated')
                                                    {
                                                        $route = route('reports.manage_reports');
                                                    }
                                                    else
                                                    {
                                                        $route = $ociLink."/osool-bt/reports/".$data->additional_param;
                                                    }
                                                }

                                                elseif($data['section_type'] == 'user' && $data['notification_sub_type'] == 'new_availability_change_request') {
                                                    $notificationType = '';
                                                    $route = route('users.leaverequest.list',[$this->encryptDecryptedString($data['additional_param']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                }

                                                if(in_array($user->user_type, array('admin', 'admin_employee')) && $data['notification_sub_type'] == "wo_completed_wo"){
                                                    $notificationSubType = 'wo_completed_wo_po';
                                                }

                                                else{
                                                    $notificationSubType = $data['notification_sub_type'];
                                                }

                                                if($data['section_type'] == "contracts") {
                                                    $route = route('data.contracts.contract-documents.list', $this->encryptDecryptedString($data['section_id']));
                                                }elseif($data['section_type'] == "advance_contracts" || $data['section_type'] == "variation_order") {
                                                      $route = route('advance-contracts.view', base64_encode($data['section_id']));
                                                }
                                                elseif($data['section_type'] == "complaints") {
                                                    $route = route('complaints.list');
                                                }
                                                elseif($data['section_type'] == "bookings") {
                                                    $route = route('data.tenants.appointments.list');
                                                }
                                                elseif($data['section_type'] == "milestones") {
                                                    $route = route('CRMProjects.list');
                                                }
                                                elseif($data['notification_sub_type'] == "overdue_payment")
                                                {
                                                    $route = route('data.operational-contracts.show', [$this->encryptDecryptedString($data['section_id']), 'notification-read', $this->encryptDecryptedString($data['id'])]);
                                                }

                                                if(str_starts_with($data['notification_sub_type'], "marketplace_")) {
                                                    if($data['notification_sub_type'] === 'marketplace_request_received') {
                                                        $route = route('marketplace.my-requests.explore', ['id' => encrypt($data['section_id'])]);
                                                    }
                                                }
                                            @endphp

                                            <a href = "{{$route}}" @if($data->section_type == 'report') target =  "_blank" @endif class = "subject stretched-link text-truncate" style = "max-width: 180px;"></a>
                                            <span>
                                                @if($data['section_type'] <> 'report')
                                                    @if(str_starts_with($data['notification_sub_type'], "marketplace_"))
                                                        {{ $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] }}
                                                    @elseif($data['section_type'] == "contracts" || $data['section_type'] == "complaints")
                                                        {{ $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] }}
                                                    @elseif($data['section_type'] == "variation_order" || $data['section_type'] == "advance_contracts")
                                                        {{ $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] }}
                                                    @elseif($data['section_type'] == "user" && $data['notification_sub_type'] == "new_availability_change_request")
                                                        {{ $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] }}
                                                    @elseif($data['section_type'] == "bookings")
                                                        {{ $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] }}
                                                    @elseif($data['section_type'] == "milestones")
                                                        {{ $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] }}
                                                    @elseif($data['section_type'] == "work_order" && $data['notification_sub_type'] == "new_reschedule_request")
                                                        {{ $selectedLanguage == "en" ? $data['message_desc'] : $data['message_desc_ar'] }}
                                                    @elseif($data['section_type'] == "work_order" && $data['notification_sub_type'] == "pause_workorder")
                                                        {{ $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] }}
                                                    @elseif($data['section_type'] == "user" && $data['notification_sub_type'] == 'scheduled_new_workerorder')
                                                        {!! '<span class="text-primary"> '.$notificationType.' </span>'!!} {!!trans('notifications.lists.'.$notificationSubType,['done_by' => $data['additional_param'],'wo'=>'<span class="text-primary"> '.$notificationType.' </span>'])!!}
                                                    @elseif($data['notification_sub_type'] == "overdue_payment")
                                                        {!! $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] !!}
                                                    @else
                                                        {!!trans('notifications.lists.'.$notificationSubType,['done_by' => $data['additional_param'],'wo'=>'<span class="text-primary"> '.$notificationType.' </span>'])!!}
                                                    @endif
                                                @else

                                                    {{ $selectedLanguage == "en" ? $data['message'] : $data['message_ar'] }}
                                                @endif
                                            </span>
                                        </p>
                                        <p class = "notification_time_container mb-0 d-flex align-items-center">
                                            <span class = "notification-date" @if($selectedLanguage == "ar") dir = "ltr" @endif>
                                                <span class = "time-posted">{{ $this->changeDateFormat('h:i A', $data->created_at) ?? '---' }}</span>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            @if($list->total() > $perPage)
                <div>
                    <div class = "col-12">
                        @if ($list->hasMorePages())
                            <div class = "text-center">
                                <center>
                                    <div wire:loading class = "text-center" wire:target = "incrementPerPage">
                                        <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                            <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                            @lang('data_beneficiary.beneficiary_button.loading')
                                        </button>
                                    </div>

                                    <button type = "button" class = "btn btn-primary" wire:click = "incrementPerPage" wire:loading.class = "hide">
                                        @lang('notifications.common.load_more_notifications')
                                    </button>
                                </center>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    @else
        <div class = "contents">
            <div class = "container-fluid">
                <div class = "row">
                    <div class = "col-lg-12">
                        <div class = "project-progree-breadcrumb">
                            <div class = "breadcrumb-main user-member justify-content-sm-between mb-xs-none">
                                <div class = "d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                                    <div class = "page-title-wraps">
                                        <div class = "page-title bm-page-title justify-content-between">
                                            <div class = "d-flex align-items-center user-member__title justify-content-center mr-sm-25 page-title__left ml-0">
                                                <h4 class = "text-capitalize fw-500 breadcrumb-title">
                                                    <a href = "{{ url()->previous() }}">
                                                        <i class = "las la-arrow-left"></i>
                                                    </a>
                                                    @lang('notifications.common.notifications')
                                                </h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class = "row">
                    <div class = "col-lg-12">
                        <div class = "row">
                            <div class = "PropertyListEmpty">
                                <img src = "{{asset('empty-icon/freepik_Bell_inject_1.svg')}}" class = "fourth_img">
                                <h4 class = "first_title">@lang('general_sentence.empty_ui.No_notifications_yet')</h4>
                                <h6 class = "second_title">@lang('general_sentence.empty_ui.Your_received_notifications_will_appear_here')</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
