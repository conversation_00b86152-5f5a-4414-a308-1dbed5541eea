<div>
    {{--  @include('livewire.common.loader') --}}




    @if (count($selectedTasks) > 0)
        <div class="card mb-3">
            <div class="card-body">
                <p>{{ count($selectedTasks) }} task(s) selected.</p>
                <div class="d-flex flex-wrap gap-10">
                    <button wire:click="openModalbulkAssigUserToTask" class="btn bg-new-primary btn-sm text-white ">
                        @lang('Assign User')
                    </button>
                    <button wire:click="openModalBulksUpdateStatus" class="btn bg-success btn-sm text-white ">
                        @lang('CRMProjects.common.update_status')
                    </button>

                </div>


            </div>
        </div>
    @endif




    <div class="table-responsive new-scrollbar crm-leads">
        <div class="d-flex gap-10 pb-4">
            @forelse ($records['stages'] ?? [] as $stage)
                <div class="col-md-3 col-sm-6 mb-md-0 mb-2 p-0">
                    <div class="card">
                        <div class="card-header header-left d-flex justify-content-between p-2 min-h-0 border-0 gap-10">
                            <h6 class="fs-14 fw-400">{{ $stage['name'] }}</h6>
                            <span
                                class="p-2 d-inline-block btn-primary btn-sm wh-30 d-flex align-items-center justify-content-center rounded-xl">{{ count($stage['tasks']) }}</span>
                        </div>

                        <div class="card-body p-2">
                            <div id="stage-{{ $stage['id'] }}" class="sortable-leads">

                                @foreach ($stage['tasks'] ?? [] as $task)
                                    @php
                                        $taskData = [
                                            'id_task' => data_get($task, 'id', '---'),
                                            'title' => data_get($task, 'title', '---'),
                                            'priority' => data_get($task, 'priority', '---'),
                                            'assign_to' => data_get($task, 'users', []),
                                            'milestone' => data_get($task, 'milestone.title', '---'),
                                            'milestoneData' => data_get($task, 'milestone', '---'),
                                            'description' => data_get($task, 'description', '---'),
                                            'start_date' => data_get($task, 'start_date', '---'),
                                            'due_date' => data_get($task, 'due_date', '---'),
                                            'comments' => data_get($task, 'comments', []),
                                            'users' => data_get($task, 'users', []),
                                            'workorder_id' =>  data_get( $task, 'workorder_id',  null ),
                                            'workorder_type' =>  data_get( $task, 'workorder_type',  null ),
                                            'property_name' =>  data_get( $task, 'property_name',  null ),
                                        ];
                                    @endphp
                                    <div data-task-id="{{ $task['id'] }}" data-stage-id="{{ $stage['id'] }}"
                                        data-task-type="{{ $task['task_type'] ?? 'Untangible' }}"
                                        class="card radius-xl p-2 shadow-lg sortable-leads">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex">
                                                @if ($task['task_type'] == 'tangible')
                                                    <input type="checkbox" disabled checked>
                                                @else
                                                    <input type="checkbox" wire:model="selectedTasks"
                                                        value="{{ $task['id'] }}">
                                                @endif

                                                <a class="ml-2"
                                                    wire:click='showUntangibleTask(@json($taskData))'
                                                    href="#" title="{{ $task['task_type'] ?? '' }}"
                                                    data-toggle="tooltip">

                                                    {{ $task['title'] }}
                                                    <i class="las la-angle-right rotate-ar-y"></i></a>
                                            </div>


                                            <div class="dropdown">
                                                <span
                                                    class="d-block rounded border wh-20 text-center  dropdown-toggle cursor-pointer"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i
                                                        class="las la-ellipsis-v"></i></span>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    <a href="javascript:void(0)" class="dropdown-item"
                                                        wire:click='showUntangibleTask(@json($taskData))'>
                                                        <span data-feather="eye"></span>
                                                        @lang('CRMProjects.common.view')
                                                    </a>
                                                    @if ($task['task_type'] == 'tangible')
                                                        <a href="javascript:void(0)" class="dropdown-item"
                                                            wire:click='editTangibleTask(@json($taskData))'><span
                                                                data-feather="edit"></span>
                                                            @lang('CRMProjects.common.edit')</a>
                                                    @else
                                                        <a href="javascript:void(0)" class="dropdown-item"
                                                            wire:click='editUntangibleTask(@json($taskData))'><span
                                                                data-feather="edit"></span>
                                                            @lang('CRMProjects.common.edit')</a>
                                                    @endif



                                                    <a class="dropdown-item"
                                                        wire:click="$emit('confirmDelete', {{ @$task['id'] }}, '{{ @$task['title'] }}', 'deleteTask')"
                                                        href="#"><span data-feather="trash-2"></span>
                                                        @lang('lead.common.delete')
                                                    </a>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-osool mt-2">
                                            <div class="d-flex justify-content-between align-items-center mt-2">
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mt-2">
                                                <span>{{ date('d M Y', strtotime($task['start_date'])) }}</span>
                                            </div>
                                            <div
                                                class="d-flex justify-content-between align-items-center border-top mx-m10 px-2 pt-1">
                                                <span>@lang('lead.forms.label.users')</span>

                                                <div class="profile-group">
                                                    @foreach ($task['users'] as $index => $user)
                                                        @if ($index < 4)
                                                            <div class="profile">
                                                                <img data-toggle="tooltip" src="{{ $user['avatar'] }}"
                                                                    title="{{ $user['name'] }}"
                                                                    alt="{{ $user['name'] }}">
                                                            </div>
                                                        @endif
                                                    @endforeach
                                                    @if (count($task['users']) > 4)
                                                        <div
                                                            class="extra d-flex align-items-center justify-content-center">
                                                            +{{ count($task['users']) - 4 }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                @include('livewire.sales.common.no-data-tr')
            @endforelse
        </div>
    </div>

    <script>
        window.addEventListener('openUntangibleTaskModal', () => {
            $('#show-untangible-task-modal').modal('show');
        });
    </script>



    @include('livewire.project.bug-report.modals.Task-Initialise-WorkOrder')
    @include('livewire.project.task-board.modals.show-untangible-task')
    @include('livewire.project.task-board.modals.edit-untangible-task')
    @include('livewire.project.task-board.modals.edit-tangible-task')
    @include('livewire.project.task-board.modals.Bulk-Actions.assignUserToTaskBulk')
    @include('livewire.project.task-board.modals.Bulk-Actions.updateTaskBulk')
</div>
@livewire('common.delete-confirm')
<script src="/js/livewire/manage-loader.js"></script>
<script src="/js/livewire/manage-tooltip.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        @foreach ($records['stages'] ?? [] as $stage)
            new Sortable(document.getElementById('stage-{{ $stage['id'] }}'), {
                group: 'shared',
                animation: 150,
                draggable: '.card',
                onEnd(evt) {
                    const taskId = evt.item.getAttribute('data-task-id');
                    const taskType = evt.item.getAttribute('data-task-type');
                    const oldStageId = evt.item.getAttribute('data-stage-id');
                    const newStageId = evt.to.getAttribute('id').replace('stage-', '');
                    const newOrder = evt.newIndex + 1;

                    @this.emit('updateTaskStatusAndOrder', newStageId, oldStageId, taskId, newOrder,
                        taskType);
                }
            });
        @endforeach
    });

    document.addEventListener("DOMContentLoaded", function() {
        window.Livewire.on('updateTaskStatusAndOrder', function() {
            showLoader();
        });

        window.Livewire.on('fetchData', function() {
            // showLoader();
        });
        window.Livewire.on('deleteTask', function() {
            showLoader();
        });
    });


    window.addEventListener('open-modal-ById', event => {
        const modalId = event.detail.modalId;
        if (modalId) {
            $('#' + modalId).modal('show');
        }
    });

    window.addEventListener('close-modal-ById', event => {
        const modalId = event.detail.modalId;
        if (modalId) {
            $('#' + modalId).modal('hide');
        }
    });
</script>
