<div wire:ignore.self class="modal fade" id="edit-tangible-task-modal" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">@lang('CRMProjects.common.edit_task')</h5>
                <button wire:ignore type="button" class="close border-0" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form wire:submit.prevent="updateTangible">
            <div class="modal-body">
             

                    <div class="row">

                        
                        <div class="form-group col-md-12">
                            <label class="col-form-label">@lang('CRMProjects.milestone')</label> <span class="text-danger">*</span>
                            <select class="form-control form-control-light" wire:model.defer="taskEdit.selected_milestone" id="task-priority">
                                <option value="">@lang('Select')</option>
                                @foreach ($milestones as $milestone)
                                                        <option value="{{ $milestone['id'] }}" 
                                    >
                                    {{ $milestone['title'] }} 
                                </option>

                            @endforeach
                            </select>
                            @error('taskEdit.selected_milestone')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
             

                        <div class="form-group col-md-12">
                            <label class="col-form-label"> @lang('Title')</label> <span class="text-danger">*</span>
                            <input type="text" class="form-control form-control-light" id="task-title"
                                wire:model.defer="taskEdit.title" disabled readonly>
                            @error('taskEdit.title')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    <div class="form-group col-md-12">
                        <label class="col-form-label">@lang('Priority')</label> <span class="text-danger">*</span>
                        <select class="form-control form-control-light" wire:model.defer="taskEdit.selected_priority" id="task-priority">
                            <option value="">@lang('Select')</option>
                            @foreach ($priorities as $key => $val)
                                <option value="{{ $key }}" 
                                    @if ($key == $taskEdit['selected_priority']) selected @endif>
                                    {{ $val }}
                                </option>
                            @endforeach
                        </select>
                        @error('taskEdit.selected_priority')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                        <div class="form-group col-md-12">
                            <div class="form-group">
                                <label for="user" class="form-label">@lang('Assign User')</label> <span
                                    class="text-danger">*</span>
                                    <div class="atbd-select">
                                                       

                                  
                              <select class="form-control" id="editAssignedUsertangible" wire:model="taskEdit.selected_assign_to" multiple>
                                @foreach ($users as $user)
                                    <option value="{{ $user['id'] }}"  >
                                        {{ $user['name'] }}
                                    </option>
                                @endforeach
                            </select>

                            </div>
                                @error('taskEdit.selected_assign_to')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
       

                        <div class="form-group col-md-6"  >
                            <label class="col-form-label"> @lang('Start Date') <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-light datepicker" 
                                wire:model.defer="taskEdit.start_date">
                                @error('taskEdit.start_date')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror

                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-form-label"> @lang('CRMProjects.due-date') <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-light datepicker" 
                                wire:model.defer="taskEdit.due_date">

                                @error('taskEdit.due_date')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group col-md-12">
                            <label class="col-form-label">@lang('Description') <span class="text-danger">*</span></label>
                            <textarea class="form-control form-control-light" id="task-description" rows="3"
                                wire:model.defer="taskEdit.description" ></textarea>
                                 @error('taskEdit.description')
                                <span class="text-danger">{{ $message }}</span>
                                 @enderror

                        </div>
                    
                    </div>
              
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn bg-hold-light text-white radius-xl">
                    @lang('Cancel')
                </button>
                <button type="submit" class="btn bg-new-primary radius-xl ml-10">
                    @lang('Update')
                </button>
            </div>
            </form>
        </div>
    </div>
</div>
  @push('scripts')
  <script>
            document.addEventListener('livewire:load', function () {
        let selectassign_to = $('#editAssignedUsertangible');
        selectassign_to.select2();
    });
    </script>
    @endpush