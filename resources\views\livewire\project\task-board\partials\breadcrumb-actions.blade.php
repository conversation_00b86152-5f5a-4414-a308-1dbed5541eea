<div>
    <div class="d-flex gap-10 breadcrumb_right_icons">
        <div class="d-flex gap-10 breadcrumb_right_icons">
            @if (Route::currentRouteName() == 'CRMProjects.task-board')
                <a href="{{ route('CRMProjects.task-board-list-view', ['id' => request()->id]) }}"
                    class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                    title="@lang('CRMProjects.show-list-view')">
                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="hamburger-menu"></i>
                </a>
            @else
                <a href="{{ route('CRMProjects.task-board', ['id' => request()->id]) }}"
                    class="btn btn-white btn-default text-center svg-20 wh-45"
                    data-toggle="tooltip" title="@lang('CRMProjects.show-card-view')">
                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="layout-3"></i>
                </a>
            @endif
            @php
                $enabledToCreateTangibleTask= $WOTasksTrait = (new class { use \App\Http\Traits\CRMProjects\WOTasksTrait; })->enableToCreateTangibleTask();
                  
            @endphp
            @if ( $enabledToCreateTangibleTask)
                
           
            <button class="btn btn-default btn-light w-100 no-wrap" type="button"
            wire:click="$emit('openChooseWOTypeMoal','{{ request()->id }}')">
            <i class="las la-plus fs-16"></i>@lang('CRMProjects.create_tangible_task')
        </button>
        @endif
            <button class="btn btn-default btn-primary w-100 no-wrap" type="button"
                wire:click="$emit('openCreateTask','{{ request()->id }}')">
                <i class="las la-plus fs-16"></i>@lang('CRMProjects.create_Untangible_task')
            </button>
        </div>
    </div>
</div>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        window.Livewire.on('openCreateTask', function() {
            showLoader();
        });
        window.Livewire.on('openChooseWOTypeMoal', function() {
            showLoader();
        });
    });
</script>
