<?php

use App\Http\Controllers\Admin\AdvanceContracts\AdvanceContractsController;
use App\Http\Controllers\Admin\AssetController;
use App\Http\Controllers\Admin\Beneficiary\BeneficiaryController;
use App\Http\Controllers\Admin\Ppm\ManagePpmController;
use App\Http\Controllers\Admin\Property\ManageUnitsController;
use App\Http\Controllers\Admin\CalendarController;
use App\Http\Controllers\Admin\Configurations\AssetNameController;
use App\Http\Controllers\Admin\Configurations\CheckListController;
use App\Http\Controllers\Admin\Configurations\GeneralSettingsController;
use App\Http\Controllers\Admin\Configurations\PriorityController;
use App\Http\Controllers\Admin\Configurations\ServiceAssetController;
use App\Http\Controllers\Admin\Configurations\ServiceTypeController;
use App\Http\Controllers\Admin\Configurations\WorkTimeController;
use App\Http\Controllers\Admin\Contacts\ManagecontactsController;
use App\Http\Controllers\Admin\Contracts\ContractPerformanceIndicatorController;
use App\Http\Controllers\Admin\Contracts\ContractsController;
use App\Http\Controllers\Admin\Contracts\SubcontractsController;
use App\Http\Controllers\Admin\OperationalContracts\OperationalContractsController;
use App\Http\Controllers\Admin\DashboardControllerNew;
use App\Http\Controllers\Admin\LanguageController;
use App\Http\Controllers\Admin\MailController;
use App\Http\Controllers\Admin\MaintenanceAdminPortal\MaintenanceAdminPortal;
use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Admin\PerformanceIndicatorController;
use App\Http\Controllers\Admin\Property\PropertyFinalController;
use App\Http\Controllers\Admin\ProviderController;
use App\Http\Controllers\Admin\PspRegistrationRequests\ManageRegistrationRequestsController;
use App\Http\Controllers\Admin\SelectProjectController;
use App\Http\Controllers\Admin\Serviceprovider\ServiceproviderController;
use App\Http\Controllers\Admin\SupportController;
use App\Http\Controllers\Admin\Tenants\TenantAdminController;
use App\Http\Controllers\Admin\User\UserControllerNew;
use App\Http\Controllers\Admin\Workorder\CreateWorkOrderController;
use App\Http\Controllers\Admin\Workorder\EditWorkOrderController;
use App\Http\Controllers\Admin\Workorder\SingleEditWorkOrder;
use App\Http\Controllers\Admin\Workorder\WorkOrderController;
use App\Http\Controllers\Admin\Workorder\WorkOrderItemsController;
use App\Http\Controllers\Admin\Workspace\ReleaseNotesController;
use App\Http\Controllers\Admin\Workspace\WorkspaceController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\SystemController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ErrorController;
use App\Http\Controllers\ExportsController;
use App\Http\Controllers\GoogleLoginController;
use App\Http\Controllers\MaintenanceWebPortal\MaintenanceWebPortal;
use App\Http\Controllers\PageController;
use App\Http\Controllers\Admin\AssetsManagementController;

use App\Http\Controllers\Admin\PurchaseAndSales\PurchaseRequest\PurchaseRequestController;
use App\Http\Controllers\Admin\PurchaseAndSales\Quotation\QuotationController;
use App\Http\Controllers\Admin\PurchaseAndSales\Quotation\CreateQuotationController;
use App\Http\Controllers\Admin\PurchaseAndSales\PurchaseRequest\CreatePurchaseRequestController;
use App\Http\Controllers\Admin\PurchaseAndSales\PurchaseRequest\EditPurchaseRequestController;
use App\Http\Controllers\Admin\PurchaseAndSales\Quotation\EditQuotationController;
use App\Http\Controllers\Admin\PurchaseAndSales\Quotation\EditBafoQuotationController;
use App\Http\Controllers\Admin\PurchaseAndSales\SalesOrder\SalesOrderController;
use App\Http\Controllers\Admin\PurchaseAndSales\SalesOrder\CreateSalesOrderController;
use App\Http\Controllers\Admin\PurchaseAndSales\SalesOrder\EditSalesOrderController;
use App\Http\Controllers\Admin\PurchaseAndSales\PurchaseOrder\PurchaseOrderController;
use App\Http\Controllers\Admin\PurchaseAndSales\PurchaseOrder\CreatePurchaseOrderController;
use App\Http\Controllers\Admin\PurchaseAndSales\PurchaseOrder\EditPurchaseOrderController;
use App\Http\Controllers\Admin\PurchaseAndSales\Bills\BillsController;
use App\Http\Controllers\Admin\PurchaseAndSales\Bills\CreateBillsController;
use App\Http\Controllers\Admin\PurchaseAndSales\Bills\EditBillsController;
use App\Http\Controllers\Admin\PurchaseAndSales\Invoice\InvoiceController;
use App\Http\Controllers\Admin\PurchaseAndSales\Invoice\CreateInvoiceController;
use App\Http\Controllers\Admin\PurchaseAndSales\Invoice\EditInvoiceController;
use App\Http\Controllers\Admin\ServiceRequestController;
use App\Http\Controllers\SocialController;
use App\Http\Controllers\TheReportController;
use App\Http\Controllers\Admin\Contracts\ContractDocumentsController;
use App\Http\Controllers\Admin\Contracts\ContractPayrollController;
use App\Http\Controllers\Admin\Contracts\ContractInspectionReportsController;
use App\Http\Controllers\Admin\Complaints\ComplaintsController;
use App\Http\Controllers\Admin\BulkImportProjectController;
use App\Http\Controllers\Admin\ConfigurationController;
use App\Http\Controllers\Admin\Beneficiary\NewBeneficiaryController;
use App\Http\Controllers\Admin\Finance\IncomeController;
use App\Http\Controllers\Admin\Finance\CustomerController;
use App\Http\Controllers\Admin\Finance\BankAccountController;
use App\Http\Controllers\Admin\Finance\ChartAccountController;
use App\Http\Controllers\Admin\Finance\TransferController;
use App\Http\Controllers\Admin\Finance\CreditNoteController;
use App\Http\Controllers\Admin\Finance\DebitNoteController;
use App\Http\Controllers\Admin\Finance\PaymentsController;
use App\Http\Controllers\Admin\Finance\BudgetPlannerController;
use App\Http\Controllers\Admin\Finance\BillController;
use App\Http\Controllers\Admin\Finance\InvoiceController as AccountingInvoiceController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\User\RoleController;
use App\Http\Controllers\Admin\User\PermissionController;
use App\Http\Controllers\Admin\Templating\TemplatingController;
use App\Http\Controllers\Admin\Workorder\PerformanceController;
use App\Http\Controllers\Admin\Workorder\NewServiceProviderController;
use App\Http\Controllers\Admin\Workorder\NewWorkOrderController;
use App\Http\Controllers\UnitReceivalPlaceController;
use App\Http\Controllers\Admin\CRM\DashboardController;
use App\Http\Controllers\Admin\MarketPlace\DetailController as MarketplaceDetailController;
use App\Http\Controllers\Admin\MarketPlace\WorkOrderController as MarketPlaceWorkOrderController;
use App\Http\Controllers\Admin\Project\BugReportController;
use App\Http\Controllers\Admin\Project\TaskBoardController;
use App\Http\Controllers\Admin\Sale\CallController;
use App\Http\Controllers\Admin\Sale\CaseController;
use App\Http\Controllers\Admin\Sale\ContactController as SaleContactController;
use App\Http\Controllers\Admin\Sale\DocumentController;
use App\Http\Controllers\Admin\Sale\InvoiceController as SaleInvoiceController;
use App\Http\Controllers\Admin\Sale\MeetingController;
use App\Http\Controllers\Admin\Sale\OpportunityController;
use App\Http\Controllers\Admin\Sale\OrderController;
use App\Http\Controllers\Admin\Sale\QuoteController;
use App\Http\Controllers\Admin\Sale\ReportController;
use App\Http\Controllers\Admin\Sale\SalesAccountsController;
use App\Http\Controllers\Admin\Sale\StreamController;
use App\Http\Controllers\Admin\Sale\SystemSetupController;
use App\Http\Controllers\CRM\LabelsController;
use App\Http\Controllers\CRM\PipelineController;
use App\Http\Controllers\CRM\PipelineTagsController;
use App\Http\Controllers\CRM\SourcesController;
use Barryvdh\Snappy\Facades\SnappyPdf;
use App\Http\Controllers\Admin\Project\SystemSetupController as ProjectSystemSetupController;
use App\Http\Controllers\CRMProjects\ProjectController;
use App\Http\Controllers\CRMProjects\ProjectTemplateController;
use App\Http\Controllers\CRMProjects\GantChartController;
use App\Http\Controllers\SuperAdmin\Nhc\ManageOrderController;
use App\Http\Controllers\SuperAdmin\Nhc\ManagePackageController;
use App\Http\Controllers\SuperAdmin\Nhc\PaymentController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Request;

Route::get('/run-artisan', function () {
    $command = Request::get('cmd'); // e.g., /run-artisan?cmd=cache:clear

    if (!$command) {
        return 'No command provided.';
    }

    try {
        Artisan::call($command);
        return Response::make(nl2br(Artisan::output()));
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
});

Route::get('/nhc-integration/ss', function(){
    return view('applications.admin.nhc-integration.subscription_packages_FE');
});

Route::get('/testmypdf', function () {
    $html = view('testpdf')->render();

    $pdf = SnappyPdf::loadHTML($html)
                    ->setOption('margin-top', '0mm')
                    ->setOption('margin-bottom', '0mm')
                    ->setOption('margin-left', '0mm')
                    ->setOption('margin-right', '0mm')
                    ->setOption('enable-local-file-access', true)
                    ->setOption('enable-javascript', true)
                    ->setOption('print-media-type',  true)
        ->setOption('javascript-delay', value: 5000) // Wait for the charts to render
        ->setOption('no-stop-slow-scripts', true)
        ->setOption('enable-smart-shrinking', true)
        ->setOption('viewport-size', '1280x1024') // Ensures charts render properly
                    ->setOption('encoding', 'UTF-8'); // Ensures proper Arabic rendering

    return $pdf->inline('test-arabic.pdf'); // Display in browser
});


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!|
 */

/**
 * Will be redirected to the Webiste.. Opens pages related to front end website
 */
Route::get('linkofQuotation{id?}', [QuotationController::class,'Link'])->name('Link');
route::get('/', [PageController::class, 'redirectToHome'])->name('home');
route::get('/learnmore', [PageController::class, 'learnMore'])->name('home.learnmore');
route::get('/privacy-policy/{lang?}', [PageController::class, 'privacypolicy'])->name('home.privacypolicy');

route::get('/welcome', [PageController::class, 'tenantHome'])->name('home.tenant');//using for send reminder sms to tenant
route::get('/reload-captcha', [PageController::class, 'reloadCaptcha'])->name('home.reloadcaptcha');

Route::get('temporary-password-reset', function() {
    $user = App\Models\User::where('email', '<EMAIL>')->first();
    $user->password = Hash::make('123456');
    $user->save();
    return 'Success!';
});

require __DIR__ . '/partials/psp-registration.php';


/*
|--------------------------------------------------------------------------
| Frontend Previewer: just running in dev ||local environment
|--------------------------------------------------------------------------
 */

if (in_array(config('app.env'), ['dev', 'local'])) {
    require __DIR__ . '/partials/frontend-previewer.php';
}


/*
|--------------------------------------------------------------------------
| Maintenance Web Portal
|--------------------------------------------------------------------------
|
| Users can raise a request from the webportal. It will be recieved by the BM(Building Manager)
| BM will create a Workorder
 */
Route::group(['prefix'=>'maintenance','as'=>'maintenance.'],function(){
    Route::get('/need-help/{id}', [MaintenanceWebPortal::class, 'taskNeedHelp'])->name('need.help');
    Route::match(array('GET','POST'), '/create-task', [MaintenanceWebPortal::class, 'createTask'])->name('create.task');
    Route::get('/task-sent-confirmation', [MaintenanceWebPortal::class, 'confirmationTask'])->name('confirmation.task');
    Route::get('/task-details/{id}', [MaintenanceWebPortal::class, 'taskDetails'])->name('task.details');
    Route::post('/task-feedback', [MaintenanceWebPortal::class, 'taskFeedback'])->name('task.feedback');
    Route::get('/task-feeback-completed', [MaintenanceWebPortal::class, 'feedbackTask'])->name('feedback.task');
    Route::get('ajax-get-room-data/{id?}',[MaintenanceWebPortal::class, 'AjaxRoomDataGet'])->name('ajax-get-room-data');
    Route::post('/skip', [MaintenanceWebPortal::class, 'skip'])->name('skip');
});

/**
 * Will be used to save the contact details submitted by the Visitors
 */
route::post('/contact-save', [ContactController::class, 'contactSave'])->name('contact.save');
route::post('/package-save', [ContactController::class, 'packageSave'])->name('package.save');

/*
|--------------------------------------------------------------------------
| Notifications
|--------------------------------------------------------------------------
|
| These Routes should be grouped
 */
//Route::get('/notifications-list', [NotificationController::class, 'index'])->name('notificationlist');
Route::get('/notifications-list-ajax', [NotificationController::class, 'index'])->name('notificationlist.ajax');
route::get('/platform.notifications', [NotificationController::class, 'get_nodification_details'])->name('platform.notifications');
route::post('/platform.notifications.update', [NotificationController::class, 'update_nodifications'])->name('platform.notifications.update');
route::get('/platform.notifications.readall', [NotificationController::class, 'readall_notifications'])->name('platform.notifications.readall');
Route::get('/notifications-list', [NotificationController::class, 'openNotificationsList'])->name('openNotificationsList');
/**
 * Mail routig system
 */
Route::get('send-mail', [MailController::class, 'RegiserSuccess'])->name('register-success');
Route::get('/language/{locale}', [LanguageController::class, 'changeLanguage'])->name('changeLanguage');

/**
 * Without Auth, the below routes are used for user login, Password Resets, Verifying Emails and testing the emails. These routes should be grouped. And the Controller name should be updated to UserController
 */
Route::get('/test_mail', [UserControllerNew::class, 'test_mail'])->name('test_mail');
Route::get('/reset_password/{user_id}', [UserControllerNew::class, 'reset_password'])->name('reset_password');
Route::get('/password/reset/{token?}', [ForgotPasswordController::class, 'showResetForm'])->name('password.reset');
Route::any('/password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.reset_request');
Route::post('/update_reset_password', [UserControllerNew::class, 'update_reset_password'])->name('update_reset_password');
Route::get('/login', [LoginController::class, 'loginForm']);
Route::get('verify-email/{lang}/{email}', [UserControllerNew::class, 'verify_email'])->name('email.verify');


/**
 * After the authentication
 */
Route::group(['middleware' => 'auth'], function () {

    Route::get('/check-crm-workspace', function(){
        return auth()->user()->workspace;
    });
    Route::group(['prefix' => 'subscribers', 'as' => 'subscribers.'], function () {
        Route::get('received-service-orders', [ManageOrderController::class, 'index'])->name('manage-orders');
        Route::get('subscriptions', [ManagePackageController::class, 'index'])->name('manage-packages');
        Route::get('subscriptions/create', [ManagePackageController::class, 'createSubscription'])->name('create-subscription');
        Route::get('subscriptions/edit/{id}', [ManagePackageController::class, 'editSubscription'])->name('edit-subscription');
        Route::get('payments', [PaymentController::class, 'index'])->name('payments');
    });

    //This route is used for Super admin and osool admin to switch between the projects
    Route::post('userSwitch/{user_id?}', [SystemController::class, 'AuthUserSwitch'])->name('admin.user-switch');
    Route::get('user-switch-return/{user_id?}', [SystemController::class, 'AuthUserSwitchReturn'])->name('admin.return_user');
    //The reports Module Routes
    Route::group(['prefix'=>'reports','as'=>'reports.'],function(){
        Route::get('/create', [TheReportController::class, 'createReport'])->name('create');
        Route::get('/manage_reports', [TheReportController::class, 'manage_reports'])->name('manage_reports');
        Route::get('/delete-report/{id}', [TheReportController::class, 'deleteReport'])->name('delete.report');
        Route::get('/testPdf', [TheReportController::class, 'testPdf'])->name('testPdf');

        Route::post('/create', [TheReportController::class, 'exportCsv'])->name('exportCsv');
        Route::get('/downloadfile/{file_name}/{rpt_no}', [TheReportController::class, 'downloadfile'])->name('downloadfile');
        Route::post('/validateCount', [TheReportController::class, 'validateCount'])->name('validateCount');
        Route::get('/ajax/get_building_list_report/{id?}/{project_ids?}', [TheReportController::class, 'get_building_list'])->name('ajax-building-list-report');

        Route::post('/ajax/getBmSpsFromBuildingIds', [TheReportController::class, 'getBmSpsFromBuildingIds'])->name('getBmSpsFromBuildingIds');


    });

    //The Exports Module Routes
    Route::group(['prefix'=>'export','as'=>'export.'],function(){
        Route::post('/initiateExport', [ExportsController::class, 'store'])->name('initiateExport');
        Route::post('/initiateExportQR', [ExportsController::class, 'initiateExportQR'])->name('initiateExportQR');
        Route::post('/initiateExport', [ExportsController::class, 'store'])->name('initiateExport');
        Route::post('/initiateExportMr', [ExportsController::class, 'initiateExportMr'])->name('initiateExportMr');
        Route::post('/getPropertiesList', [ExportsController::class, 'getPropertiesList'])->name('getPropertiesList');
        Route::get('/manage_exports',  [ExportsController::class, 'index'])->name('manage_exports');
        Route::get('/test_exports',  [ExportsController::class, 'show'])->name('test_exports');
        Route::get('/delete-export/{id}', [ExportsController::class, 'destroy'])->name('delete.export');
        Route::get('/downloadfile/{file_name}/{export_no}', [TheReportController::class, 'downloadfile'])->name('downloadfile');

    });
    //This route is for POA and other users
    Route::group(['namespace'=>'Admin'],function(){
        //This route is for workspace
        Route::group(['prefix'=>'workspace','as'=>'workspace.','namespace'=>'Workspace'],function(){
            Route::get('/', [WorkspaceController::class, 'index'])->name('home');
            Route::get('adminListAjax/{id?}', [WorkspaceController::class, 'adminListAjax'])->name('list.ajax');
            /**Manage Admin*/
            Route::post('validate', [WorkspaceController::class, 'validateUser'])->name('admin.validate');
            Route::get('privileges', [WorkspaceController::class, 'showPrivileges'])->name('admin.privileges');
            Route::get('/create-admin',[WorkspaceController::class, 'create'])->name('admin.create');
            Route::post('/post-create-admin',[WorkspaceController::class, 'store'])->name('admin.store');
            Route::get('/edit-admin/{id}',[WorkspaceController::class, 'edit'])->name('admin.edit');
            Route::post('validate/edit', [WorkspaceController::class, 'validateUserEdit'])->name('admin.validate.edit');
            Route::get('privileges/edit', [WorkspaceController::class, 'editPrivileges'])->name('admin.privileges.edit');
            Route::post('update-admin', [WorkspaceController::class, 'update'])->name('admin.update');
            Route::get('/edit-admin-confirmations-os/{id}',[WorkspaceController::class, 'editConfirmation'])->name('admin.edit.confirmations.os');
            Route::get('/projects', [WorkspaceController::class, 'project'])->name('project');
            Route::get('adminProjectsListAjax/{id?}', [WorkspaceController::class, 'adminProjectListAjax'])->name('list_projects.ajax');
            Route::get('/enter/{id}',[WorkspaceController::class, 'selectproject'])->name('enter');
            Route::get('/create-project',[WorkspaceController::class, 'createproject'])->name('admin.createproject');
            Route::get('/admins-assignment/{id?}',[WorkspaceController::class, 'viewAdminsAssignmentsPage'])->name('project.manager_admins_assignment');
            Route::post('/save-project/{id?}',[WorkspaceController::class, 'saveProject'])->name('save-project');
            Route::get('/edit-project/{id}',[WorkspaceController::class, 'projectedit'])->name('project.edit');
            Route::match(array('GET','POST'),'/update/{id}',[WorkspaceController::class, 'projectupdate'])->name('project.update');
            Route::get('/delete-project/{id}',[WorkspaceController::class, 'deleteProject'])->name('delete.project');

            // Routes to Manage the release notes
            Route::group(['prefix' => 'manage-releases'], function () {
                Route::get('/', [ReleaseNotesController::class, 'index'])->name('manage-releases.list');
                Route::get('create/{page}', [ReleaseNotesController::class, 'create'])->name('manage-releases.group-create');
                Route::post('validate', [ReleaseNotesController::class, 'validateNote'])->name('manage-releases.validate');
                Route::post('validate-edit', [ReleaseNotesController::class, 'validateNoteEdit'])->name('manage-releases.validate-edit');
                Route::get('/view-version-details/{version}', [ReleaseNotesController::class, 'view'])->name('manage-releases.view-version-details');
                Route::get('confirm/{group_id}', [ReleaseNotesController::class, 'confirm'])->name('manage-releases.confirm');
                Route::post('store', [ReleaseNotesController::class, 'store'])->name('manage-releases.store');
                Route::get('/create',[ReleaseNotesController::class, 'create'])->name('manage-releases.create');
                Route::post('/add-releasenote-step', [ReleaseNotesController::class, 'addreleasenoteStep'])->name('manage-releases.add_releasenote_step');
                Route::get('/remove-releasenote-step', [ReleaseNotesController::class, 'removeReleaseNoteStep'])->name('manage-releases.remove_releasenote_step');
                Route::get('/edit-version-details/{version}', [ReleaseNotesController::class, 'editVersionDetails'])->name('manage-releases.edit-version-details');
                Route::post('/delete-note', [ReleaseNotesController::class, 'deleteNote'])->name('manage-releases.delete-note');
                Route::post('/delete-version', [ReleaseNotesController::class, 'deleteVersion'])->name('manage-releases.delete-version');
                Route::get('/edit-note-details/{id}', [ReleaseNotesController::class, 'editNoteDetails'])->name('manage-releases.edit-note');
                Route::get('view/{version}', [ReleaseNotesController::class, 'view'])->name('manage-releases.view');
                Route::get('view', [ReleaseNotesController::class, 'view_all'])->name('manage-releases.view_all');
                Route::post('/store-release-notes-seen', [ReleaseNotesController::class, 'storeReleaseNotesSeen'])->name('manage-releases.store.release.notes.seen');
            });

        });


        /*
        |--------------------------------------------------------------------------
        | Assets Module
        |--------------------------------------------------------------------------
        |
        | Note: The assets module routes should be included in the configuration Module routes
        */
        Route::group(['prefix'=>'asset','as'=>'asset.'],function(){
            Route::post('/ajax/ajax_check_projectname_unique/{id?}', [AssetController::class, 'ajax_check_projectname_unique'])->name('ajax_check_projectname_unique');

            //Email Triggers
            Route::get('/comminucation', [AssetController::class, 'comminucation'])->name('comminucation');
            Route::post('/comminucation-emailtriggercreate', [AssetController::class, 'emailTriggerCreate'])->name('comminucation-emailtriggercreate');
            Route::post('/comminucation-emailtriggerupdate', [AssetController::class, 'emailTriggerUpdate'])->name('comminucation-emailtriggerupdate');
            Route::post('/comminucation-emailtriggerdelete', [AssetController::class, 'emailTriggerDelete'])->name('comminucation-emailtriggerdelete');

            //Email Escalations
            Route::post('/comminucation-emailescalationcreate', [AssetController::class, 'emailEscalationCreate'])->name('comminucation-emailescalationcreate');
            Route::post('/comminucation-emailescalationupdate', [AssetController::class, 'emailEscalationUpdate'])->name('comminucation-emailescalationupdate');
            Route::post('/comminucation-emailescalationdelete', [AssetController::class, 'emailEscalationDelete'])->name('comminucation-emailescalationdelete');

            Route::post('/comminucation-get-usertypes', [AssetController::class, 'comminucation_get_usertypes'])->name('comminucation-get-usertypes');
            Route::get('/get_asset_name_edit_data', [AssetController::class, 'get_asset_name_edit_data'])->name('get_asset_name_edit_data');

            Route::post('/asset-softdelete', [AssetController::class, 'AssetSoftdelete'])->name('asset-softdelete');
            Route::post('/get-building-manager-employees', [AssetController::class, 'getBuildingManagerEmployees'])->name('get-building-manager-employees');
            Route::post('/get-supervisors', [AssetController::class, 'getSupervisors'])->name('get-supervisors');

            //Asset frequency creation and edition
            Route::get('/frequency', [AssetController::class, 'frequency'])->name('frequency');
            Route::post('/frequency-create', [AssetController::class, 'FrequencyCreate'])->name('frequency-create');
            Route::post('/frequency-softdelete', [AssetController::class, 'FrequencySoftdelete'])->name('frequency-softdelete');

            //Asset priority creation and edition
            Route::get('/priority', [AssetController::class, 'priority'])->name('priority');
            Route::post('/priority-create', [AssetController::class, 'prioritytCreate'])->name('priority-create');
            Route::post('/priority-softdelete', [AssetController::class, 'prioritySoftdelete'])->name('priority-softdelete');

            //Room Types creation and edition
            Route::get('/roomtype', [AssetController::class, 'roomtype'])->name('roomtype');
            Route::post('/roomtype-create', [AssetController::class, 'roomtypeCreate'])->name('roomtype-create');
            Route::post('/unitreceivalplace-create', [UnitReceivalPlaceController::class, 'unitReceivalPlaceStore'])->name('unitreceivalplace-create');
            // Route::resource('/unitreceivalplace', UnitReceivalPlaceController::class);
            Route::post('/unitreceivalplace-softdelete', [UnitReceivalPlaceController::class, 'unitreceivalplaceSoftdelete'])->name('unitreceivalplace-softdelete');
            Route::post('/unitreceivalplace-changeorder', [UnitReceivalPlaceController::class, 'unitreceivalplaceChangeorder'])->name('unitreceivalplace-changeorder');
            Route::post('/roomtype-softdelete', [AssetController::class, 'roomtypeSoftdelete'])->name('roomtype-softdelete');
            Route::post('/roomtype-changeorder', [AssetController::class, 'roomtypeChangeorder'])->name('roomtype-changeorder');

            /*
            |--------------------------------------------------------------------------
            | Configurations Module
            |--------------------------------------------------------------------------
            |
            | Note: The above assets module should be included in the configuration Module
            */
            Route::group(['namespace'=>'Configurations'],function(){
                //Checklists Module Routes
                Route::group(['prefix'=>'checklist','as'=>'checklist.'],function(){
                    Route::get('/add-check-list', [CheckListController::class, 'addNewTask'])->name('add-check-list');
                    Route::post('/addCreate-check-list', [CheckListController::class, 'CreateCheckList'])->name('addCreate-check-list');
                    Route::post('/add-check-list-softdelete', [CheckListController::class, 'addcheckListSoftdelete'])->name('add-check-list-softdelete');
                    Route::post('/region-city', [CheckListController::class, 'regionCity'])->name('region-city');
                    Route::post('/asset-name', [CheckListController::class, 'assetName'])->name('asset-name');
                    Route::post('/create-check-list-item', [CheckListController::class, 'CreateNewTask'])->name('create-check-list-item');
                    Route::get('/add-check-information', [CheckListController::class, 'checkListInformation'])->name('add-check-information');
                    Route::get('/edit-check-information/{id?}', [CheckListController::class, 'editCheckListInformation'])->name('edit-check-information');
                    Route::post('/property-name', [CheckListController::class, 'propertyName'])->name('property-name');
                    Route::post('/add-checktask-list-softdelete', [CheckListController::class, 'addChecktaskSoftdelete'])->name('add-checktask-list-softdelete');
                    Route::get('/add-check-list-item/{id}', [CheckListController::class, 'addChecklist'])->name('add-check-list-item');
                    Route::post('/addCreate-check-list-item', [CheckListController::class, 'addCreateChecklist'])->name('addCreate-check-list-item');
                    Route::get('/check-list-confirmation', [CheckListController::class, 'checkListConfirmation'])->name('check-list-confirmation');
                    Route::get('/check-list-complete', [CheckListController::class, 'checkListComplete'])->name('check-list-complete');
                    Route::post('/save-session-subtaskaction', [CheckListController::class, 'SaveSessionCreateSubCheckactionlist'])->name('save-session-subtaskaction');
                    Route::post('/check-session-subtaskaction', [CheckListController::class, 'CheckSessionCreateSubCheckactionlist'])->name('check-session-subtaskaction');
                    Route::post('/edit-session-subtaskaction', [CheckListController::class, 'EditSessionCreateSubCheckactionlist'])->name('edit-session-subtaskaction');
                    Route::get('/view-checklist-details/{id}', [CheckListController::class, 'view_checklist_details'])->name('view-checklist-details');
                });

                //work time frame module routes
                Route::group(['prefix'=>'work-time','as'=>'worktime.'],function(){
                    Route::get('/', [WorkTimeController::class, 'workTime'])->name('work-time-add');
                    Route::post('/work-time-Create', [WorkTimeController::class, 'workTimeCreate'])->name('work-time-Create');
                });

                //system config module routes
                Route::group(['prefix'=>'system','as'=>'system.'],function(){
                    Route::get('/', [SystemController::class, 'index'])->name('view');
                    Route::post('/store', [SystemController::class, 'store'])->name('store');
                });
            });

            //Unit QR Settings update
            Route::group(['prefix'=>'unit_qr_settings','as'=>'unit_qr_settings.'],function(){
                Route::post('/update', [AssetController::class, 'update_unit_qr_settings'])->name('update');
            });
        });

        /*
        |--------------------------------------------------------------------------
        | Configuration Module
        |--------------------------------------------------------------------------
        */
        Route::group(['prefix' => 'configuration','as'=>'configuration.'], function () {
            // Routes for General settings module
            Route::group(['prefix' => 'general-settings','as'=>'general_settings.'], function () {
                // General Settings Index
                Route::get('/', [GeneralSettingsController::class, 'index'])->name('index');
                // Update General Settings
                Route::post('/update', [GeneralSettingsController::class, 'update'])->name('update');
            });

            // Routes for Service Assets module
            Route::group(['prefix' => 'service-assets','as'=>'service_assets.'], function () {
                // List the asset categories and Asset Names
                Route::get('/category', [ServiceAssetController::class, 'list'])->name('category');
                // Store a new asset
            });

            // Routes for Service Assets module
            Route::group(['prefix' => 'service-types','as'=>'service_types.'], function () {
                // Store a new service type
                Route::post('/store', [ServiceTypeController::class, 'store'])->name('store');
                // Update an existing service type
                Route::post('/update/{id}', [ServiceTypeController::class, 'update'])->name('update');
                // Delete the service type
                Route::post('/delete/{id}', [ServiceTypeController::class, 'delete'])->name('delete');
            });

            // Routes for Service Assets module
            Route::group(['prefix' => 'asset-names','as'=>'asset_names.'], function () {
                // Store a new asset name
                Route::post('/store', [AssetNameController::class, 'store'])->name('store');
                // Update an existing asset name
                Route::put('/update/{id}', [AssetNameController::class, 'update'])->name('update');
            });

            // Routes for Priorities module
            Route::group(['prefix' => 'priorities','as'=>'priorities.'], function () {
                // Get Priorites along the deleted priority if its selected before deletion
                Route::get('/retrieve-priorities-including-deleted/{priority_id}', [PriorityController::class, 'retrievePrioritiesIncludingDeleted'])->name('retrieve_priorities_including_deleted');
            });


        });

        Route::name('marketplace.')->prefix('marketplace')->group(function() {
            Route::get('/', [\App\Http\Controllers\Admin\MarketPlace\ServiceProvider\ServiceProviderController::class, 'index'])->name('providers-list');
            Route::get('/detail/{id}', [MarketplaceDetailController::class, 'index'])->name('provider-details');
            Route::get('/my-requests', [MarketplaceDetailController::class, 'myRequests'])->name('my-requests');
            Route::get('/my-requests/explore/{id}', [MarketplaceDetailController::class, 'myRequestExplore'])->name('my-requests.explore');
            Route::get('/open-offers', [MarketplaceDetailController::class, 'openOffers'])->name('open-offers');
            Route::get('/direct-offers', [MarketplaceDetailController::class, 'directOffers'])->name('direct-offers');
            Route::get('/work-orders', [MarketPlaceWorkOrderController::class, 'index'])->name('work-orders');
            Route::get('/work-orders/{id}', [MarketPlaceWorkOrderController::class, 'show'])->name('work-orders.view');
        });

      /*   Route::name('projects.')->prefix('projects')->group(function() {

        }); */

        //This route is for User Management.
        Route::group(['prefix'=>'user','as'=>'users.','namespace'=>'User'],function(){
            Route::get('/resend_email_varification',  [UserControllerNew::class, 'resend_email_varification'])->name('resend_email_varification');
            Route::get('/',  [UserControllerNew::class, 'profile'])->name('profile');
            Route::post('/update-profile', [UserControllerNew::class, 'update_profile'])->name('profile.update');
            Route::post('/update-profile-contact', [UserControllerNew::class, 'update_profile_contact'])->name('profile.update2');
            Route::post('/update-profile-password', [UserControllerNew::class, 'update_profile_password'])->name('profile.update3');
            Route::post('/ajax/ajax_check_useroldpw/{id?}',  [UserControllerNew::class, 'ajax_check_useroldpw'])->name('ajax_check_useroldpw');
            Route::post('/update-profile-img', [UserControllerNew::class, 'update_img'])->name('profile.update_img');
            Route::get('/users-list/{availability_request_id?}/{notification_read?}/{notification_id?}', [UserControllerNew::class, 'list'])->name('list');
            Route::get('/users-group', [UserControllerNew::class, 'group'])->name('group');
            Route::get('/pspa-list', [UserControllerNew::class, 'pspa'])->name('pspa');
            Route::get('/user-activation', [UserControllerNew::class, 'user_activation'])->name('status.active_inactive');
            Route::post('/user-show-hide-wo-columns', [UserControllerNew::class, 'show_hide_wo_columns'])->name('show_hide_wo_columns');

            Route::get('/pspa-users-list/{id}', [UserControllerNew::class, 'pspa_users_list'])->name('pspa_users_list');
            Route::get('/selected_assets', [UserControllerNew::class, 'selected_assets'])->name('selected_assets');
            Route::get('/pspa-users-list/edit_ps/{service_provider?}/{id}', [UserControllerNew::class, 'editPSPUsers'])->name('sp_edit_user');
            Route::get('/pspa-users-list-ajax/{id?}', [UserControllerNew::class, 'pspa_user_list_ajax'])->name('pspa_user_list_ajax');
            Route::get('userPSPAListAjax/{id?}',  [UserControllerNew::class, 'userPSPAListAjax'])->name('pspalist.ajax');
              /****************for user listing response***************/
            Route::get('/worker-list/{availability_request_id?}/{notification_read?}/{notification_id?}', [UserControllerNew::class, 'getWorkerlist'])->name('workerlist');
            Route::get('/leave-request-list/{availability_request_id?}/{notification_read?}/{notification_id?}', [UserControllerNew::class, 'getLeaveRequestList'])->name('leaverequest.list');
            Route::get('userListAjax/{id?}',  [UserControllerNew::class, 'userListAjax'])->name('list.ajax');
            Route::get('workerListAjax/{id?}',  [UserControllerNew::class, 'workerListAjax'])->name('workerlist.ajax');
            Route::get('fetchLeaveRequestListAjax/{id?}',  [UserControllerNew::class, 'fetchLeaveRequestListAjax'])->name('leaverequestlist.ajax');
            Route::get('/attendance-list', [UserControllerNew::class, 'getAttendanceList'])->name('attendancelist');
            Route::get('attendanceListAjax/{id?}',  [UserControllerNew::class, 'fetchAttendanceListAjax'])->name('attendancelist.ajax');
            Route::get('attendanceHistoryAjax/{id?}',  [UserControllerNew::class, 'fetchAttendanceHistoryAjax'])->name('attendancehistorylist.ajax');
            Route::post('/fetch-attendance-details', [UserControllerNew::class, 'fetchAttendanceDetails'])->name('fetch-attendance-details');
            Route::post('/clock-out-by-sp', [UserControllerNew::class, 'clockOutBySp'])->name('clockoutbysp');
            Route::post('/export-attendance', [UserControllerNew::class, 'fetchAttendanceExportData'])->name('export-attendance');
            /**user registration proccess*/
            Route::get('/add-user-info',  [UserControllerNew::class, 'createInfo'])->name('create.info');
            Route::match(array('GET','POST'),'/add-user-role', [UserControllerNew::class, 'createRole'])->name('create.role');
            Route::match(array('GET','POST'),'/add-user-privileges', [UserControllerNew::class, 'createPrivileges'])->name('create.privileges');
            Route::match(array('GET','POST'),'/add-user-confirmation', [UserControllerNew::class, 'createConfirmation'])->name('create.confirmation');
            Route::match(array('GET','POST'),'/create_worker_password', [UserControllerNew::class, 'create_worker_password'])->name('create.create_worker_password');
            Route::get('/delete-user/{id}', [UserControllerNew::class, 'deleteUser'])->name('delete.user');
            Route::post('/delete-user-sp-admin', [UserControllerNew::class, 'deleteUserSpAdmin'])->name('delete.user-sp-admin');
            Route::get('{id}/edit', [UserControllerNew::class, 'editUsers'])->name('edit.info');
            Route::get('{id}/spedit', [UserControllerNew::class, 'editUsersSP'])->name('edit.info.sp');
            Route::match(array('GET','POST'),'{id}/edit/role', [UserControllerNew::class, 'editUserRole'])->name('edit.role');
            Route::match(array('GET','POST'),'{id}/edit/privileges', [UserControllerNew::class, 'editUserPrivileges'])->name('edit.privileges');
            Route::match(array('GET','POST'),'{id}/edit/confirm', [UserControllerNew::class, 'editUserConfirm'])->name('edit.confirm');
            Route::match(array('GET','POST'),'{id}/edit/edit_worker_password', [UserControllerNew::class, 'edit_worker_password'])->name('edit.edit_worker_password');

            /************added for unique checking ********************/
            Route::post('/ajax/ajax_check_unique_useremail/{id?}',  [UserControllerNew::class, 'ajax_check_unique_useremail'])->name('ajax_check_unique_useremail');
            Route::post('/ajax/ajax_check_building_deleted/{id?}',  [UserControllerNew::class, 'ajax_check_building_deleted'])->name('ajax_check_building_deleted');
            Route::post('/ajax/ajax_check_unique_usernumber/{id?}',  [UserControllerNew::class, 'ajax_check_unique_usernumber'])->name('ajax_check_unique_usernumber');
            Route::post('/ajax/ajax_check_unique_emp_id/{id?}',  [UserControllerNew::class, 'ajaxCheckUniqueEmpId'])->name('ajax_check_unique_emp_id');
            Route::get('/ajax/ajax_check_unique_useremail_edit/{id?}',  [UserControllerNew::class, 'ajax_check_unique_useremail_edit'])->name('ajax_check_unique_useremail_edit');
            Route::get('/ajax/ajax_check_unique_usernumber_edit/{id?}',  [UserControllerNew::class, 'ajax_check_unique_usernumber_edit'])->name('ajax_check_unique_usernumber_edit');
            Route::post('/ajax/ajax_check_userphone_unique_edit/{id?}',  [UserControllerNew::class, 'ajax_check_userphone_unique_edit'])->name('ajax_check_userphone_unique_edit');
            Route::get('/ajax/get_sp_admins_list/{user_type_val?}',  [UserControllerNew::class, 'ajaxGetSpAdminsList'])->name('ajax-spadmin-list');
            Route::post('/ajax/get_sp_admins_list_by_id',  [UserControllerNew::class, 'ajaxGetSpAdminsListById'])->name('ajax-spadmin-list-by-id');
            Route::get('/ajax/get_city_list/{id?}',  [UserControllerNew::class, 'ajaxGetCityList'])->name('ajax-city-list');
            Route::get('/ajax/get_city_list_region/{id?}',  [UserControllerNew::class, 'ajaxGetCityListRegionsId'])->name('ajax-city-list-region');
            Route::get('/ajax/get_building_list_region/{id?}',  [UserControllerNew::class, 'ajaxGetBuildingListRegionsId'])->name('ajax-building-list-region');
            Route::get('/ajax/get_categories_list/{id?}',  [UserControllerNew::class, 'ajaxGetCategoriesList'])->name('ajax-categories-list-contacts');
            Route::get('/ajax/assigned_worker_list/{id?}',  [UserControllerNew::class, 'fetchAssignedWorkers'])->name('ajax-assigned-worker-list');
            Route::get('/ajax/get_sp_list/{id?}',  [UserControllerNew::class, 'ajaxGetSPList'])->name('ajax-sp-list');
            Route::get('user_type_filter',  [UserControllerNew::class, 'user_type_filter'])->name('user_type_filter');
            Route::get('/ajax/get_userrole_list/{id?}',  [UserControllerNew::class, 'get_userrole_list'])->name('get_userrole_list');
            Route::post('/populate_by_contract', [UserControllerNew::class, 'populate_by_contract'])->name('populate_by_contract');
            Route::post('/create-availability-request', [UserControllerNew::class, 'createAvailabilityRequest'])->name('create-availability-request');
            Route::post('/fetch-availability-request-details', [UserControllerNew::class, 'fetchAvailabilityRequestDetails'])->name('fetch-availability-request-details');
            Route::post('/reject-availability-request', [UserControllerNew::class, 'rejectAvailabilityRequest'])->name('reject-availability-request');
            Route::post('/approve-availability-request', [UserControllerNew::class, 'approveAvailabilityRequest'])->name('approve-availability-request');
            Route::get('/change-availability-request-status', [UserControllerNew::class, 'changeAvailabilityRequestStatus'])->name('change-availability-request-status');
            Route::post('/ajax/check_bma_area_manager/{userID?}',  [UserControllerNew::class, 'ajax_check_bma_area_manager_for_project'])->name('ajax_check_bma_area_manager_for_project');
        });

        //This route is for Contact Management
        Route::group(['prefix'=>'contacts','as'=>'contacts.','namespace'=>'Contacts'],function(){
            Route::get('/inquiry-list', [ManagecontactsController::class, 'list'])->name('list');
            Route::get('/demorequest-list', [ManagecontactsController::class, 'demolist'])->name('demolist');
            //for user listing response
            Route::get('contactListAjax/{id?}',  [ManagecontactsController::class, 'contactListAjax'])->name('list.ajax');
            Route::get('demoListAjax/{id?}',  [ManagecontactsController::class, 'demoListAjax'])->name('demolist.ajax');
            Route::get('/delete-demo/{id}', [ManagecontactsController::class, 'deleteDemo'])->name('delete.demo');
            Route::get('/addrechedout-demo/{id}', [ManagecontactsController::class, 'updateAddreachedout'])->name('addreachedout.demo');
            Route::get('/deleterechedout-demo/{id}', [ManagecontactsController::class, 'updateDeletereachedout'])->name('deletereachedout.demo');
        });

        Route::group(['prefix' => 'contracts', 'as' => 'contracts.', 'namespace' => 'Contracts'], function (){
            Route::post('/render-items',  [\App\Http\Controllers\Admin\Contracts\ContractItemController::class, 'render'])->name('items.render');
            Route::post('/render-items-form',  [\App\Http\Controllers\Admin\Contracts\ContractItemController::class, 'renderCreateForm'])->name('items.createFormRender');
            Route::post('/render-items-update-form',  [\App\Http\Controllers\Admin\Contracts\ContractItemController::class, 'renderUpdateForm'])->name('items.updateFormRender');

            Route::post('/store-item',  [\App\Http\Controllers\Admin\Contracts\ContractItemController::class, 'store'])->name('items.store');
            Route::post('/update-item/{id?}',  [\App\Http\Controllers\Admin\Contracts\ContractItemController::class, 'update'])->name('items.update');
            Route::post('/destroy-item/{id?}',  [\App\Http\Controllers\Admin\Contracts\ContractItemController::class, 'destroy'])->name('items.destroy');
        });

        //This route is for Work Order Management
        Route::group(['prefix' => 'work-order','as' => 'workorder.',"namespace"=>"Workorder"], function () {
            Route::get('/taskCheckListDetails', [WorkOrderController::class, 'taskCheckListDetails'])->name('result.taskCheckListDetails');
            Route::get('/sp_preventive_details/{id}', [WorkOrderController::class, 'sp_preventive_details'])->name('sp_preventive_details');
            // @flip1@ get checklist details
            Route::get('/checkListDetailsAjax', [WorkOrderController::class, 'checkListDetailsAjax'])->name('result.checkListDetailsAjax');
            Route::post('/list_details', [WorkOrderController::class,'WorkorderChecklistDetails'])->name('checklist.list_details');

            Route::post('/render-items',  [WorkOrderItemsController::class, 'renderWorkOrderItems'])->name('items.render');
            Route::post('/render-items-form',  [WorkOrderItemsController::class, 'renderWorkOrderItemsCreateForm'])->name('items.createFormRender');
            Route::post('/render-items-form/{id?}',  [WorkOrderItemsController::class, 'renderWorkOrderItemsUpdateForm'])->name('items.updateFormRender');

            Route::post('/store-item',  [WorkOrderItemsController::class, 'store'])->name('items.store');
            Route::post('/update-item/{id?}',  [WorkOrderItemsController::class, 'update'])->name('items.update');
            Route::post('/destroy-item/{id?}',  [WorkOrderItemsController::class, 'destroy'])->name('items.destroy');

            //new routes for performance work orders
            Route::get('/new-service-providers-list/{type?}', [NewServiceProviderController::class, 'openServiceProvidersList'])->name('openServiceProvidersList');
            Route::get('/new-work-orders-list/{type?}/{id?}', [NewWorkOrderController::class, 'openWorkOrdersList'])->name('openWorkOrdersList');
            Route::get('/new-pm-work-orders-list', [NewWorkOrderController::class, 'openPmWorkOrdersList'])->name('openPmWorkOrdersList');

            /**Asset Ajax list */
            Route::get('ajax-get-asset-categories/{id?}', [CreateWorkOrderController::class, 'AjaxGetAssetCategories'])->name('ajax-get-asset-categories');
            Route::get('ajax-get-asset-categories-priority/{id?}', [CreateWorkOrderController::class, 'AjaxGetAssetCategoriesPriorities'])->name('ajax-get-asset-categories-priorities');
            Route::get('ajax-get-contract-asset-categories-priority/{id?}', [CreateWorkOrderController::class, 'AjaxGetContractAssetCategoriesPriorities'])->name('ajax-get-contract-asset-categories-priorities');
            Route::get('ajax-asset-name-get-s/{id?}', [CreateWorkOrderController::class, 'AjaxAssetNameGet'])->name('ajax-asset-name-get-s');
            Route::get('ajax-asset-number-get-s/{id?}', [CreateWorkOrderController::class, 'AjaxAssetNumberGet'])->name('ajax-asset-number-get-s');
            Route::get('ajax-get-room-data/{id?}',[CreateWorkOrderController::class, 'AjaxRoomDataGet'])->name('ajax-get-room-data');
            Route::get('fetch-property-assets/{id?}', [CreateWorkOrderController::class, 'fetchPropertyAssets'])->name('fetch-property-assets');
            // Get contract Piorities
            Route::get('AjaxPrioritiesGet/{id?}', [CreateWorkOrderController::class, 'AjaxPrioritiesGet'])->name('AjaxPrioritiesGet');
            Route::get('GetContractFrequency/{id?}', [CreateWorkOrderController::class, 'GetContractFrequency'])->name('GetContractFrequency');
            Route::get('/get-asset-details', [CreateWorkOrderController::class, 'getAssetDetails'])->name('getassetdetails');
            /**work order checklist */
             Route::get('check-list-data/{id?}',[WorkOrderController::class, 'checkListDetails'])->name('checklist');
             Route::get('check-list-ajax/{id?}', [WorkOrderController::class, 'checkListDetails'])->name('checklist.list.ajax');
             Route::get('reopen-workorder-details/{id?}',[WorkOrderController::class, 'reopenworkorderDetails'])->name('reopen_workorder_details');
             Route::get('reopencheck-list-data/{id?}',[WorkOrderController::class, 'reopencheckListDetails'])->name('reopenchecklist');
             Route::get('reopencheck-list-ajax/{id?}', [WorkOrderController::class, 'reopencheckListDetails'])->name('reopenchecklist.list.ajax');
            /**create workorder for all user */
            /**reactive */
            Route::get('/complete-workorder-status/{id}',[CreateWorkOrderController::class, 'complete_workorder_status'])->name('workorder.complete_status');
            Route::get('/complete-workorder',[CreateWorkOrderController::class, 'complete_workorder'])->name('workorder.complete');
            Route::get('/create-reactive-maintance-type/{via?}',[CreateWorkOrderController::class, 'createWorkOrder'])->name('c-reactive.maintance');
            Route::post('/create-mvalidate',[CreateWorkOrderController::class, 'createWorkOrderVali'])->name('c-reactive.mvalidate');
            Route::get('/create-reactive-location',[CreateWorkOrderController::class, 'createWorkOrderLocation'])->name('c-reactive.location');
            Route::get('/create-reactive-location/{page}',[CreateWorkOrderController::class, 'createWorkOrderLocation'])->name('c-reactive.group-location');
            Route::post('/reactive-location-validate',[CreateWorkOrderController::class, 'workOrderLocationValidate'])->name('c-reactive.lvalidate');
            Route::get('/create-reactive-confirmation',[CreateWorkOrderController::class, 'createWorkOrderConfirmation'])->name('c-reactive.confirm');
            Route::post('/create-reactive-save-data',[CreateWorkOrderController::class, 'createWorkOrderStore'])->name('c-reactive.save-data');
            Route::get('/create-reactive-complete',[CreateWorkOrderController::class, 'createWorkOrderComplete'])->name('c-reactive.complete');
            /**preventive */
            Route::get('/create-preventive-maintance-type',[CreateWorkOrderController::class, 'preventiveCreateWorkOrder'])->name('c-preventive.maintance');
            Route::post('/create-preventive-mt-validate',[CreateWorkOrderController::class, 'preventiveCreateWorkOrderValidation'])->name('c-preventive.maintance.validate');
            Route::get('/create-preventive-location',[CreateWorkOrderController::class, 'preventiveCreateWorkOrderLocation'])->name('c-preventive.location');
            Route::post('/create-preventive-ln-validate',[CreateWorkOrderController::class, 'preventiveValidationWorkOrderLocation'])->name('c-preventive.location.validate');
            Route::get('/create-preventive-confirmation',[CreateWorkOrderController::class, 'preventiveCreateWorkOrderConfirmation'])->name('c-preventive.confirm');
            Route::match(array('GET','POST'),'/create-preventive-save-data',
            [CreateWorkOrderController::class, 'createWorkOrderPreventiveStore'])->name('c-preventive.save-data');
            Route::get('/create-preventive-complete',[CreateWorkOrderController::class, 'preventiveCreateWorkOrderComplete'])->name('c-preventive.complete');
            Route::get('/create-preventive-complete-page/{frequency}',[CreateWorkOrderController::class, 'preventiveCreateWorkOrderCompletePage'])->name('c-preventive.complete.page');
            Route::get('/remove-workorder-step/{step}', [CreateWorkOrderController::class, 'removeWorkorderStep'])->name('remove_workorder_step');
            Route::get('/add-workorder-step', [CreateWorkOrderController::class, 'addWorkorderStep'])->name('add_workorder_step');


            Route::get('/checklist-show/{id}',[WorkOrderController::class, 'checklistShow'])->name('checklist.show');
            /**end preventive */
            Route::get('/', [WorkOrderController::class, 'serviceProviderList'])->name('serviceprovider.list');
            Route::get('/list/{type}', [WorkOrderController::class, 'serviceProviderList'])->name('serviceprovider.list.get');
            Route::get('service-provider-list-ajax/{id?}', [WorkOrderController::class, 'serviceProviderList'])->name('serviceprovider.list.ajax');
            /**manage PM work order edit , delete*/
            Route::post('/delete-work-order-preventive',[WorkOrderController::class, 'deletePreventiveWorkOrder'])->name('delete.workorder.p');
            Route::get('/delete-work-order-bm/{id?}',[WorkOrderController::class, 'deletePreventiveWorkOrderBm'])->name('delete.workorder.bma');
            Route::get('/send-reminder/{id?}',[WorkOrderController::class, 'sendReminder'])->name('send-reminder');
            /*preventive workorder edit*/
            Route::post('get_checklists_asset_ids', [EditWorkOrderController::class, 'get_checklists_asset_ids'])->name('get_checklists_asset_ids');
            Route::post('get-check-list-by-property', [CreateWorkOrderController::class, 'getCheckListByProperty'])->name('getCheckListByProperty');
            Route::get('edit/{id}/preventive-info', [EditWorkOrderController::class, 'editPreventiveWorkOrder'])->name('edit.preventive.info');
            Route::post('edit/preventive-edit-validate', [EditWorkOrderController::class, 'preventiveEditWorkOrderValidation'])->name('edit.preventive.info.validate');
            Route::get('/edit/{id}/preventive-location', [EditWorkOrderController::class, 'preventiveEditWorkOrderLocation'])->name('edit.preventive.location');
            Route::post('edit/preventive-location-validate', [EditWorkOrderController::class, 'preventiveEditValidationWorkOrderLocation'])->name('edit-p.location.validate');
            Route::get('/edit/{id}/preventive-confirmation', [EditWorkOrderController::class, 'editPreventiveCreateWorkOrderConfirmation'])->name('edit.preventive.confirmation');
            Route::post('/edit-preventive-save-data', [EditWorkOrderController::class, 'editWorkOrderPreventiveStore'])->name('edit.preventive.save');
            Route::get('/work-order-list/{id}', [WorkOrderController::class, 'list'])->name('workorders.list');
            Route::get('/new-work-order-list/{id}', [WorkOrderController::class, 'new_list'])/*->name('workorders.list')*/;
            Route::get('/open-workorders/{id}', [WorkOrderController::class, 'list'])->name('workorders.open.list');
            Route::get('/in-progress-workorders/{id}', [WorkOrderController::class, 'list'])->name('workorders.in_progress.list');
            Route::get('/under-evaluation-workorders/{id}', [WorkOrderController::class, 'list'])->name('workorders.under_evaluation.list');
            Route::get('/pending-work-order-list/{id}', [WorkOrderController::class, 'list'])->name('workorders.pending.list');
            Route::get('/request-work-order-list/{id}', [WorkOrderController::class, 'list'])->name('workorders.request.list');
            Route::get('/work-order-closed-list/{id}', [WorkOrderController::class, 'list'])->name('workorders.closed.list');
            Route::get('/spare-parts-workorders/{id}', [WorkOrderController::class, 'list'])->name('workorders.spare_parts.list');
            Route::post('/work-order-list-count-ajax/{id}', [WorkOrderController::class, 'list_ajax_count'])->name('workorders.list_ajax_count');
            Route::post('/store-selected-sp-session', [WorkOrderController::class, 'storeServiceproviderSelected'])->name('workorders.store_selected_sp_session');

            Route::get('workorder-list-ajax/{id?}', [WorkOrderController::class, 'workOrderList'])->name('workorders.list.ajax');
            Route::get('/pm_work-order-list/{id?}', [WorkOrderController::class, 'PMWorkOrderList'])->name('workorders.list.pm');
            Route::get('pm_workorder-list-ajax/{id?}', [WorkOrderController::class, 'PMWorkOrderList'])->name('workorders.list.pm.ajax');
            Route::post('/assign_pm_worker', [WorkOrderController::class, 'assign_pm_worker'])->name('workorders.assign_pm_worker');
            //Signle work order edit Reactive and preventive
            Route::get('single/edit/{id}/reactive-info', [SingleEditWorkOrder::class, 'editReWorkOrder'])->name('single.edit.reactive.info');
            Route::post('single/edit/reactive-edit-validate', [SingleEditWorkOrder::class, 'editReWorkOrderVali'])->name('single.edit.reactive.info.validate');
            Route::get('single/edit/{id}/reactive-location', [SingleEditWorkOrder::class, 'editReWorkOrderLocation'])->name('single.edit.reactive.location');
            Route::post('single/edit/reactive-location-validate', [SingleEditWorkOrder::class, 'editReLocationValidate'])->name('single.edit-reactive.location.validate');
            Route::get('single/edit/{id}/reactive-confirmation', [SingleEditWorkOrder::class, 'editReWorkOrderConfirmation'])->name('single.edit.reactive.confirmation');
            Route::post('single/edit-reactive-save-data', [SingleEditWorkOrder::class, 'editReWorkOrderStore'])->name('single.edit.reactive.save');
            Route::get('/workorder/{id?}', [WorkOrderController::class, 'fetch_unread_message'])->name('fetch_unread_message');
            Route::get('/work-order-result/{id}', [WorkOrderController::class, 'OrderResult'])->name('result.show');
            Route::get('/{id?}/{notificationread?}/{notification_id?}', [WorkOrderController::class, 'show'])->name('show');
            Route::post('submit-message', [WorkOrderController::class, 'submit_message'])->name('submit_message');
            Route::post('check-receiver-message', [WorkOrderController::class, 'checkReceiverMessage'])->name('check_receiver_message');
            Route::post('submit-warranty-comment', [WorkOrderController::class, 'submit_warranty_comment'])->name('submit_warranty_comment');
            Route::post('update-response-action', [WorkOrderController::class, 'update_response_action'])->name('update_response_action');
            Route::post('{id}/assign-worker-for-scheduled', [WorkOrderController::class, 'assignWorkerForScheduled'])->name('assign-worker-for-scheduled');

            Route::post('approve-reject-sp-date', [WorkOrderController::class, 'approve_reject_sp_date'])->name('approve_reject_sp_date');
            Route::post('complete-job-by-sp', [WorkOrderController::class, 'complete_job_by_sp'])->name('complete_job_by_sp');
            Route::post('approve-job-by-sp', [WorkOrderController::class, 'approve_job_by_sp'])->name('approve_job_by_sp');
            Route::post('re-open-job', [WorkOrderController::class, 're_open_job'])->name('re_open_job');
            Route::post('sp-reopen-job', [WorkOrderController::class, 'sp_reopen_job'])->name('sp_reopen_job');
            Route::post('bm-accept-reject-reopen', [WorkOrderController::class, 'bm_accept_reject_reopen'])->name('bm_accept_reject_reopen');
            Route::post('bm-action-on_rejects-wo-by-sp', [WorkOrderController::class, 'bm_action_on_rejects_wo_by_sp'])->name('bm_action_on_rejects_wo_by_sp');
            Route::post('sp-action-on-rejects-wo-by-bm', [WorkOrderController::class, 'sp_action_on_rejects_wo_by_bm'])->name('sp_action_on_rejects_wo_by_bm');
            Route::get('/soft-service/{id}', [WorkOrderController::class, 'softServiceShow'])->name('softservice.show');
            Route::get('/close-order/{id}', [WorkOrderController::class, 'closeOrderShow'])->name('closeorder.show');
            Route::POST('/workorder-edit-image', [WorkOrderController::class, 'workorder_edit_image'])->name('workorder_edit_image');
            Route::POST('/workorder-delete-image', [WorkOrderController::class, 'workorder_delete_image2'])->name('workorder_delete_image2');
            Route::POST('/changeItemRequestByWorkerStatus', [WorkOrderController::class, 'changeItemRequestByWorkerStatus'])->name('changeItemRequestByWorkerStatus');
            Route::POST('/changeItemRequestBySpStatus', [WorkOrderController::class, 'changeItemRequestBySpStatus'])->name('changeItemRequestBySpStatus');
            Route::post('/assign-supervisor', [WorkOrderController::class, 'assignSupervisor'])->name('assign_supervisor');
            Route::post('/inventory/send-to-project-owner', [WorkOrderController::class, 'sendToProjectOwner'])->name('send_to_project_owner');
            Route::post('/checklist/show-worker-checklist', [WorkOrderController::class, 'getSubmittedChecklistData'])->name('show_worker_checklist');
        });



Route::get('/market_place/reg_sp_vendor', function(){
return view('applications.admin.market_place.reg_sp_vendor');
});
Route::get('/market_place/reg_sp_vendor_details', function(){
return view('applications.admin.market_place.reg_sp_vendor_details');
});
Route::get('/market_place/reg_sp_vendor_details_1', function(){
return view('applications.admin.market_place.reg_sp_vendor_details_1');
});






        //This route is for Property Management
        Route::group(['prefix'=>'property','as'=>'property.','namespace'=>'Property'],function(){
            Route::get('/', [PropertyFinalController::class,'list'])->name('list');

            //Add Property Management
            Route::get('/add-property',  [PropertyFinalController::class, 'create'])->name('create');
            Route::get('/add_information',  [PropertyFinalController::class, 'add_information'])->name('create.information');
            Route::post('/add_information_post',  [PropertyFinalController::class, 'add_information_post'])->name('create.information.post');
            Route::post('/get_cities',  [PropertyFinalController::class, 'get_cities'])->name('get_cities');
            Route::post('/get_bme_list',  [PropertyFinalController::class, 'get_bme_list'])->name('get_bme_list');
            Route::post('/get_building_data',  [PropertyFinalController::class, 'get_building_data'])->name('get_building_data');
            Route::match(array('GET','POST'),'/add_structure_details',  [PropertyFinalController::class, 'add_structure_details'])->name('create.structure_details');
            Route::post('check_building_name_exists',  [PropertyFinalController::class, 'check_building_name_exists'])->name('check_building_name_exists');
            Route::match(array('GET','POST'),'/add_property_confirmation',  [PropertyFinalController::class, 'add_property_confirmation'])->name('create.property_confirmation');
            Route::match(array('GET','POST'),'/store',  [PropertyFinalController::class, 'store'])->name('store');

            //Complex specific Property
            Route::get('/add-property-complex2/{id}',  [PropertyFinalController::class, 'complex_structure'])->name('complex_structure');
             Route::match(array('GET','POST'),'/add_structure_complex',  [PropertyFinalController::class, 'add_structure_complex'])->name('create.structure_details_complex');

             //Edit the property
             Route::get('/edit-property/{id}',  [PropertyFinalController::class, 'edit'])->name('edit');

             Route::get('/edit_information',  [PropertyFinalController::class, 'edit_information'])->name('edit.information');
             Route::post('/edit_information_post',  [PropertyFinalController::class, 'edit_information_post'])->name('edit.information.post');

             Route::match(array('GET','POST'),'/edit_structure_details',  [PropertyFinalController::class, 'edit_structure_details'])->name('edit.structure_details');
             Route::match(array('GET','POST'),'/edit_property_confirmation',  [PropertyFinalController::class, 'edit_property_confirmation'])->name('edit.property_confirmation');
            Route::match(array('GET','POST'),'/update',  [PropertyFinalController::class, 'update'])->name('update');

            //Editing a complex
            Route::get('/edit-property-complex2/{id}/{property_id}/{is_redirect?}',  [PropertyFinalController::class, 'edit_complex_structure'])->name('edit_complex_structure');
            Route::match(array('GET','POST'),'/edit_structure_complex',  [PropertyFinalController::class, 'edit_structure_complex'])->name('edit.structure_details_complex');// @flip1@ change method

            //Route::get('/load-remaining-rooms/{property_id}/', [PropertyFinalController::class, 'loadRemainingRooms'])->name('load_remaining_rooms');

            //Downloading the QR Codes
            Route::get('/downloadQrConfirmation',function(){
                return view('applications.admin.property.downloadedConfirmation');
            })->name('downloadqrconfirmation');

            Route::get('/details/{id}',  [PropertyFinalController::class, 'details'])->name('details');
            Route::get('/assets-list-ajax', [PropertyFinalController::class, 'fetchAssetsofProperty'])->name('property_assetlist.ajax');
            Route::get('/get_all_buildings/{id}',  [PropertyFinalController::class, 'get_all_buildings'])->name('get_all_buildings');
            Route::post('/delete_complex',  [PropertyFinalController::class, 'delete_complex'])->name('delete_complex');
            Route::get('/delete-data/{id}', [PropertyFinalController::class, 'deleteData'])->name('delete');
            Route::post('/asset/store',  [PropertyFinalController::class, 'asset_store'])->name('asset.store');
            Route::post('/asset/update',  [PropertyFinalController::class, 'asset_update'])->name('asset.update');
            Route::get('/asset/edit-modal/{id}/{property_id}',  [PropertyFinalController::class, 'asset_edit_modal'])->name('asset.asset_edit_modal');
            Route::get('/asset/view-modal/{id}/{property_id}',  [PropertyFinalController::class, 'asset_view_modal'])->name('asset.asset_view_modal');
            Route::get('/asset/view-modal/wo_list',  [PropertyFinalController::class, 'asset_view_modal_wolist'])->name('asset.asset_view_modal_wo_list');

            Route::get('/asset/delete-data/{id}', [PropertyFinalController::class, 'deleteAssetData'])->name('asset.delete');
            Route::post('ajax_check_project_url/{id?}',  [PropertyFinalController::class, 'ajax_check_project_url'])->name('ajax_check_project_url');
            Route::post('ajax_check_asset_url/{id?}/{property_id?}',  [PropertyFinalController::class, 'ajax_check_asset_url'])->name('ajax_check_asset_url');
            Route::post('ajax_check_complex_name_validate/{id?}',  [PropertyFinalController::class, 'ajax_check_complex_name_validate'])->name('ajax_check_complex_name_validate');
            Route::get('getAllAssetBarcodeValues',  [PropertyFinalController::class, 'getAllAssetBarcodeValues'])->name('units.getAllAssetBarcodeValues');
            Route::post('/asset/get_asset_names',  [PropertyFinalController::class, 'get_asset_names'])->name('get_asset_names');

            // Route to fetch asset categories based on selected asset names.
            Route::post('/asset/get-asset-categories', [PropertyFinalController::class, 'getAssetCategories'])->name('get-asset-categories');

            Route::post('/asset/get_asset_names_download',  [PropertyFinalController::class, 'get_asset_names_download'])->name('get_asset_names_download');
            Route::get('pdfview/{property_id}', [PropertyFinalController::class, 'pdfview'])->name('pdfview');
            Route::get('barcodejs', [PropertyFinalController::class, 'barcodejs'])->name('barcodejs');
            Route::post('/asset/get_asset_rooms/{floor_id?}/{property_id?}',  [PropertyFinalController::class, 'get_asset_rooms'])->name('get_asset_rooms');
            Route::post('/asset/get_asset_rooms_new/{floor_id?}/{property_id?}',  [PropertyFinalController::class, 'get_asset_rooms_new'])->name('get_asset_rooms_new');
            Route::post('/asset/deleteBulkAssets',  [PropertyFinalController::class, 'deleteBulkAssets'])->name('asset.deleteBulkAssets');
            Route::post('/asset/downloadAssetBarcode',  [PropertyFinalController::class, 'downloadAssetBarcode'])->name('asset.downloadAssetBarcode');
            Route::get('/asset/redirectafetrAssetDownload',  [PropertyFinalController::class, 'redirectafetrAssetDownload'])->name('asset.redirectafetrAssetDownload');
            Route::get('/asset/getAllFloorsAssetsAjax',  [PropertyFinalController::class, 'getAllFloorsAssetsAjax'])->name('asset.getAllFloorsAssetsAjax');
            Route::post('/asset/get_asset_number/{property_id?}/{asset_category_id_asset?}',  [PropertyFinalController::class, 'get_asset_number'])->name('get_asset_number');
            Route::post('/asset/downloadBarcodesManualAsset',  [PropertyFinalController::class, 'downloadBarcodesManualAsset'])->name('asset.downloadBarcodesManualAsset');

            // Units Routes
            Route::get('/downloadthefile',  [PropertyFinalController::class, 'downloadthefile'])->name('units.downloadthefile');
            Route::post('/units/qrdownload',  [PropertyFinalController::class, 'qrDownload'])->name('units.qrdownload');
            Route::post('/units/deleteBulkQr',  [PropertyFinalController::class, 'deleteBulkQr'])->name('units.deleteBulkQr');
            Route::post('/units/addNewUnit',  [PropertyFinalController::class, 'addNewUnit'])->name('units.addNewUnit');
            Route::get('/units/getUnitByIdJquery',  [PropertyFinalController::class, 'getUnitByIdJquery'])->name('units.getUnitByIdJquery');
            Route::post('/units/updateUnitById',  [PropertyFinalController::class, 'updateUnitById'])->name('units.updateUnitById');
            Route::post('/units/deleteUnitById',  [PropertyFinalController::class, 'deleteUnitById'])->name('units.deleteUnitById');
            Route::post('/units/downloadBarcodesManual',  [PropertyFinalController::class, 'downloadBarcodesManual'])->name('units.downloadBarcodesManual');
            Route::get('/units/getAllFloorsAjax',  [PropertyFinalController::class, 'getAllFloorsAjax'])->name('units.getAllFloorsAjax');
            Route::get('/units/getAllUnitsByFloorAjax',  [PropertyFinalController::class, 'getAllUnitsByFloorAjax'])->name('units.getAllUnitsByFloorAjax');
            Route::get('/units/redirectconfirmation',  [PropertyFinalController::class, 'redirect'])->name('units.redirectconfirmation');

            //New route added to get asset zones
            Route::post('/asset/get_asset_zones/{buildingid?}',  [PropertyFinalController::class, 'get_asset_zones'])->name('get_asset_zones');

            Route::post('/asset_documents_url',  [PropertyFinalController::class, 'asset_documents_url'])->name('asset_documents_url');
            Route::post('/get_count_asset_documents',  [PropertyFinalController::class, 'get_count_asset_documents'])->name('get_count_asset_documents');
            Route::post('/delete_asset_documents',  [PropertyFinalController::class, 'delete_asset_documents'])->name('delete_asset_documents');
            Route::get('/getUpdatepropertylocationByProjectname',  [PropertyFinalController::class, 'getUpdatepropertylocationByProjectname'])->name('getUpdatepropertylocationByProjectname');
            Route::get('/getUpdateAssetfilenames',  [PropertyFinalController::class, 'getUpdateAssetfilenames'])->name('getUpdateAssetfilenames');

            // Route::get('/generate-qr-code',  [PropertyFinalController::class, 'generate_qr_code'])->name('generate-qr-code');
            Route::get('/deactive-qr-code/{id}',  [PropertyFinalController::class, 'deactiveQrCode'])->name('deactive-qr-code');
            Route::get('/active-qr-code/{id}',  [PropertyFinalController::class, 'activeQrCode'])->name('active-qr-code');
            Route::get('/re-generate-qr-code/{id}',  [PropertyFinalController::class, 'reGenerateQrCode'])->name('re-generate-qr-code');

            Route::get('/units/{id}',  [ManageUnitsController::class, 'openManageUnitList'])->name('manageunits')->middleware('property_unit_permission');
            Route::get('/add-units/{id}',  [ManageUnitsController::class, 'openCreateAddUnit'])->name('addunits')->middleware('property_unit_permission');
        });

         //New Bulk import project
        Route::group(['prefix' => 'bulk-import','as' => 'bulk-import.','namespace' => 'bulk-import'],function(){
            Route::get('/upload',  [BulkImportProjectController::class, 'openUploadFile'])->name('openUploadFile')->middleware('bulk_import_permission');
            Route::get('/project-not-found',  [BulkImportProjectController::class, 'openProjectNotFound'])->name('openProjectNotFound')->middleware('bulk_import_permission');
            Route::post('/store-file',  [BulkImportProjectController::class, 'storeFile'])->name('storeFile')->middleware('bulk_import_permission'); 
            Route::get('/select-sheets/{token}',  [BulkImportProjectController::class, 'oepnSelectsheets'])->name('oepnSelectsheets')->middleware('bulk_import_permission');
            Route::post('/update-select-sheet-bulk-import/{token}',  [BulkImportProjectController::class, 'updateSelectSheetBulkImport'])->name('updateSelectSheetBulkImport')->middleware('bulk_import_permission');
            Route::get('/file-mapping/{token}',  [BulkImportProjectController::class, 'openFileMapping'])->name('openFileMapping')->middleware('bulk_import_permission');
            Route::get('/validation/{token}',  [BulkImportProjectController::class, 'openErrorsValidation'])->name('openErrorsValidation')->middleware('bulk_import_permission');
            Route::get('/done',  [BulkImportProjectController::class, 'openFinishInsertion'])->name('openFinishInsertion')->middleware('bulk_import_permission');
        });

        Route::group(['prefix' => 'settings','as' => 'settings.','namespace' => 'settings'],function(){
            Route::get('/list',  [ConfigurationController::class, 'openConfigurationList'])->name('openConfigurationList')->middleware('configuration_permission');
            Route::get('/create',  [ConfigurationController::class, 'openCreateNewConfiguration'])->name('openCreateNewConfiguration')->middleware('configuration_permission');
            Route::post('/store',  [ConfigurationController::class, 'storeConfiguration'])->name('storeConfiguration')->middleware('configuration_permission');
            Route::get('/crm-configuration',  [ConfigurationController::class, 'crmConfiguration'])->name('crmConfiguration')->middleware('configuration_permission');
        });

        Route::group(['prefix' => 'settings','as' => 'settings.','namespace' => 'settings'],function(){
            Route::get('/list',  [ConfigurationController::class, 'openConfigurationList'])->name('openConfigurationList')->middleware('configuration_permission');
            Route::get('/create',  [ConfigurationController::class, 'openCreateNewConfiguration'])->name('openCreateNewConfiguration')->middleware('configuration_permission');
            Route::post('/store',  [ConfigurationController::class, 'storeConfiguration'])->name('storeConfiguration')->middleware('configuration_permission');
            Route::get('/configuration/{token}',  [ConfigurationController::class, 'openViewConfiguration'])->name('openViewConfiguration')->middleware('configuration_permission');
            Route::get('/edit/{token}',  [ConfigurationController::class, 'openEditConfiguration'])->name('openEditConfiguration')->middleware('configuration_permission');
            Route::post('/update/{token}',  [ConfigurationController::class, 'updateConfiguration'])->name('updateConfiguration')->middleware('configuration_permission');
        });

    /*
        |--------------------------------------------------------------------------
        |  Roles and Permissions Routes Groupe
        |--------------------------------------------------------------------------
        |
        */
        // 'middleware' => ['role:super_admin|admin']
        Route::group(['prefix' => 'settings'], function() {
            Route::get('/permissions', [PermissionController::class, 'index'])->name('settings.userspermissions');
            Route::get('/permissions/{permissionId}/delete', [PermissionController::class, 'destroy']);

            Route::get('/roles', [RoleController::class, 'index'])->name('settings.usersroles');
            Route::get('/roles/{roleId}/delete', [RoleController::class, 'destroy']);
            Route::get('/roles/{roleId}/give-permissions', [RoleController::class, 'addPermissionToRole']);
            Route::put('/roles/{roleId}/give-permissions', [RoleController::class, 'givePermissionToRole']);
        });


        /*
        |--------------------------------------------------------------------------
        | Temporary Routes Remove it after templating
        | Routes for blade templating by FOUZAN
        |--------------------------------------------------------------------------
        |
        */
        // 'middleware' => ['role:super_admin|admin']
        Route::group(['prefix' => 'templating'], function() {
            //BAFO
            Route::get('/bafo', [TemplatingController::class, 'bafoIndex'])->name('bafo.index');
            Route::get('/bafo/show', [TemplatingController::class, 'bafoShow'])->name('bafo.show');
            Route::get('/bafo/edit', [TemplatingController::class, 'bafoEdit'])->name('bafo.edit');
            //Quotations
            Route::get('/quotations', [TemplatingController::class, 'quotationsIndex'])->name('quotations.index');
            Route::get('/quotations/show', [TemplatingController::class, 'quotationsShow'])->name('quotations.show');
            Route::get('/quotations/edit', [TemplatingController::class, 'quotationsEdit'])->name('quotations.edit');
            Route::get('/quotations/new', [TemplatingController::class, 'quotationsNew'])->name('quotations.new');
            //Bills
            Route::get('/bills', [TemplatingController::class, 'billsIndex'])->name('bills.index');
            Route::get('/bills/show', [TemplatingController::class, 'billsShow'])->name('bills.show');
            Route::get('/bills/edit', [TemplatingController::class, 'billsEdit'])->name('bills.edit');

            Route::get('/bills/convert', [TemplatingController::class, 'billsConvertToPurchaseOrder'])->name('bills.convert');
            Route::get('/bills/new', [TemplatingController::class, 'billsNew'])->name('bills.new');
            Route::get('/bills/payment', [TemplatingController::class, 'billsAddPayment'])->name('bills.payment');

            //sales
            Route::get('/sales', [TemplatingController::class, 'salesIndex'])->name('sales.index');
            Route::get('/sales/show', [TemplatingController::class, 'salesShow'])->name('sales.show');
            Route::get('/sales/edit', [TemplatingController::class, 'salesEdit'])->name('sales.edit');
            Route::get('/sales/new', [TemplatingController::class, 'salesNew'])->name('sales.new');
            Route::get('/sales/clone', [TemplatingController::class, 'salesClone'])->name('sales.clone');
            Route::get('/sales/report', [TemplatingController::class, 'salesReport'])->name('sales.report');
            Route::get('/sales/settings', [TemplatingController::class, 'salesSettings'])->name('sales.settings');
            //invoices
            Route::get('/invoices', [TemplatingController::class, 'invoicesIndex'])->name('invoices.index');
            Route::get('/invoices/show', [TemplatingController::class, 'invoicesShow'])->name('invoices.show');
            Route::get('/invoices/edit', [TemplatingController::class, 'invoicesEdit'])->name('invoices.edit');
            Route::get('/invoices/new', [TemplatingController::class, 'invoicesNew'])->name('invoices.new');
            Route::get('/invoices/clone', [TemplatingController::class, 'invoicesClone'])->name('invoices.clone');

        });


        //Support Tickets Module Routes
        Route::group(['prefix'=>'support','as'=>'support.'],function()
        {
            Route::get('/list/{id?}', [SupportController::class, 'Support'])->name('list');
            Route::post('/create', [SupportController::class, 'Create'])->name('create');
            Route::get('/view/{id?}', [SupportController::class, 'viewTickets'])->name('view');
            Route::get('/status-change/{status?}/{id?}',[SupportController::class, 'StatusChange'])->name('status-change');
            Route::get('/ajax-check-ticket',[SupportController::class, 'AjaxCheckTicket']);
        });

        //Service providers module routes
        Route::group(['prefix'=>'serviceproviders','as'=>'serviceproviders.',"namespace"=>"Serviceprovider"],function(){
            Route::get('/', [ServiceproviderController::class, 'list'])->name('list');
            Route::get('/workspace', [ServiceproviderController::class, 'mainlist'])->name('mainlist');
            Route::get('/list', [ServiceproviderController::class, 'publiclist'])->name('publiclist');
            Route::get('/add-serviceprovider', [ServiceproviderController::class, 'create'])->name('create');
            Route::match(array('GET','POST'),'/add-serviceprovider2', [ServiceproviderController::class, 'create2'])->name('create2');
            Route::match(array('GET','POST'),'/add-serviceprovider3', [ServiceproviderController::class, 'create3'])->name('create3');
            Route::match(array('GET','POST'),'/store', [ServiceproviderController::class, 'store'])->name('store');
            Route::get('/edit-serviceprovider/{id}', [ServiceproviderController::class, 'edit'])->name('edit');
            Route::match(array('GET','POST'),'/edit-serviceprovider2', [ServiceproviderController::class, 'edit2'])->name('edit2');
            Route::match(array('GET','POST'),'/edit-serviceprovider3', [ServiceproviderController::class, 'edit3'])->name('edit3');
            Route::match(array('GET','POST'),'/update', [ServiceproviderController::class, 'update'])->name('update');
            Route::get('/delete-provider/{id}',[ServiceproviderController::class, 'deleteData'])->name('delete');

            //Ajax Calls
            Route::post('/ajax/ajax_check_unique_serviceproid/{id?}', [ServiceproviderController::class, 'ajax_check_unique_serviceproid'])->name('ajax_check_unique_serviceproid');
            Route::post('/ajax/ajax_check_provider_name_unique/{id?}', [ServiceproviderController::class, 'ajax_check_provider_name_unique'])->name('ajax_check_provider_name_unique');
            Route::post('/ajax/ajax_check_unique_serviceproemail/{id?}', [ServiceproviderController::class, 'ajax_check_unique_serviceproemail'])->name('ajax_check_unique_serviceproemail');
            Route::post('/ajax/ajax_check_unique_servicepronumber/{id?}', [ServiceproviderController::class, 'ajax_check_unique_servicepronumber'])->name('ajax_check_unique_servicepronumber');
        });

        /*
        |--------------------------------------------------------------------------
        | Data Module
        |--------------------------------------------------------------------------
        |
        | Note: The Properties module routes should be included in the data Module routes
        */
        Route::group(['prefix'=>'data','as'=>'data.'],function(){

            // Subcontracts module routes
            Route::group(['prefix' => 'subcontracts', 'as' => 'subcontracts.', 'namespace' => 'Contracts'], function (){

                Route::get('/{contractId}', [SubContractsController::class, 'list'])->name('list');

                # create -> info
                Route::get('/{contractId}/create', [SubContractsController::class, 'create'])->name('create.info');
                Route::post('/{contractId}/create-validate', [SubContractsController::class, 'validateCreate'])->name('create.validated');

                # create -> asset
                Route::get('/{contractId}/asset-details', [SubContractsController::class, 'asset'])->name('create.asset');
                Route::post('/{contractId}/asset-validate', [SubContractsController::class, 'validateCreateAsset'])->name('create.asset-validate');

                # create -> workforce
                Route::get('/{contractId}/workforce-amount', [SubContractsController::class, 'workForceAmount'])->name('create.workforce');
                Route::post('/{contractId}/workforce-amount', [SubContractsController::class, 'validateWorkForceAmount'])->name('create.workforce.validate');

                # create -> service agreement
                Route::get('/{contractId}/service-agreement', [SubContractsController::class, 'serviceAgreement'])->name('create.service');
                Route::get('/{contractId}/confirm', [SubContractsController::class, 'contractsConfirmation'])->name('create.service.validate');

                # create -> save data
                Route::post('/{contractId}/save-data', [SubContractsController::class, 'contractsPost'])->name('create.save-data');

                # edit -> info
                Route::get('/edit/{id}', [SubContractsController::class, 'edit'])->name('edit');
                Route::post('/edit-validate', [SubContractsController::class, 'editInfoPost'])->name('edit.validated');

                Route::get('/delete/{id}', [SubContractsController::class, 'delete'])->name('delete');

                Route::get('/{id}', [SubContractsController::class, 'show'])->name('show');
            });

            //Contracts Module Routes
            Route::group(['prefix' => 'contracts','as' => 'contracts.',"namespace"=>"Contracts"], function () {

                Route::post('/render-performance',  [ContractPerformanceIndicatorController::class, 'renderContractPerformanceIndicators'])->name('performanceIndicators.render');
//                Route::post('/render-items-form',  [WorkOrderItemsController::class, 'renderWorkOrderItemsCreateForm'])->name('items.createFormRender');
                Route::post('/render-performance-form/{performance_indicator?}',  [ContractPerformanceIndicatorController::class, 'renderWorkOrderItemsUpdateForm'])->name('performanceIndicators.updateFormRender');
//
//                Route::post('/store-item',  [WorkOrderItemsController::class, 'store'])->name('items.store');
                Route::post('/update-performance/{performance_indicator?}',  [ContractPerformanceIndicatorController::class, 'update'])->name('performanceIndicators.update');
                Route::get('/debug-performance',  [ContractPerformanceIndicatorController::class, 'debug'])->name('performanceIndicators.debug');

//                Route::post('/destroy-item/{id?}',  [WorkOrderItemsController::class, 'destroy'])->name('items.destroy');


                /*
                |--------------------------------------------------------------------------
                | Contract Documents Module
                |--------------------------------------------------------------------------
                */
                Route::group(['prefix' => 'contract-documents','as' => 'contract-documents.'], function () {
                    // Route for downloading payroll of the checklist ids
                    Route::get('/download-selected-items',  [ContractDocumentsController::class, 'downloadSelectedItems'])->name('download-selected-items');
                    Route::get('/{contractId}/{archived?}', [ContractDocumentsController::class, 'list'])
                    ->name('list')
                    ->where('archived', 'archived|nonarchived'); // This ensures that the parameter can only be 'archived' or 'nonarchived'
                    // Route for submitting documents
                    Route::post('/store-document',  [ContractDocumentsController::class, 'storeDocument'])->name('store-document');
                    // Route for approving document
                    Route::post('approve/{id}', [ContractDocumentsController::class, 'approveDocument'])->name('approve-document');
                    // Route for rejecting document
                    Route::post('reject', [ContractDocumentsController::class, 'rejectDocument'])->name('reject-document');
                    // Route for deleting document
                    Route::get('delete/{id}', [ContractDocumentsController::class, 'deleteDocument'])->name('delete-document');
                    // Route for updating documents
                    Route::post('/update-document-request',  [ContractDocumentsController::class, 'updateDocument'])->name('update-document-request');
                    // Route for submitting file
                    Route::post('/submit-file',  [ContractDocumentsController::class, 'submitFile'])->name('submit-file');
                    // Route for getting document history
                    Route::get('/history/{documentId}',  [ContractDocumentsController::class, 'history'])->name('history');
                    // Route for archiving document
                    Route::get('archive/{id}', [ContractDocumentsController::class, 'archiveDocument'])->name('archive');
                    // Route for Showing or hiding the buttons according to the availablity
                    Route::post('/update-selected-items',  [ContractDocumentsController::class, 'updateSelectedItems'])->name('update-selected-items');
                    // Route for archiving document of the checklist ids
                    Route::post('/archive-selected-items',  [ContractDocumentsController::class, 'archiveSelectedItems'])->name('archive-selected-items');
                    // Route for deleting document of the checklist ids
                    Route::post('/delete-selected-items',  [ContractDocumentsController::class, 'deleteSelectedItems'])->name('delete-selected-items');
                    // Route for removing archived document
                    Route::get('remove-archive/{id}', [ContractDocumentsController::class, 'removeArchiveDocument'])->name('remove-archive');
                });


                /*
                |--------------------------------------------------------------------------
                | Contract Payrolls Module
                |--------------------------------------------------------------------------
                */
                Route::group(['prefix' => 'contract-payroll','as' => 'contract-payroll.'], function () {
                    // Route for downloading payroll of the checklist ids
                    Route::get('/download-selected-items',  [ContractPayrollController::class, 'downloadSelectedItems'])->name('download-selected-items');
                    Route::get('/{contractId}/{archived?}', [ContractPayrollController::class, 'list'])
                    ->name('list')
                    ->where('archived', 'archived|nonarchived'); // This ensures that the parameter can only be 'archived' or 'nonarchived'
                    // Route for submitting Payroll
                    Route::post('/store-payroll',  [ContractPayrollController::class, 'storePayroll'])->name('store-payroll');
                    // Route for approving payroll
                    Route::post('approve/{id}', [ContractPayrollController::class, 'approvePayroll'])->name('approve-payroll');
                    // Route for rejecting payroll
                    Route::post('reject', [ContractPayrollController::class, 'rejectPayroll'])->name('reject-payroll');
                    // Route for deleting payroll
                    Route::get('delete/{id}', [ContractPayrollController::class, 'deletePayroll'])->name('delete-payroll');
                    // Route for updating Payroll
                    Route::post('/update-payroll-request',  [ContractPayrollController::class, 'updatePayroll'])->name('update-payroll-request');
                    // Route for submitting file
                    Route::post('/submit-file',  [ContractPayrollController::class, 'submitFile'])->name('submit-file');
                    // Route for getting payroll history
                    Route::get('/history/{payrollId}',  [ContractPayrollController::class, 'history'])->name('history');
                    // Route for archiving payroll
                    Route::get('archive/{id}', [ContractPayrollController::class, 'archivePayroll'])->name('archive');
                    // Route for Showing or hiding the buttons according to the availablity
                    Route::post('/update-selected-items',  [ContractPayrollController::class, 'updateSelectedItems'])->name('update-selected-items');
                    // Route for archiving payroll of the checklist ids
                    Route::post('/archive-selected-items',  [ContractPayrollController::class, 'archiveSelectedItems'])->name('archive-selected-items');
                    // Route for deleting payroll of the checklist ids
                    Route::post('/delete-selected-items',  [ContractPayrollController::class, 'deleteSelectedItems'])->name('delete-selected-items');
                    // Route for removing archived payroll
                    Route::get('remove-archive/{id}', [ContractPayrollController::class, 'removeArchivePayroll'])->name('remove-archive');
                });

                /*
                |--------------------------------------------------------------------------
                | Contract Inspection Reports Module
                |--------------------------------------------------------------------------
                */
                Route::group(['prefix' => 'contract-inspection-reports','as' => 'contract-inspection-reports.'], function () {
                    // Route for downloading inspection report of the checklist ids
                    Route::get('/download-selected-items',  [ContractInspectionReportsController::class, 'downloadSelectedItems'])->name('download-selected-items');
                    Route::get('/{contractId}/{archived?}', [ContractInspectionReportsController::class, 'list'])
                    ->name('list')
                    ->where('archived', 'archived|nonarchived'); // This ensures that the parameter can only be 'archived' or 'nonarchived'
                    // Route for submitting inspection report
                    Route::post('/store-inspection-report',  [ContractInspectionReportsController::class, 'storeInspectionReport'])->name('store-inspection-report');
                    // Route for approving inspection report
                    Route::post('approve/{id}', [ContractInspectionReportsController::class, 'approveInspectionReport'])->name('approve-inspection-report');
                    // Route for rejecting inspection report
                    Route::post('reject', [ContractInspectionReportsController::class, 'rejectInspectionReport'])->name('reject-inspection-report');
                    // Route for deleting inspection report
                    Route::get('delete/{id}', [ContractInspectionReportsController::class, 'deleteInspectionReport'])->name('delete-inspection-report');
                    // Route for updating inspection report
                    Route::post('/update-inspection-report-request',  [ContractInspectionReportsController::class, 'updateInspectionReport'])->name('update-inspection-report-request');
                    // Route for submitting file
                    Route::post('/submit-file',  [ContractInspectionReportsController::class, 'submitFile'])->name('submit-file');
                    // Route for getting inspection report history
                    Route::get('/history/{reportId}',  [ContractInspectionReportsController::class, 'history'])->name('history');
                    // Route for archiving inspection report
                    Route::get('archive/{id}', [ContractInspectionReportsController::class, 'archiveInspectionReport'])->name('archive');
                    // Route for Showing or hiding the buttons according to the availablity
                    Route::post('/update-selected-items',  [ContractInspectionReportsController::class, 'updateSelectedItems'])->name('update-selected-items');
                    // Route for archiving inspection report of the checklist ids
                    Route::post('/archive-selected-items',  [ContractInspectionReportsController::class, 'archiveSelectedItems'])->name('archive-selected-items');
                    // Route for deleting inspection report of the checklist ids
                    Route::post('/delete-selected-items',  [ContractInspectionReportsController::class, 'deleteSelectedItems'])->name('delete-selected-items');
                    // Route for removing archived inspection report
                    Route::get('remove-archive/{id}', [ContractInspectionReportsController::class, 'removeArchiveInspectionReport'])->name('remove-archive');
                });

                route::post('update-contracts-priority-minutes/{id?}', [ContractsController::class, 'AjaxUpdatePriorities'])->name('update-contracts-priority-minutes');
                route::post('update-contracts-frequency/{id?}', [ContractsController::class, 'AjaxUpdateFrequencies'])->name('update-contracts-frequency');
                route::post('update-contracts-priority/{id?}', [ContractsController::class, 'AjaxUpCoPr'])->name('update-contracts-priority');
                route::get('get-contracts-priority-ajax', [ContractsController::class, 'AjaxUpCoPrGet'])->name('get-contracts-priority-ajax');
                Route::get('get-contracts-city-list', [ContractsController::class, 'AjaxGetCityList'])->name('get-contracts-city-list');
                Route::get('get-contracts-property-list', [ContractsController::class, 'AjaxGetPropertyList'])->name('get-contracts-property-list');
                Route::get('get-contracts-formatted-property-list', [ContractsController::class, 'AjaxGetFormattedPropertyList'])->name('get-contracts-formatted-property-list');
                Route::post('BusinessListAjax/{id?}',  [ContractsController::class, 'BusinessListAjax'])->name('businesslist.ajax');
                Route::post('BusinessviewListAjax/{id?}',  [ContractsController::class, 'BusinessviewListAjax'])->name('businessviewlist.ajax');
                Route::get('get-service-provider-supervisor-list', [ContractsController::class, 'AjaxGetServiceProviderSupervisorList'])->name('get-service-provider-supervisor-list');
                Route::match(array('GET','POST'), 'save-data', [ContractsController::class, 'contractsPost'])->name('save-data');
                Route::get('ajax-asset-name/{id?}', [ContractsController::class, 'ajaxGetAssetName'])->name('ajax-get-asset-name');
                Route::get('/create',  [ContractsController::class, 'create'])->name('create.info');
                Route::post('/create-validate',  [ContractsController::class, 'validateCreate'])->name('create.validated');
                Route::post('/render-asset-items',  [ContractsController::class, 'renderAssetItems'])->name('asset.items.render');
                Route::post('/render-asset-items-form',  [ContractsController::class, 'renderAssetItemsCreateForm'])->name('asset.items.create');
                Route::post('/store-asset-items',  [ContractsController::class, 'storeAssetItems'])->name('asset.items.store');
                Route::post('/set-asset-items-owner', [ContractsController::class, 'setAssetItemsOwner'])->name('asset.items.set-owner');
                Route::get('/asset-details',  [ContractsController::class, 'asset'])->name('create.asset');
                Route::post('/asset-validate',  [ContractsController::class, 'validateCreateAsset'])->name('asset-validate');
                Route::get('/workforce-amount',  [ContractsController::class, 'workForceAmount'])->name('create.workforce');
                Route::post('/workforce-amount',  [ContractsController::class, 'validateWorkForceAmount'])->name('create.workforce.validate');
                Route::get('/service-agreement',  [ContractsController::class, 'serviceAgreement'])->name('create.service');
                Route::post('/confirmation',  [ContractsController::class, 'contractsConfirmation'])->name('create.confirmation');

                Route::post('/subcontract/save', [ContractsController::class, 'storeSubcontract'])->name('subcontract.store');

                /**edit contracts */
                Route::get('/{id}/edit/info', [ContractsController::class, 'editInfo'])->name('edit.info');
                Route::get('/{id}/edit/asset', [ContractsController::class, 'editAsset'])->name('edit.asset');
                Route::get('/{id}/edit/workforce-amount', [ContractsController::class, 'editWorkForceAmount'])->name('edit.workform-amount');
                Route::get('/{id}/edit/service-agreement', [ContractsController::class, 'editServiceAgreemnet'])->name('edit.service-agreemnet');
                Route::post('/{id}/edit/confirmation',  [ContractsController::class, 'editServiceAgreemnetPost'])->name('edit.confirmation');

                Route::post('/edit-validate',  [ContractsController::class, 'editInfoPost'])->name('edit.validated');
                Route::post('/edit/asset-validate',  [ContractsController::class, 'editAssetPost'])->name('edit.asset-validate');
                Route::post('/edit/workforce-amount',  [ContractsController::class, 'editWorkForceAmountPost'])->name('edit.workforce.validate');
                Route::match(array('GET','POST'), '/{id}/update-data', [ContractsController::class, 'contractsPostUpdate'])->name('edit.save-data');
                /**end edit contracts */
                Route::get('/delete-data/{id}', [ContractsController::class, 'deleteData'])->name('delete');
                Route::get('/',  [ContractsController::class, 'list'])->name('list');
                Route::get('/{id}',  [ContractsController::class, 'show'])->name('show');
            });


            //Operational Contract Routes

            Route::group(['prefix' => 'operational-contracts','as' => 'operational-contracts.',"namespace"=>"OperationalContracts"], function () {

                Route::get('/create',  [OperationalContractsController::class, 'create'])->name('create');
                Route::post('/subcontract/save', [OperationalContractsController::class, 'storeSubcontract'])->name('subcontract.store');
                Route::get('/delete-data/{id}', [OperationalContractsController::class, 'deleteData'])->name('delete');
                Route::get('/',  [OperationalContractsController::class, 'list'])->name('list');
                Route::get('fetch-unit-list', [OperationalContractsController::class, 'fetchUnitusingPropertyId'])->name('fetch-units');
                Route::get('fetch-tenant-list', [OperationalContractsController::class, 'fetchTenant'])->name('fetch-tenants');
                Route::match(array('GET','POST'), 'save-data', [OperationalContractsController::class, 'savecontractsPost'])->name('storedata');
                Route::get('/confirmation', [OperationalContractsController::class, 'confirmation'])->name('confirmation');

                Route::get('/{id}/{notification_read?}/{notification_id?}',  [OperationalContractsController::class, 'show'])->name('show');
                Route::match(array('GET','POST'), 'update-payment', [OperationalContractsController::class, 'makePayment'])->name('makepayment');

            });

            //Beneficiary Module Routes
           // Route::group(['prefix' => 'beneficiary','as' => 'beneficiary.',"namespace"=>"Beneficiary"], function () {
                /*Route::get('/create-info', [BeneficiaryController::class, 'createInfo'])->name('create.info');
                Route::post('/create-confirm', [BeneficiaryController::class, 'createConfirm'])->name('create.confirm');
                Route::get('/edit/{id}', [BeneficiaryController::class, 'editInfo'])->name('edit.info');
                Route::post('/edit-confirm', [BeneficiaryController::class, 'editConfirm'])->name('edit.confirm');*/
                /************update benificiary *****************/
                /*Route::post('/update', [BeneficiaryController::class, 'update'])->name('update');
                Route::get('/', [BeneficiaryController::class, 'list'])->name('list');
                Route::get('/{id}', [BeneficiaryController::class, 'show'])->name('show');*/
                /************added for unique checking ********************/
               // Route::post('/ajax/ajax_check_unique_beneficiary/{id?}', [BeneficiaryController::class, 'ajax_check_unique_beneficiary'])->name('ajax_check_unique_beneficiary');
                /************store benificiary *****************/
               // Route::post('/store', [BeneficiaryController::class, 'store'])->name('store');
                /************delete beneficiary ********************/
               // Route::get('/delete-beneficiary/{id}',[BeneficiaryController::class, 'deleteData'])->name('delete');
            //});

            Route::group(['prefix' => 'beneficiary','as' => 'beneficiary.','namespace' => 'beneficiary'],function(){
                Route::get('/list',  [NewBeneficiaryController::class, 'openBeneficirayList'])->name('openBeneficirayList')->middleware('beneficiary_permission');
                Route::get('/beneficiary/{token}',  [NewBeneficiaryController::class, 'openViewBeneficiary'])->name('openViewBeneficiary')->middleware('beneficiary_permission');
                Route::get('/create',  [NewBeneficiaryController::class, 'openCreateBeneficiary'])->name('openCreateBeneficiary')->middleware('beneficiary_permission');
                Route::get('/edit/{token}',  [NewBeneficiaryController::class, 'openEditBeneficiary'])->name('openEditBeneficiary')->middleware('beneficiary_permission');
            });

            // Advance Contract Controller Route
            // Route::group(['prefix' => 'advance-contracts','as' => 'advance-contracts.','namespace' => 'AdvanceContracts'],function(){
            //     Route::get('/create-data-agreement-kpi',  [AdvanceContractsController::class, 'openCreateDataAgreement'])->name('openCreateDataAgreement');
            // });

            //Tenant Management Module Routes
            Route::group(['prefix' => 'tenants','as' => 'tenants.',"namespace"=>"Tenants",'middleware'=>['user_role_privilege:!sp_admin,!supervisor']], function () {
                Route::get('/building-list', [TenantAdminController::class, 'buildinglist'])->name('buildinglist');
                Route::get('/building-list-ajax', [TenantAdminController::class, 'buildinglist'])->name('list.ajax');
                Route::get('/ComplexBuildingslist-list-ajax', [TenantAdminController::class, 'ComplexBuildingslist'])->name('ComplexBuildingslist.ajax');
                Route::get('/complex-building-list/{id}', [TenantAdminController::class, 'ComplexBuildingslist'])->name('ComplexBuildingslist');

                Route::get('appointments', [\App\Http\Controllers\Admin\Tenants\BookingController::class, 'index'])->name('appointments.list');

                Route::get('/building/{id}', [TenantAdminController::class, 'building_detail'])->name('building_detail');
                Route::get('/create-info/{id}', [TenantAdminController::class, 'createInfo'])->name('create.info');
                Route::match(array('GET','POST'),'/create-docuements/{id}', [TenantAdminController::class, 'saveInfo'])->name('save.info');
                Route::get('/upload-document', [TenantAdminController::class, 'showUploadDocument'])->name('show.upload.document');
                Route::post('/upload-document', [TenantAdminController::class, 'uploadDocument'])->name('upload.document');
                Route::get('/confirmation', [TenantAdminController::class, 'tenantConfirmation'])->name('confimation');
                Route::match(array('GET','POST'), '/save-data', [TenantAdminController::class, 'storeTenant'])->name('save-data');
                Route::get('/edit/{building_id}/{id}', [TenantAdminController::class, 'editInfo'])->name('edit.info');
                Route::match(array('GET','POST'),'/ducument-upload/{building_id}/{id}', [TenantAdminController::class, 'edit2'])->name('ducument.upload');
                Route::get('/editDocumentShow/{building_id}/{id}', [TenantAdminController::class, 'edituploadDocumentShow'])->name('edit.document.show');
                Route::post('/private-file-delete', [TenantAdminController::class, 'privatefile_delete'])->name('privatefile.delete');
                Route::match(array('GET','POST'),'/edit-upload-document', [TenantAdminController::class, 'edituploadDocument'])->name('edit.upload.document');
                Route::get('/edit-confirmation', [TenantAdminController::class, 'edittenantConfirmation'])->name('edit.confimation');
                Route::match(array('GET','POST'),'/update-data', [TenantAdminController::class, 'editstoreTenant'])->name('edit.update.data');
                Route::match(array('GET','POST'),'/editConfirmation', [TenantAdminController::class, 'edit3'])->name('edit3');
                Route::post('/edit-confirm', [TenantAdminController::class, 'editConfirm'])->name('edit.confirm');
                Route::post('/update', [TenantAdminController::class, 'update'])->name('update');
                Route::get('/show/{building_id}/{id?}', [TenantAdminController::class, 'show'])->name('show');
                Route::post('/store', [TenantAdminController::class, 'store'])->name('store');
                Route::get('/{building_id}/{id}', [TenantAdminController::class, 'editSharedFile'])->name('edit.sharefile');
                Route::post('/savesharefile', [TenantAdminController::class, 'saveSharedFile'])->name('save.sharefile');
                Route::get('/filelist', [TenantAdminController::class, 'filelist'])->name('filelist');
                Route::post('fileListAjax', [TenantAdminController::class, 'private_file_upload_ajax'])->name('upload.private_file_upload_ajax');
                Route::post('edit-shared-file', [TenantAdminController::class, 'edit_shared_file'])->name('sharedfile.edit');
                Route::post('fileListAjaxCount', [TenantAdminController::class, 'private_file_upload_ajax_count'])->name('upload.private_file_upload_ajax_count');
                Route::post('/fetch-tenant-units', [TenantAdminController::class, 'fetchTenantUnits'])->name('fetchtenantunits');
                Route::post('/delete-tenant-units', [TenantAdminController::class, 'deleteTenantUnitById'])->name('deletetenantunits');

            });
        });
        Route::group(['prefix' => 'data'], function () {
            include 'advance_contracts.php';
        });

        //Maintenance Request Module
        Route::group(['prefix'=>'maintenance-requests','as'=>'maintenance_requests.'],function(){
//            Route::get('/list/{notificationread?}/{notification_id?}/{maintanance_request_id?}', [MaintenanceAdminPortal::class,'list'])->name('list');
            Route::get('/list/{notificationread?}/{notification_id?}/{maintanance_request_id?}', [MaintenanceAdminPortal::class,'listLivewire'])->name('list');
            Route::get('/edit/{id}', [MaintenanceAdminPortal::class,'edit'])->name('edit');
            Route::post('/details', [MaintenanceAdminPortal::class,'details'])->name('details');
            Route::post('/reject', [MaintenanceAdminPortal::class,'reject'])->name('reject');
            Route::post('/list_post', [MaintenanceAdminPortal::class,'list'])->name('list_post');
            Route::post('/list_details', [MaintenanceAdminPortal::class,'MaintenanceRequestDetails'])->name('list_details');

            // List the grouped maintenance requests
//            Route::get('/grouped', [MaintenanceAdminPortal::class, 'list'])->name('list.grouped');
            Route::get('/grouped', [MaintenanceAdminPortal::class, 'listLivewire'])->name('list.grouped');
            // Get grouped maintenance requests using groupId and display in a popup
            Route::get('/grouped/{groupId}', [MaintenanceAdminPortal::class, 'groupedMaintenanceList'])->name('grouped.list');
            // Reject group of maintenance requests
            Route::post('/reject/group', [MaintenanceAdminPortal::class, 'rejectGroupedMaintenanceRequests'])->name('reject.group');
        });

        /*
        |--------------------------------------------------------------------------
        | Tenant Management Other Routes
        |--------------------------------------------------------------------------
        |
        | Note: The below routes should be included in the Data -> Tenant Management
        */
        Route::post('/shared-document-ajax', [TenantAdminController::class, 'sharedDocument'])->name('tenant.upload.shared_document');
        Route::get('/delete-sharefile/{id}',[TenantAdminController::class, 'deleteShareFile'])->name('delete');
        Route::get('/delete-tenants/{id}',[TenantAdminController::class, 'deleteData'])->name('deletetanent');
        Route::get('/send-sms-to-tenant/{id}',[TenantAdminController::class, 'sendSmsTenantEdit'])->name('sendsmstotenant');
        Route::get('/send-sms-to-all-buliding-all-tenant',[TenantAdminController::class, 'sendSmstoallbuldiingallTenant'])->name('sendsmstoallbulidingalltenant');
        Route::get('/send-sms-to-one-buliding-all-tenant/{id}',[TenantAdminController::class, 'sendSmstoonebuldiingallTenant'])->name('sendsmstoonebulidingalltenant');
        Route::get('/contactinformation_list/{id}',[TenantAdminController::class, 'contactinformation_list'])->name('contactinformation.show');
        Route::post('/contactinformationedit', [TenantAdminController::class, 'contactinformationedit'])->name('contactinformationedit');
        Route::get('/sharedfiles_list/{id}',[TenantAdminController::class, 'sharedfiles_list'])->name('sharedfiles.show');
        Route::get('/sharedfiles-list-ajax/{id}', [TenantAdminController::class, 'sharedfiles_list'])->name('sharedfile.ajax');
        Route::get('/upload-document/{id}', [TenantAdminController::class, 'shareduploaddocument'])->name('tenant.upload.document');
        Route::post('/upload-document', [TenantAdminController::class, 'sharedsavedocument'])->name('tenant.save.document');
        Route::get('/upload-confirmation/{id}', [TenantAdminController::class, 'documentConfirmation'])->name('tenant.document.confimation');
        Route::get('/upload-confirmation1/{id}', [TenantAdminController::class, 'documentConfirmation1'])->name('tenant.document.confimation1');

        /*
        |--------------------------------------------------------------------------
        | Select or Enter Project Module
        |--------------------------------------------------------------------------
        |
        | Note: These routes are used to switch between the project, selecting a particular project etc
        */
        Route::get('/get-ajax-data-client-industry', [SelectProjectController::class, 'getClientIndustry'])->name('switch.get_client_idustry');
        Route::get('/get-ajax-data-project-list', [SelectProjectController::class, 'getClientProjectList'])->name('switch.get_client_project');
        Route::post('/StoreProject', [SelectProjectController::class, 'storProject'])->name('switch.storeproject');
        Route::post('/create-project', [SelectProjectController::class, 'storeProjectByImage'])->name('switch.storeprojectbyimage');
        Route::post('/ajax/ajax_check_project_name_unique/{id?}',  [SelectProjectController::class, 'ajax_check_project_name_unique'])->name('ajax_check_project_name_unique');
        Route::post('/ajax/ajax_check_project_name_unique_arabic/{id?}',  [SelectProjectController::class, 'ajax_check_project_name_unique_arabic'])->name('ajax_check_project_name_unique_arabic');

        /*
        |--------------------------------------------------------------------------
        | Calendar Module
        |--------------------------------------------------------------------------
        |
        | Note: These routes should be grouped.
        */
        Route::get('/calendar/{id?}/{name?}/{search?}/{month?}', [CalendarController::class, 'index'])->name('calendar');
        Route::get('/calender_list_current_date_url/{date?}', [CalendarController::class, 'getTaskListCurrentDate'])->name('calender_list_current_date_url');
        Route::get('/calender_get_work_order_data/{date?}', [CalendarController::class, 'getWorkOrderForModal'])->name('calender_get_work_order_data');
        Route::get('/property_filter', [CalendarController::class, 'property_filter'])->name('property_filter');
        Route::get('/ppmrequests', [ManagePpmController::class, 'openManagePpmList'])->name('ppmrequests');
        Route::post('/export-ppm', [CalendarController::class, 'exportPpm'])->name('export-ppm');
        Route::get('ppmrequestListAjax/{id?}',  [CalendarController::class, 'fetchPpmRequestListAjax'])->name('ppmrequestlist.ajax');
        Route::post('/manage-ppm-approval', [CalendarController::class, 'markApprovePpmRequest'])->name('manage_ppm_approval.ajax');
        Route::get('fetch-assetnamesby-category/{id?}', [CalendarController::class, 'fetchAssetnamesbyCategory'])->name('fetch-assetnamesby-category');

        //Added for service provider
        Route::get('/serviceprovider', [ProviderController::class, 'index'])->name('serviceprovider.index');
        Route::get('serviceprovider-list-ajax/{id?}', [ProviderController::class, 'index'])->name('serviceprovider.list.ajax');

        /*
        |--------------------------------------------------------------------------
        | Dashboard Module
        |--------------------------------------------------------------------------
        |
        | Note: These routes should be grouped. The name of the controller should be changed to DashboardController
        */
        Route::get('/performance', [DashboardControllerNew::class, 'performance'])->name('service_provider.performance');
        Route::get('/performance-details/{id?}/{contractId?}', [PerformanceController::class, 'openPerformanceDetails'])->name('service_provider.performance_details');
        Route::get('service-provider-performance-list-ajax/{id?}', [DashboardControllerNew::class, 'performance'])->name('service_provider.performance.list.ajax');

        Route::get('/{date?}', [DashboardControllerNew::class, 'index'])->name('admin.dashboard');
        Route::get('/dashboards/admin', [DashboardControllerNew::class, 'index'])->name('admin.dashboard');
        Route::get('/dashboards/payment', [DashboardControllerNew::class, 'getPaymentDashboard'])->name('payment.dashboard');
        Route::get('/dashboards/get_filter_details', [DashboardControllerNew::class, 'getOpenDetails'])->name('admin.get_filter_details');
        Route::get('/dashboards/get_filter_details_closed', [DashboardControllerNew::class, 'getOpenDetailsClosed'])->name('admin.get_filter_details_closed');
        Route::get('/dashboards/blank-dashboard', [DashboardControllerNew::class, 'blankDashbaord'])->name('admin.blank_dashboard');
        Route::get('/dashboard/dash-ajax-bm-work-order',[DashboardControllerNew::class, 'getBuildingManagerWorkOrderList'])->name('ajax.bm-work-orderlist');
        Route::post('/dashboard/calculate-worker-percentage', [DashboardControllerNew::class, 'calculateWorkerPercentage'])->name('admin.dashboard.calculate-worker-percentage');


        //Added for notification
        Route::get('/notification/{id?}', [NotificationController::class, 'index'])->name('notification.index');
        Route::get('/dashboard/data', [DashboardControllerNew::class, 'getDashboardData'])->name('dashboard.data');

        Route::group(['prefix'=>'sales','as'=>'sales.'],function(){
            Route::get('account-type', [SystemSetupController::class, 'accountType'])->name('accountType');
            Route::get('account-industry', [SystemSetupController::class, 'accountIndustry'])->name('accountIndustry');
            Route::get('opportunities-stage', [SystemSetupController::class, 'opportunitiesStage'])->name('opportunitiesStage');
            Route::get('case-type', [SystemSetupController::class, 'caseType'])->name('caseType');
            Route::get('shipping-provider', [SystemSetupController::class, 'shippingProvider'])->name('shippingProvider');
            Route::get('document-type', [SystemSetupController::class, 'documentType'])->name('documentType');
            Route::get('document-folder', [SystemSetupController::class, 'documentFolder'])->name('documentFolder');

            Route::get('accounts', [SalesAccountsController::class, 'index'])->name('accounts');
            Route::get('accounts/{item_id}/edit', [SalesAccountsController::class, 'edit'])->name('editAccount');

            Route::get('contacts', [SaleContactController::class, 'index'])->name('contacts');
            Route::get('contacts/{item_id}/edit', [SaleContactController::class, 'edit'])->name('editContact');

            Route::get('opportunities', [OpportunityController::class, 'index'])->name('opportunities');
            Route::get('opportunities/{item_id}/edit', [OpportunityController::class, 'edit'])->name('editOpportunity');

            Route::get('quotes', [QuoteController::class, 'index'])->name('quotes');
            Route::get('quotes/{item_id}/edit', [QuoteController::class, 'edit'])->name('editQuote');
            Route::get('quotes/{item_id}', [QuoteController::class, 'view'])->name('viewQuote');

            Route::get('invoices', [SaleInvoiceController::class, 'index'])->name('invoices');
            Route::get('sales-invoices/{item_id}/edit', [SaleInvoiceController::class, 'edit'])->name('editInvoice');
            Route::get('invoices/{item_id}/view', [SaleInvoiceController::class, 'view'])->name('viewInvoice');

            Route::get('orders', [OrderController::class, 'index'])->name('orders');
            Route::get('orders/{item_id}/edit', [OrderController::class, 'edit'])->name('editOrder');
            Route::get('orders/{item_id}', [OrderController::class, 'view'])->name('viewOrder');

            Route::get('cases', [CaseController::class, 'index'])->name('cases');
            Route::get('cases/{item_id}/edit', [CaseController::class, 'edit'])->name('editCase');

            Route::get('streams', [StreamController::class, 'index'])->name('streams');

            Route::get('documents', [DocumentController::class, 'index'])->name('documents');
            Route::get('documents/{item_id}/edit', [DocumentController::class, 'edit'])->name('editDocument');

            Route::get('calls', [CallController::class, 'index'])->name('calls');
            Route::get('calls/{item_id}/edit', [CallController::class, 'edit'])->name('editCall');

            Route::get('meetings', [MeetingController::class, 'index'])->name('meetings');
            Route::get('meetings/{item_id}/edit', [MeetingController::class, 'edit'])->name('editMeeting');

            Route::get('quote-analytics', [ReportController::class, 'quote'])->name('quoteAnalytics');
            Route::get('invoice-analytics', [ReportController::class, 'invoice'])->name('invoiceAnalytics');
            Route::get('order-analytics', [ReportController::class, 'order'])->name('orderAnalytics');
        });

        /*
       |--------------------------------------------------------------------------
       | PerformanceIndicator Module
       |--------------------------------------------------------------------------
       |
       | Note: These routes should be grouped.
       */
        Route::get('/performance-indicator/index/{kpi?}', [PerformanceIndicatorController::class, 'index'])->name('performance-indicator.index');
        Route::post('/performance-indicator/index/{kpi?}', [PerformanceIndicatorController::class, 'index'])->name('performance-indicator.index');
        Route::get('/performance-indicator/debug/{contract_number?}', [PerformanceIndicatorController::class, 'debug'])->name('performance-indicator.debug');

      /*
        |--------------------------------------------------------------------------
        |  Asset Management Routes Groupe
        |--------------------------------------------------------------------------
        |
        | Note: - This group of routes is for the new Asset Management feature.
        |       - All related functionalities are handled by the AssetsManagementController.
        */
        Route::group(['prefix'=>'assets-managements','as'=>'asset-management.'],function(){
            Route::get('index', [AssetsManagementController::class,'index'])->name('index');
            Route::post('store', [AssetsManagementController::class,'store'])->name('store');
            Route::get('/get-floors', [AssetsManagementController::class, 'getFloors'])->name('get-floors');
            Route::get('details/{id}', [AssetsManagementController::class, 'details'])->name('details');
            Route::post('qrDownload',  [AssetsManagementController::class, 'initiateExportQr'])->name('qrdownload');
            Route::post('deleteBulkAssets',  [AssetsManagementController::class, 'deleteBulkAssets'])->name('deleteBulkAssets');
            Route::post('update',  [AssetsManagementController::class, 'asset_update'])->name('update');
            Route::post('transfer',  [AssetsManagementController::class, 'transfer_assset'])->name('transfer');
            Route::post('Verif-Asset-Number',[AssetsManagementController::class, 'verifAssetNumber'])->name('verifAssetNumber');
            Route::post('Verif-Asset-Tag', [AssetsManagementController::class, 'verifAssetTag'])->name('verifAssetTag');
            Route::get('edit-modal/{id}/{property_id}',  [AssetsManagementController::class, 'asset_edit_modal'])->name('asset_edit_modal');
            Route::get('asset-history/{id}', [AssetsManagementController::class, 'getHistoryByAsset'])->name('asset_history');
            Route::get('work-order/{id}', [AssetsManagementController::class, 'getWorkOrders'])->name('getWorkOrders');
            Route::get('history', [AssetsManagementController::class, 'getAllHistory'])->name('all_history');
            Route::get('view-asset-details/{encryptedAssetId}', [AssetsManagementController::class,'viewAssetDetails'])->name('viewAssetDetails');
            Route::get('qr-code-error', [AssetsManagementController::class, 'openQrCodeError'])->name('openQrCodeError');

        });
        Route::group(['prefix'=>'purchases/purchases-requests','as'=>'purchase-request.'],function(){
            Route::get('', [PurchaseRequestController::class,'index'])->name('index');
            Route::name('create.')
            ->group(function () {
                Route::get('create-add-company-information', [CreatePurchaseRequestController::class, 'createCompanyInformation'])->name('createCompanyInformation');
                Route::post('create-store-add-company-information', [CreatePurchaseRequestController::class, 'storeCompanyInformation'])->name('storeCompanyInformation');
                Route::get('create-add-billing-information', [CreatePurchaseRequestController::class, 'createBillingInformation'])->name('createBillingInformation');
                Route::post('create-store-billing-information', [CreatePurchaseRequestController::class, 'storeBillingInformation'])->name('storeBillingInformation');
                Route::get('create-items', [CreatePurchaseRequestController::class, 'createItems'])->name('createItems');
                Route::post('create-store-items', [CreatePurchaseRequestController::class, 'storeItems'])->name('storeItems');
                Route::get('create-advance', [CreatePurchaseRequestController::class, 'createAdvance'])->name('createAdvance');
                Route::post('create-store-advance', [CreatePurchaseRequestController::class, 'storeAdvance'])->name('storeAdvance');
                Route::get('create-confirmation', [CreatePurchaseRequestController::class, 'createConfirmation'])->name('createConfirmation');
                Route::post('create-store-confirmation', [CreatePurchaseRequestController::class, 'storeConfirmation'])->name('storeConfirmation');
            });
            Route::name('edit.')
            ->group(function () {
               Route::get('edit-company-information/{id}', [EditPurchaseRequestController::class, 'editCompanyInformation'])->name('editCompanyInformation');
               Route::post('edit-update-add-company-information/{id}', [EditPurchaseRequestController::class, 'updateCompanyInformation'])->name('updateCompanyInformation');
               Route::get('edit-billing-information/{id}', [EditPurchaseRequestController::class, 'editBillingInformation'])->name('editBillingInformation');
               Route::post('edit-update-billing-information/{id}', [EditPurchaseRequestController::class, 'updateBillingInformation'])->name('updateBillingInformation');
               Route::get('edit-items/{id}', [EditPurchaseRequestController::class, 'editItems'])->name('editItems');
               Route::post('edit-update-items/{id}', [EditPurchaseRequestController::class, 'updateItems'])->name('updateItems');
               Route::get('edit-advance/{id}', [EditPurchaseRequestController::class, 'editAdvance'])->name('editAdvance');
               Route::post('edit-update-advance/{id}', [EditPurchaseRequestController::class, 'updateAdvance'])->name('updateAdvance');
               Route::get('edit-confirmation/{id}', [EditPurchaseRequestController::class, 'editConfirmation'])->name('editConfirmation');
               Route::post('edit-update-confirmation/{id}', [EditPurchaseRequestController::class, 'updateConfirmation'])->name('updateConfirmation');
             });

            Route::get('show-{id}', [PurchaseRequestController::class,'show'])->name('show');
            Route::post('update-{id}', [PurchaseRequestController::class,'update'])->name('update');
            Route::post('delete-pr{id?}', [PurchaseRequestController::class, 'delete'])->name('delete');
            Route::post('delete', [PurchaseRequestController::class, 'deleteBulk'])->name('deleteall');
            Route::post('clone-Purchase', [PurchaseRequestController::class,'clone'])->name('clone');
            Route::post('updateStatus{id?}/setStatus/{status?}', [PurchaseRequestController::class,'updateStatus'])->name('updateStatus');
            Route::post('updateStatus', [PurchaseRequestController::class,'UpdateListStatus'])->name('UpdateAllStatus');
            Route::post('sentEmailToVendor{id?}', [PurchaseRequestController::class,'sentEmailToVendor'])->name('sentEmailToVendor');
            Route::post('SetBafoTag{id}', [PurchaseRequestController::class,'SetBafoTag'])->name('SetBafoTag');
            Route::post('ConvertToQuotation{id}', [PurchaseRequestController::class,'convertToQuotation'])->name('ConvertToQuotation');
          Route::post('/item/auto-purchase/{status}', [PurchaseRequestController::class, 'updateAutoPurchaseStatus'])->name('item.updateAutoPurchaseStatus');

        });
        Route::group(['prefix' => 'purchaseModule-setup-company'], function () {
            Route::get('Config', [\App\Http\Controllers\Admin\PurchaseAndSales\SetupCompany\ConfigController::class, 'index'])->name('sup-admin.purchase-config');
            Route::post('markAsConfigured', [\App\Http\Controllers\Admin\PurchaseAndSales\SetupCompany\ConfigController::class, 'markAsConfigured'])->name('purchase-history_mark_as_configured');

        });
        Route::group(['prefix'=>'purchases/quotations','as'=>'quotation.'],function(){
            Route::get('', [QuotationController::class,'index'])->name('index');
            Route::name('create.')
            ->group(function () {
                Route::get('create-add-company-information', [CreateQuotationController::class, 'createCompanyInformation'])->name('createCompanyInformation');
                Route::post('create-store-add-company-information', [CreateQuotationController::class, 'storeCompanyInformation'])->name('storeCompanyInformation');
                Route::get('create-add-billing-information', [CreateQuotationController::class, 'createBillingInformation'])->name('createBillingInformation');
                Route::post('create-store-billing-information', [CreateQuotationController::class, 'storeBillingInformation'])->name('storeBillingInformation');
                Route::get('create-items', [CreateQuotationController::class, 'createItems'])->name('createItems');
                Route::post('create-store-items', [CreateQuotationController::class, 'storeItems'])->name('storeItems');
                Route::get('create-advance', [CreateQuotationController::class, 'createAdvance'])->name('createAdvance');
                Route::post('create-store-advance', [CreateQuotationController::class, 'storeAdvance'])->name('storeAdvance');
                Route::get('create-confirmation', [CreateQuotationController::class, 'createConfirmation'])->name('createConfirmation');
                Route::post('create-store-confirmation', [CreateQuotationController::class, 'storeConfirmation'])->name('storeConfirmation');
            });
            Route::name('edit.')
            ->group(function () {
               Route::get('edit-company-information/{id}', [EditQuotationController::class, 'editCompanyInformation'])->name('editCompanyInformation');
               Route::post('edit-update-add-company-information/{id}', [EditQuotationController::class, 'updateCompanyInformation'])->name('updateCompanyInformation');
               Route::get('edit-billing-information/{id}', [EditQuotationController::class, 'editBillingInformation'])->name('editBillingInformation');
               Route::post('edit-update-billing-information/{id}', [EditQuotationController::class, 'updateBillingInformation'])->name('updateBillingInformation');
               Route::get('edit-items/{id}', [EditQuotationController::class, 'editItems'])->name('editItems');
               Route::post('edit-update-items/{id}', [EditQuotationController::class, 'updateItems'])->name('updateItems');
               Route::get('edit-advance/{id}', [EditQuotationController::class, 'editAdvance'])->name('editAdvance');
               Route::post('edit-update-advance/{id}', [EditQuotationController::class, 'updateAdvance'])->name('updateAdvance');
               Route::get('edit-confirmation/{id}', [EditQuotationController::class, 'editConfirmation'])->name('editConfirmation');
               Route::post('edit-update-confirmation/{id}', [EditQuotationController::class, 'updateConfirmation'])->name('updateConfirmation');
             });
            Route::name('bafo.')
            ->group(function () {
               Route::get('bafo-edit-items/{id}', [EditBafoQuotationController::class, 'editItems'])->name('editItems');
               Route::post('bafo-edit-update-items/{id}', [EditBafoQuotationController::class, 'updateItems'])->name('updateItems');

             });
            Route::get('show-{id}', [QuotationController::class,'show'])->name('show');
            Route::post('update-{id}', [QuotationController::class,'update'])->name('update');
            Route::post('delete', [QuotationController::class,'delete'])->name('delete');
            Route::post('clone', [QuotationController::class,'clone'])->name('clone');
            Route::post('updateStatus{id?}/setStatus/{status?}', [QuotationController::class,'updateStatus'])->name('setStatus.ajax');
            Route::post('updateStatus', [QuotationController::class,'UpdateListStatus'])->name('UpdateAllStatus');
            Route::post('appoveAndConvertToPO{id?}', [QuotationController::class,'appoveAndConvertToPO'])->name('appoveAndConvertToPO');
            Route::post('Request-BAFO-from-Vendor-{id}', [QuotationController::class,'requestBafoFromVendor'])->name('requestBafoFromVendor');
            Route::post('send-Bafo-By-Vendor-{id}', [QuotationController::class,'sendBafoByVendor'])->name('sendBafoByVendor');

            Route::post('sentEmailToCustomer{id?}', [QuotationController::class,'sentEmailToCustomer'])->name('sentEmailToCustomer');
        });
        Route::group(['prefix' => 'purchases/purchases-orders', 'as' => 'purchase-orders.'], function () {
            Route::get('', [PurchaseOrderController::class, 'index'])->name('index');

            // Create routes group
            Route::name('create.')
                ->group(function () {
                    Route::get('create-add-company-information', [CreatePurchaseOrderController::class, 'createCompanyInformation'])->name('createCompanyInformation');
                    Route::post('create-store-add-company-information', [CreatePurchaseOrderController::class, 'storeCompanyInformation'])->name('storeCompanyInformation');
                    Route::get('create-add-billing-information', [CreatePurchaseOrderController::class, 'createBillingInformation'])->name('createBillingInformation');
                    Route::post('create-store-billing-information', [CreatePurchaseOrderController::class, 'storeBillingInformation'])->name('storeBillingInformation');
                    Route::get('create-items', [CreatePurchaseOrderController::class, 'createItems'])->name('createItems');
                    Route::post('create-store-items', [CreatePurchaseOrderController::class, 'storeItems'])->name('storeItems');
                    Route::get('create-advance', [CreatePurchaseOrderController::class, 'createAdvance'])->name('createAdvance');
                    Route::post('create-store-advance', [CreatePurchaseOrderController::class, 'storeAdvance'])->name('storeAdvance');
                    Route::get('create-confirmation', [CreatePurchaseOrderController::class, 'createConfirmation'])->name('createConfirmation');
                    Route::post('create-store-confirmation', [CreatePurchaseOrderController::class, 'storeConfirmation'])->name('storeConfirmation');
                });

            // Edit routes group
            Route::name('edit.')
                ->group(function () {
                    Route::get('edit-company-information/{id}', [EditPurchaseOrderController::class, 'editCompanyInformation'])->name('editCompanyInformation');
                    Route::post('edit-update-add-company-information/{id}', [EditPurchaseOrderController::class, 'updateCompanyInformation'])->name('updateCompanyInformation');
                    Route::get('edit-billing-information/{id}', [EditPurchaseOrderController::class, 'editBillingInformation'])->name('editBillingInformation');
                    Route::post('edit-update-billing-information/{id}', [EditPurchaseOrderController::class, 'updateBillingInformation'])->name('updateBillingInformation');
                    Route::get('edit-items/{id}', [EditPurchaseOrderController::class, 'editItems'])->name('editItems');
                    Route::post('edit-update-items/{id}', [EditPurchaseOrderController::class, 'updateItems'])->name('updateItems');
                    Route::get('edit-advance/{id}', [EditPurchaseOrderController::class, 'editAdvance'])->name('editAdvance');
                    Route::post('edit-update-advance/{id}', [EditPurchaseOrderController::class, 'updateAdvance'])->name('updateAdvance');
                    Route::get('edit-confirmation/{id}', [EditPurchaseOrderController::class, 'editConfirmation'])->name('editConfirmation');
                    Route::post('edit-update-confirmation/{id}', [EditPurchaseOrderController::class, 'updateConfirmation'])->name('updateConfirmation');
                });

            // Single action routes
            Route::get('show-{id}', [PurchaseOrderController::class, 'show'])->name('show');
            Route::post('update-{id}', [PurchaseOrderController::class, 'update'])->name('update');
            Route::post('delete', [PurchaseOrderController::class, 'delete'])->name('delete');
            Route::post('clone', [PurchaseOrderController::class, 'clone'])->name('clone');
            Route::post('updateStatus{id?}/setStatus/{status?}', [PurchaseOrderController::class, 'updateStatus'])->name('setStatus.ajax');
            Route::post('sentEmailToCustomer{id?}', [PurchaseOrderController::class, 'sentEmailToCustomer'])->name('sentEmailToCustomer');
            Route::post('updateStatus', [PurchaseOrderController::class,'UpdateListStatus'])->name('UpdateAllStatus');
            Route::post('ConvertToBill{id}', [PurchaseOrderController::class,'ConvertToBill'])->name('ConvertToBill');
            Route::post('ConvertToSales{id}', [PurchaseOrderController::class,'convertPOToSalesOrder'])->name('convertPOToSalesOrder');
            Route::post('approve_amount{id}', [PurchaseOrderController::class,'approve_amount'])->name('approve_amount');

        });
        Route::group(['prefix' => 'purchases/bills', 'as' => 'purchases.bills.'], function () {
            // Replace SalesOrderController with BillsController
            Route::get('', [BillsController::class, 'index'])->name('index');

            // Create routes group
            Route::name('create.')
                ->group(function () {
                    Route::get('create-add-company-information', [CreateBillsController::class, 'createCompanyInformation'])->name('createCompanyInformation');
                    Route::post('create-store-add-company-information', [CreateBillsController::class, 'storeCompanyInformation'])->name('storeCompanyInformation');
                    Route::get('create-add-billing-information', [CreateBillsController::class, 'createBillingInformation'])->name('createBillingInformation');
                    Route::post('create-store-billing-information', [CreateBillsController::class, 'storeBillingInformation'])->name('storeBillingInformation');
                    Route::get('create-items', [CreateBillsController::class, 'createItems'])->name('createItems');
                    Route::post('create-store-items', [CreateBillsController::class, 'storeItems'])->name('storeItems');
                    Route::get('create-advance', [CreateBillsController::class, 'createAdvance'])->name('createAdvance');
                    Route::post('create-store-advance', [CreateBillsController::class, 'storeAdvance'])->name('storeAdvance');
                    Route::get('create-confirmation', [CreateBillsController::class, 'createConfirmation'])->name('createConfirmation');
                    Route::post('create-store-confirmation', [CreateBillsController::class, 'storeConfirmation'])->name('storeConfirmation');
                });

            // Edit routes group
            Route::name('edit.')
                ->group(function () {
                    Route::get('edit-company-information/{id}', [EditBillsController::class, 'editCompanyInformation'])->name('editCompanyInformation');
                    Route::post('edit-update-add-company-information/{id}', [EditBillsController::class, 'updateCompanyInformation'])->name('updateCompanyInformation');
                    Route::get('edit-billing-information/{id}', [EditBillsController::class, 'editBillingInformation'])->name('editBillingInformation');
                    Route::post('edit-update-billing-information/{id}', [EditBillsController::class, 'updateBillingInformation'])->name('updateBillingInformation');
                    Route::get('edit-items/{id}', [EditBillsController::class, 'editItems'])->name('editItems');
                    Route::post('edit-update-items/{id}', [EditBillsController::class, 'updateItems'])->name('updateItems');
                    Route::get('edit-advance/{id}', [EditBillsController::class, 'editAdvance'])->name('editAdvance');
                    Route::post('edit-update-advance/{id}', [EditBillsController::class, 'updateAdvance'])->name('updateAdvance');
                    Route::get('edit-confirmation/{id}', [EditBillsController::class, 'editConfirmation'])->name('editConfirmation');
                    Route::post('edit-update-confirmation/{id}', [EditBillsController::class, 'updateConfirmation'])->name('updateConfirmation');
                });

            // Single action routes
            Route::get('show-{id}', [BillsController::class, 'show'])->name('show');
            Route::post('update-{id}', [BillsController::class, 'update'])->name('update');
            Route::post('delete', [BillsController::class, 'delete'])->name('delete');
            Route::post('clone', [BillsController::class, 'clone'])->name('clone');
            Route::post('updateStatus{id?}/setStatus/{status?}', [BillsController::class, 'updateStatus'])->name('setStatus.ajax');
            Route::post('sentEmailToCustomer{id?}', [BillsController::class, 'sentEmailToCustomer'])->name('sentEmailToCustomer');
            Route::post('ConvertToStock{id?}', [BillsController::class, 'ConvertToStock'])->name('ConvertToStock');
            Route::post('updateStatus', [BillsController::class,'UpdateListStatus'])->name('UpdateAllStatus');

        });

        Route::group(['prefix' => 'sales/sales-orders', 'as' => 'sales-orders.'], function () {
            Route::get('', [SalesOrderController::class, 'index'])->name('index');
            // Create routes group
            Route::name('create.')
                ->group(function () {
                    Route::get('create-add-company-information', [CreateSalesOrderController::class, 'createCompanyInformation'])->name('createCompanyInformation');
                    Route::post('create-store-add-company-information', [CreateSalesOrderController::class, 'storeCompanyInformation'])->name('storeCompanyInformation');
                    Route::get('create-add-billing-information', [CreateSalesOrderController::class, 'createBillingInformation'])->name('createBillingInformation');
                    Route::post('create-store-billing-information', [CreateSalesOrderController::class, 'storeBillingInformation'])->name('storeBillingInformation');
                    Route::get('create-items', [CreateSalesOrderController::class, 'createItems'])->name('createItems');
                    Route::post('create-store-items', [CreateSalesOrderController::class, 'storeItems'])->name('storeItems');
                    Route::get('create-advance', [CreateSalesOrderController::class, 'createAdvance'])->name('createAdvance');
                    Route::post('create-store-advance', [CreateSalesOrderController::class, 'storeAdvance'])->name('storeAdvance');
                    Route::get('create-confirmation', [CreateSalesOrderController::class, 'createConfirmation'])->name('createConfirmation');
                    Route::post('create-store-confirmation', [CreateSalesOrderController::class, 'storeConfirmation'])->name('storeConfirmation');
                });
            // Edit routes group
            Route::name('edit.')
                ->group(function () {
                    Route::get('edit-company-information/{id}', [EditSalesOrderController::class, 'editCompanyInformation'])->name('editCompanyInformation');
                    Route::post('edit-update-add-company-information/{id}', [EditSalesOrderController::class, 'updateCompanyInformation'])->name('updateCompanyInformation');
                    Route::get('edit-billing-information/{id}', [EditSalesOrderController::class, 'editBillingInformation'])->name('editBillingInformation');
                    Route::post('edit-update-billing-information/{id}', [EditSalesOrderController::class, 'updateBillingInformation'])->name('updateBillingInformation');
                    Route::get('edit-items/{id}', [EditSalesOrderController::class, 'editItems'])->name('editItems');
                    Route::post('edit-update-items/{id}', [EditSalesOrderController::class, 'updateItems'])->name('updateItems');
                    Route::get('edit-advance/{id}', [EditSalesOrderController::class, 'editAdvance'])->name('editAdvance');
                    Route::post('edit-update-advance/{id}', [EditSalesOrderController::class, 'updateAdvance'])->name('updateAdvance');
                    Route::get('edit-confirmation/{id}', [EditSalesOrderController::class, 'editConfirmation'])->name('editConfirmation');
                    Route::post('edit-update-confirmation/{id}', [EditSalesOrderController::class, 'updateConfirmation'])->name('updateConfirmation');
                });
            // Single action routes
            Route::get('show-{id}', [SalesOrderController::class, 'show'])->name('show');
            Route::post('update-{id}', [SalesOrderController::class, 'update'])->name('update');
            Route::post('delete', [SalesOrderController::class, 'delete'])->name('delete');
            Route::post('clone', [SalesOrderController::class, 'clone'])->name('clone');
            Route::post('updateStatus{id?}/setStatus/{status?}', [SalesOrderController::class, 'updateStatus'])->name('updateStatus');
            Route::post('sentEmailToCustomer{id?}', [SalesOrderController::class, 'sentEmailToCustomer'])->name('sentEmailToCustomer');
            Route::post('updateStatus', [SalesOrderController::class,'UpdateListStatus'])->name('UpdateAllStatus');
            Route::post('appoveAndConvertToPO{id?}', [SalesOrderController::class,'appoveAndConvertToPO'])->name('appoveAndConvertToPO');
            Route::post('ConvertToInvoice{id?}', [SalesOrderController::class,'ConvertToInvoice'])->name('ConvertToInvoice');

        });
        Route::group(['prefix' => 'sales/invoice-Doc', 'as' => 'invoice.'], function () {
            Route::get('', [InvoiceController::class, 'index'])->name('index');
            // Create routes group
            Route::name('create.')
                ->group(function () {
                    Route::get('create-add-company-information', [CreateInvoiceController::class, 'createCompanyInformation'])->name('createCompanyInformation');
                    Route::post('create-store-add-company-information', [CreateInvoiceController::class, 'storeCompanyInformation'])->name('storeCompanyInformation');
                    Route::get('create-add-billing-information', [CreateInvoiceController::class, 'createBillingInformation'])->name('createBillingInformation');
                    Route::post('create-store-billing-information', [CreateInvoiceController::class, 'storeBillingInformation'])->name('storeBillingInformation');
                    Route::get('create-items', [CreateInvoiceController::class, 'createItems'])->name('createItems');
                    Route::post('create-store-items', [CreateInvoiceController::class, 'storeItems'])->name('storeItems');
                    Route::get('create-advance', [CreateInvoiceController::class, 'createAdvance'])->name('createAdvance');
                    Route::post('create-store-advance', [CreateInvoiceController::class, 'storeAdvance'])->name('storeAdvance');
                    Route::get('create-confirmation', [CreateInvoiceController::class, 'createConfirmation'])->name('createConfirmation');
                    Route::post('create-store-confirmation', [CreateInvoiceController::class, 'storeConfirmation'])->name('storeConfirmation');
                });
            // Edit routes group
            Route::name('edit.')
                ->group(function () {
                    Route::get('edit-company-information/{id}', [EditInvoiceController::class, 'editCompanyInformation'])->name('editCompanyInformation');
                    Route::post('edit-update-add-company-information/{id}', [EditInvoiceController::class, 'updateCompanyInformation'])->name('updateCompanyInformation');
                    Route::get('edit-billing-information/{id}', [EditInvoiceController::class, 'editBillingInformation'])->name('editBillingInformation');
                    Route::post('edit-update-billing-information/{id}', [EditInvoiceController::class, 'updateBillingInformation'])->name('updateBillingInformation');
                    Route::get('edit-items/{id}', [EditInvoiceController::class, 'editItems'])->name('editItems');
                    Route::post('edit-update-items/{id}', [EditInvoiceController::class, 'updateItems'])->name('updateItems');
                    Route::get('edit-advance/{id}', [EditInvoiceController::class, 'editAdvance'])->name('editAdvance');
                    Route::post('edit-update-advance/{id}', [EditInvoiceController::class, 'updateAdvance'])->name('updateAdvance');
                    Route::get('edit-confirmation/{id}', [EditInvoiceController::class, 'editConfirmation'])->name('editConfirmation');
                    Route::post('edit-update-confirmation/{id}', [EditInvoiceController::class, 'updateConfirmation'])->name('updateConfirmation');
                });
            // Single action routes
            Route::get('show-{id}', [InvoiceController::class, 'show'])->name('show');
            Route::post('update-{id}', [InvoiceController::class, 'update'])->name('update');
            Route::post('delete', [InvoiceController::class, 'delete'])->name('delete');
            Route::post('clone', [InvoiceController::class, 'clone'])->name('clone');
            Route::post('updateStatus{id?}/setStatus/{status?}', [InvoiceController::class, 'updateStatus'])->name('updateStatus');
            Route::post('sentEmailToCustomer{id?}', [InvoiceController::class, 'sentEmailToCustomer'])->name('sentEmailToCustomer');
            Route::post('updateStatus', [InvoiceController::class,'UpdateListStatus'])->name('UpdateAllStatus');


        });

        Route::group(['prefix'=>'crm','as'=>'crm.'],function(){

            Route::get('/dashboard', [DashboardController::class, 'Dashboard'])->name('dashboard');
            Route::get('/deals_dashboard', [DashboardController::class, 'dealsDashboard'])->name('deals_dashboard');
            Route::get('/leads_dashboard', [DashboardController::class, 'leadsDashboard'])->name('leads_dashboard');

            Route::name('deals.')->group(function() {
                Route::get('deals/details/{leadId}', [\App\Http\Controllers\Admin\CRM\DealController::class, 'details'])->name('details');
                Route::get('deals', [\App\Http\Controllers\Admin\CRM\DealController::class, 'index'])->name('list');
                Route::get('deals/form/{dealID?}', [\App\Http\Controllers\Admin\CRM\LeadController::class, 'form'])->name('form');
                Route::get('deals/stages', [\App\Http\Controllers\Admin\CRM\DealStageController::class, 'index'])->name('stages');
            });

            Route::name('users.')->group(function(){
                Route::get('users', [\App\Http\Controllers\Admin\CRM\UserController::class, 'index'])->name('list');
                Route::get('ajax-list', [\App\Http\Controllers\Admin\CRM\UserController::class, 'userListAjax'])->name('ajax-list');
                Route::get('ajax-list-search', [\App\Http\Controllers\Admin\CRM\UserController::class, 'userListSearchAjax'])->name('ajax-list-search');
                Route::get('users/create', [\App\Http\Controllers\Admin\CRM\UserController::class, 'create'])->name('create');
                Route::post('users', [\App\Http\Controllers\Admin\CRM\UserController::class, 'store'])->name('store');
                Route::get('users/{userId}/edit', [\App\Http\Controllers\Admin\CRM\UserController::class, 'edit'])->name('edit');
                Route::put('users/{userId}', [\App\Http\Controllers\Admin\CRM\UserController::class, 'update'])->name('update');
                Route::delete('users/{userId}', [\App\Http\Controllers\Admin\CRM\UserController::class, 'destroy'])->name('destroy');
                Route::post('users/search', [\App\Http\Controllers\Admin\CRM\UserController::class, 'search'])->name('search');

            });
            Route::name('leads.')->group(function() {
                Route::get('leads', [\App\Http\Controllers\Admin\CRM\LeadController::class, 'index'])->name('list');
                Route::get('leads/details/{leadId}', [\App\Http\Controllers\Admin\CRM\LeadController::class, 'details'])->name('details');
                Route::get('leads/form/{leadId?}', [\App\Http\Controllers\Admin\CRM\LeadController::class, 'form'])->name('form');
                Route::get('leads/stages', [\App\Http\Controllers\Admin\CRM\LeadStageController::class, 'index'])->name('stages');
            });
            Route::name('pipelines.')->group(function() {
                Route::get('pipelines', [PipelineController::class, 'listPipelines'])->name('list');  // This is correct for listing pipelines
                Route::get('pipelines/tags/{workspaceSlug}', [PipelineTagsController::class, 'listPipelinesTags'])->name('listPipelinesTags');
            });
            Route::get('sources', [SourcesController::class, 'listSources'])->name('sources.list');

            Route::name('omnichat.')->group(function() {
                Route::get('omnichat/{slug}', [\App\Http\Controllers\CRM\OmniController::class, 'omniChat'])->name('omniChat');
                Route::get('omni-configuration',  [ConfigurationController::class, 'crmConfiguration'])->name('omni-configuration');

            });
        });
        Route::group(['prefix'=>'CRMProjects','as'=>'CRMProjects.'],function(){
            Route::get('dashboard', [\App\Http\Controllers\CRMProjects\ProjectController::class, 'dashboard'])->name('dashboard');
            Route::get('List', [\App\Http\Controllers\CRMProjects\ProjectController::class, 'list'])->name('list');
            Route::get('/details/{id}', [\App\Http\Controllers\CRMProjects\ProjectController::class, 'details'])->name('details');
            Route::get('{id}/task-board', [TaskBoardController::class, 'index'])->name('task-board');
            Route::get('{id}/task-board-list-view', [TaskBoardController::class, 'listView'])->name('task-board-list-view');
            Route::get('{id}/incident-report', [BugReportController::class, 'index'])->name('incident-report');
            Route::get('{id}/incident-report-list-view', [BugReportController::class, 'listView'])->name('incident-report-list-view');
            Route::get('system-setup/incident-stages', [ProjectSystemSetupController::class, 'incidentStages'])->name('system-setup.incident-stages');
            Route::get('{id}/calendar', [ProjectController::class, 'calendar'])->name('calendar');
            Route::get('system-setup/task-stages', [ProjectSystemSetupController::class, 'taskStages'])->name('system-setup.task-stages');

            Route::group(['prefix' => 'finance-manage', 'as' => 'finance-manage.'], function () {
                Route::get('proposol/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'index'])->name('proposol');
                Route::get('invoice/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'index'])->name('invoice');
                Route::get('bill/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'index'])->name('bill');
                Route::group(['prefix' => 'details', 'as' => 'details.'], function () {
                    Route::get('{projectId?}/proposol/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'details'])->name('proposol');
                    Route::get('{projectId?}/invoice/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'details'])->name('invoice');
                    Route::get('{projectId?}/bill/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'details'])->name('bill');
                });
                Route::group(['prefix' => 'details', 'as' => 'details.'], function () {
                    Route::get('{projectId?}/proposol/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'details'])->name('proposol');
                    Route::get('{projectId?}/invoice/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'details'])->name('invoice');
                    Route::get('{projectId?}/bill/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'details'])->name('bill');
                });
                Route::group(['prefix' => 'create', 'as' => 'create.'], function () {
                    Route::get('proposol/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'create'])->name('proposol');
                    Route::get('invoice/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'create'])->name('invoice');
                    Route::get('bill/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'create'])->name('bill');
                });
                Route::group(['prefix' => 'edit', 'as' => 'edit.'], function () {
                    Route::get('{projectId?}/proposol/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'edit'])->name('proposol');
                    Route::get('{projectId?}/invoice/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'edit'])->name('invoice');
                    Route::get('{projectId?}/bill/{id?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'edit'])->name('bill');
                });

                Route::group(['prefix' => 'attachments', 'as' => 'attachments.'], function () {
                    Route::post('upload-attachments/{link?}', [\App\Http\Controllers\CRMProjects\ManageFinanceController::class, 'uploadAttachments'])->name('uploadAttachments');
                });
            });

            Route::group(['prefix' => 'charts', 'as' => 'charts.'], function () {
                Route::get('/gant/{projectId}', [GantChartController::class, 'openGantChart'])->name('openGantChart');
            });
        });


        Route::group(['prefix'=>'CRMProjects/reports','as'=>'crm.projects.reports.'],function(){
            Route::get('', [\App\Http\Controllers\CRMProjects\ProjectReportController::class, 'index'])->name('index');
            Route::get( "token", [\App\Http\Controllers\CRMProjects\ProjectReportController::class, 'token']);
            Route::get("{id}", [\App\Http\Controllers\CRMProjects\ProjectReportController::class, 'show'])->name('show');
        });
        Route::group(['prefix'=>'CRMProjects/templates','as'=>'crm.projects.templates.'],function(){
            Route::get('', [ProjectTemplateController::class, 'index'])->name('cards');
            Route::get('list', [ProjectTemplateController::class, 'list'])->name('list');
            Route::get('{id}/view', [ProjectTemplateController::class, 'view'])->name('view');
        });


        Route::post('/clear-delete-session-Aku', [DashboardControllerNew::class, 'clearSessionAkauntingObjectForDelete'])->name('clearSessionAkauntingObjectForDelete');
    });

    // Finance Module Routes
    Route::group(['prefix' => 'finance', 'as' => 'finance.'], function () {
        // Income management routes
        Route::get('income', [IncomeController::class, 'index'])->name('income');
        Route::get('income/export', [IncomeController::class, 'export'])->name('income.export');
        Route::get('income/{id}', [IncomeController::class, 'show'])->name('income.show');

        // Customer management routes
        Route::get('customers', [CustomerController::class, 'index'])->name('customers');
        Route::get('customers/{id}', [CustomerController::class, 'show'])->name('customers.show');

        Route::get('bank-account', [BankAccountController::class, 'index'])->name('bank-account');

        Route::get('chart-account', [ChartAccountController::class, 'index'])->name('chart-account');

        Route::get('transfer', [TransferController::class, 'index'])->name('transfer');
        Route::get('credit-note', [CreditNoteController::class, 'index'])->name('credit-note');
        Route::get('debit-note', [DebitNoteController::class, 'index'])->name('debit-note');
        Route::get('payment', [PaymentsController::class, 'index'])->name('payment');

        Route::get('budget-planner', [BudgetPlannerController::class, 'index'])->name('budget-planner');
        Route::get('budget-planner/create', [BudgetPlannerController::class, 'create'])->name('budget-planner.create');
        Route::get('budget-planner/edit/{id}', [BudgetPlannerController::class, 'edit'])->name('budget-planner.edit');
        Route::get('budget-planner/view/{id}', [BudgetPlannerController::class, 'view'])->name('budget-planner.view');

        Route::get('bill', [BillController::class, 'index'])->name('bill');
        Route::get('bill/create', [BillController::class, 'create'])->name('bill.create');
        Route::get('bill/edit/{id}', [BillController::class, 'edit'])->name('bill.edit');
        Route::get('bill/view/{id}', [BillController::class, 'view'])->name('bill.view');


        Route::get('invoice', [AccountingInvoiceController::class, 'index'])->name('invoice');
        Route::get('invoice/create', [AccountingInvoiceController::class, 'create'])->name('invoice.create');
        Route::get('invoice/edit/{id}', [AccountingInvoiceController::class, 'edit'])->name('invoice.edit');
        Route::get('invoice/view/{id}', [AccountingInvoiceController::class, 'view'])->name('invoice.view');


        // Goal management routes
        Route::get('goals', [\App\Http\Controllers\Admin\Finance\GoalController::class, 'index'])->name('goals');
        Route::get('goals/export', [\App\Http\Controllers\Admin\Finance\GoalController::class, 'export'])->name('goals.export');
        Route::get('goals/{id}', [\App\Http\Controllers\Admin\Finance\GoalController::class, 'show'])->name('goals.show');

        // Journal management routes
        Route::get('journals', [\App\Http\Controllers\Admin\Finance\JournalController::class, 'index'])->name('journals');
        Route::get('journals/create', [\App\Http\Controllers\Admin\Finance\JournalController::class, 'create'])->name('journals.create');
        Route::get('journals/{id}/edit', [\App\Http\Controllers\Admin\Finance\JournalController::class, 'edit'])->name('journals.edit');
        Route::get('journals/export', [\App\Http\Controllers\Admin\Finance\JournalController::class, 'export'])->name('journals.export');
        Route::get('journals/{id}', [\App\Http\Controllers\Admin\Finance\JournalController::class, 'show'])->name('journals.show');
    });

     Route::group(['prefix'=>'marketplace','as'=>'marketplace.'],function(){

        Route::get('/service-request-list', [ServiceRequestController::class,'list'])->name('service-request-list');

        Route::get('/service-request-detail/{id}', [ServiceRequestController::class,'detail'])->name('service-request-detail');

    });


    //compaints module routes
    Route::group(['prefix'=>'complaints','as'=>'complaints.'],function(){
        // Get the list of the complaints
        Route::get('/list', [ComplaintsController::class,'list'])->name('list');
        // open the add page
        Route::get('/add', [ComplaintsController::class,'add'])->name('add');
        // Get the contracts using the ajax call
        Route::get('/get-contracts/{serviceProviderId}', [ComplaintsController::class, 'getContracts'])->name('get-contracts');
        // Get the workorders using the ajax call
        Route::get('/get-workorders/{contractId}', [ComplaintsController::class, 'getWorkorders'])->name('get-workorders');
        // Route to store the complaint
        Route::post('/store', [ComplaintsController::class, 'store'])->name('store');
        // Route to show the confirmation page
        Route::get('/confirmation', [ComplaintsController::class,'confirmation'])->name('confirmation');
        // Route to store the complaint
        Route::post('/store-confirmation', [ComplaintsController::class, 'storeConfirmation'])->name('store-confirmation');
        // Route for approving complaint
        Route::post('/approve/{id}', [ComplaintsController::class, 'approve'])->name('approve');
        // Route for rejecting complaint
        Route::post('/reject', [ComplaintsController::class, 'reject'])->name('reject');
        // Route for fetching complaint details
        Route::get('/details/{id}', [ComplaintsController::class, 'details'])->name('details');
        // Route for closing complaint
        Route::post('/close', [ComplaintsController::class, 'close'])->name('close');
    });


    Route::group(['prefix'=>'psp-registration-requests'],function(){
        Route::get('/list',[ManageRegistrationRequestsController::class,'index'])->name('psp-registration-requests.index');
        Route::get('/details/{id}',[ManageRegistrationRequestsController::class,'details'])->name('psp-registration-requests.details');
        Route::post('/change_application_status/{id}/{status}',[ManageRegistrationRequestsController::class,'change_application_status'])->name('psp-registration-requests.change_status');
        Route::post('/ajax/reject_application',[ManageRegistrationRequestsController::class,'rejectApplication'])->name('psp-registration-requests.rejectApplication');
    });
});

// Route::get('/advanced-contracts/main_information', function(){
//     return view('applications.admin.advanced-contracts.main_information');
// });
// Route::get('/advanced-contracts/data_agreement_kpi', function(){
//     return view('applications.admin.advanced-contracts.data_agreement_kpi');
// });
// Route::get('/advanced-contracts/meta-connection-admin', function(){
//     return view('applications.admin.advanced-contracts.meta-connection-admin');
// });
// Route::get('/advanced-contracts/assets_ppm', function(){
//     return view('applications.admin.advanced-contracts.assets_ppm');
// });
// Route::get('/advanced-contracts/workforce_team', function(){
//     return view('applications.admin.advanced-contracts.workforce_team');
// });
// Route::get('/advanced-contracts/extras', function(){
//     return view('applications.admin.advanced-contracts.extras');
// });
// Route::get('/advanced-contracts/confirmation', function(){
//     return view('applications.admin.advanced-contracts.confirmation');
// });

Route::get('/advanced-contracts/details', function(){
    return view('applications.admin.advanced-contracts.advance_contract_details');
});
//Error Controller
Route::get('404', [ErrorController::class, 'index'])->name('error');
Route::get('maintenance', [ErrorController::class, 'maintenance'])->name('maintenance');

// Facebook login routes
Route::get('/auth/redirect/{provider}', [SocialController::class, 'redirect']);
Route::get('/callback/{provider}', [SocialController::class, 'callback']);
// Google login routes
Route::get('auth/google', [GoogleLoginController::class, 'redirectToGoogle']);
Route::get('auth/google/callback', [GoogleLoginController::class, 'handleGoogleCallback']);
Route::get('/advanced-contracts/advance_contract_variation_details', function(){
    return view('applications.admin.advanced-contracts.advance_contract_variation_details');
});
Route::get('/advanced-contracts/advance_contract_variation_changes', function(){
    return view('applications.admin.advanced-contracts.advance_contract_variation_changes');
});
// Layout set routes
Route::get('setlayout/{layout}', function ($layout) {
    if (in_array($layout, \Config::get('app.layouts'))) {
        session(['layout' => $layout]);
    }
    return redirect()->back();
});

Auth::routes();
Route::group(['prefix'=>'supervisor','as'=>'supervisor.'],function(){
    Route::get('/create-user/{supervisor?}', [UserControllerNew::class, 'createInfo'])->name('create.user');
});

// @ add protected chat file routes
require __DIR__.'/protected/chat_file.php';
Route::get('admin-notification', function () {
	event(new App\Events\AdminNotification());
	return "called admin notification";
});


