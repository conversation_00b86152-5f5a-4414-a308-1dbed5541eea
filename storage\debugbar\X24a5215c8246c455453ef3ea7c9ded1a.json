{"__meta": {"id": "X24a5215c8246c455453ef3ea7c9ded1a", "datetime": "2025-07-27 12:02:55", "utime": 1753606975.251174, "method": "POST", "uri": "/workspace/manage-releases/store-release-notes-seen", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[12:02:55] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753606975.167467, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753606973.327248, "end": 1753606975.251225, "duration": 1.9239768981933594, "duration_str": "1.92s", "measures": [{"label": "Booting", "start": 1753606973.327248, "relative_start": 0, "end": 1753606975.109474, "relative_end": 1753606975.109474, "duration": 1.7822258472442627, "duration_str": "1.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753606975.109485, "relative_start": 1.7822368144989014, "end": 1753606975.251229, "relative_end": 4.0531158447265625e-06, "duration": 0.14174413681030273, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 36915368, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST workspace/manage-releases/store-release-notes-seen", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\Workspace\\ReleaseNotesController@storeReleaseNotesSeen", "as": "workspace.manage-releases.store.release.notes.seen", "namespace": "App\\Http\\Controllers\\Admin\\Workspace", "prefix": "workspace/manage-releases", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\Workspace\\ReleaseNotesController.php&line=801\">\\app\\Http\\Controllers\\Admin\\Workspace\\ReleaseNotesController.php:801-814</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.008749999999999999, "accumulated_duration_str": "8.75ms", "statements": [{"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00428, "duration_str": "4.28ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 48.914}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7070 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_test_db", "start_percent": 48.914, "width_percent": 12.457}, {"sql": "insert into `release_notes_users_seens` (`version`, `user_id`, `updated_at`, `created_at`) values ('00', 7070, '2025-07-27 12:02:55', '2025-07-27 12:02:55')", "type": "query", "params": [], "bindings": ["00", "7070", "2025-07-27 12:02:55", "2025-07-27 12:02:55"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\Workspace\\ReleaseNotesController.php", "line": 811}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0033799999999999998, "duration_str": "3.38ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\Workspace\\ReleaseNotesController.php:811", "connection": "osool_test_db", "start_percent": 61.371, "width_percent": 38.629}]}, "models": {"data": {"App\\Models\\UserCompany": 1, "App\\Models\\User": 1}, "count": 2}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu", "captcha_answer": "14", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=X5f673c4b71d77902f95ed17162072935&op=get\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7070", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/workspace/manage-releases/store-release-notes-seen", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2079620482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2079620482\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1365968018 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str title=\"2 characters\">00</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365968018\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Inh1WlRGYy9oOEpEV2tMOUdiVjN4a0E9PSIsInZhbHVlIjoiR1ZyN2JiNys3VWY0dVFxMnR4azhhcFdpQnlNUE9QUjdlaUtVdmlHV0ZOZW4zOFM5dlRZOStmV3dLRXNkN1Z1Q1RqTTNKTlBNMGk0dzhoY2hQUnVINWsrM0l3MDJ0SnN2eFJ0OCtLVjRMUTdTZ0dRVm5JZ2dUeDZqK1U5aWZ1K2MiLCJtYWMiOiJkZDEyNWI4MDgzNzI1NWE1Y2Q0YzhhMGI1YTAxMGVkMGFkMzU4OGEzN2E4ZGM2YThkMTkzM2MxM2E2ZTliNGNkIiwidGFnIjoiIn0%3D; osool_session=hkNrb9FJ1kqTEMf1QHtNAqHAQi92HkpWnXkHEhjw</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Inh1WlRGYy9oOEpEV2tMOUdiVjN4a0E9PSIsInZhbHVlIjoiR1ZyN2JiNys3VWY0dVFxMnR4azhhcFdpQnlNUE9QUjdlaUtVdmlHV0ZOZW4zOFM5dlRZOStmV3dLRXNkN1Z1Q1RqTTNKTlBNMGk0dzhoY2hQUnVINWsrM0l3MDJ0SnN2eFJ0OCtLVjRMUTdTZ0dRVm5JZ2dUeDZqK1U5aWZ1K2MiLCJtYWMiOiJkZDEyNWI4MDgzNzI1NWE1Y2Q0YzhhMGI1YTAxMGVkMGFkMzU4OGEzN2E4ZGM2YThkMTkzM2MxM2E2ZTliNGNkIiwidGFnIjoiIn0%3D; osool_session=hkNrb9FJ1kqTEMf1QHtNAqHAQi92HkpWnXkHEhjw</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52063</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"51 characters\">/workspace/manage-releases/store-release-notes-seen</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"51 characters\">/workspace/manage-releases/store-release-notes-seen</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753606973.3272</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753606973</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2133376697 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133376697\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-223271701 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 27 Jul 2025 09:02:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im9JTFd1ZTdJamEzazF3bVlJeEl1ZGc9PSIsInZhbHVlIjoiQkV4SGFVUXJzSGg5dDFlWis0UVMrS21mNml4RGFMV1ZUa2M1ak9FYlJQeXk5K1pKT3p1a1VzMUJOMGsvRDBBeGpaMnpJTW5Kby9hQUdFYkhPb3BId0xoc0lLM0JxMUxzYklGVVdmL05EWStyVGNuZlZRZ0hqaDVNOUc2Zzc1UXYiLCJtYWMiOiI2MTI3ZjEwZWJkNjgzNDdjNWU5ZDgwOTdkZmVmYWY2MjY5ZTBmZGU0MzlkMjM1ODc2NDRhNDBmYzNjNTRiZDQyIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:02:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6Inl2UlJ5WXQ3MFpYbnhZak51SXhHYXc9PSIsInZhbHVlIjoiOVpHN3V4UkYzbFRmODY3RitrSnBQRE1GNlBwZXVNUlBPaCtKeWV6L21aMVN0TTc3NUVIS2duMjIwNHhtTDQ3T1E4MGFHY2RPcDR6UkdCZ0VvN0hCS3FNeDF3ajlMby95NWpMUWVYVVBvei9LSUhJOE5NMnJET1MycWd0ZWNWbTYiLCJtYWMiOiIyZjBlMjg1NDdiMGI2ODg2YjdkOWIyMjU5ZmU0NWNiOWIwYmQ1M2E5Y2RkOTRjYTRhYmJlNmZiZGJmZTVlZjJlIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:02:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im9JTFd1ZTdJamEzazF3bVlJeEl1ZGc9PSIsInZhbHVlIjoiQkV4SGFVUXJzSGg5dDFlWis0UVMrS21mNml4RGFMV1ZUa2M1ak9FYlJQeXk5K1pKT3p1a1VzMUJOMGsvRDBBeGpaMnpJTW5Kby9hQUdFYkhPb3BId0xoc0lLM0JxMUxzYklGVVdmL05EWStyVGNuZlZRZ0hqaDVNOUc2Zzc1UXYiLCJtYWMiOiI2MTI3ZjEwZWJkNjgzNDdjNWU5ZDgwOTdkZmVmYWY2MjY5ZTBmZGU0MzlkMjM1ODc2NDRhNDBmYzNjNTRiZDQyIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:02:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6Inl2UlJ5WXQ3MFpYbnhZak51SXhHYXc9PSIsInZhbHVlIjoiOVpHN3V4UkYzbFRmODY3RitrSnBQRE1GNlBwZXVNUlBPaCtKeWV6L21aMVN0TTc3NUVIS2duMjIwNHhtTDQ3T1E4MGFHY2RPcDR6UkdCZ0VvN0hCS3FNeDF3ajlMby95NWpMUWVYVVBvei9LSUhJOE5NMnJET1MycWd0ZWNWbTYiLCJtYWMiOiIyZjBlMjg1NDdiMGI2ODg2YjdkOWIyMjU5ZmU0NWNiOWIwYmQ1M2E5Y2RkOTRjYTRhYmJlNmZiZGJmZTVlZjJlIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:02:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223271701\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-249094951 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=X5f673c4b71d77902f95ed17162072935&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7070</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249094951\", {\"maxDepth\":0})</script>\n"}}