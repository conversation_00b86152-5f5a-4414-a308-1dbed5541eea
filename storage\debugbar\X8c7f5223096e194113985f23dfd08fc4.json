{"__meta": {"id": "X8c7f5223096e194113985f23dfd08fc4", "datetime": "2025-07-27 14:13:23", "utime": **********.660951, "method": "GET", "uri": "/CRMProjects/List", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 10, "messages": [{"message": "[14:13:21] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.80301, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:21] LOG.warning: Creation of dynamic property App\\Http\\Livewire\\CRMProjects\\ProjectsList::$workspaceSlug is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Livewire\\CRMProjects\\ProjectsList.php on line 75", "message_html": null, "is_string": false, "label": "warning", "time": **********.870696, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:23] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2416", "message_html": null, "is_string": false, "label": "warning", "time": **********.502771, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:23] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2476", "message_html": null, "is_string": false, "label": "warning", "time": **********.502865, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:23] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3617", "message_html": null, "is_string": false, "label": "warning", "time": **********.503476, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:23] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.516206, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:23] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.516248, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:23] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.516283, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:23] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3414", "message_html": null, "is_string": false, "label": "warning", "time": **********.527546, "xdebug_link": null, "collector": "log"}, {"message": "[14:13:23] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.53332, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.43785, "end": **********.660982, "duration": 2.2231318950653076, "duration_str": "2.22s", "measures": [{"label": "Booting", "start": **********.43785, "relative_start": 0, "end": **********.784661, "relative_end": **********.784661, "duration": 0.34681105613708496, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.78467, "relative_start": 0.3468201160430908, "end": **********.660983, "relative_end": 1.1920928955078125e-06, "duration": 1.8763129711151123, "duration_str": "1.88s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 45059072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 22, "templates": [{"name": "CRMProjects.projects-list (\\resources\\views\\CRMProjects\\projects-list.blade.php)", "param_count": 13, "params": ["workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.projects-list (\\resources\\views\\livewire\\c-r-m-projects\\projects-list.blade.php)", "param_count": 65, "params": ["errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.cards-view (\\resources\\views\\livewire\\c-r-m-projects\\partials\\cards-view.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.paginator (\\resources\\views\\livewire\\c-r-m-projects\\partials\\paginator.blade.php)", "param_count": 73, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "loop", "usersToShow", "userCount", "index", "perPageOptions"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.delete-confirm (\\resources\\views\\livewire\\c-r-m-projects\\modals\\delete-confirm.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.create (\\resources\\views\\livewire\\c-r-m-projects\\modals\\create.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.edit (\\resources\\views\\livewire\\c-r-m-projects\\modals\\edit.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.inviteUser (\\resources\\views\\livewire\\c-r-m-projects\\modals\\inviteUser.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.shareClient (\\resources\\views\\livewire\\c-r-m-projects\\modals\\shareClient.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.shareVendors (\\resources\\views\\livewire\\c-r-m-projects\\modals\\shareVendors.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.duplication (\\resources\\views\\livewire\\c-r-m-projects\\modals\\duplication.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.saveProjectAsTemplate (\\resources\\views\\livewire\\c-r-m-projects\\modals\\saveProjectAsTemplate.blade.php)", "param_count": 67, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workspaceSlug", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 17, "params": ["__env", "app", "errors", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials.check_crm_session (\\resources\\views\\layouts\\partials\\check_crm_session.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET CRMProjects/List", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\CRMProjects\\ProjectController@list", "as": "CRMProjects.list", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/CRMProjects", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php&line=16\">\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php:16-18</a>"}, "queries": {"nb_statements": 17, "nb_failed_statements": 0, "accumulated_duration": 0.022439999999999998, "accumulated_duration_str": "22.44ms", "statements": [{"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00344, "duration_str": "3.44ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 15.33}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7070 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_test_db", "start_percent": 15.33, "width_percent": 2.406}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 173 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 17.736, "width_percent": 3.832}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3046}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0016899999999999999, "duration_str": "1.69ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3046", "connection": "osool_test_db", "start_percent": 21.569, "width_percent": 7.531}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 7070 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_test_db", "start_percent": 29.1, "width_percent": 2.674}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'building_manager' limit 1", "type": "query", "params": [], "bindings": ["building_manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1927}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1927", "connection": "osool_test_db", "start_percent": 31.774, "width_percent": 3.922}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 173 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_test_db", "start_percent": 35.695, "width_percent": 2.629}, {"sql": "select count(*) as aggregate from `maintanance_request` where `building_id` in ('5936', '5947') and `status` = 'pending'", "type": "query", "params": [], "bindings": ["5936", "5947", "pending"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\MaintenanceRequestTrait.php", "line": 14}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 201}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 77}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00185, "duration_str": "1.85ms", "stmt_id": "\\app\\Http\\Traits\\MaintenanceRequestTrait.php:14", "connection": "osool_test_db", "start_percent": 38.324, "width_percent": 8.244}, {"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00152, "duration_str": "1.52ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 46.569, "width_percent": 6.774}, {"sql": "select * from `user_sub_privileges` where `user_sub_privileges`.`privilage_user_id` in (7070)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 21, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 53.342, "width_percent": 2.362}, {"sql": "select * from `user_type` where `user_type`.`slug` in ('building_manager')", "type": "query", "params": [], "bindings": ["building_manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 21, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 55.704, "width_percent": 1.738}, {"sql": "select `id` from `sub_privileges` where `user_type` = 4 and `slug` = 'work_order_approve'", "type": "query", "params": [], "bindings": ["4", "work_order_approve"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4377}, {"index": 14, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4377", "connection": "osool_test_db", "start_percent": 57.442, "width_percent": 1.783}, {"sql": "select count(*) as aggregate from `maintanance_request` where `building_id` in ('5936', '5947') and `status` = 'pending'", "type": "query", "params": [], "bindings": ["5936", "5947", "pending"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\WorkOrders.php", "line": 2400}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 511}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.0025, "duration_str": "2.5ms", "stmt_id": "\\app\\Models\\WorkOrders.php:2400", "connection": "osool_test_db", "start_percent": 59.225, "width_percent": 11.141}, {"sql": "select exists(select * from `projects_details` where `id` = 173 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["173", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 70.365, "width_percent": 2.718}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7070) as `exists`", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 73.084, "width_percent": 3.075}, {"sql": "select exists(select * from `projects_details` where `id` = 173 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["173", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00385, "duration_str": "3.85ms", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 76.159, "width_percent": 17.157}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7070) as `exists`", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 93.316, "width_percent": 6.684}]}, "models": {"data": {"App\\Models\\SubPrivileges": 1, "App\\Models\\UserTypes": 1, "App\\Models\\UserSubPrivileges": 1, "App\\Models\\CrmUser": 1, "App\\Models\\ReleaseNotes": 2, "App\\Models\\ProjectsDetails": 2, "App\\Models\\UserCompany": 1, "App\\Models\\User": 2}, "count": 11}, "livewire": {"data": {"c-r-m-projects.projects-list #hQey9l12pAExkpq6Jus9": "array:5 [\n  \"data\" => array:50 [\n    \"viewMode\" => \"cards\"\n    \"projects\" => array:1 [\n      0 => array:16 [\n        \"id\" => 649\n        \"name\" => \"Notifications\"\n        \"status\" => \"Draft\"\n        \"project_type\" => \"maintenance\"\n        \"priority_level\" => \"critical\"\n        \"description\" => \"Test notifications\"\n        \"start_date\" => \"2025-07-21\"\n        \"end_date\" => \"2026-02-01\"\n        \"budget\" => \"﷼100000\"\n        \"users\" => array:2 [\n          0 => array:3 [\n            \"name\" => \"Khalil <PERSON>\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n          1 => array:3 [\n            \"name\" => \"Staff\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 4\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"Client1\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"vendors\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"Vendor\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"milestones\" => array:4 [\n          0 => array:6 [\n            \"title\" => \"test\"\n            \"status\" => \"incomplete\"\n            \"start_date\" => \"2025-07-24\"\n            \"end_date\" => \"2025-07-24\"\n            \"progress\" => null\n            \"cost\" => \"﷼1000\"\n          ]\n          1 => array:6 [\n            \"title\" => \"test2\"\n            \"status\" => \"incomplete\"\n            \"start_date\" => \"2025-07-23\"\n            \"end_date\" => \"2025-07-24\"\n            \"progress\" => null\n            \"cost\" => \"﷼1000\"\n          ]\n          2 => array:6 [\n            \"title\" => \"Past\"\n            \"status\" => \"incomplete\"\n            \"start_date\" => \"2025-07-24\"\n            \"end_date\" => \"2025-07-24\"\n            \"progress\" => null\n            \"cost\" => \"﷼2000\"\n          ]\n          3 => array:6 [\n            \"title\" => \"Future\"\n            \"status\" => \"complete\"\n            \"start_date\" => \"2025-07-31\"\n            \"end_date\" => \"2025-07-31\"\n            \"progress\" => \"100\"\n            \"cost\" => \"﷼2000\"\n          ]\n        ]\n      ]\n    ]\n    \"start_date\" => null\n    \"end_date\" => null\n    \"filter_start_date\" => null\n    \"filter_end_date\" => null\n    \"search\" => null\n    \"budget\" => null\n    \"project_type\" => \"\"\n    \"nextPageUrl\" => null\n    \"prevPageUrl\" => null\n    \"status\" => null\n    \"projectID\" => null\n    \"projecTid\" => null\n    \"UsersforCreateProject\" => []\n    \"users\" => array:2 [\n      0 => array:3 [\n        \"id\" => 693\n        \"name\" => \"Staff\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 696\n        \"name\" => \"KH BMA from POA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"projectType\" => array:8 [\n      0 => array:2 [\n        \"value\" => \"new_construction\"\n        \"label\" => \"New Construction\"\n      ]\n      1 => array:2 [\n        \"value\" => \"renovation\"\n        \"label\" => \"Renovation\"\n      ]\n      2 => array:2 [\n        \"value\" => \"maintenance\"\n        \"label\" => \"Maintenance\"\n      ]\n      3 => array:2 [\n        \"value\" => \"expansion\"\n        \"label\" => \"Expansion\"\n      ]\n      4 => array:2 [\n        \"value\" => \"demolition\"\n        \"label\" => \"Demolition\"\n      ]\n      5 => array:2 [\n        \"value\" => \"infrastructure\"\n        \"label\" => \"Infrastructure\"\n      ]\n      6 => array:2 [\n        \"value\" => \"design_projects\"\n        \"label\" => \"Design Projects\"\n      ]\n      7 => array:2 [\n        \"value\" => \"compliance_safety\"\n        \"label\" => \"Compliance & Safety\"\n      ]\n    ]\n    \"priorityLevel\" => array:4 [\n      0 => array:2 [\n        \"value\" => \"critical\"\n        \"label\" => \"Critical\"\n      ]\n      1 => array:2 [\n        \"value\" => \"high\"\n        \"label\" => \"High\"\n      ]\n      2 => array:2 [\n        \"value\" => \"medium\"\n        \"label\" => \"Medium\"\n      ]\n      3 => array:2 [\n        \"value\" => \"low\"\n        \"label\" => \"Low\"\n      ]\n    ]\n    \"priority_level\" => null\n    \"clients\" => array:1 [\n      0 => array:3 [\n        \"id\" => 692\n        \"name\" => \"Client1\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"vendors\" => array:1 [\n      0 => array:3 [\n        \"id\" => 694\n        \"name\" => \"Vendor\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"vendor\" => []\n    \"selectedUsers\" => []\n    \"selectedUsersForInvite\" => []\n    \"usersAlreadyInvited\" => []\n    \"selectedvendorsForShare\" => []\n    \"vendorsAlreadyInProject\" => []\n    \"selectedclientsForShare\" => []\n    \"clientssAlreadyInProject\" => []\n    \"name\" => null\n    \"description\" => null\n    \"ProjectTemplateName\" => null\n    \"project\" => \"\"\n    \"statuss\" => null\n    \"bug\" => []\n    \"task\" => []\n    \"activity\" => []\n    \"user\" => []\n    \"client\" => []\n    \"milestone\" => []\n    \"project_file\" => []\n    \"building_manager\" => []\n    \"page\" => 1\n    \"totalPages\" => 1.0\n    \"perPage\" => 8\n    \"totalItems\" => 1\n    \"isRefreshButton\" => true\n    \"sortDirection\" => \"desc\"\n    \"sortField\" => \"\"\n    \"workspaceSlug\" => \"khalil-project\"\n  ]\n  \"name\" => \"c-r-m-projects.projects-list\"\n  \"view\" => \"livewire.c-r-m-projects.projects-list\"\n  \"component\" => \"App\\Http\\Livewire\\CRMProjects\\ProjectsList\"\n  \"id\" => \"hQey9l12pAExkpq6Jus9\"\n]", "notifications.messages-notifications-list #nhFFVdd8rSduEVJuGv2x": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"khalil-project\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"nhFFVdd8rSduEVJuGv2x\"\n]", "notifications.new-notifications-list-top-nav #2luIJEANGObT2SbIUzA8": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"2luIJEANGObT2SbIUzA8\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3964\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 13:57:47\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjEzODY4LCJleHAiOjE3NTM2MTc0NjgsIm5iZiI6MTc1MzYxMzg2OCwianRpIjoiWUNKR2t6NHk3TGN2aFZRWCIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.r3ITi6B34xPDa3vVPWcCd59gBp92FKXvXwj73_AbakU\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 13:57:47\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjEzODY4LCJleHAiOjE3NTM2MTc0NjgsIm5iZiI6MTc1MzYxMzg2OCwianRpIjoiWUNKR2t6NHk3TGN2aFZRWCIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.r3ITi6B34xPDa3vVPWcCd59gBp92FKXvXwj73_AbakU\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:3 [\n        \"userCompany\" => App\\Models\\UserCompany {#3971\n          #connection: \"mysql\"\n          #table: \"user_company\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #original: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #changes: []\n          #casts: array:2 [\n            \"user_id\" => \"int\"\n            \"company_id\" => \"int\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: array:2 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n          ]\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"company_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3990\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 13:57:48\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #original: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 13:57:48\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => App\\Models\\CrmUser {#3995\n          #connection: \"mysql\"\n          #table: \"crm_user\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 19\n            \"user_id\" => 7070\n            \"crm_user_id\" => 680\n            \"created_at\" => \"2025-07-24 18:44:46\"\n            \"updated_at\" => \"2025-07-24 18:44:46\"\n            \"instagram_connect\" => 0\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n            \"whatsapp_account_id\" => 0\n            \"whatsapp_number_status\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 19\n            \"user_id\" => 7070\n            \"crm_user_id\" => 680\n            \"created_at\" => \"2025-07-24 18:44:46\"\n            \"updated_at\" => \"2025-07-24 18:44:46\"\n            \"instagram_connect\" => 0\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n            \"whatsapp_account_id\" => 0\n            \"whatsapp_number_status\" => null\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"crm_user_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:54 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 1\n    \"projectId\" => null\n    \"project\" => App\\Models\\ProjectsDetails {#4064\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 173\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khalil project\"\n        \"project_name_ar\" => \"مشروع خليل\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 173\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khalil project\"\n        \"project_name_ar\" => \"مشروع خليل\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => null\n    \"flagWorkorderSidebarMenu\" => true\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => null\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu", "captcha_answer": "14", "_flash": "array:2 [\n  \"old\" => array:4 [\n    0 => \"flash_message\"\n    1 => \"flash_type\"\n    2 => \"flash_message\"\n    3 => \"flash_type\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/CRMProjects/List\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7070", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]", "flash_message": "You are not authorized to access that page.", "flash_type": "warning"}, "request": {"path_info": "/CRMProjects/List", "status_code": "<pre class=sf-dump id=sf-dump-596652698 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-596652698\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1744853748 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744853748\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1796571474 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1796571474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1967569579 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/notifications-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImlVT1RoOUdIY0dKczJTc1NDU0NyL0E9PSIsInZhbHVlIjoic3daelJjZFJ2L2orTjRSK0U5cDhpVWtTTndyZ0dLV05QUm1zc2xpc1g3amU2YVlPM3FNMUlJdkVGNlc2R0FiM2hLWGh0YVlrWVZKUy9TejY1WUxyUFprMzB3V21kV3M5M2RENDg5Ky9NOFVITjVKWFZpa09Qa3FEZndrdHdRUGEiLCJtYWMiOiJkZGNhZThjZGI4MDA2OTI0YzcxOWUyM2FiNTA2MjhhZmM2NzViYWQyMjUwZTg3ZTZmZWYwYzM1NWViZDAxOTk2IiwidGFnIjoiIn0%3D; osool_session=Rb5ItbLIlEs1RXFQ4JRjIyg6sqt25NrSVovF6J8c</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967569579\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1590607390 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/notifications-list</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImlVT1RoOUdIY0dKczJTc1NDU0NyL0E9PSIsInZhbHVlIjoic3daelJjZFJ2L2orTjRSK0U5cDhpVWtTTndyZ0dLV05QUm1zc2xpc1g3amU2YVlPM3FNMUlJdkVGNlc2R0FiM2hLWGh0YVlrWVZKUy9TejY1WUxyUFprMzB3V21kV3M5M2RENDg5Ky9NOFVITjVKWFZpa09Qa3FEZndrdHdRUGEiLCJtYWMiOiJkZGNhZThjZGI4MDA2OTI0YzcxOWUyM2FiNTA2MjhhZmM2NzViYWQyMjUwZTg3ZTZmZWYwYzM1NWViZDAxOTk2IiwidGFnIjoiIn0%3D; osool_session=Rb5ItbLIlEs1RXFQ4JRjIyg6sqt25NrSVovF6J8c</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62140</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/CRMProjects/List</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/CRMProjects/List</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.4378</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590607390\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1170440323 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170440323\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1647866139 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 27 Jul 2025 11:13:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBheFZyemlMK0d3ZG9vOGtQd1JCMVE9PSIsInZhbHVlIjoiU00yT3l6ZnZLRG9EanhERXFVaGVmc0Q5SGIxVEdITGJQWnVoYjNXNHZVNW91aFFtb3JqY3d5Nm82VzdZd0lFUjh6SDU5T0ovYVJKYUR1NVdNM1R5U2QzUnlvMjlZUmUzOGJEa1ZxWUcrVWZxNnJpS2t1eE85ZHd3UDBkV0VCR2ciLCJtYWMiOiIwOTlmYmZhNjhmZTQxOGYwOTA2YThhM2MwODIwOTc5NjllMWNlNzkyZGIwZDU4ZTU3M2YzODM4NWU1MjE3MmVkIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 13:13:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6Im40Y2ZYeDNZYzFyUi9vR0hDRW9oNFE9PSIsInZhbHVlIjoiSzYvVjNtS0JBcUpSL21oOWxqS2E1bDNsTFF1MCtBWHUrTmJZQzJDT1RYVnpyUzdvM1hBOWNqN0pGZ0lYQ0dDUkg5dWJWNmtyT0lTQW5ibE1Qd0pYcVp5TEt1c2hVTUlRS2JoTnFYLy9GSmtDYWxUQWpDazRLQjJaNytrcGt4RmsiLCJtYWMiOiI4YTA5YjBjNTUyYjY0NmUyZWFjMDc3NDc4MjlkZTdmNTljMzcyZDAyZjJiZWE2MzIwYzIwZjdmZGE2YmQ3NjZhIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 13:13:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBheFZyemlMK0d3ZG9vOGtQd1JCMVE9PSIsInZhbHVlIjoiU00yT3l6ZnZLRG9EanhERXFVaGVmc0Q5SGIxVEdITGJQWnVoYjNXNHZVNW91aFFtb3JqY3d5Nm82VzdZd0lFUjh6SDU5T0ovYVJKYUR1NVdNM1R5U2QzUnlvMjlZUmUzOGJEa1ZxWUcrVWZxNnJpS2t1eE85ZHd3UDBkV0VCR2ciLCJtYWMiOiIwOTlmYmZhNjhmZTQxOGYwOTA2YThhM2MwODIwOTc5NjllMWNlNzkyZGIwZDU4ZTU3M2YzODM4NWU1MjE3MmVkIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 13:13:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6Im40Y2ZYeDNZYzFyUi9vR0hDRW9oNFE9PSIsInZhbHVlIjoiSzYvVjNtS0JBcUpSL21oOWxqS2E1bDNsTFF1MCtBWHUrTmJZQzJDT1RYVnpyUzdvM1hBOWNqN0pGZ0lYQ0dDUkg5dWJWNmtyT0lTQW5ibE1Qd0pYcVp5TEt1c2hVTUlRS2JoTnFYLy9GSmtDYWxUQWpDazRLQjJaNytrcGt4RmsiLCJtYWMiOiI4YTA5YjBjNTUyYjY0NmUyZWFjMDc3NDc4MjlkZTdmNTljMzcyZDAyZjJiZWE2MzIwYzIwZjdmZGE2YmQ3NjZhIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 13:13:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647866139\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-257411752 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">flash_message</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">flash_type</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">flash_message</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">flash_type</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/CRMProjects/List</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7070</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>flash_message</span>\" => \"<span class=sf-dump-str title=\"43 characters\">You are not authorized to access that page.</span>\"\n  \"<span class=sf-dump-key>flash_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257411752\", {\"maxDepth\":0})</script>\n"}}