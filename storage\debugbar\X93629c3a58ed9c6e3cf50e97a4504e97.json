{"__meta": {"id": "X93629c3a58ed9c6e3cf50e97a4504e97", "datetime": "2025-07-27 12:40:32", "utime": **********.616093, "method": "POST", "uri": "/livewire/message/notifications.new-notifications-list-top-nav", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 6, "messages": [{"message": "[12:40:31] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.042531, "xdebug_link": null, "collector": "log"}, {"message": "[12:40:31] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.097904, "xdebug_link": null, "collector": "log"}, {"message": "[12:40:31] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.097977, "xdebug_link": null, "collector": "log"}, {"message": "[12:40:31] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.098041, "xdebug_link": null, "collector": "log"}, {"message": "[12:40:31] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3414", "message_html": null, "is_string": false, "label": "warning", "time": **********.117734, "xdebug_link": null, "collector": "log"}, {"message": "[12:40:31] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.122905, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.514281, "end": **********.616115, "duration": 2.1018340587615967, "duration_str": "2.1s", "measures": [{"label": "Booting", "start": **********.514281, "relative_start": 0, "end": **********.021344, "relative_end": **********.021344, "duration": 0.5070629119873047, "duration_str": "507ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.021359, "relative_start": 0.5070779323577881, "end": **********.616116, "relative_end": 9.5367431640625e-07, "duration": 1.594757080078125, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 38835952, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 1.46154, "accumulated_duration_str": "1.46s", "statements": [{"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00369, "duration_str": "3.69ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 0.252}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7070 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_test_db", "start_percent": 0.252, "width_percent": 0.05}, {"sql": "select `id_configuration`, `name`, `code`, `value`, `description`, `active`, `platform` from `configurations` where `code` = 3 and `configurations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ConfigurationTrait.php", "line": 49}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 167}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 205}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Traits\\ConfigurationTrait.php:49", "connection": "osool_test_db", "start_percent": 0.302, "width_percent": 0.064}, {"sql": "select `id_configuration`, `name`, `code`, `value`, `description`, `active`, `platform` from `configurations` where `code` = 3 and `configurations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ConfigurationTrait.php", "line": 49}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 167}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 154}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 207}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Traits\\ConfigurationTrait.php:49", "connection": "osool_test_db", "start_percent": 0.367, "width_percent": 0.052}, {"sql": "select `notifications`.*, `work_orders`.`work_order_id`, `work_orders`.`work_order_type`, `work_orders`.`assigned_to`, `work_orders`.`supervisor_id` from `notifications` left join `work_orders` on `work_orders`.`id` = `notifications`.`section_id` left join `user_assets_mapping` on `user_assets_mapping`.`contract_id` = `work_orders`.`contract_id` where (`notifications`.`is_timeline` = 'no' or `notifications`.`is_timeline` is null) and (NOT find_in_set(7070, notifications.user_id)) and (0 = 1 and `notifications`.`section_type` = 'work_order' and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31' or (`work_orders`.`asset_category_id` is null and `notifications`.`notification_sub_type` = 'new_maintenance_request' and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31')) and (`notifications`.`building_ids` in ('5936', '5947') and `notifications`.`notification_sub_type` not in ('new_work_order_created') and `notifications`.`notification_sub_type` in ('worker_assigned_by_bm', 'sp_has_approved_wo', 'new_maintenance_request', 'sp_has_an_issue', 'sp_respond_to_bm_rejection', 'sp_has_did_not_agreed_on_workorder', 'sp_has_edited_target_date', 'wo_started_wo', 'sp_has_marked_wo_completed', 'sp_has_automatically_approved_wo', 'pause_workorder') and `notifications`.`section_type` in ('new_chat_message', 'work_order', 'report') and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`notification_sub_type` = 'new_chat_message' and `notifications`.`not_receivers_user_id` != 7070 and `notifications`.`building_ids` in ('5936', '5947') and 0 = 1 and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`section_type` in ('bookings') and `notifications`.`user_id` in (7070) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'report' and `notifications`.`user_id` = 7070 and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'contracts' and FIND_IN_SET(7070, notifications.user_id) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'variation_order' and FIND_IN_SET( 7070, notifications.user_id) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'advance_contracts' and FIND_IN_SET(7070, notifications.user_id) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'complaints' and FIND_IN_SET(7070, notifications.user_id) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') group by `notifications`.`id` order by `notifications`.`created_at` desc limit 5", "type": "query", "params": [], "bindings": ["no", "work_order", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "new_maintenance_request", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "5936", "5947", "new_work_order_created", "worker_assigned_by_bm", "sp_has_approved_wo", "new_maintenance_request", "sp_has_an_issue", "sp_respond_to_bm_rejection", "sp_has_did_not_agreed_on_workorder", "sp_has_edited_target_date", "wo_started_wo", "sp_has_marked_wo_completed", "sp_has_automatically_approved_wo", "pause_workorder", "new_chat_message", "work_order", "report", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "new_chat_message", "7070", "5936", "5947", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "bookings", "7070", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "report", "7070", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "contracts", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "variation_order", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "advance_contracts", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "complaints", "2025-06-27 12:40:31", "2025-07-27 12:40:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 286}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 217}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 30}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.78008, "duration_str": "780ms", "stmt_id": "\\app\\Http\\Traits\\NotificationTrait.php:286", "connection": "osool_test_db", "start_percent": 0.419, "width_percent": 53.374}, {"sql": "select `notifications`.*, `work_orders`.`work_order_id`, `work_orders`.`work_order_type`, `work_orders`.`assigned_to`, `work_orders`.`supervisor_id` from `notifications` left join `work_orders` on `work_orders`.`id` = `notifications`.`section_id` left join `user_assets_mapping` on `user_assets_mapping`.`contract_id` = `work_orders`.`contract_id` where (`notifications`.`is_timeline` = 'no' or `notifications`.`is_timeline` is null) and (NOT find_in_set(7070, notifications.user_id)) and (0 = 1 and `notifications`.`section_type` = 'work_order' and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31' or (`work_orders`.`asset_category_id` is null and `notifications`.`notification_sub_type` = 'new_maintenance_request' and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31')) and (`notifications`.`building_ids` in ('5936', '5947') and `notifications`.`notification_sub_type` not in ('new_work_order_created') and `notifications`.`notification_sub_type` in ('worker_assigned_by_bm', 'sp_has_approved_wo', 'new_maintenance_request', 'sp_has_an_issue', 'sp_respond_to_bm_rejection', 'sp_has_did_not_agreed_on_workorder', 'sp_has_edited_target_date', 'wo_started_wo', 'sp_has_marked_wo_completed', 'sp_has_automatically_approved_wo', 'pause_workorder') and `notifications`.`section_type` in ('new_chat_message', 'work_order', 'report') and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`notification_sub_type` = 'new_chat_message' and `notifications`.`not_receivers_user_id` != 7070 and `notifications`.`building_ids` in ('5936', '5947') and 0 = 1 and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`section_type` in ('bookings') and `notifications`.`user_id` in (7070) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'report' and `notifications`.`user_id` = 7070 and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'contracts' and FIND_IN_SET(7070, notifications.user_id) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'variation_order' and FIND_IN_SET( 7070, notifications.user_id) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'advance_contracts' and FIND_IN_SET(7070, notifications.user_id) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') or (`notifications`.`section_type` = 'complaints' and FIND_IN_SET(7070, notifications.user_id) and `notifications`.`created_at` between '2025-06-27 12:40:31' and '2025-07-27 12:40:31') group by `notifications`.`id` order by `notifications`.`created_at` desc", "type": "query", "params": [], "bindings": ["no", "work_order", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "new_maintenance_request", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "5936", "5947", "new_work_order_created", "worker_assigned_by_bm", "sp_has_approved_wo", "new_maintenance_request", "sp_has_an_issue", "sp_respond_to_bm_rejection", "sp_has_did_not_agreed_on_workorder", "sp_has_edited_target_date", "wo_started_wo", "sp_has_marked_wo_completed", "sp_has_automatically_approved_wo", "pause_workorder", "new_chat_message", "work_order", "report", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "new_chat_message", "7070", "5936", "5947", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "bookings", "7070", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "report", "7070", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "contracts", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "variation_order", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "advance_contracts", "2025-06-27 12:40:31", "2025-07-27 12:40:31", "complaints", "2025-06-27 12:40:31", "2025-07-27 12:40:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 276}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\NotificationTrait.php", "line": 297}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 227}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav.php", "line": 31}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.67438, "duration_str": "674ms", "stmt_id": "\\app\\Http\\Traits\\NotificationTrait.php:276", "connection": "osool_test_db", "start_percent": 53.793, "width_percent": 46.142}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 173 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 99.934, "width_percent": 0.066}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\Configuration": 2, "App\\Models\\UserCompany": 1, "App\\Models\\User": 1}, "count": 5}, "livewire": {"data": {"notifications.new-notifications-list-top-nav #JTWGbES2JSmdU4vVSGPG": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => App\\Models\\User {#3223\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 12:03:07\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjA2OTg4LCJleHAiOjE3NTM2MTA1ODgsIm5iZiI6MTc1MzYwNjk4OCwianRpIjoiVzA3VG5PQTMxTWxsZXdZYiIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.aKYANSsHkRiJcEUihqU1a4enp7GiwI0fm9JOoOtWj2c\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 12:03:07\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjA2OTg4LCJleHAiOjE3NTM2MTA1ODgsIm5iZiI6MTc1MzYwNjk4OCwianRpIjoiVzA3VG5PQTMxTWxsZXdZYiIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.aKYANSsHkRiJcEUihqU1a4enp7GiwI0fm9JOoOtWj2c\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"userCompany\" => App\\Models\\UserCompany {#3204\n          #connection: \"mysql\"\n          #table: \"user_company\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #original: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #changes: []\n          #casts: array:2 [\n            \"user_id\" => \"int\"\n            \"company_id\" => \"int\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: array:2 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n          ]\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"company_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3838\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 12:03:09\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #original: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 12:03:09\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:54 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"perPage\" => 5\n    \"assignedAsset\" => []\n    \"contractsIds\" => []\n    \"accessBuildingsIds\" => array:2 [\n      0 => \"5936\"\n      1 => \"5947\"\n    ]\n    \"currentDate\" => \"2025-07-27\"\n    \"currentDateTime\" => \"2025-07-27 12:40:31\"\n    \"readyToLoad\" => true\n    \"configOciLink\" => App\\Models\\Configuration {#3811\n      #connection: \"mysql\"\n      #table: \"configurations\"\n      #primaryKey: \"id_configuration\"\n      #keyType: \"int\"\n      +incrementing: false\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id_configuration\" => 3\n        \"name\" => \"OciLink\"\n        \"code\" => 3\n        \"value\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n        \"description\" => \"The OCI storage link\"\n        \"active\" => 1\n        \"platform\" => \"1\"\n      ]\n      #original: array:7 [\n        \"id_configuration\" => 3\n        \"name\" => \"OciLink\"\n        \"code\" => 3\n        \"value\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n        \"description\" => \"The OCI storage link\"\n        \"active\" => 1\n        \"platform\" => \"1\"\n      ]\n      #changes: []\n      #casts: array:3 [\n        \"platform\" => \"App\\Enums\\Platform\"\n        \"active\" => \"App\\Enums\\Status\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:13 [\n        0 => \"id_configuration\"\n        1 => \"platform\"\n        2 => \"name\"\n        3 => \"code\"\n        4 => \"value\"\n        5 => \"description\"\n        6 => \"active\"\n        7 => \"created_by\"\n        8 => \"deleted_by\"\n        9 => \"updated_by\"\n        10 => \"deleted_at\"\n        11 => \"created_at\"\n        12 => \"updated_at\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"ociLink\" => \"https://axpem4hcpiq4.compat.objectstorage.me-jeddah-1.oraclecloud.com\"\n    \"selectedLanguage\" => \"en\"\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"JTWGbES2JSmdU4vVSGPG\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu", "captcha_answer": "14", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/notifications-list\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7070", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/notifications.new-notifications-list-top-nav", "status_code": "<pre class=sf-dump id=sf-dump-1957410378 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1957410378\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1069370846 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1069370846\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1781979047 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">JTWGbES2JSmdU4vVSGPG</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"44 characters\">notifications.new-notifications-list-top-nav</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"18 characters\">notifications-list</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">47a312c0</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>assignedAsset</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>contractsIds</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>accessBuildingsIds</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentDate</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentDateTime</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>readyToLoad</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>configOciLink</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>ociLink</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedLanguage</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">2ab7234380e598ed24d37c107fc2ffdb40f04b51e72d47cc4302a28a258caefd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pwia</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">loadData</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781979047\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-228049478 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">635</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/notifications-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjdGYVFJNVB6MFpCSnlxYzd5ODJBcVE9PSIsInZhbHVlIjoiM3dTNlM5QzJDQVFyU3dudTJRS3N3NFRvOVFmR0ZSTUlWL0ZKSVV0SzJROHkzU2tyRGtlbTdyT05oNm5wTUZsbjRvQWUzc1I3TWJNdi9FcDdPTWdxVVBHY0c1SVJWUjFkMWNEeVhRaEtPYlo0U1dhU0Y5ckx3RExZVkZwbkh5MFEiLCJtYWMiOiJkMGZjYjU5MDA4Y2M0ZDAxY2I1ZjU0NDY2OTY2MWQzZDQxMjc4OGMzOGYwNzNjN2FhMWVlZGViYmY4NjBjMmZkIiwidGFnIjoiIn0%3D; osool_session=5tOvYh30csBo5TgTW3MJCkfiHndoNWdsemDikzzh</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228049478\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1335869436 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">635</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/notifications-list</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjdGYVFJNVB6MFpCSnlxYzd5ODJBcVE9PSIsInZhbHVlIjoiM3dTNlM5QzJDQVFyU3dudTJRS3N3NFRvOVFmR0ZSTUlWL0ZKSVV0SzJROHkzU2tyRGtlbTdyT05oNm5wTUZsbjRvQWUzc1I3TWJNdi9FcDdPTWdxVVBHY0c1SVJWUjFkMWNEeVhRaEtPYlo0U1dhU0Y5ckx3RExZVkZwbkh5MFEiLCJtYWMiOiJkMGZjYjU5MDA4Y2M0ZDAxY2I1ZjU0NDY2OTY2MWQzZDQxMjc4OGMzOGYwNzNjN2FhMWVlZGViYmY4NjBjMmZkIiwidGFnIjoiIn0%3D; osool_session=5tOvYh30csBo5TgTW3MJCkfiHndoNWdsemDikzzh</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54771</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/livewire/message/notifications.new-notifications-list-top-nav</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/livewire/message/notifications.new-notifications-list-top-nav</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.5143</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335869436\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2012440542 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012440542\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-592349514 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 27 Jul 2025 09:40:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjN5dU1tNW5MYzhCWUhuU0svWkt2UEE9PSIsInZhbHVlIjoiRGJPZlVaRGQ5QmZPUXVVV1ZyUkVPaTA3WDFXZnNpSVpzMjZqNzF6SVlFd2NwRGJWY01LK0UvMFVIQ2JWbWRDdW1BVCs1L3BKTXQvVkY0azlaOGdJaXk0SlNHWWpnTkVRb244eEJyTWtCZVJZYjJadTU0Y21BR3FWMmovYUdTYlEiLCJtYWMiOiIxOTA1NDM0YTEzNTZlMjI5MzM4NzVlZjM3OTVjNGVkNjU3ODExYzM3N2QwZTc3NWViY2ViNWJlNDJhNjRjM2E1IiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:40:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IkhZZ2NEdmFRMnJtcEl4WWRhelorMkE9PSIsInZhbHVlIjoicjdTL1dnVnliM2JTYkdueTN4S0tIRTk3S1NML1JMYldDL1dCQVlFK3ByVkZDbkhMOGRpdHdUUytTbDhiaW1GNnprcDJneVdXdDh6a3ZTVXRqaG0wQXo4NXNQZWlyelVucnVPSHBrOWpSOWtOeVFsY3FSN1plRW1FcU5wWDI3dEoiLCJtYWMiOiIyNTU3ZWI0MGJlOGExMGQwZDRjNDMxY2Y0ZTUwZTc0MGU0NDhiNThhYzNiMDJlZWZjZjMxZDE1YzZmMmQyYWE3IiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:40:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjN5dU1tNW5MYzhCWUhuU0svWkt2UEE9PSIsInZhbHVlIjoiRGJPZlVaRGQ5QmZPUXVVV1ZyUkVPaTA3WDFXZnNpSVpzMjZqNzF6SVlFd2NwRGJWY01LK0UvMFVIQ2JWbWRDdW1BVCs1L3BKTXQvVkY0azlaOGdJaXk0SlNHWWpnTkVRb244eEJyTWtCZVJZYjJadTU0Y21BR3FWMmovYUdTYlEiLCJtYWMiOiIxOTA1NDM0YTEzNTZlMjI5MzM4NzVlZjM3OTVjNGVkNjU3ODExYzM3N2QwZTc3NWViY2ViNWJlNDJhNjRjM2E1IiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:40:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IkhZZ2NEdmFRMnJtcEl4WWRhelorMkE9PSIsInZhbHVlIjoicjdTL1dnVnliM2JTYkdueTN4S0tIRTk3S1NML1JMYldDL1dCQVlFK3ByVkZDbkhMOGRpdHdUUytTbDhiaW1GNnprcDJneVdXdDh6a3ZTVXRqaG0wQXo4NXNQZWlyelVucnVPSHBrOWpSOWtOeVFsY3FSN1plRW1FcU5wWDI3dEoiLCJtYWMiOiIyNTU3ZWI0MGJlOGExMGQwZDRjNDMxY2Y0ZTUwZTc0MGU0NDhiNThhYzNiMDJlZWZjZjMxZDE1YzZmMmQyYWE3IiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:40:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592349514\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1686443527 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://osool-b2g.test/notifications-list</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7070</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686443527\", {\"maxDepth\":0})</script>\n"}}