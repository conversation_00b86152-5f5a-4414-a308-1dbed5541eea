{"__meta": {"id": "X9e31ed1e2a8b5586eab2048324a49199", "datetime": "2025-07-27 12:28:07", "utime": 1753608487.437455, "method": "POST", "uri": "/livewire/message/project.task-board.list-view", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 13, "messages": [{"message": "[12:27:31] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753608451.845253, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:31] LOG.warning: parse_str(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Services\\DashCrmService.php on line 90", "message_html": null, "is_string": false, "label": "warning", "time": 1753608451.920029, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:33] LOG.info: MilestoneNotificationTrait: Found completed milestone {\"milestone_id\":588,\"milestone_title\":\"Future\"}", "message_html": null, "is_string": false, "label": "info", "time": 1753608453.371033, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:33] LOG.debug: MilestoneNotificationTrait: User IDs for notification {\"user_ids\":[693,680]}", "message_html": null, "is_string": false, "label": "debug", "time": 1753608453.371111, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:33] LOG.info: MilestoneNotificationTrait: Sending notifications to users {\"milestone_data\":{\"milestone_name\":\"Future\",\"project_name\":\"\",\"completion_date\":\"2025-07-27\"},\"user_count\":2}", "message_html": null, "is_string": false, "label": "info", "time": 1753608453.373837, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:33] LOG.warning: Callables of the form [\"Swift_Message\", \"Swift_Mime_SimpleMessage::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\Message.php on line 46", "message_html": null, "is_string": false, "label": "warning", "time": 1753608453.396373, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:41] LOG.error: MilestoneNotificationTrait: Error sending email notification {\"error\":\"Connection could not be established with host  :stream_socket_client(): Unable to connect to :587 (No connection could be made because the target machine actively refused it)\",\"user_id\":6721,\"locale\":\"en\",\"trace\":\"#0 [internal function]: Swift_Transport_StreamBuffer->{closure}(2, 'stream_socket_c...', 'C:\\\\\\\\laragon\\\\\\\\www\\\\\\\\...', 264)\\n#1 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\StreamBuffer.php(264): stream_socket_client(':587', 0, '', 30, 4, Resource id #1152)\\n#2 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\StreamBuffer.php(58): Swift_Transport_StreamBuffer->establishSocketConnection()\\n#3 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\AbstractSmtpTransport.php(143): Swift_Transport_StreamBuffer->initialize(Array)\\n#4 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Mailer.php(65): Swift_Transport_AbstractSmtpTransport->start()\\n#5 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(521): Swift_Mailer->send(Object(Swift_Message), Array)\\n#6 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(288): Illuminate\\\\Mail\\\\Mailer->sendSwiftMessage(Object(Swift_Message))\\n#7 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailable.php(181): Illuminate\\\\Mail\\\\Mailer->send('mail.milestone_...', Array, Object(Closure))\\n#8 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Support\\\\Traits\\\\Localizable.php(29): Illuminate\\\\Mail\\\\Mailable->Illuminate\\\\Mail\\\\{closure}()\\n#9 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailable.php(174): Illuminate\\\\Mail\\\\Mailable->withLocale('en', Object(Closure))\\n#10 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(304): Illuminate\\\\Mail\\\\Mailable->send(Object(Illuminate\\\\Mail\\\\Mailer))\\n#11 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(258): Illuminate\\\\Mail\\\\Mailer->sendMailable(Object(App\\\\Mail\\\\MilestoneMail))\\n#12 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\PendingMail.php(124): Illuminate\\\\Mail\\\\Mailer->send(Object(App\\\\Mail\\\\MilestoneMail))\\n#13 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(127): Illuminate\\\\Mail\\\\PendingMail->send(Object(App\\\\Mail\\\\MilestoneMail))\\n#14 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(95): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->sendEmailNotification(Object(App\\\\Models\\\\CrmUser), Array, 'en')\\n#15 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(28): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->sendMilestoneCompletionNotifications(Array)\\n#16 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView.php(141): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->checkAndSendMilestoneCompletionNotifications(Array)\\n#17 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(36): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->updateSelectedStatus()\\n#18 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\Util.php(40): Illuminate\\\\Container\\\\BoundMethod::Illuminate\\\\Container\\\\{closure}()\\n#19 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(93): Illuminate\\\\Container\\\\Util::unwrapIfClosure(Object(Closure))\\n#20 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(35): Illuminate\\\\Container\\\\BoundMethod::callBoundMethod(Object(Illuminate\\\\Foundation\\\\Application), Array, Object(Closure))\\n#21 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\ComponentConcerns\\\\HandlesActions.php(149): Illuminate\\\\Container\\\\BoundMethod::call(Object(Illuminate\\\\Foundation\\\\Application), Array, Array)\\n#22 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\HydrationMiddleware\\\\PerformActionCalls.php(36): Livewire\\\\Component->callMethod('updateSelectedS...', Array, Object(Closure))\\n#23 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\LifecycleManager.php(89): Livewire\\\\HydrationMiddleware\\\\PerformActionCalls::hydrate(Object(App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView), Object(Livewire\\\\Request))\\n#24 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\Connection\\\\ConnectionHandler.php(13): Livewire\\\\LifecycleManager->hydrate()\\n#25 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\Controllers\\\\HttpConnectionHandler.php(19): Livewire\\\\Connection\\\\ConnectionHandler->handle(Array)\\n#26 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\ControllerDispatcher.php(48): Livewire\\\\Controllers\\\\HttpConnectionHandler->__invoke('project.task-bo...')\\n#27 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(261): Illuminate\\\\Routing\\\\ControllerDispatcher->dispatch(Object(Illuminate\\\\Routing\\\\Route), Object(Livewire\\\\Controllers\\\\HttpConnectionHandler), '__invoke')\\n#28 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(205): Illuminate\\\\Routing\\\\Route->runController()\\n#29 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(721): Illuminate\\\\Routing\\\\Route->run()\\n#30 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(128): Illuminate\\\\Routing\\\\Router->Illuminate\\\\Routing\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#31 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\AkauntingCompanyMiddleware.php(50): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#32 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\AkauntingCompanyMiddleware->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#33 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\CheckSuperLogin.php(42): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#34 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\CheckSuperLogin->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#35 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\ThemeLayout.php(29): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#36 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\ThemeLayout->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#37 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings.php(50): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#38 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#39 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken.php(78): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#40 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#41 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession.php(49): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#42 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#43 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#44 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#45 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#46 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse.php(37): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#47 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#48 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php(67): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#49 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#50 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(103): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#51 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(719): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#52 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(698): Illuminate\\\\Routing\\\\Router->runRouteWithinStack(Object(Illuminate\\\\Routing\\\\Route), Object(Illuminate\\\\Http\\\\Request))\\n#53 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(662): Illuminate\\\\Routing\\\\Router->runRoute(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Routing\\\\Route))\\n#54 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(651): Illuminate\\\\Routing\\\\Router->dispatchToRoute(Object(Illuminate\\\\Http\\\\Request))\\n#55 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(167): Illuminate\\\\Routing\\\\Router->dispatch(Object(Illuminate\\\\Http\\\\Request))\\n#56 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(128): Illuminate\\\\Foundation\\\\Http\\\\Kernel->Illuminate\\\\Foundation\\\\Http\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#57 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\DisableBrowserCache.php(19): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#58 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Livewire\\\\DisableBrowserCache->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#59 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\barryvdh\\\\laravel-debugbar\\\\src\\\\Middleware\\\\InjectDebugbar.php(66): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#60 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#61 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\Localization.php(54): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#62 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\Localization->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#63 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#64 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#65 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php(36): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#66 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#67 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#68 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#69 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php(86): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#70 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#71 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\fruitcake\\\\laravel-cors\\\\src\\\\HandleCors.php(38): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#72 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Fruitcake\\\\Cors\\\\HandleCors->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#73 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\fideloper\\\\proxy\\\\src\\\\TrustProxies.php(57): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#74 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Fideloper\\\\Proxy\\\\TrustProxies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#75 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#76 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#77 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#78 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(103): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#79 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(142): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#80 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(111): Illuminate\\\\Foundation\\\\Http\\\\Kernel->sendRequestThroughRouter(Object(Illuminate\\\\Http\\\\Request))\\n#81 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\public\\\\index.php(51): Illuminate\\\\Foundation\\\\Http\\\\Kernel->handle(Object(Illuminate\\\\Http\\\\Request))\\n#82 {main}\"}", "message_html": null, "is_string": false, "label": "error", "time": 1753608461.593684, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:41] LOG.warning: Callables of the form [\"Swift_Message\", \"Swift_Mime_SimpleMessage::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\Message.php on line 46", "message_html": null, "is_string": false, "label": "warning", "time": 1753608461.598863, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:49] LOG.error: MilestoneNotificationTrait: Error sending email notification {\"error\":\"Connection could not be established with host  :stream_socket_client(): Unable to connect to :587 (No connection could be made because the target machine actively refused it)\",\"user_id\":6721,\"locale\":\"ar\",\"trace\":\"#0 [internal function]: Swift_Transport_StreamBuffer->{closure}(2, 'stream_socket_c...', 'C:\\\\\\\\laragon\\\\\\\\www\\\\\\\\...', 264)\\n#1 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\StreamBuffer.php(264): stream_socket_client(':587', 0, '', 30, 4, Resource id #1160)\\n#2 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\StreamBuffer.php(58): Swift_Transport_StreamBuffer->establishSocketConnection()\\n#3 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\AbstractSmtpTransport.php(143): Swift_Transport_StreamBuffer->initialize(Array)\\n#4 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Mailer.php(65): Swift_Transport_AbstractSmtpTransport->start()\\n#5 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(521): Swift_Mailer->send(Object(Swift_Message), Array)\\n#6 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(288): Illuminate\\\\Mail\\\\Mailer->sendSwiftMessage(Object(Swift_Message))\\n#7 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailable.php(181): Illuminate\\\\Mail\\\\Mailer->send('mail.milestone_...', Array, Object(Closure))\\n#8 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Support\\\\Traits\\\\Localizable.php(29): Illuminate\\\\Mail\\\\Mailable->Illuminate\\\\Mail\\\\{closure}()\\n#9 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailable.php(174): Illuminate\\\\Mail\\\\Mailable->withLocale('ar', Object(Closure))\\n#10 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(304): Illuminate\\\\Mail\\\\Mailable->send(Object(Illuminate\\\\Mail\\\\Mailer))\\n#11 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(258): Illuminate\\\\Mail\\\\Mailer->sendMailable(Object(App\\\\Mail\\\\MilestoneMail))\\n#12 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\PendingMail.php(124): Illuminate\\\\Mail\\\\Mailer->send(Object(App\\\\Mail\\\\MilestoneMail))\\n#13 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(127): Illuminate\\\\Mail\\\\PendingMail->send(Object(App\\\\Mail\\\\MilestoneMail))\\n#14 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(96): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->sendEmailNotification(Object(App\\\\Models\\\\CrmUser), Array, 'ar')\\n#15 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(28): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->sendMilestoneCompletionNotifications(Array)\\n#16 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView.php(141): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->checkAndSendMilestoneCompletionNotifications(Array)\\n#17 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(36): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->updateSelectedStatus()\\n#18 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\Util.php(40): Illuminate\\\\Container\\\\BoundMethod::Illuminate\\\\Container\\\\{closure}()\\n#19 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(93): Illuminate\\\\Container\\\\Util::unwrapIfClosure(Object(Closure))\\n#20 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(35): Illuminate\\\\Container\\\\BoundMethod::callBoundMethod(Object(Illuminate\\\\Foundation\\\\Application), Array, Object(Closure))\\n#21 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\ComponentConcerns\\\\HandlesActions.php(149): Illuminate\\\\Container\\\\BoundMethod::call(Object(Illuminate\\\\Foundation\\\\Application), Array, Array)\\n#22 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\HydrationMiddleware\\\\PerformActionCalls.php(36): Livewire\\\\Component->callMethod('updateSelectedS...', Array, Object(Closure))\\n#23 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\LifecycleManager.php(89): Livewire\\\\HydrationMiddleware\\\\PerformActionCalls::hydrate(Object(App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView), Object(Livewire\\\\Request))\\n#24 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\Connection\\\\ConnectionHandler.php(13): Livewire\\\\LifecycleManager->hydrate()\\n#25 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\Controllers\\\\HttpConnectionHandler.php(19): Livewire\\\\Connection\\\\ConnectionHandler->handle(Array)\\n#26 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\ControllerDispatcher.php(48): Livewire\\\\Controllers\\\\HttpConnectionHandler->__invoke('project.task-bo...')\\n#27 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(261): Illuminate\\\\Routing\\\\ControllerDispatcher->dispatch(Object(Illuminate\\\\Routing\\\\Route), Object(Livewire\\\\Controllers\\\\HttpConnectionHandler), '__invoke')\\n#28 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(205): Illuminate\\\\Routing\\\\Route->runController()\\n#29 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(721): Illuminate\\\\Routing\\\\Route->run()\\n#30 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(128): Illuminate\\\\Routing\\\\Router->Illuminate\\\\Routing\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#31 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\AkauntingCompanyMiddleware.php(50): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#32 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\AkauntingCompanyMiddleware->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#33 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\CheckSuperLogin.php(42): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#34 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\CheckSuperLogin->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#35 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\ThemeLayout.php(29): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#36 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\ThemeLayout->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#37 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings.php(50): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#38 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#39 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken.php(78): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#40 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#41 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession.php(49): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#42 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#43 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#44 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#45 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#46 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse.php(37): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#47 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#48 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php(67): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#49 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#50 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(103): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#51 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(719): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#52 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(698): Illuminate\\\\Routing\\\\Router->runRouteWithinStack(Object(Illuminate\\\\Routing\\\\Route), Object(Illuminate\\\\Http\\\\Request))\\n#53 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(662): Illuminate\\\\Routing\\\\Router->runRoute(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Routing\\\\Route))\\n#54 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(651): Illuminate\\\\Routing\\\\Router->dispatchToRoute(Object(Illuminate\\\\Http\\\\Request))\\n#55 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(167): Illuminate\\\\Routing\\\\Router->dispatch(Object(Illuminate\\\\Http\\\\Request))\\n#56 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(128): Illuminate\\\\Foundation\\\\Http\\\\Kernel->Illuminate\\\\Foundation\\\\Http\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#57 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\DisableBrowserCache.php(19): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#58 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Livewire\\\\DisableBrowserCache->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#59 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\barryvdh\\\\laravel-debugbar\\\\src\\\\Middleware\\\\InjectDebugbar.php(66): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#60 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#61 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\Localization.php(54): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#62 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\Localization->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#63 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#64 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#65 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php(36): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#66 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#67 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#68 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#69 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php(86): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#70 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#71 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\fruitcake\\\\laravel-cors\\\\src\\\\HandleCors.php(38): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#72 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Fruitcake\\\\Cors\\\\HandleCors->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#73 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\fideloper\\\\proxy\\\\src\\\\TrustProxies.php(57): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#74 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Fideloper\\\\Proxy\\\\TrustProxies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#75 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#76 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#77 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#78 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(103): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#79 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(142): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#80 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(111): Illuminate\\\\Foundation\\\\Http\\\\Kernel->sendRequestThroughRouter(Object(Illuminate\\\\Http\\\\Request))\\n#81 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\public\\\\index.php(51): Illuminate\\\\Foundation\\\\Http\\\\Kernel->handle(Object(Illuminate\\\\Http\\\\Request))\\n#82 {main}\"}", "message_html": null, "is_string": false, "label": "error", "time": 1753608469.699627, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:49] LOG.warning: Callables of the form [\"Swift_Message\", \"Swift_Mime_SimpleMessage::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\Message.php on line 46", "message_html": null, "is_string": false, "label": "warning", "time": 1753608469.712576, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:57] LOG.error: MilestoneNotificationTrait: Error sending email notification {\"error\":\"Connection could not be established with host  :stream_socket_client(): Unable to connect to :587 (No connection could be made because the target machine actively refused it)\",\"user_id\":7070,\"locale\":\"en\",\"trace\":\"#0 [internal function]: Swift_Transport_StreamBuffer->{closure}(2, 'stream_socket_c...', 'C:\\\\\\\\laragon\\\\\\\\www\\\\\\\\...', 264)\\n#1 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\StreamBuffer.php(264): stream_socket_client(':587', 0, '', 30, 4, Resource id #1163)\\n#2 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\StreamBuffer.php(58): Swift_Transport_StreamBuffer->establishSocketConnection()\\n#3 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\AbstractSmtpTransport.php(143): Swift_Transport_StreamBuffer->initialize(Array)\\n#4 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Mailer.php(65): Swift_Transport_AbstractSmtpTransport->start()\\n#5 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(521): Swift_Mailer->send(Object(Swift_Message), Array)\\n#6 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(288): Illuminate\\\\Mail\\\\Mailer->sendSwiftMessage(Object(Swift_Message))\\n#7 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailable.php(181): Illuminate\\\\Mail\\\\Mailer->send('mail.milestone_...', Array, Object(Closure))\\n#8 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Support\\\\Traits\\\\Localizable.php(29): Illuminate\\\\Mail\\\\Mailable->Illuminate\\\\Mail\\\\{closure}()\\n#9 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailable.php(174): Illuminate\\\\Mail\\\\Mailable->withLocale('en', Object(Closure))\\n#10 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(304): Illuminate\\\\Mail\\\\Mailable->send(Object(Illuminate\\\\Mail\\\\Mailer))\\n#11 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(258): Illuminate\\\\Mail\\\\Mailer->sendMailable(Object(App\\\\Mail\\\\MilestoneMail))\\n#12 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\PendingMail.php(124): Illuminate\\\\Mail\\\\Mailer->send(Object(App\\\\Mail\\\\MilestoneMail))\\n#13 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(127): Illuminate\\\\Mail\\\\PendingMail->send(Object(App\\\\Mail\\\\MilestoneMail))\\n#14 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(95): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->sendEmailNotification(Object(App\\\\Models\\\\CrmUser), Array, 'en')\\n#15 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(28): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->sendMilestoneCompletionNotifications(Array)\\n#16 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView.php(141): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->checkAndSendMilestoneCompletionNotifications(Array)\\n#17 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(36): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->updateSelectedStatus()\\n#18 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\Util.php(40): Illuminate\\\\Container\\\\BoundMethod::Illuminate\\\\Container\\\\{closure}()\\n#19 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(93): Illuminate\\\\Container\\\\Util::unwrapIfClosure(Object(Closure))\\n#20 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(35): Illuminate\\\\Container\\\\BoundMethod::callBoundMethod(Object(Illuminate\\\\Foundation\\\\Application), Array, Object(Closure))\\n#21 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\ComponentConcerns\\\\HandlesActions.php(149): Illuminate\\\\Container\\\\BoundMethod::call(Object(Illuminate\\\\Foundation\\\\Application), Array, Array)\\n#22 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\HydrationMiddleware\\\\PerformActionCalls.php(36): Livewire\\\\Component->callMethod('updateSelectedS...', Array, Object(Closure))\\n#23 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\LifecycleManager.php(89): Livewire\\\\HydrationMiddleware\\\\PerformActionCalls::hydrate(Object(App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView), Object(Livewire\\\\Request))\\n#24 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\Connection\\\\ConnectionHandler.php(13): Livewire\\\\LifecycleManager->hydrate()\\n#25 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\Controllers\\\\HttpConnectionHandler.php(19): Livewire\\\\Connection\\\\ConnectionHandler->handle(Array)\\n#26 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\ControllerDispatcher.php(48): Livewire\\\\Controllers\\\\HttpConnectionHandler->__invoke('project.task-bo...')\\n#27 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(261): Illuminate\\\\Routing\\\\ControllerDispatcher->dispatch(Object(Illuminate\\\\Routing\\\\Route), Object(Livewire\\\\Controllers\\\\HttpConnectionHandler), '__invoke')\\n#28 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(205): Illuminate\\\\Routing\\\\Route->runController()\\n#29 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(721): Illuminate\\\\Routing\\\\Route->run()\\n#30 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(128): Illuminate\\\\Routing\\\\Router->Illuminate\\\\Routing\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#31 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\AkauntingCompanyMiddleware.php(50): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#32 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\AkauntingCompanyMiddleware->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#33 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\CheckSuperLogin.php(42): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#34 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\CheckSuperLogin->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#35 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\ThemeLayout.php(29): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#36 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\ThemeLayout->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#37 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings.php(50): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#38 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#39 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken.php(78): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#40 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#41 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession.php(49): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#42 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#43 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#44 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#45 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#46 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse.php(37): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#47 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#48 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php(67): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#49 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#50 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(103): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#51 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(719): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#52 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(698): Illuminate\\\\Routing\\\\Router->runRouteWithinStack(Object(Illuminate\\\\Routing\\\\Route), Object(Illuminate\\\\Http\\\\Request))\\n#53 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(662): Illuminate\\\\Routing\\\\Router->runRoute(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Routing\\\\Route))\\n#54 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(651): Illuminate\\\\Routing\\\\Router->dispatchToRoute(Object(Illuminate\\\\Http\\\\Request))\\n#55 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(167): Illuminate\\\\Routing\\\\Router->dispatch(Object(Illuminate\\\\Http\\\\Request))\\n#56 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(128): Illuminate\\\\Foundation\\\\Http\\\\Kernel->Illuminate\\\\Foundation\\\\Http\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#57 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\DisableBrowserCache.php(19): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#58 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Livewire\\\\DisableBrowserCache->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#59 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\barryvdh\\\\laravel-debugbar\\\\src\\\\Middleware\\\\InjectDebugbar.php(66): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#60 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#61 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\Localization.php(54): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#62 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\Localization->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#63 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#64 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#65 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php(36): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#66 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#67 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#68 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#69 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php(86): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#70 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#71 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\fruitcake\\\\laravel-cors\\\\src\\\\HandleCors.php(38): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#72 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Fruitcake\\\\Cors\\\\HandleCors->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#73 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\fideloper\\\\proxy\\\\src\\\\TrustProxies.php(57): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#74 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Fideloper\\\\Proxy\\\\TrustProxies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#75 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#76 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#77 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#78 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(103): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#79 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(142): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#80 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(111): Illuminate\\\\Foundation\\\\Http\\\\Kernel->sendRequestThroughRouter(Object(Illuminate\\\\Http\\\\Request))\\n#81 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\public\\\\index.php(51): Illuminate\\\\Foundation\\\\Http\\\\Kernel->handle(Object(Illuminate\\\\Http\\\\Request))\\n#82 {main}\"}", "message_html": null, "is_string": false, "label": "error", "time": 1753608477.831288, "xdebug_link": null, "collector": "log"}, {"message": "[12:27:57] LOG.warning: Callables of the form [\"Swift_Message\", \"Swift_Mime_SimpleMessage::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\Message.php on line 46", "message_html": null, "is_string": false, "label": "warning", "time": 1753608477.841988, "xdebug_link": null, "collector": "log"}, {"message": "[12:28:05] LOG.error: MilestoneNotificationTrait: Error sending email notification {\"error\":\"Connection could not be established with host  :stream_socket_client(): Unable to connect to :587 (No connection could be made because the target machine actively refused it)\",\"user_id\":7070,\"locale\":\"ar\",\"trace\":\"#0 [internal function]: Swift_Transport_StreamBuffer->{closure}(2, 'stream_socket_c...', 'C:\\\\\\\\laragon\\\\\\\\www\\\\\\\\...', 264)\\n#1 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\StreamBuffer.php(264): stream_socket_client(':587', 0, '', 30, 4, Resource id #1166)\\n#2 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\StreamBuffer.php(58): Swift_Transport_StreamBuffer->establishSocketConnection()\\n#3 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Transport\\\\AbstractSmtpTransport.php(143): Swift_Transport_StreamBuffer->initialize(Array)\\n#4 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\swiftmailer\\\\swiftmailer\\\\lib\\\\classes\\\\Swift\\\\Mailer.php(65): Swift_Transport_AbstractSmtpTransport->start()\\n#5 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(521): Swift_Mailer->send(Object(Swift_Message), Array)\\n#6 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(288): Illuminate\\\\Mail\\\\Mailer->sendSwiftMessage(Object(Swift_Message))\\n#7 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailable.php(181): Illuminate\\\\Mail\\\\Mailer->send('mail.milestone_...', Array, Object(Closure))\\n#8 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Support\\\\Traits\\\\Localizable.php(29): Illuminate\\\\Mail\\\\Mailable->Illuminate\\\\Mail\\\\{closure}()\\n#9 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailable.php(174): Illuminate\\\\Mail\\\\Mailable->withLocale('ar', Object(Closure))\\n#10 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(304): Illuminate\\\\Mail\\\\Mailable->send(Object(Illuminate\\\\Mail\\\\Mailer))\\n#11 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\Mailer.php(258): Illuminate\\\\Mail\\\\Mailer->sendMailable(Object(App\\\\Mail\\\\MilestoneMail))\\n#12 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Mail\\\\PendingMail.php(124): Illuminate\\\\Mail\\\\Mailer->send(Object(App\\\\Mail\\\\MilestoneMail))\\n#13 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(127): Illuminate\\\\Mail\\\\PendingMail->send(Object(App\\\\Mail\\\\MilestoneMail))\\n#14 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(96): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->sendEmailNotification(Object(App\\\\Models\\\\CrmUser), Array, 'ar')\\n#15 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Traits\\\\MilestoneNotificationTrait.php(28): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->sendMilestoneCompletionNotifications(Array)\\n#16 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView.php(141): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->checkAndSendMilestoneCompletionNotifications(Array)\\n#17 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(36): App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView->updateSelectedStatus()\\n#18 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\Util.php(40): Illuminate\\\\Container\\\\BoundMethod::Illuminate\\\\Container\\\\{closure}()\\n#19 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(93): Illuminate\\\\Container\\\\Util::unwrapIfClosure(Object(Closure))\\n#20 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(35): Illuminate\\\\Container\\\\BoundMethod::callBoundMethod(Object(Illuminate\\\\Foundation\\\\Application), Array, Object(Closure))\\n#21 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\ComponentConcerns\\\\HandlesActions.php(149): Illuminate\\\\Container\\\\BoundMethod::call(Object(Illuminate\\\\Foundation\\\\Application), Array, Array)\\n#22 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\HydrationMiddleware\\\\PerformActionCalls.php(36): Livewire\\\\Component->callMethod('updateSelectedS...', Array, Object(Closure))\\n#23 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\LifecycleManager.php(89): Livewire\\\\HydrationMiddleware\\\\PerformActionCalls::hydrate(Object(App\\\\Http\\\\Livewire\\\\Project\\\\TaskBoard\\\\ListView), Object(Livewire\\\\Request))\\n#24 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\Connection\\\\ConnectionHandler.php(13): Livewire\\\\LifecycleManager->hydrate()\\n#25 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\Controllers\\\\HttpConnectionHandler.php(19): Livewire\\\\Connection\\\\ConnectionHandler->handle(Array)\\n#26 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\ControllerDispatcher.php(48): Livewire\\\\Controllers\\\\HttpConnectionHandler->__invoke('project.task-bo...')\\n#27 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(261): Illuminate\\\\Routing\\\\ControllerDispatcher->dispatch(Object(Illuminate\\\\Routing\\\\Route), Object(Livewire\\\\Controllers\\\\HttpConnectionHandler), '__invoke')\\n#28 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Route.php(205): Illuminate\\\\Routing\\\\Route->runController()\\n#29 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(721): Illuminate\\\\Routing\\\\Route->run()\\n#30 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(128): Illuminate\\\\Routing\\\\Router->Illuminate\\\\Routing\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#31 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\AkauntingCompanyMiddleware.php(50): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#32 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\AkauntingCompanyMiddleware->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#33 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\CheckSuperLogin.php(42): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#34 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\CheckSuperLogin->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#35 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\ThemeLayout.php(29): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#36 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\ThemeLayout->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#37 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings.php(50): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#38 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#39 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken.php(78): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#40 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\VerifyCsrfToken->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#41 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession.php(49): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#42 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#43 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#44 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#45 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#46 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse.php(37): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#47 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#48 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies.php(67): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#49 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#50 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(103): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#51 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(719): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#52 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(698): Illuminate\\\\Routing\\\\Router->runRouteWithinStack(Object(Illuminate\\\\Routing\\\\Route), Object(Illuminate\\\\Http\\\\Request))\\n#53 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(662): Illuminate\\\\Routing\\\\Router->runRoute(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Routing\\\\Route))\\n#54 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Routing\\\\Router.php(651): Illuminate\\\\Routing\\\\Router->dispatchToRoute(Object(Illuminate\\\\Http\\\\Request))\\n#55 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(167): Illuminate\\\\Routing\\\\Router->dispatch(Object(Illuminate\\\\Http\\\\Request))\\n#56 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(128): Illuminate\\\\Foundation\\\\Http\\\\Kernel->Illuminate\\\\Foundation\\\\Http\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#57 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\livewire\\\\livewire\\\\src\\\\DisableBrowserCache.php(19): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#58 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Livewire\\\\DisableBrowserCache->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#59 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\barryvdh\\\\laravel-debugbar\\\\src\\\\Middleware\\\\InjectDebugbar.php(66): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#60 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#61 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\app\\\\Http\\\\Middleware\\\\Localization.php(54): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#62 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): App\\\\Http\\\\Middleware\\\\Localization->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#63 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#64 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#65 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings.php(36): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#66 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#67 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize.php(27): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#68 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#69 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance.php(86): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#70 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#71 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\fruitcake\\\\laravel-cors\\\\src\\\\HandleCors.php(38): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#72 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Fruitcake\\\\Cors\\\\HandleCors->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#73 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\fideloper\\\\proxy\\\\src\\\\TrustProxies.php(57): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#74 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Fideloper\\\\Proxy\\\\TrustProxies->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#75 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(121): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#76 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Session\\\\Middleware\\\\StartSession.php(64): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handleStatefulRequest(Object(Illuminate\\\\Http\\\\Request), Object(Illuminate\\\\Session\\\\Store), Object(Closure))\\n#77 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(167): Illuminate\\\\Session\\\\Middleware\\\\StartSession->handle(Object(Illuminate\\\\Http\\\\Request), Object(Closure))\\n#78 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Pipeline\\\\Pipeline.php(103): Illuminate\\\\Pipeline\\\\Pipeline->Illuminate\\\\Pipeline\\\\{closure}(Object(Illuminate\\\\Http\\\\Request))\\n#79 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(142): Illuminate\\\\Pipeline\\\\Pipeline->then(Object(Closure))\\n#80 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Http\\\\Kernel.php(111): Illuminate\\\\Foundation\\\\Http\\\\Kernel->sendRequestThroughRouter(Object(Illuminate\\\\Http\\\\Request))\\n#81 C:\\\\laragon\\\\www\\\\Osool-B2G\\\\public\\\\index.php(51): Illuminate\\\\Foundation\\\\Http\\\\Kernel->handle(Object(Illuminate\\\\Http\\\\Request))\\n#82 {main}\"}", "message_html": null, "is_string": false, "label": "error", "time": 1753608485.970621, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753608451.490868, "end": 1753608487.437498, "duration": 35.946630001068115, "duration_str": "35.95s", "measures": [{"label": "Booting", "start": 1753608451.490868, "relative_start": 0, "end": 1753608451.826673, "relative_end": 1753608451.826673, "duration": 0.33580493927001953, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753608451.826684, "relative_start": 0.3358159065246582, "end": 1753608487.437503, "relative_end": 5.0067901611328125e-06, "duration": 35.61081910133362, "duration_str": "35.61s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 41210072, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 11, "templates": [{"name": "mail.milestone_notification (\\resources\\views\\mail\\milestone_notification.blade.php)", "param_count": 26, "params": ["milestoneData", "notificationType", "locale", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained", "message", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "mail.milestone_notification (\\resources\\views\\mail\\milestone_notification.blade.php)", "param_count": 26, "params": ["milestoneData", "notificationType", "locale", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained", "message", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "mail.milestone_notification (\\resources\\views\\mail\\milestone_notification.blade.php)", "param_count": 26, "params": ["milestoneData", "notificationType", "locale", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained", "message", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "mail.milestone_notification (\\resources\\views\\mail\\milestone_notification.blade.php)", "param_count": 26, "params": ["milestoneData", "notificationType", "locale", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained", "message", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.project.task-board.list-view (\\resources\\views\\livewire\\project\\task-board\\list-view.blade.php)", "param_count": 46, "params": ["errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.project.bug-report.modals.Task-Initialise-WorkOrder (\\resources\\views\\livewire\\project\\bug-report\\modals\\Task-Initialise-WorkOrder.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.show-untangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\show-untangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.edit-untangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\edit-untangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.edit-tangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\edit-tangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.Bulk-Actions.assignUserToTaskBulk (\\resources\\views\\livewire\\project\\task-board\\modals\\Bulk-Actions\\assignUserToTaskBulk.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.Bulk-Actions.updateTaskBulk (\\resources\\views\\livewire\\project\\task-board\\modals\\Bulk-Actions\\updateTaskBulk.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.023600000000000003, "accumulated_duration_str": "23.6ms", "statements": [{"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00296, "duration_str": "2.96ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 12.542}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7070 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_test_db", "start_percent": 12.542, "width_percent": 1.864}, {"sql": "select * from `crm_user` where `crm_user_id` in (693, 680)", "type": "query", "params": [], "bindings": ["693", "680"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 61}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 28}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 141}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:61", "connection": "osool_test_db", "start_percent": 14.407, "width_percent": 4.195}, {"sql": "insert into `notifications` (`user_id`, `message`, `is_read`, `created_at`) values (6721, 'تم إكمال «Future».', 0, '2025-07-27 12:27:33')", "type": "query", "params": [], "bindings": ["6721", "تم إكمال &laquo;Future&raquo;.", "0", "2025-07-27 12:27:33"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 85}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 28}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 141}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0040999999999999995, "duration_str": "4.1ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:85", "connection": "osool_test_db", "start_percent": 18.602, "width_percent": 17.373}, {"sql": "select * from `users` where `users`.`id` = 6721 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6721"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 117}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 95}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 28}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:117", "connection": "osool_test_db", "start_percent": 35.975, "width_percent": 4.661}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 173 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 40.636, "width_percent": 2.458}, {"sql": "select * from `users` where `users`.`id` = 6721 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6721"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 117}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 96}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 28}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00216, "duration_str": "2.16ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:117", "connection": "osool_test_db", "start_percent": 43.093, "width_percent": 9.153}, {"sql": "insert into `notifications` (`user_id`, `message`, `is_read`, `created_at`) values (7070, 'تم إكمال «Future».', 0, '2025-07-27 12:27:49')", "type": "query", "params": [], "bindings": ["7070", "تم إكمال &laquo;Future&raquo;.", "0", "2025-07-27 12:27:49"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 85}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 28}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 141}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.005, "duration_str": "5ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:85", "connection": "osool_test_db", "start_percent": 52.246, "width_percent": 21.186}, {"sql": "select * from `users` where `users`.`id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 117}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 95}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 28}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.0017900000000000001, "duration_str": "1.79ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:117", "connection": "osool_test_db", "start_percent": 73.432, "width_percent": 7.585}, {"sql": "select * from `users` where `users`.`id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 117}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 96}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 28}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.0044800000000000005, "duration_str": "4.48ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:117", "connection": "osool_test_db", "start_percent": 81.017, "width_percent": 18.983}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\CrmUser": 2, "App\\Models\\UserCompany": 1, "App\\Models\\User": 5}, "count": 9}, "livewire": {"data": {"project.task-board.list-view #dN0pezX5AGQJDyNPeapu": "array:5 [\n  \"data\" => array:31 [\n    \"records\" => array:9 [\n      \"items\" => array:3 [\n        0 => array:21 [\n          \"id\" => 1690\n          \"title\" => \"task 1\"\n          \"priority\" => \"Medium\"\n          \"description\" => \"test\"\n          \"start_date\" => \"2025-07-24 13:37:01\"\n          \"due_date\" => \"2025-07-25 13:37:01\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 680\n              \"name\" => \"Khalil POA\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"Notifications\"\n          \"milestone_name\" => \"Future\"\n          \"milestone_id\" => 588\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-24T13:37:01.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:38:59.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        1 => array:21 [\n          \"id\" => 1691\n          \"title\" => \"task 1\"\n          \"priority\" => \"Medium\"\n          \"description\" => \"test\"\n          \"start_date\" => \"2025-07-24 13:37:31\"\n          \"due_date\" => \"2025-07-25 13:37:31\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 680\n              \"name\" => \"Khalil POA\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"Notifications\"\n          \"milestone_name\" => \"Future\"\n          \"milestone_id\" => 588\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-24T13:37:31.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:38:59.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        2 => array:21 [\n          \"id\" => 1700\n          \"title\" => \"TASK2\"\n          \"priority\" => \"High\"\n          \"description\" => \"test\"\n          \"start_date\" => \"2025-07-27 09:27:08\"\n          \"due_date\" => \"2025-07-27 09:27:08\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 680\n              \"name\" => \"Khalil POA\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"Notifications\"\n          \"milestone_name\" => \"Future\"\n          \"milestone_id\" => 588\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-27T09:27:08.000000Z\"\n          \"updated_at\" => \"2025-07-27T09:27:34.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n      ]\n      \"next_page_url\" => null\n      \"prev_page_url\" => null\n      \"per_page\" => 10\n      \"total\" => 3\n      \"allUsers\" => array:2 [\n        0 => array:3 [\n          \"id\" => 693\n          \"name\" => \"Staff\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n        1 => array:3 [\n          \"id\" => 680\n          \"name\" => \"Khalil POA\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n      ]\n      \"allMilestones\" => array:4 [\n        0 => array:2 [\n          \"id\" => 585\n          \"title\" => \"test\"\n        ]\n        1 => array:2 [\n          \"id\" => 586\n          \"title\" => \"test2\"\n        ]\n        2 => array:2 [\n          \"id\" => 587\n          \"title\" => \"Past\"\n        ]\n        3 => array:2 [\n          \"id\" => 588\n          \"title\" => \"Future\"\n        ]\n      ]\n      \"priority\" => array:3 [\n        \"Low\" => \"Low\"\n        \"Medium\" => \"Medium\"\n        \"High\" => \"High\"\n      ]\n      \"taskStages\" => array:4 [\n        0 => array:9 [\n          \"id\" => 475\n          \"name\" => \"Todo\"\n          \"color\" => \"#77b6ea\"\n          \"complete\" => 0\n          \"workspace_id\" => 132\n          \"order\" => 0\n          \"created_by\" => 680\n          \"created_at\" => \"2025-07-21T13:59:10.000000Z\"\n          \"updated_at\" => \"2025-07-21T13:59:10.000000Z\"\n        ]\n        1 => array:9 [\n          \"id\" => 476\n          \"name\" => \"In Progress\"\n          \"color\" => \"#545454\"\n          \"complete\" => 0\n          \"workspace_id\" => 132\n          \"order\" => 1\n          \"created_by\" => 680\n          \"created_at\" => \"2025-07-21T13:59:10.000000Z\"\n          \"updated_at\" => \"2025-07-21T13:59:10.000000Z\"\n        ]\n        2 => array:9 [\n          \"id\" => 477\n          \"name\" => \"Review\"\n          \"color\" => \"#3cb8d9\"\n          \"complete\" => 0\n          \"workspace_id\" => 132\n          \"order\" => 2\n          \"created_by\" => 680\n          \"created_at\" => \"2025-07-21T13:59:10.000000Z\"\n          \"updated_at\" => \"2025-07-21T13:59:10.000000Z\"\n        ]\n        3 => array:9 [\n          \"id\" => 478\n          \"name\" => \"Done\"\n          \"color\" => \"#37b37e\"\n          \"complete\" => 0\n          \"workspace_id\" => 132\n          \"order\" => 3\n          \"created_by\" => 680\n          \"created_at\" => \"2025-07-21T13:59:10.000000Z\"\n          \"updated_at\" => \"2025-07-21T13:59:10.000000Z\"\n        ]\n      ]\n    ]\n    \"itemId\" => 649\n    \"priorities\" => array:3 [\n      \"Low\" => \"Low\"\n      \"Medium\" => \"Medium\"\n      \"High\" => \"High\"\n    ]\n    \"usersForAssign\" => array:2 [\n      0 => array:3 [\n        \"id\" => 693\n        \"name\" => \"Staff\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 680\n        \"name\" => \"Khalil POA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"userAssigned\" => \"\"\n    \"milestonesList\" => array:4 [\n      0 => array:2 [\n        \"id\" => 585\n        \"title\" => \"test\"\n      ]\n      1 => array:2 [\n        \"id\" => 586\n        \"title\" => \"test2\"\n      ]\n      2 => array:2 [\n        \"id\" => 587\n        \"title\" => \"Past\"\n      ]\n      3 => array:2 [\n        \"id\" => 588\n        \"title\" => \"Future\"\n      ]\n    ]\n    \"selectedmilestone\" => \"\"\n    \"prioritiesList\" => array:3 [\n      \"Low\" => \"Low\"\n      \"Medium\" => \"Medium\"\n      \"High\" => \"High\"\n    ]\n    \"selectedprioritie\" => \"\"\n    \"work_order_type\" => null\n    \"viewType\" => null\n    \"Bulk_assign_to\" => null\n    \"selected_WorkOrder\" => \"\"\n    \"work_orders_list\" => []\n    \"selected_work_order_data\" => null\n    \"sortField\" => \"\"\n    \"sortByPriority\" => \"\"\n    \"sortDirection\" => \"asc\"\n    \"start_date\" => \"\"\n    \"assign_to\" => \"\"\n    \"end_date\" => \"\"\n    \"change_status\" => \"Done\"\n    \"users\" => array:2 [\n      0 => array:3 [\n        \"id\" => 693\n        \"name\" => \"Staff\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 680\n        \"name\" => \"Khalil POA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"selectedTasks\" => []\n    \"taskStages\" => array:4 [\n      0 => array:9 [\n        \"id\" => 475\n        \"name\" => \"Todo\"\n        \"color\" => \"#77b6ea\"\n        \"complete\" => 0\n        \"workspace_id\" => 132\n        \"order\" => 0\n        \"created_by\" => 680\n        \"created_at\" => \"2025-07-21T13:59:10.000000Z\"\n        \"updated_at\" => \"2025-07-21T13:59:10.000000Z\"\n      ]\n      1 => array:9 [\n        \"id\" => 476\n        \"name\" => \"In Progress\"\n        \"color\" => \"#545454\"\n        \"complete\" => 0\n        \"workspace_id\" => 132\n        \"order\" => 1\n        \"created_by\" => 680\n        \"created_at\" => \"2025-07-21T13:59:10.000000Z\"\n        \"updated_at\" => \"2025-07-21T13:59:10.000000Z\"\n      ]\n      2 => array:9 [\n        \"id\" => 477\n        \"name\" => \"Review\"\n        \"color\" => \"#3cb8d9\"\n        \"complete\" => 0\n        \"workspace_id\" => 132\n        \"order\" => 2\n        \"created_by\" => 680\n        \"created_at\" => \"2025-07-21T13:59:10.000000Z\"\n        \"updated_at\" => \"2025-07-21T13:59:10.000000Z\"\n      ]\n      3 => array:9 [\n        \"id\" => 478\n        \"name\" => \"Done\"\n        \"color\" => \"#37b37e\"\n        \"complete\" => 0\n        \"workspace_id\" => 132\n        \"order\" => 3\n        \"created_by\" => 680\n        \"created_at\" => \"2025-07-21T13:59:10.000000Z\"\n        \"updated_at\" => \"2025-07-21T13:59:10.000000Z\"\n      ]\n    ]\n    \"milestones\" => array:4 [\n      0 => array:2 [\n        \"id\" => 585\n        \"title\" => \"test\"\n      ]\n      1 => array:2 [\n        \"id\" => 586\n        \"title\" => \"test2\"\n      ]\n      2 => array:2 [\n        \"id\" => 587\n        \"title\" => \"Past\"\n      ]\n      3 => array:2 [\n        \"id\" => 588\n        \"title\" => \"Future\"\n      ]\n    ]\n    \"id_task\" => null\n    \"activeTab\" => \"comments\"\n    \"taskDetails\" => array:10 [\n      \"id_task\" => 0\n      \"title\" => \"\"\n      \"priority\" => \"\"\n      \"assign_to\" => []\n      \"milestone\" => \"\"\n      \"description\" => \"\"\n      \"start_date\" => \"\"\n      \"due_date\" => \"\"\n      \"comments\" => []\n      \"users\" => []\n    ]\n    \"taskEdit\" => array:13 [\n      \"title\" => \"\"\n      \"priority\" => \"\"\n      \"selected_priority\" => \"\"\n      \"assign_to\" => []\n      \"selected_assign_to\" => []\n      \"milestone\" => \"\"\n      \"selected_milestone\" => \"\"\n      \"description\" => \"\"\n      \"start_date\" => \"\"\n      \"due_date\" => \"\"\n      \"workorder_id\" => \"\"\n      \"workorder_type\" => \"\"\n      \"property_name\" => \"\"\n    ]\n    \"currentPage\" => array:1 [\n      \"fetchData\" => 1\n    ]\n  ]\n  \"name\" => \"project.task-board.list-view\"\n  \"view\" => \"livewire.project.task-board.list-view\"\n  \"component\" => \"App\\Http\\Livewire\\Project\\TaskBoard\\ListView\"\n  \"id\" => \"dN0pezX5AGQJDyNPeapu\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu", "captcha_answer": "14", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=X8d689a914b9a8fae8b430b0b3c69d830&op=get\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7070", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/project.task-board.list-view", "status_code": "<pre class=sf-dump id=sf-dump-104602369 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-104602369\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-875956861 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-875956861\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1932810980 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">dN0pezX5AGQJDyNPeapu</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">project.task-board.list-view</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"233 characters\">CRMProjects/eyJpdiI6ImhPRjFXamgzY08rSEFPRmh4SUdUWEE9PSIsInZhbHVlIjoiajVJZ2hZeHFCb1I1dzFpUjZpNSt0Zz09IiwibWFjIjoiMmNiNWUyYmY4Nzk3NDk1MjBmOWQ0NjgwMmJiMjQwYjQ0ZjQzMTNjZGFhYzQ0ZTkyZTIwMDdjYzc5ODU5NGI0ZCIsInRhZyI6IiJ9/task-board-list-view</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>l38340390-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">eIF4Ix6kssc03bBDfRbt</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>comments-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">9fpxOY3D64blPft4C1EE</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>l38340390-1</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">q97ThQKIDUru0Z6ZLh3b</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">e75e4b11</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:31</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>records</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1690</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"6 characters\">task 1</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-24 13:37:01</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-25 13:37:01</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>680</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Khalil POA</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Notifications</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Future</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>588</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-24T13:37:01.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-24T13:38:59.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1691</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"6 characters\">task 1</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-24 13:37:31</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-25 13:37:31</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>680</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Khalil POA</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Notifications</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Future</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>588</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-24T13:37:31.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-24T13:38:59.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1700</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">TASK2</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-27 09:27:08</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-27 09:27:08</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>680</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Khalil POA</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Notifications</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Future</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>588</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Todo</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-27T09:27:08.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-27T09:27:08.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>next_page_url</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>prev_page_url</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>per_page</span>\" => <span class=sf-dump-num>10</span>\n        \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>allUsers</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>693</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Staff</span>\"\n            \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>680</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Khalil POA</span>\"\n            \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>allMilestones</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>585</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>586</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">test2</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>587</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Past</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>588</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Future</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n          \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n          \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>taskStages</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>475</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Todo</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#77b6ea</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>132</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>680</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>476</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#545454</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>132</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>680</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>477</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Review</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#3cb8d9</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>132</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>680</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>478</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#37b37e</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>132</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>680</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>itemId</span>\" => <span class=sf-dump-num>649</span>\n      \"<span class=sf-dump-key>priorities</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n        \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n        \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>usersForAssign</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>693</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Staff</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>680</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Khalil POA</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>userAssigned</span>\" => \"\"\n      \"<span class=sf-dump-key>milestonesList</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>585</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>586</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">test2</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>587</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Past</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>588</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Future</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>selectedmilestone</span>\" => \"\"\n      \"<span class=sf-dump-key>prioritiesList</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n        \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n        \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>selectedprioritie</span>\" => \"\"\n      \"<span class=sf-dump-key>work_order_type</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>viewType</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>Bulk_assign_to</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selected_WorkOrder</span>\" => \"\"\n      \"<span class=sf-dump-key>work_orders_list</span>\" => []\n      \"<span class=sf-dump-key>selected_work_order_data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"\"\n      \"<span class=sf-dump-key>sortByPriority</span>\" => \"\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>start_date</span>\" => \"\"\n      \"<span class=sf-dump-key>assign_to</span>\" => \"\"\n      \"<span class=sf-dump-key>end_date</span>\" => \"\"\n      \"<span class=sf-dump-key>change_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n      \"<span class=sf-dump-key>users</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>693</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Staff</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"23 characters\"><EMAIL></span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>680</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Khalil POA</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>selectedTasks</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1700</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>taskStages</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>475</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Todo</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#77b6ea</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>132</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>680</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>476</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#545454</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>132</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>680</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>477</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Review</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#3cb8d9</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>132</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>680</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>478</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#37b37e</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>132</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>680</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T13:59:10.000000Z</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>milestones</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>585</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>586</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">test2</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>587</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Past</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>588</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Future</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>id_task</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>activeTab</span>\" => \"<span class=sf-dump-str title=\"8 characters\">comments</span>\"\n      \"<span class=sf-dump-key>taskDetails</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id_task</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>title</span>\" => \"\"\n        \"<span class=sf-dump-key>priority</span>\" => \"\"\n        \"<span class=sf-dump-key>assign_to</span>\" => []\n        \"<span class=sf-dump-key>milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>description</span>\" => \"\"\n        \"<span class=sf-dump-key>start_date</span>\" => \"\"\n        \"<span class=sf-dump-key>due_date</span>\" => \"\"\n        \"<span class=sf-dump-key>comments</span>\" => []\n        \"<span class=sf-dump-key>users</span>\" => []\n      </samp>]\n      \"<span class=sf-dump-key>taskEdit</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>title</span>\" => \"\"\n        \"<span class=sf-dump-key>priority</span>\" => \"\"\n        \"<span class=sf-dump-key>selected_priority</span>\" => \"\"\n        \"<span class=sf-dump-key>assign_to</span>\" => []\n        \"<span class=sf-dump-key>selected_assign_to</span>\" => []\n        \"<span class=sf-dump-key>milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>selected_milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>description</span>\" => \"\"\n        \"<span class=sf-dump-key>start_date</span>\" => \"\"\n        \"<span class=sf-dump-key>due_date</span>\" => \"\"\n        \"<span class=sf-dump-key>workorder_id</span>\" => \"\"\n        \"<span class=sf-dump-key>workorder_type</span>\" => \"\"\n        \"<span class=sf-dump-key>property_name</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>currentPage</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>fetchData</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">2793d1ed0cd7722b628521aba1d4f259841df5a816d21c99929ac4bbdf32789e</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">ijrz</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateSelectedStatus</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932810980\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-299512341 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6039</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"262 characters\">http://osool-b2g.test/CRMProjects/eyJpdiI6ImhPRjFXamgzY08rSEFPRmh4SUdUWEE9PSIsInZhbHVlIjoiajVJZ2hZeHFCb1I1dzFpUjZpNSt0Zz09IiwibWFjIjoiMmNiNWUyYmY4Nzk3NDk1MjBmOWQ0NjgwMmJiMjQwYjQ0ZjQzMTNjZGFhYzQ0ZTkyZTIwMDdjYzc5ODU5NGI0ZCIsInRhZyI6IiJ9/task-board-list-view?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IlNaUXBOYzFoR3I3NXFuZ1JJUVNTd2c9PSIsInZhbHVlIjoidDVOWERBVlk4WUplWm1oQ0k4ZjZVbE4xWE13RkEzRkFIdHJIZ2Z3UGRXQkhJeTNhVzVacjF2OUt2K1FLNjRVYm9NK0dsNERiOTZNeWMxaHZlelNqR2VZOXd3TFRyNDg3dkZ6MEhsYnhmN2l6LzNGOVhTbkptdEg3Y2ZBUWkrcWQiLCJtYWMiOiJhM2E4MzEzZTczZDQxNjY2MzdmZmE0Y2RiMGVkYjBiMjNhMzIwNjZiNTA2ZDk3ZjQ4MDYyODkyMjdiY2U4N2M1IiwidGFnIjoiIn0%3D; osool_session=BuE5KKIk1JvS6OomqXykNzGi9OJrSA7wV71lIXz5</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299512341\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2067644043 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6039</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"262 characters\">http://osool-b2g.test/CRMProjects/eyJpdiI6ImhPRjFXamgzY08rSEFPRmh4SUdUWEE9PSIsInZhbHVlIjoiajVJZ2hZeHFCb1I1dzFpUjZpNSt0Zz09IiwibWFjIjoiMmNiNWUyYmY4Nzk3NDk1MjBmOWQ0NjgwMmJiMjQwYjQ0ZjQzMTNjZGFhYzQ0ZTkyZTIwMDdjYzc5ODU5NGI0ZCIsInRhZyI6IiJ9/task-board-list-view?page=1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IlNaUXBOYzFoR3I3NXFuZ1JJUVNTd2c9PSIsInZhbHVlIjoidDVOWERBVlk4WUplWm1oQ0k4ZjZVbE4xWE13RkEzRkFIdHJIZ2Z3UGRXQkhJeTNhVzVacjF2OUt2K1FLNjRVYm9NK0dsNERiOTZNeWMxaHZlelNqR2VZOXd3TFRyNDg3dkZ6MEhsYnhmN2l6LzNGOVhTbkptdEg3Y2ZBUWkrcWQiLCJtYWMiOiJhM2E4MzEzZTczZDQxNjY2MzdmZmE0Y2RiMGVkYjBiMjNhMzIwNjZiNTA2ZDk3ZjQ4MDYyODkyMjdiY2U4N2M1IiwidGFnIjoiIn0%3D; osool_session=BuE5KKIk1JvS6OomqXykNzGi9OJrSA7wV71lIXz5</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53760</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"46 characters\">/livewire/message/project.task-board.list-view</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"46 characters\">/livewire/message/project.task-board.list-view</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753608451.4909</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753608451</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067644043\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1721021970 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721021970\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1421405584 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 27 Jul 2025 09:28:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxTSWZFM0hwTkExQlF2THdWZktXRnc9PSIsInZhbHVlIjoib3E0S0NSZDlhTGYvaWJHbFA4MFdnS1FibWZTalltRlBJaEZkTXNFYVNDNFpvdUQxOW44d0kvbWsrN2NlNmc0a25RWWJRYjhLWCtaU0VvL0NEVFZZS25NZHRJL0ZmK2h2SGZ1ZEhmUzQ0NkR3SkViRDVtOVRnZUpyRHErQ0JoWHoiLCJtYWMiOiIyYzUxMzZmZmU3NzhkNDYyOTFhMzMwNTFmY2M3ZGYyYmRjYjkzN2I0ZTcyMWQwMTQ5Njk1YmJjMDEzMDNmODNjIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:28:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IndZbkxjVUNScjdCd0toWjY5c01UaUE9PSIsInZhbHVlIjoiTXZMSEVIMjFhcVplV29WQ29uZlZNc2NheTIwZU5pb3BNUjZsdHlTU0F0aklNaThuZHBiWTc3clRkV0FVQ0dqdUx1NWFmd2tHQXZwaFl4cEZ2aitsK3lHWktLWjJOM2RETG13NUo3Z3pJU2w4NmtrU1lyWTZuT0NjY1MwbVJXT1oiLCJtYWMiOiJjYzJhNjY2YWU4YzI4NGJlNjI0NzQ3YzdiODg1ZjFlZjVmYWViYWNlNDk5N2FjNGY2OTFjZmE3OWJlZDhlZDQ0IiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:28:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxTSWZFM0hwTkExQlF2THdWZktXRnc9PSIsInZhbHVlIjoib3E0S0NSZDlhTGYvaWJHbFA4MFdnS1FibWZTalltRlBJaEZkTXNFYVNDNFpvdUQxOW44d0kvbWsrN2NlNmc0a25RWWJRYjhLWCtaU0VvL0NEVFZZS25NZHRJL0ZmK2h2SGZ1ZEhmUzQ0NkR3SkViRDVtOVRnZUpyRHErQ0JoWHoiLCJtYWMiOiIyYzUxMzZmZmU3NzhkNDYyOTFhMzMwNTFmY2M3ZGYyYmRjYjkzN2I0ZTcyMWQwMTQ5Njk1YmJjMDEzMDNmODNjIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:28:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IndZbkxjVUNScjdCd0toWjY5c01UaUE9PSIsInZhbHVlIjoiTXZMSEVIMjFhcVplV29WQ29uZlZNc2NheTIwZU5pb3BNUjZsdHlTU0F0aklNaThuZHBiWTc3clRkV0FVQ0dqdUx1NWFmd2tHQXZwaFl4cEZ2aitsK3lHWktLWjJOM2RETG13NUo3Z3pJU2w4NmtrU1lyWTZuT0NjY1MwbVJXT1oiLCJtYWMiOiJjYzJhNjY2YWU4YzI4NGJlNjI0NzQ3YzdiODg1ZjFlZjVmYWViYWNlNDk5N2FjNGY2OTFjZmE3OWJlZDhlZDQ0IiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:28:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421405584\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1910116498 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=X8d689a914b9a8fae8b430b0b3c69d830&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7070</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910116498\", {\"maxDepth\":0})</script>\n"}}