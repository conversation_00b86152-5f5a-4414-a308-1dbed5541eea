{"__meta": {"id": "X9e780647a54e4f8715e14c003711c6dc", "datetime": "2025-07-27 12:05:47", "utime": **********.42519, "method": "GET", "uri": "/CRMProjects/details/eyJpdiI6IlpNNXRENHcyeFpQRFJOUmZrTW51c2c9PSIsInZhbHVlIjoiTDhrd2dpRUJaYnZ5NlJRSXVMWEJzUT09IiwibWFjIjoiZjYyM2RiMGY3MjJjNWE2ODk1YWNiNzhmNDk3N2EyNjQwY2M2NWU3NmVlODVkMzI3ZGExMDk4YzUxODJlNTk4YSIsInRhZyI6IiJ9", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 76, "messages": [{"message": "[12:05:32] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.262543, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:32] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.434208, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:32] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.434258, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:32] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.434348, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.159862, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.160215, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.160531, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.160901, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.16097, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.16102, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getMovedStatusMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 133", "message_html": null, "is_string": false, "label": "warning", "time": **********.161062, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": **********.161096, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 155", "message_html": null, "is_string": false, "label": "warning", "time": **********.161131, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.16118, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.164464, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.164509, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.164548, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.164586, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.16462, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.164734, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.164906, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.165211, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.165287, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.16534, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.165379, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.165417, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.165458, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.165501, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.16594, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.165986, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.166026, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.166062, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.166096, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.166134, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.166175, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.166448, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.166487, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.166523, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.166556, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.166588, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.166625, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.166664, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.166936, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.166969, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.167014, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.167056, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.167087, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.16712, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.167155, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.167414, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.167446, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.167479, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.167508, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.167536, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.167569, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.167604, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.167847, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.167881, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.167913, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.167943, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.167972, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.168005, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.168037, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.168259, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.168292, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.168323, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.168353, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.168381, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.168414, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.16845, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2416", "message_html": null, "is_string": false, "label": "warning", "time": **********.234416, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2476", "message_html": null, "is_string": false, "label": "warning", "time": **********.234681, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3617", "message_html": null, "is_string": false, "label": "warning", "time": **********.235314, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3414", "message_html": null, "is_string": false, "label": "warning", "time": **********.260134, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.266053, "xdebug_link": null, "collector": "log"}, {"message": "[12:05:47] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 73", "message_html": null, "is_string": false, "label": "warning", "time": **********.297347, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.643544, "end": **********.425331, "duration": 15.781787157058716, "duration_str": "15.78s", "measures": [{"label": "Booting", "start": **********.643544, "relative_start": 0, "end": **********.218234, "relative_end": **********.218234, "duration": 0.5746901035308838, "duration_str": "575ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.218248, "relative_start": 0.5747039318084717, "end": **********.425333, "relative_end": 1.9073486328125e-06, "duration": 15.207085132598877, "duration_str": "15.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 46244272, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 31, "templates": [{"name": "CRMProjects.project-details (\\resources\\views\\CRMProjects\\project-details.blade.php)", "param_count": 14, "params": ["projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.project-details (\\resources\\views\\livewire\\c-r-m-projects\\project-details.blade.php)", "param_count": 83, "params": ["projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "errors", "_instance", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.vendor-client-user-share (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\vendor-client-user-share.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.delete-confirm (\\resources\\views\\livewire\\c-r-m-projects\\modals\\delete-confirm.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.confirm-delete-file (\\resources\\views\\livewire\\c-r-m-projects\\modals\\confirm-delete-file.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.milestones (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\milestones.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.files (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\files.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.activity (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\activity.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.documents (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\documents.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.common.loader (\\resources\\views\\livewire\\common\\loader.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.sales.common.no-data-tr (\\resources\\views\\livewire\\sales\\common\\no-data-tr.blade.php)", "param_count": 88, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount", "__empty_1", "__currentLoopData", "loop"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.setting (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\setting.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.bma_list (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\bma_list.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.inviteUser (\\resources\\views\\livewire\\c-r-m-projects\\modals\\inviteUser.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.shareClient (\\resources\\views\\livewire\\c-r-m-projects\\modals\\shareClient.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.shareVendors (\\resources\\views\\livewire\\c-r-m-projects\\modals\\shareVendors.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.assign_BMA (\\resources\\views\\livewire\\c-r-m-projects\\modals\\assign_BMA.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.CancelProjectAccessModal (\\resources\\views\\livewire\\c-r-m-projects\\modals\\CancelProjectAccessModal.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.delete-confirm-milstone (\\resources\\views\\livewire\\c-r-m-projects\\modals\\delete-confirm-milstone.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.deleteAssignBMA (\\resources\\views\\livewire\\c-r-m-projects\\modals\\deleteAssignBMA.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.balde_js_script (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\balde_js_script.blade.php)", "param_count": 88, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount", "__currentLoopData", "pt", "loop"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials.check_crm_session (\\resources\\views\\layouts\\partials\\check_crm_session.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET CRMProjects/details/{id}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\CRMProjects\\ProjectController@details", "as": "CRMProjects.details", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/CRMProjects", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php&line=19\">\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php:19-32</a>"}, "queries": {"nb_statements": 19, "nb_failed_statements": 0, "accumulated_duration": 0.03371000000000001, "accumulated_duration_str": "33.71ms", "statements": [{"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00349, "duration_str": "3.49ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 10.353}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7070 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_test_db", "start_percent": 10.353, "width_percent": 2.729}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 173 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_test_db", "start_percent": 13.082, "width_percent": 2.433}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3046}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00392, "duration_str": "3.92ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3046", "connection": "osool_test_db", "start_percent": 15.515, "width_percent": 11.629}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 7070 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_test_db", "start_percent": 27.143, "width_percent": 2.551}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'building_manager' limit 1", "type": "query", "params": [], "bindings": ["building_manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1927}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1927", "connection": "osool_test_db", "start_percent": 29.694, "width_percent": 3.026}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 173 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0018, "duration_str": "1.8ms", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_test_db", "start_percent": 32.72, "width_percent": 5.34}, {"sql": "select count(*) as aggregate from `maintanance_request` where `building_id` in ('5936', '5947') and `status` = 'pending'", "type": "query", "params": [], "bindings": ["5936", "5947", "pending"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\MaintenanceRequestTrait.php", "line": 14}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 201}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 77}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00656, "duration_str": "6.56ms", "stmt_id": "\\app\\Http\\Traits\\MaintenanceRequestTrait.php:14", "connection": "osool_test_db", "start_percent": 38.06, "width_percent": 19.46}, {"sql": "select `building_ids`, `project_id` from `users` where `id` = 7070 and `status` = 1 and `is_deleted` = 'no' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070", "1", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 294}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\WorkOrdersTrait.php", "line": 2070}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 191}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 78}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:294", "connection": "osool_test_db", "start_percent": 57.52, "width_percent": 1.869}, {"sql": "select count(*) as aggregate from `work_orders` inner join `contracts` on `contracts`.`id` = `work_orders`.`contract_id` inner join `property_buildings` on `property_buildings`.`id` = `work_orders`.`property_id` inner join `users` on `users`.`id` = `work_orders`.`created_by` where `work_orders`.`status` = 4 and `work_orders`.`is_deleted` = 'no' and `work_orders`.`start_date` <= '2025-07-27' and `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('')", "type": "query", "params": [], "bindings": ["4", "no", "2025-07-27", "6721", "5936", "5947", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\WorkOrdersTrait.php", "line": 2102}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 191}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 78}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.005889999999999999, "duration_str": "5.89ms", "stmt_id": "\\app\\Http\\Traits\\WorkOrdersTrait.php:2102", "connection": "osool_test_db", "start_percent": 59.389, "width_percent": 17.473}, {"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 76.861, "width_percent": 2.937}, {"sql": "select * from `user_sub_privileges` where `user_sub_privileges`.`privilage_user_id` in (7070)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 21, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 79.798, "width_percent": 2.225}, {"sql": "select * from `user_type` where `user_type`.`slug` in ('building_manager')", "type": "query", "params": [], "bindings": ["building_manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 21, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 82.023, "width_percent": 1.661}, {"sql": "select `id` from `sub_privileges` where `user_type` = 4 and `slug` = 'work_order_approve'", "type": "query", "params": [], "bindings": ["4", "work_order_approve"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4377}, {"index": 14, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4377", "connection": "osool_test_db", "start_percent": 83.684, "width_percent": 4.034}, {"sql": "select count(*) as aggregate from `maintanance_request` where `building_id` in ('5936', '5947') and `status` = 'pending'", "type": "query", "params": [], "bindings": ["5936", "5947", "pending"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\WorkOrders.php", "line": 2400}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 511}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00189, "duration_str": "1.89ms", "stmt_id": "\\app\\Models\\WorkOrders.php:2400", "connection": "osool_test_db", "start_percent": 87.719, "width_percent": 5.607}, {"sql": "select exists(select * from `projects_details` where `id` = 173 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["173", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 93.325, "width_percent": 2.166}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7070) as `exists`", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 95.491, "width_percent": 1.691}, {"sql": "select exists(select * from `projects_details` where `id` = 173 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["173", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 97.182, "width_percent": 1.513}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7070) as `exists`", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 98.695, "width_percent": 1.305}]}, "models": {"data": {"App\\Models\\SubPrivileges": 1, "App\\Models\\UserTypes": 1, "App\\Models\\UserSubPrivileges": 1, "App\\Models\\CrmUser": 1, "App\\Models\\ReleaseNotes": 2, "App\\Models\\ProjectsDetails": 2, "App\\Models\\UserCompany": 1, "App\\Models\\User": 3}, "count": 12}, "livewire": {"data": {"c-r-m-projects.project-details #9AZOKfc2qhcIJWKGU4Ji": "array:5 [\n  \"data\" => array:67 [\n    \"projectID\" => 649\n    \"workspaceSlug\" => \"khalil-project\"\n    \"projectData\" => array:36 [\n      \"id\" => 649\n      \"type\" => \"project\"\n      \"project_type\" => \"maintenance\"\n      \"priority_level\" => \"critical\"\n      \"title\" => \"Notifications\"\n      \"status\" => \"Draft\"\n      \"description\" => \"Test notifications\"\n      \"start_date\" => \"2025-07-21\"\n      \"end_date\" => \"2026-02-01\"\n      \"cost\" => 100000\n      \"tags\" => null\n      \"estimated_hrs\" => 0\n      \"currency\" => \"$\"\n      \"project_progress\" => \"false\"\n      \"progress\" => \"0\"\n      \"task_progress\" => \"true\"\n      \"total_task\" => 2\n      \"total_comment\" => 0\n      \"created_by\" => \"<EMAIL>\"\n      \"budget\" => \"﷼100000\"\n      \"users\" => array:2 [\n        0 => array:5 [\n          \"id\" => 680\n          \"name\" => \"Khalil POA\"\n          \"email\" => \"<EMAIL>\"\n          \"complate_task\" => \"0/2\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n        1 => array:5 [\n          \"id\" => 693\n          \"name\" => \"Staff\"\n          \"email\" => \"<EMAIL>\"\n          \"complate_task\" => \"0/0\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n      ]\n      \"clients\" => array:1 [\n        0 => array:4 [\n          \"id\" => 692\n          \"name\" => \"Client1\"\n          \"email\" => \"<EMAIL>\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n      ]\n      \"milestones\" => array:4 [\n        0 => array:10 [\n          \"id\" => 585\n          \"title\" => \"test\"\n          \"status\" => \"incomplete\"\n          \"start_date\" => \"2025-07-24\"\n          \"end_date\" => \"2025-07-24\"\n          \"progress\" => 0\n          \"summary\" => \"test\"\n          \"cost\" => \"﷼1000\"\n          \"created_at\" => \"2025-07-24T13:14:36.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:14:36.000000Z\"\n        ]\n        1 => array:10 [\n          \"id\" => 586\n          \"title\" => \"test2\"\n          \"status\" => \"incomplete\"\n          \"start_date\" => \"2025-07-23\"\n          \"end_date\" => \"2025-07-24\"\n          \"progress\" => 0\n          \"summary\" => \"test\"\n          \"cost\" => \"﷼1000\"\n          \"created_at\" => \"2025-07-24T13:15:02.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:15:02.000000Z\"\n        ]\n        2 => array:10 [\n          \"id\" => 587\n          \"title\" => \"Past\"\n          \"status\" => \"incomplete\"\n          \"start_date\" => \"2025-07-24\"\n          \"end_date\" => \"2025-07-24\"\n          \"progress\" => 0\n          \"summary\" => \"test\"\n          \"cost\" => \"﷼2000\"\n          \"created_at\" => \"2025-07-24T13:35:15.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:35:15.000000Z\"\n        ]\n        3 => array:10 [\n          \"id\" => 588\n          \"title\" => \"Future\"\n          \"status\" => \"complete\"\n          \"start_date\" => \"2025-07-31\"\n          \"end_date\" => \"2025-07-31\"\n          \"progress\" => 100\n          \"summary\" => \"test\"\n          \"cost\" => \"﷼2000\"\n          \"created_at\" => \"2025-07-24T13:35:47.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:38:59.000000Z\"\n        ]\n      ]\n      \"vendors\" => array:1 [\n        0 => array:4 [\n          \"id\" => 694\n          \"name\" => \"Vendor\"\n          \"email\" => \"<EMAIL>\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n      ]\n      \"buildingManager\" => array:1 [\n        0 => array:4 [\n          \"id\" => 696\n          \"name\" => \"KH BMA from POA\"\n          \"email\" => \"<EMAIL>\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n      ]\n      \"files\" => []\n      \"activities\" => array:9 [\n        0 => array:9 [\n          \"id\" => 4452\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Move\"\n          \"log_info\" => \" Move Tasktask 1from Todo to Review\"\n          \"created_at\" => \"2025-07-24T13:38:48.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:38:48.000000Z\"\n          \"remark\" => array:3 [\n            \"title\" => \"task 1\"\n            \"old_status\" => \"Todo\"\n            \"new_status\" => \"Review\"\n          ]\n        ]\n        1 => array:9 [\n          \"id\" => 4451\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new Tasktask 1\"\n          \"created_at\" => \"2025-07-24T13:37:31.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:37:31.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"task 1\"\n          ]\n        ]\n        2 => array:9 [\n          \"id\" => 4450\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new Tasktask 1\"\n          \"created_at\" => \"2025-07-24T13:37:01.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:37:01.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"task 1\"\n          ]\n        ]\n        3 => array:9 [\n          \"id\" => 4449\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new MilestoneFuture\"\n          \"created_at\" => \"2025-07-24T13:35:47.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:35:47.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"Future\"\n          ]\n        ]\n        4 => array:9 [\n          \"id\" => 4448\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new MilestonePast\"\n          \"created_at\" => \"2025-07-24T13:35:15.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:35:15.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"Past\"\n          ]\n        ]\n        5 => array:9 [\n          \"id\" => 4439\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new Milestonetest2\"\n          \"created_at\" => \"2025-07-24T13:15:02.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:15:02.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"test2\"\n          ]\n        ]\n        6 => array:9 [\n          \"id\" => 4438\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new Milestonetest\"\n          \"created_at\" => \"2025-07-24T13:14:36.000000Z\"\n          \"updated_at\" => \"2025-07-24T13:14:36.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"test\"\n          ]\n        ]\n        7 => array:9 [\n          \"id\" => 4436\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Share with Vendor\"\n          \"log_info\" => null\n          \"created_at\" => \"2025-07-24T12:30:33.000000Z\"\n          \"updated_at\" => \"2025-07-24T12:30:33.000000Z\"\n          \"remark\" => array:2 [\n            \"vendor_id\" => 694\n            \"vendor_name\" => \"Vendor\"\n          ]\n        ]\n        8 => array:9 [\n          \"id\" => 4435\n          \"user_id\" => 680\n          \"user_name\" => \"Khalil POA\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Share with Client\"\n          \"log_info\" => \" Share Project with ClientClient1\"\n          \"created_at\" => \"2025-07-24T12:30:23.000000Z\"\n          \"updated_at\" => \"2025-07-24T12:30:23.000000Z\"\n          \"remark\" => array:2 [\n            \"client_id\" => 692\n            \"client_name\" => \"Client1\"\n          ]\n        ]\n      ]\n      \"documents\" => []\n      \"project_setting\" => array:12 [\n        \"member\" => \"on\"\n        \"client\" => \"on\"\n        \"milestone\" => \"off\"\n        \"progress\" => \"off\"\n        \"basic_details\" => \"on\"\n        \"activity\" => \"off\"\n        \"attachment\" => \"on\"\n        \"bug_report\" => \"on\"\n        \"task\" => \"off\"\n        \"invoice\" => \"off\"\n        \"timesheet\" => \"off\"\n        \"password_protected\" => \"off\"\n      ]\n      \"bma_users\" => array:1 [\n        0 => array:3 [\n          \"id\" => 696\n          \"name\" => \"KH BMA from POA\"\n          \"email\" => \"<EMAIL>\"\n        ]\n      ]\n      \"all_users\" => array:2 [\n        0 => array:3 [\n          \"id\" => 693\n          \"name\" => \"Staff\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        1 => array:3 [\n          \"id\" => 696\n          \"name\" => \"KH BMA from POA\"\n          \"email\" => \"<EMAIL>\"\n        ]\n      ]\n      \"all_clients\" => array:1 [\n        0 => array:3 [\n          \"id\" => 692\n          \"name\" => \"Client1\"\n          \"email\" => \"<EMAIL>\"\n        ]\n      ]\n      \"all_vendors\" => array:1 [\n        0 => array:3 [\n          \"id\" => 694\n          \"name\" => \"Vendor\"\n          \"email\" => \"<EMAIL>\"\n        ]\n      ]\n      \"document_types\" => []\n      \"priority_levels\" => array:4 [\n        0 => array:2 [\n          \"value\" => \"critical\"\n          \"label\" => \"Critical\"\n        ]\n        1 => array:2 [\n          \"value\" => \"high\"\n          \"label\" => \"High\"\n        ]\n        2 => array:2 [\n          \"value\" => \"medium\"\n          \"label\" => \"Medium\"\n        ]\n        3 => array:2 [\n          \"value\" => \"low\"\n          \"label\" => \"Low\"\n        ]\n      ]\n      \"project_types\" => array:8 [\n        0 => array:2 [\n          \"value\" => \"new_construction\"\n          \"label\" => \"New Construction\"\n        ]\n        1 => array:2 [\n          \"value\" => \"renovation\"\n          \"label\" => \"Renovation\"\n        ]\n        2 => array:2 [\n          \"value\" => \"maintenance\"\n          \"label\" => \"Maintenance\"\n        ]\n        3 => array:2 [\n          \"value\" => \"expansion\"\n          \"label\" => \"Expansion\"\n        ]\n        4 => array:2 [\n          \"value\" => \"demolition\"\n          \"label\" => \"Demolition\"\n        ]\n        5 => array:2 [\n          \"value\" => \"infrastructure\"\n          \"label\" => \"Infrastructure\"\n        ]\n        6 => array:2 [\n          \"value\" => \"design_projects\"\n          \"label\" => \"Design Projects\"\n        ]\n        7 => array:2 [\n          \"value\" => \"compliance_safety\"\n          \"label\" => \"Compliance & Safety\"\n        ]\n      ]\n    ]\n    \"attachment\" => null\n    \"chartData\" => array:7 [\n      475 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      476 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      477 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      478 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      \"label\" => array:7 [\n        0 => \"Mon\"\n        1 => \"Tue\"\n        2 => \"Wed\"\n        3 => \"Thu\"\n        4 => \"Fri\"\n        5 => \"Sat\"\n        6 => \"Sun\"\n      ]\n      \"color\" => array:4 [\n        0 => \"#77b6ea\"\n        1 => \"#545454\"\n        2 => \"#3cb8d9\"\n        3 => \"#37b37e\"\n      ]\n      \"stages\" => array:4 [\n        475 => \"Todo\"\n        476 => \"In Progress\"\n        477 => \"Review\"\n        478 => \"Done\"\n      ]\n    ]\n    \"daysleft\" => 189\n    \"start_date\" => null\n    \"filenametoDelete\" => null\n    \"progress\" => 0\n    \"end_date\" => null\n    \"budget\" => null\n    \"description\" => null\n    \"name\" => null\n    \"input_name\" => null\n    \"users\" => array:2 [\n      0 => array:3 [\n        \"id\" => 693\n        \"name\" => \"Staff\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 696\n        \"name\" => \"KH BMA from POA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"project_type\" => null\n    \"projectType\" => array:8 [\n      0 => array:2 [\n        \"value\" => \"new_construction\"\n        \"label\" => \"New Construction\"\n      ]\n      1 => array:2 [\n        \"value\" => \"renovation\"\n        \"label\" => \"Renovation\"\n      ]\n      2 => array:2 [\n        \"value\" => \"maintenance\"\n        \"label\" => \"Maintenance\"\n      ]\n      3 => array:2 [\n        \"value\" => \"expansion\"\n        \"label\" => \"Expansion\"\n      ]\n      4 => array:2 [\n        \"value\" => \"demolition\"\n        \"label\" => \"Demolition\"\n      ]\n      5 => array:2 [\n        \"value\" => \"infrastructure\"\n        \"label\" => \"Infrastructure\"\n      ]\n      6 => array:2 [\n        \"value\" => \"design_projects\"\n        \"label\" => \"Design Projects\"\n      ]\n      7 => array:2 [\n        \"value\" => \"compliance_safety\"\n        \"label\" => \"Compliance & Safety\"\n      ]\n    ]\n    \"priorityLevel\" => array:4 [\n      0 => array:2 [\n        \"value\" => \"critical\"\n        \"label\" => \"Critical\"\n      ]\n      1 => array:2 [\n        \"value\" => \"high\"\n        \"label\" => \"High\"\n      ]\n      2 => array:2 [\n        \"value\" => \"medium\"\n        \"label\" => \"Medium\"\n      ]\n      3 => array:2 [\n        \"value\" => \"low\"\n        \"label\" => \"Low\"\n      ]\n    ]\n    \"priority_level\" => null\n    \"projectExists\" => true\n    \"isModalOpen\" => false\n    \"attachments\" => []\n    \"documentTypes\" => []\n    \"projects\" => []\n    \"DocumentsPaginated\" => array:5 [\n      \"items\" => []\n      \"next_page_url\" => null\n      \"prev_page_url\" => null\n      \"per_page\" => 10\n      \"total\" => 0\n    ]\n    \"files\" => []\n    \"isDeleteModalOpen\" => false\n    \"filePaths\" => []\n    \"folderKey\" => \"uploads\"\n    \"maxFileSize\" => 5120\n    \"attachmentFiles\" => null\n    \"currentDate\" => null\n    \"user\" => null\n    \"settingsState\" => array:19 [\n      \"basic_details\" => 1\n      \"member\" => 1\n      \"client\" => 1\n      \"vendor\" => 0\n      \"milestone\" => 0\n      \"activity\" => 0\n      \"attachment\" => 1\n      \"task\" => 0\n      \"bug_report\" => 1\n      \"invoice\" => 0\n      \"bill\" => 0\n      \"timesheet\" => 0\n      \"documents\" => 0\n      \"progress\" => 0\n      \"password_protected\" => 0\n      \"password\" => 0\n      \"retainer\" => 0\n      \"proposal\" => 0\n      \"procurement\" => 0\n    ]\n    \"milestoneData\" => array:4 [\n      0 => array:10 [\n        \"id\" => 585\n        \"title\" => \"test\"\n        \"status\" => \"incomplete\"\n        \"start_date\" => \"2025-07-24\"\n        \"end_date\" => \"2025-07-24\"\n        \"progress\" => 0\n        \"summary\" => \"test\"\n        \"cost\" => \"﷼1000\"\n        \"created_at\" => \"2025-07-24T13:14:36.000000Z\"\n        \"updated_at\" => \"2025-07-24T13:14:36.000000Z\"\n      ]\n      1 => array:10 [\n        \"id\" => 586\n        \"title\" => \"test2\"\n        \"status\" => \"incomplete\"\n        \"start_date\" => \"2025-07-23\"\n        \"end_date\" => \"2025-07-24\"\n        \"progress\" => 0\n        \"summary\" => \"test\"\n        \"cost\" => \"﷼1000\"\n        \"created_at\" => \"2025-07-24T13:15:02.000000Z\"\n        \"updated_at\" => \"2025-07-24T13:15:02.000000Z\"\n      ]\n      2 => array:10 [\n        \"id\" => 587\n        \"title\" => \"Past\"\n        \"status\" => \"incomplete\"\n        \"start_date\" => \"2025-07-24\"\n        \"end_date\" => \"2025-07-24\"\n        \"progress\" => 0\n        \"summary\" => \"test\"\n        \"cost\" => \"﷼2000\"\n        \"created_at\" => \"2025-07-24T13:35:15.000000Z\"\n        \"updated_at\" => \"2025-07-24T13:35:15.000000Z\"\n      ]\n      3 => array:10 [\n        \"id\" => 588\n        \"title\" => \"Future\"\n        \"status\" => \"complete\"\n        \"start_date\" => \"2025-07-31\"\n        \"end_date\" => \"2025-07-31\"\n        \"progress\" => 100\n        \"summary\" => \"test\"\n        \"cost\" => \"﷼2000\"\n        \"created_at\" => \"2025-07-24T13:35:47.000000Z\"\n        \"updated_at\" => \"2025-07-24T13:38:59.000000Z\"\n      ]\n    ]\n    \"status\" => \"incomplete\"\n    \"statuss\" => null\n    \"cost\" => null\n    \"summary\" => null\n    \"fileData\" => []\n    \"type\" => null\n    \"user_id\" => null\n    \"subject\" => null\n    \"notes\" => null\n    \"project\" => null\n    \"document_id\" => null\n    \"projectDetails\" => null\n    \"selectedUsersForInvite\" => []\n    \"usersAlreadyInvited\" => array:2 [\n      0 => \"<EMAIL>\"\n      1 => \"<EMAIL>\"\n    ]\n    \"selectedvendorsForShare\" => []\n    \"vendorsAlreadyInProject\" => array:1 [\n      0 => \"<EMAIL>\"\n    ]\n    \"selectedclientsForShare\" => []\n    \"clientssAlreadyInProject\" => array:1 [\n      0 => \"<EMAIL>\"\n    ]\n    \"clients\" => array:1 [\n      0 => array:3 [\n        \"id\" => 692\n        \"name\" => \"Client1\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"vendors\" => array:1 [\n      0 => array:3 [\n        \"id\" => 694\n        \"name\" => \"Vendor\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"fileType\" => 1\n    \"fileLanguage\" => 1\n    \"bmas_list\" => []\n    \"assignedBMAList\" => array:1 [\n      0 => array:3 [\n        \"id\" => 696\n        \"name\" => \"KH BMA from POA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"projectAccessUserId\" => 0\n    \"projectAccessDeleteMessage\" => \"\"\n    \"projectAccessUserName\" => \"\"\n    \"projectAccessUserType\" => \"\"\n    \"selectedBuildingsManager\" => []\n    \"allBuildingsManager\" => array:1 [\n      0 => array:3 [\n        \"id\" => 696\n        \"name\" => \"KH BMA from POA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"deleteAssignBMA\" => array:2 [\n      \"bma_id\" => \"\"\n      \"bma_name\" => \"\"\n    ]\n    \"currentPage\" => []\n  ]\n  \"name\" => \"c-r-m-projects.project-details\"\n  \"view\" => \"livewire.c-r-m-projects.project-details\"\n  \"component\" => \"App\\Http\\Livewire\\CRMProjects\\ProjectDetails\"\n  \"id\" => \"9AZOKfc2qhcIJWKGU4Ji\"\n]", "notifications.messages-notifications-list #TI6zLo7HrZcOA22oOqGB": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"khalil-project\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"TI6zLo7HrZcOA22oOqGB\"\n]", "notifications.new-notifications-list-top-nav #nyNZHziM16grhzkptiGr": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"nyNZHziM16grhzkptiGr\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3965\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 12:03:07\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjA2OTg4LCJleHAiOjE3NTM2MTA1ODgsIm5iZiI6MTc1MzYwNjk4OCwianRpIjoiVzA3VG5PQTMxTWxsZXdZYiIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.aKYANSsHkRiJcEUihqU1a4enp7GiwI0fm9JOoOtWj2c\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 12:03:07\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjA2OTg4LCJleHAiOjE3NTM2MTA1ODgsIm5iZiI6MTc1MzYwNjk4OCwianRpIjoiVzA3VG5PQTMxTWxsZXdZYiIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.aKYANSsHkRiJcEUihqU1a4enp7GiwI0fm9JOoOtWj2c\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:3 [\n        \"userCompany\" => App\\Models\\UserCompany {#3972\n          #connection: \"mysql\"\n          #table: \"user_company\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #original: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #changes: []\n          #casts: array:2 [\n            \"user_id\" => \"int\"\n            \"company_id\" => \"int\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: array:2 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n          ]\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"company_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3991\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 12:03:09\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #original: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 12:03:09\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => App\\Models\\CrmUser {#4090\n          #connection: \"mysql\"\n          #table: \"crm_user\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 19\n            \"user_id\" => 7070\n            \"crm_user_id\" => 680\n            \"created_at\" => \"2025-07-24 18:44:46\"\n            \"updated_at\" => \"2025-07-24 18:44:46\"\n            \"instagram_connect\" => 0\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n            \"whatsapp_account_id\" => 0\n            \"whatsapp_number_status\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 19\n            \"user_id\" => 7070\n            \"crm_user_id\" => 680\n            \"created_at\" => \"2025-07-24 18:44:46\"\n            \"updated_at\" => \"2025-07-24 18:44:46\"\n            \"instagram_connect\" => 0\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n            \"whatsapp_account_id\" => 0\n            \"whatsapp_number_status\" => null\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"crm_user_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:54 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 1\n    \"projectId\" => null\n    \"project\" => App\\Models\\ProjectsDetails {#4134\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 173\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khalil project\"\n        \"project_name_ar\" => \"مشروع خليل\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 173\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khalil project\"\n        \"project_name_ar\" => \"مشروع خليل\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => null\n    \"flagWorkorderSidebarMenu\" => true\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => 0\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => null\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu", "captcha_answer": "14", "_flash": "array:2 [\n  \"old\" => array:4 [\n    0 => \"flash_message\"\n    1 => \"flash_type\"\n    2 => \"flash_message\"\n    3 => \"flash_type\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlpNNXRENHcyeFpQRFJOUmZrTW51c2c9PSIsInZhbHVlIjoiTDhrd2dpRUJaYnZ5NlJRSXVMWEJzUT09IiwibWFjIjoiZjYyM2RiMGY3MjJjNWE2ODk1YWNiNzhmNDk3N2EyNjQwY2M2NWU3NmVlODVkMzI3ZGExMDk4YzUxODJlNTk4YSIsInRhZyI6IiJ9\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7070", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]", "flash_message": "You are not authorized to access that page.", "flash_type": "warning"}, "request": {"path_info": "/CRMProjects/details/eyJpdiI6IlpNNXRENHcyeFpQRFJOUmZrTW51c2c9PSIsInZhbHVlIjoiTDhrd2dpRUJaYnZ5NlJRSXVMWEJzUT09IiwibWFjIjoiZjYyM2RiMGY3MjJjNWE2ODk1YWNiNzhmNDk3N2EyNjQwY2M2NWU3NmVlODVkMzI3ZGExMDk4YzUxODJlNTk4YSIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-983401534 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-983401534\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-202360815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-202360815\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-402618704 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-402618704\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-858090629 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InhVaUVoeldSakM2Q29qZWQzYzNWbUE9PSIsInZhbHVlIjoiYzRLd0NPM0l0QzNEK2dDWHJqcGIrTldydDJ0K2NPRytNcEV4VXlPbTVPN3o1YVMrQllGTHNsenBJaFRtaUc3RkhDSHp3TW90L2h2d1YvbXNvQW9xZXMxNmxIR2ZoTlV1VTlBdUVRY0lENkZZd1NNbXB4RkVqbllnbHFpYmI0Q1ciLCJtYWMiOiIzZjZlNzU0YWI3ZjIyZmYwYzg4Mzc5YWIyOWEzNzkxZjNlZDFlMzFhOGY4M2Y4NTEwZjIyMzAyZDBjYzQ0Yjc2IiwidGFnIjoiIn0%3D; osool_session=NiVteAvg6weKi8vcChIxtwIjUoMFsszSWIUzngbR</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858090629\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2128613501 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InhVaUVoeldSakM2Q29qZWQzYzNWbUE9PSIsInZhbHVlIjoiYzRLd0NPM0l0QzNEK2dDWHJqcGIrTldydDJ0K2NPRytNcEV4VXlPbTVPN3o1YVMrQllGTHNsenBJaFRtaUc3RkhDSHp3TW90L2h2d1YvbXNvQW9xZXMxNmxIR2ZoTlV1VTlBdUVRY0lENkZZd1NNbXB4RkVqbllnbHFpYmI0Q1ciLCJtYWMiOiIzZjZlNzU0YWI3ZjIyZmYwYzg4Mzc5YWIyOWEzNzkxZjNlZDFlMzFhOGY4M2Y4NTEwZjIyMzAyZDBjYzQ0Yjc2IiwidGFnIjoiIn0%3D; osool_session=NiVteAvg6weKi8vcChIxtwIjUoMFsszSWIUzngbR</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52368</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"221 characters\">/CRMProjects/details/eyJpdiI6IlpNNXRENHcyeFpQRFJOUmZrTW51c2c9PSIsInZhbHVlIjoiTDhrd2dpRUJaYnZ5NlJRSXVMWEJzUT09IiwibWFjIjoiZjYyM2RiMGY3MjJjNWE2ODk1YWNiNzhmNDk3N2EyNjQwY2M2NWU3NmVlODVkMzI3ZGExMDk4YzUxODJlNTk4YSIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"221 characters\">/CRMProjects/details/eyJpdiI6IlpNNXRENHcyeFpQRFJOUmZrTW51c2c9PSIsInZhbHVlIjoiTDhrd2dpRUJaYnZ5NlJRSXVMWEJzUT09IiwibWFjIjoiZjYyM2RiMGY3MjJjNWE2ODk1YWNiNzhmNDk3N2EyNjQwY2M2NWU3NmVlODVkMzI3ZGExMDk4YzUxODJlNTk4YSIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.6435</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128613501\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1540453336 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540453336\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2090851068 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 27 Jul 2025 09:05:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InVUZ000c0dBL2VWUnJ5cUJ5K3F0WWc9PSIsInZhbHVlIjoiT2Z4S3pZV1NldHlVWW5Sakx5Y0hHM2JqUXN2L1BxU0ExK3Vaa3pDSGZrRFdaN1N3L0lGcGxrdTNtZCtNcmRUeU14UkQ2U3BadDY4U1RFamdQSjBuN1M5bjlYUlB3bnpGaXNwM29GVXFMZlFrOUx4dlZmUXVqaWxCLzRTalZGdFUiLCJtYWMiOiI4NDNkOWMwOTM0YTM2ZWJmYjhhYjEwZTViMmU2NGU0YmQzYmY2OTQ5NWU4MmVkNTI0MTdmOGI4ZTgxNjJkMjNlIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:05:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IjBxa0wyNnJmdm4rL2xUdmMvQmRpNWc9PSIsInZhbHVlIjoiWTYySzNJeWlYUG5YSFVpTC9vRVFMUWtHbnQ0Y0ZwM2NlZ3JRMUFCb1A5L2h6S0hMbDJIOE12ZlhFcnk3OUtRSTJRcEZYcjl4MzJrcndzU3V1NTNoYVV6Y0pwV1htL3dKSjFPdU1Ga3lCSzVMRDhZc2lMTHNDUzdRdmRLNG1meEkiLCJtYWMiOiI5YWE3OGFiM2I2ZTBkMDY0ZTJhODRiNDQ2ZDJlOWQyYTFmNzE5MzcwOTlhNDlhNDM5NzhiM2UwM2M1MTE0NTRlIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:05:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InVUZ000c0dBL2VWUnJ5cUJ5K3F0WWc9PSIsInZhbHVlIjoiT2Z4S3pZV1NldHlVWW5Sakx5Y0hHM2JqUXN2L1BxU0ExK3Vaa3pDSGZrRFdaN1N3L0lGcGxrdTNtZCtNcmRUeU14UkQ2U3BadDY4U1RFamdQSjBuN1M5bjlYUlB3bnpGaXNwM29GVXFMZlFrOUx4dlZmUXVqaWxCLzRTalZGdFUiLCJtYWMiOiI4NDNkOWMwOTM0YTM2ZWJmYjhhYjEwZTViMmU2NGU0YmQzYmY2OTQ5NWU4MmVkNTI0MTdmOGI4ZTgxNjJkMjNlIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:05:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IjBxa0wyNnJmdm4rL2xUdmMvQmRpNWc9PSIsInZhbHVlIjoiWTYySzNJeWlYUG5YSFVpTC9vRVFMUWtHbnQ0Y0ZwM2NlZ3JRMUFCb1A5L2h6S0hMbDJIOE12ZlhFcnk3OUtRSTJRcEZYcjl4MzJrcndzU3V1NTNoYVV6Y0pwV1htL3dKSjFPdU1Ga3lCSzVMRDhZc2lMTHNDUzdRdmRLNG1meEkiLCJtYWMiOiI5YWE3OGFiM2I2ZTBkMDY0ZTJhODRiNDQ2ZDJlOWQyYTFmNzE5MzcwOTlhNDlhNDM5NzhiM2UwM2M1MTE0NTRlIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:05:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090851068\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-850447531 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">flash_message</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">flash_type</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">flash_message</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">flash_type</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"242 characters\">http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlpNNXRENHcyeFpQRFJOUmZrTW51c2c9PSIsInZhbHVlIjoiTDhrd2dpRUJaYnZ5NlJRSXVMWEJzUT09IiwibWFjIjoiZjYyM2RiMGY3MjJjNWE2ODk1YWNiNzhmNDk3N2EyNjQwY2M2NWU3NmVlODVkMzI3ZGExMDk4YzUxODJlNTk4YSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7070</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>flash_message</span>\" => \"<span class=sf-dump-str title=\"43 characters\">You are not authorized to access that page.</span>\"\n  \"<span class=sf-dump-key>flash_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850447531\", {\"maxDepth\":0})</script>\n"}}