{"__meta": {"id": "Xc660ce6c3e823cc5fd1efb4ec383bad1", "datetime": "2025-07-27 12:03:26", "utime": **********.731305, "method": "GET", "uri": "/dashboards/admin?_=1753607005436", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 23, "messages": [{"message": "[12:03:26] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.369883, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.397685, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.397732, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": **********.397771, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2416", "message_html": null, "is_string": false, "label": "warning", "time": **********.450905, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2476", "message_html": null, "is_string": false, "label": "warning", "time": **********.450979, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3617", "message_html": null, "is_string": false, "label": "warning", "time": **********.451495, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.info: Dashboard User Type Checked ====>building_manager", "message_html": null, "is_string": false, "label": "info", "time": **********.452444, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3069", "message_html": null, "is_string": false, "label": "warning", "time": **********.479721, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3069", "message_html": null, "is_string": false, "label": "warning", "time": **********.505904, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3069", "message_html": null, "is_string": false, "label": "warning", "time": **********.509128, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3069", "message_html": null, "is_string": false, "label": "warning", "time": **********.511784, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3069", "message_html": null, "is_string": false, "label": "warning", "time": **********.515503, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3069", "message_html": null, "is_string": false, "label": "warning", "time": **********.518443, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php on line 688", "message_html": null, "is_string": false, "label": "warning", "time": **********.531455, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3414", "message_html": null, "is_string": false, "label": "warning", "time": **********.587433, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.593559, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Repositories\\WorkOrderRepository.php on line 171", "message_html": null, "is_string": false, "label": "warning", "time": **********.602047, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Repositories\\WorkOrderRepository.php on line 171", "message_html": null, "is_string": false, "label": "warning", "time": **********.605395, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Repositories\\WorkOrderRepository.php on line 171", "message_html": null, "is_string": false, "label": "warning", "time": **********.607844, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Repositories\\WorkOrderRepository.php on line 171", "message_html": null, "is_string": false, "label": "warning", "time": **********.610211, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Repositories\\WorkOrderRepository.php on line 171", "message_html": null, "is_string": false, "label": "warning", "time": **********.612338, "xdebug_link": null, "collector": "log"}, {"message": "[12:03:26] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Repositories\\WorkOrderRepository.php on line 171", "message_html": null, "is_string": false, "label": "warning", "time": **********.615069, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753607005.672949, "end": **********.73136, "duration": 1.058410882949829, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753607005.672949, "relative_start": 0, "end": **********.348491, "relative_end": **********.348491, "duration": 0.675541877746582, "duration_str": "676ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.348502, "relative_start": 0.6755528450012207, "end": **********.731362, "relative_end": 2.1457672119140625e-06, "duration": 0.3828601837158203, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 45018472, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 12, "templates": [{"name": "applications.admin.dashboard.building-manager-dashboard (\\resources\\views\\applications\\admin\\dashboard\\building-manager-dashboard.blade.php)", "param_count": 14, "params": ["data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.work-orders.dashboard (\\resources\\views\\livewire\\work-orders\\dashboard.blade.php)", "param_count": 22, "params": ["list", "metrics", "errors", "_instance", "user", "contractsList", "checkCreateButton", "decryptedServiceProviderId", "explodedServiceProviders", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "__currentLoopData", "loop", "acwj", "html", "ywcc"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "_instance", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "__currentLoopData", "loop", "acwj", "html", "ywcc"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "_instance", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "__currentLoopData", "loop", "acwj", "html", "ywcc"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "_instance", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "__currentLoopData", "loop", "acwj", "html", "ywcc"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 31, "params": ["__env", "app", "errors", "_instance", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "__currentLoopData", "loop", "acwj", "html", "ywcc", "url", "segments"], "type": "blade"}, {"name": "layouts.partials.check_crm_session (\\resources\\views\\layouts\\partials\\check_crm_session.blade.php)", "param_count": 31, "params": ["__env", "app", "errors", "_instance", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "__currentLoopData", "loop", "acwj", "html", "ywcc", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 31, "params": ["__env", "app", "errors", "_instance", "data", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "src", "key", "percentage", "bg_status", "color", "bg_star", "__currentLoopData", "loop", "acwj", "html", "ywcc", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET dashboards/admin", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\DashboardControllerNew@index", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "", "where": [], "as": "admin.dashboard", "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php&line=62\">\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:62-99</a>"}, "queries": {"nb_statements": 57, "nb_failed_statements": 0, "accumulated_duration": 0.06880000000000001, "accumulated_duration_str": "68.8ms", "statements": [{"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00362, "duration_str": "3.62ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_test_db", "start_percent": 0, "width_percent": 5.262}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7070 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_test_db", "start_percent": 5.262, "width_percent": 0.654}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 173 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1412}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 223}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 89}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1412", "connection": "osool_test_db", "start_percent": 5.916, "width_percent": 1.003}, {"sql": "update `users` set `allow_akaunting` = 1, `users`.`modified_at` = '2025-07-27 12:03:26' where `project_user_id` = 6721 and `user_type` not in ('osool_admin', 'super_admin') and `allow_akaunting` = 0 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-07-27 12:03:26", "6721", "osool_admin", "super_admin", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1417}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 223}, {"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 89}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.01385, "duration_str": "13.85ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1417", "connection": "osool_test_db", "start_percent": 6.919, "width_percent": 20.131}, {"sql": "select * from `users` where `users`.`id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1421}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 223}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 89}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1421", "connection": "osool_test_db", "start_percent": 27.049, "width_percent": 1.541}, {"sql": "select exists(select * from `service_providers` where `id` = '1' and `global_sp` = 1 and `service_providers`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1423}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 223}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 89}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1423", "connection": "osool_test_db", "start_percent": 28.59, "width_percent": 0.61}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`property_id` in ('5936', '5947')", "type": "query", "params": [], "bindings": ["5936", "5947"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 233}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 232}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 89}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:233", "connection": "osool_test_db", "start_percent": 29.201, "width_percent": 0.596}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `start_date` between '2021-01-01' and '2025-07-27'", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "warranty", "4", "2021-01-01", "2025-07-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 481}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:481", "connection": "osool_test_db", "start_percent": 29.797, "width_percent": 1.802}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `pass_fail` = 'pass' and `start_date` between '2021-01-01' and '2025-07-27'", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "warranty", "4", "pass", "2021-01-01", "2025-07-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 482}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00117, "duration_str": "1.17ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:482", "connection": "osool_test_db", "start_percent": 31.599, "width_percent": 1.701}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and (work_orders.response_time = 'On time' OR work_orders.sp_approove = 0)", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "2021-01-01", "2025-07-27", "warranty", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 495}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:495", "connection": "osool_test_db", "start_percent": 33.299, "width_percent": 1.177}, {"sql": "select sum(`work_orders`.`score`) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`bm_approove` = 1", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "warranty", "4", "2021-01-01", "2025-07-27", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 504}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00145, "duration_str": "1.45ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:504", "connection": "osool_test_db", "start_percent": 34.477, "width_percent": 2.108}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`bm_approove` = 1 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`bm_approove` = 1", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "warranty", "4", "2021-01-01", "2025-07-27", "1", "2021-01-01", "2025-07-27", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 509}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:509", "connection": "osool_test_db", "start_percent": 36.584, "width_percent": 0.974}, {"sql": "select avg(`work_orders`.`score`) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`bm_approove` = 1 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`bm_approove` = 1 and `work_orders`.`start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`bm_approove` = 1", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "warranty", "4", "2021-01-01", "2025-07-27", "1", "2021-01-01", "2025-07-27", "1", "2021-01-01", "2025-07-27", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 514}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:514", "connection": "osool_test_db", "start_percent": 37.558, "width_percent": 1.265}, {"sql": "select `id`, work_orders.id, COUNT(work_orders.id) as total,\nSUM(CASE WHEN work_orders.work_order_type = \"reactive\" THEN 1 ELSE 0 END) AS reactive_count,\nSUM(CASE WHEN work_orders.work_order_type = \"preventive\" THEN 1 ELSE 0 END) AS preventive_count from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `work_orders`.`start_date` <= '2025-07-27' and `work_orders`.`is_deleted` = 'no' limit 1", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "2025-07-27", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 529}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:529", "connection": "osool_test_db", "start_percent": 38.823, "width_percent": 1.89}, {"sql": "select `id`, rating, COUNT(*) as count from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `start_date` between '2021-01-01' and '2025-07-27' group by `rating`", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "warranty", "4", "2021-01-01", "2025-07-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 559}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:559", "connection": "osool_test_db", "start_percent": 40.712, "width_percent": 1.541}, {"sql": "select `id`, SUM(rating) as total_stars, COUNT(rating) as ratings, SUM(CASE WHEN work_orders.rating > 0 THEN 1 ELSE 0 END) AS rated_job_count from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `start_date` between '2021-01-01' and '2025-07-27' limit 1", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "warranty", "4", "2021-01-01", "2025-07-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 566}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:566", "connection": "osool_test_db", "start_percent": 42.253, "width_percent": 0.988}, {"sql": "select * from `work_orders` where `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `rating` = 5 and `start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["warranty", "4", "5", "2021-01-01", "2025-07-27", "6721", "5936", "5947", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 607}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00175, "duration_str": "1.75ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:607", "connection": "osool_test_db", "start_percent": 43.241, "width_percent": 2.544}, {"sql": "select * from `work_orders` where `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `rating` = 4 and `start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["warranty", "4", "4", "2021-01-01", "2025-07-27", "6721", "5936", "5947", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 607}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:607", "connection": "osool_test_db", "start_percent": 45.785, "width_percent": 1.599}, {"sql": "select * from `work_orders` where `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `rating` = 3 and `start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["warranty", "4", "3", "2021-01-01", "2025-07-27", "6721", "5936", "5947", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 607}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00164, "duration_str": "1.64ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:607", "connection": "osool_test_db", "start_percent": 47.384, "width_percent": 2.384}, {"sql": "select * from `work_orders` where `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `rating` = 2 and `start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["warranty", "4", "2", "2021-01-01", "2025-07-27", "6721", "5936", "5947", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 607}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:607", "connection": "osool_test_db", "start_percent": 49.767, "width_percent": 2.166}, {"sql": "select * from `work_orders` where `work_orders`.`contract_type` != 'warranty' and `work_orders`.`status` = 4 and `rating` = 1 and `start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["warranty", "4", "1", "2021-01-01", "2025-07-27", "6721", "5936", "5947", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 607}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:607", "connection": "osool_test_db", "start_percent": 51.933, "width_percent": 1.468}, {"sql": "select count(*) as aggregate from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`contract_type` != 'warranty'", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "2021-01-01", "2025-07-27", "warranty"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 645}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:645", "connection": "osool_test_db", "start_percent": 53.401, "width_percent": 1.599}, {"sql": "select * from `asset_categories` where `is_deleted` = 'no' and `user_id` = 6721 and `asset_categories`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["no", "6721"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 650}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:650", "connection": "osool_test_db", "start_percent": 55, "width_percent": 1.846}, {"sql": "select `work_orders`.`asset_category_id`, count(*) as count from `work_orders` where `work_orders`.`project_user_id` = 6721 and `work_orders`.`property_id` in ('5936', '5947') and `work_orders`.`asset_category_id` in ('') and `start_date` between '2021-01-01' and '2025-07-27' and `work_orders`.`contract_type` != 'warranty' and `work_orders`.`asset_category_id` in (928, 929, 931, 1258) group by `work_orders`.`asset_category_id`", "type": "query", "params": [], "bindings": ["6721", "5936", "5947", "", "2021-01-01", "2025-07-27", "warranty", "928", "929", "931", "1258"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 660}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 266}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 265}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:660", "connection": "osool_test_db", "start_percent": 56.846, "width_percent": 1.628}, {"sql": "select `id`, `contract_number`, `start_date`, `end_date`, DATEDIFF(end_date, start_date) / 365 as count from `contracts` where `user_id` = 6721 and `contracts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6721"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 815}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 761}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:815", "connection": "osool_test_db", "start_percent": 58.474, "width_percent": 1.352}, {"sql": "select `id`, `contract_number`, `start_date`, `end_date`, DATEDIFF(end_date, start_date) / 365 as count, YEAR(start_date) as year, COUNT(id) as count from `contracts` where `is_deleted` = 'no' and `deleted_at` is null and (YEAR(start_date) >= '2025' and YEAR(start_date) <= 2032) and `user_id` = 6721 and `contracts`.`deleted_at` is null group by `year`", "type": "query", "params": [], "bindings": ["no", "2025", "2032", "6721"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 838}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 761}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:838", "connection": "osool_test_db", "start_percent": 59.826, "width_percent": 1.163}, {"sql": "select count(*) as aggregate from `contracts` where `user_id` = 6721 and `is_deleted` = 'no' and `deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6721", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 769}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 287}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:769", "connection": "osool_test_db", "start_percent": 60.988, "width_percent": 1.061}, {"sql": "select `id`, `contract_number`, `start_date`, `end_date`, DATEDIFF(end_date, start_date) / 365 as count from `contracts` where `user_id` = 6721 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6721", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 774}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 287}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:774", "connection": "osool_test_db", "start_percent": 62.049, "width_percent": 1.323}, {"sql": "select sum(`contracts`.`contract_value`) as aggregate from `contracts` where `user_id` = 6721 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6721", "no", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 779}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 287}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:779", "connection": "osool_test_db", "start_percent": 63.372, "width_percent": 1.308}, {"sql": "select `contracts`.`start_date`, `contracts`.`end_date` from `contracts` inner join `service_providers` on `contracts`.`service_provider_id` = `service_providers`.`id` where `contracts`.`is_deleted` = 'no' and `contracts`.`deleted_at` is null order by `contracts`.`start_date` asc, `contracts`.`end_date` desc limit 1", "type": "query", "params": [], "bindings": ["no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 868}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 782}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:868", "connection": "osool_test_db", "start_percent": 64.68, "width_percent": 1.483}, {"sql": "select count(*) as aggregate from `contracts` where `user_id` = 6721 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and contracts.end_date <= date_add(now(), interval 6 month) and `contracts`.`deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6721", "no", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 920}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 785}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:920", "connection": "osool_test_db", "start_percent": 66.163, "width_percent": 1.584}, {"sql": "select count(*) as aggregate from `contracts` where `user_id` = 6721 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and contracts.end_date >= date_add(now(), interval 12 month) and `contracts`.`deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6721", "no", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 926}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 785}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:926", "connection": "osool_test_db", "start_percent": 67.747, "width_percent": 0.93}, {"sql": "select count(*) as aggregate from `contracts` where `user_id` = 6721 and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and `is_deleted` = 'no' and `deleted_at` is null and contracts.end_date >= date_add(now(), interval 6 month) and contracts.end_date <= date_add(now(), interval 12 month) and `contracts`.`deleted_at` is null and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6721", "no", "no", "no"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 933}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 785}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 288}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:933", "connection": "osool_test_db", "start_percent": 68.677, "width_percent": 0.785}, {"sql": "select `release_notes`.* from `release_notes` left join `release_notes_users_seens` on `release_notes`.`version` = `release_notes_users_seens`.`version` and `release_notes_users_seens`.`user_id` = 7070 inner join (SELECT version AS latest_version FROM release_notes order by id desc limit 1) as latest on `release_notes`.`version` = latest.latest_version where `release_notes_users_seens`.`version` is null and `store_status` = 1 and `release_notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7070", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1022}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 297}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 89}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1022", "connection": "osool_test_db", "start_percent": 69.462, "width_percent": 1.875}, {"sql": "select `pass_fail_target`, `target_response_rate` from `project_settings` where `project_id` = 173 limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3493}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 300}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 89}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3493", "connection": "osool_test_db", "start_percent": 71.337, "width_percent": 0.741}, {"sql": "select COUNT(*) as total_mr_count, SUM(CASE WHEN status = \"Pending\" THEN 1 ELSE 0 END) as total_pending_mr_count from `maintanance_request` where `building_id` in ('5936', '5947') limit 1", "type": "query", "params": [], "bindings": ["5936", "5947"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 312}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 309}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 89}], "duration": 0.0019199999999999998, "duration_str": "1.92ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:312", "connection": "osool_test_db", "start_percent": 72.078, "width_percent": 2.791}, {"sql": "select `id` from `contracts` where `status` = 1 and `is_deleted` = 'no' and `service_provider_id` in ('1') and `contracts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "no", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Traits\\ContractsTrait.php", "line": 137}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\WorkOrders\\Dashboard.php", "line": 44}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\WorkOrders\\Dashboard.php", "line": 34}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Traits\\ContractsTrait.php:137", "connection": "osool_test_db", "start_percent": 74.869, "width_percent": 1.148}, {"sql": "select count(*) as aggregate from `work_orders` where `start_date` <= '2025-07-27 12:03:26' and `is_deleted` <> 'yes' and ((`schedule_start_time` is not null and schedule_start_time != '' and TIME(schedule_start_time) < CURTIME()) or `schedule_start_time` is null or `schedule_start_time` = '') and `property_id` in (5936, 5947) and `asset_category_id` in (0) and `status` = 1 and `workorder_journey` = 'submitted' and `project_user_id` = 6721", "type": "query", "params": [], "bindings": ["2025-07-27 12:03:26", "yes", "", "1", "submitted", "6721"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\WorkOrderRepository.php", "line": 32}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 24}, {"index": 19, "namespace": null, "name": "\\app\\Services\\DashboardWorkorderService.php", "line": 26}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "\\app\\Repositories\\WorkOrderRepository.php:32", "connection": "osool_test_db", "start_percent": 76.017, "width_percent": 1.86}, {"sql": "select count(*) as aggregate from `work_orders` where `start_date` <= '2025-07-27 12:03:26' and `is_deleted` <> 'yes' and ((`schedule_start_time` is not null and schedule_start_time != '' and TIME(schedule_start_time) < CURTIME()) or `schedule_start_time` is null or `schedule_start_time` = '') and `property_id` in (5936, 5947) and `asset_category_id` in (0) and `workorder_journey` = 'job_execution' and `project_user_id` = 6721", "type": "query", "params": [], "bindings": ["2025-07-27 12:03:26", "yes", "", "job_execution", "6721"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\WorkOrderRepository.php", "line": 32}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 24}, {"index": 19, "namespace": null, "name": "\\app\\Services\\DashboardWorkorderService.php", "line": 27}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Repositories\\WorkOrderRepository.php:32", "connection": "osool_test_db", "start_percent": 77.878, "width_percent": 1.192}, {"sql": "select count(*) as aggregate from `work_orders` where `start_date` <= '2025-07-27 12:03:26' and `is_deleted` <> 'yes' and ((`schedule_start_time` is not null and schedule_start_time != '' and TIME(schedule_start_time) < CURTIME()) or `schedule_start_time` is null or `schedule_start_time` = '') and `property_id` in (5936, 5947) and `asset_category_id` in (0) and `status` = 4 and `project_user_id` = 6721", "type": "query", "params": [], "bindings": ["2025-07-27 12:03:26", "yes", "", "4", "6721"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\WorkOrderRepository.php", "line": 32}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 24}, {"index": 19, "namespace": null, "name": "\\app\\Services\\DashboardWorkorderService.php", "line": 29}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Repositories\\WorkOrderRepository.php:32", "connection": "osool_test_db", "start_percent": 79.07, "width_percent": 1.279}, {"sql": "select count(*) as aggregate from `work_orders` where `start_date` <= '2025-07-27 12:03:26' and `is_deleted` <> 'yes' and ((`schedule_start_time` is not null and schedule_start_time != '' and TIME(schedule_start_time) < CURTIME()) or `schedule_start_time` is null or `schedule_start_time` = '') and `property_id` in (5936, 5947) and `asset_category_id` in (0) and `status` = 6", "type": "query", "params": [], "bindings": ["2025-07-27 12:03:26", "yes", "", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\WorkOrderRepository.php", "line": 32}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 24}, {"index": 19, "namespace": null, "name": "\\app\\Services\\DashboardWorkorderService.php", "line": 30}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Repositories\\WorkOrderRepository.php:32", "connection": "osool_test_db", "start_percent": 80.349, "width_percent": 1.032}, {"sql": "select count(*) as aggregate from `work_orders` where `start_date` <= '2025-07-27 12:03:26' and `is_deleted` <> 'yes' and ((`schedule_start_time` is not null and schedule_start_time != '' and TIME(schedule_start_time) < CURTIME()) or `schedule_start_time` is null or `schedule_start_time` = '') and `property_id` in (5936, 5947) and `asset_category_id` in (0) and `status` = 3", "type": "query", "params": [], "bindings": ["2025-07-27 12:03:26", "yes", "", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\WorkOrderRepository.php", "line": 32}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 18, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 24}, {"index": 19, "namespace": null, "name": "\\app\\Services\\DashboardWorkorderService.php", "line": 31}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Repositories\\WorkOrderRepository.php:32", "connection": "osool_test_db", "start_percent": 81.381, "width_percent": 1.584}, {"sql": "select `id`, `description`, `status`, `workorder_journey`, `contract_type`, `work_order_id`, `work_order_type`, `response_time`, `sp_approove`, `start_date`, `wtf_start_time`, `job_started_at`, `contract_type`, `priority_id` as `wo_priority_id`, `priority_id`, `pass_fail` from `work_orders` where `start_date` <= '2025-07-27 12:03:26' and `is_deleted` <> 'yes' and ((`schedule_start_time` is not null and schedule_start_time != '' and TIME(schedule_start_time) < CURTIME()) or `schedule_start_time` is null or `schedule_start_time` = '') and `property_id` in (5936, 5947) and `asset_category_id` in (0) order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["2025-07-27 12:03:26", "yes", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Repositories\\WorkOrderRepository.php", "line": 145}, {"index": 15, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 33}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\app\\Repositories\\Caching\\WorkOrderRepository.php", "line": 32}, {"index": 18, "namespace": null, "name": "\\app\\Services\\DashboardWorkorderService.php", "line": 54}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\app\\Repositories\\WorkOrderRepository.php:145", "connection": "osool_test_db", "start_percent": 82.965, "width_percent": 1.715}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3046}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3046", "connection": "osool_test_db", "start_percent": 84.68, "width_percent": 1.206}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 7070 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_test_db", "start_percent": 85.887, "width_percent": 1.41}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'building_manager' limit 1", "type": "query", "params": [], "bindings": ["building_manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1927}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1927", "connection": "osool_test_db", "start_percent": 87.297, "width_percent": 0.669}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 173 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_test_db", "start_percent": 87.965, "width_percent": 0.669}, {"sql": "select count(*) as aggregate from `maintanance_request` where `building_id` in ('5936', '5947') and `status` = 'pending'", "type": "query", "params": [], "bindings": ["5936", "5947", "pending"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\MaintenanceRequestTrait.php", "line": 14}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 201}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 77}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0016899999999999999, "duration_str": "1.69ms", "stmt_id": "\\app\\Http\\Traits\\MaintenanceRequestTrait.php:14", "connection": "osool_test_db", "start_percent": 88.634, "width_percent": 2.456}, {"sql": "select * from `users` where `id` = 7070 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 91.09, "width_percent": 1.25}, {"sql": "select * from `user_sub_privileges` where `user_sub_privileges`.`privilage_user_id` in (7070)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 21, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 92.34, "width_percent": 0.741}, {"sql": "select * from `user_type` where `user_type`.`slug` in ('building_manager')", "type": "query", "params": [], "bindings": ["building_manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4376}, {"index": 21, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4376", "connection": "osool_test_db", "start_percent": 93.081, "width_percent": 0.625}, {"sql": "select `id` from `sub_privileges` where `user_type` = 4 and `slug` = 'work_order_approve'", "type": "query", "params": [], "bindings": ["4", "work_order_approve"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 4377}, {"index": 14, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 230}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:4377", "connection": "osool_test_db", "start_percent": 93.706, "width_percent": 0.654}, {"sql": "select count(*) as aggregate from `maintanance_request` where `building_id` in ('5936', '5947') and `status` = 'pending'", "type": "query", "params": [], "bindings": ["5936", "5947", "pending"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\WorkOrders.php", "line": 2400}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 511}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.0019, "duration_str": "1.9ms", "stmt_id": "\\app\\Models\\WorkOrders.php:2400", "connection": "osool_test_db", "start_percent": 94.36, "width_percent": 2.762}, {"sql": "select exists(select * from `projects_details` where `id` = 173 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["173", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 97.122, "width_percent": 0.683}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7070) as `exists`", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 97.805, "width_percent": 0.712}, {"sql": "select exists(select * from `projects_details` where `id` = 173 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["173", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 98.517, "width_percent": 0.828}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7070) as `exists`", "type": "query", "params": [], "bindings": ["7070"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_test_db", "start_percent": 99.346, "width_percent": 0.654}]}, "models": {"data": {"App\\Models\\SubPrivileges": 1, "App\\Models\\UserTypes": 1, "App\\Models\\UserSubPrivileges": 1, "App\\Models\\CrmUser": 1, "App\\Models\\ReleaseNotes": 2, "App\\Models\\MaintenanceRequest": 1, "App\\Models\\Contracts": 4, "App\\Models\\AssetCategory": 4, "App\\Models\\WorkOrders": 2, "App\\Models\\ProjectsDetails": 2, "App\\Models\\UserCompany": 1, "App\\Models\\User": 3}, "count": 23}, "livewire": {"data": {"work-orders.dashboard #HANO8yBShzsXO2v5UNjk": "array:5 [\n  \"data\" => array:5 [\n    \"user\" => App\\Models\\User {#3820\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 12:03:07\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjA2OTg4LCJleHAiOjE3NTM2MTA1ODgsIm5iZiI6MTc1MzYwNjk4OCwianRpIjoiVzA3VG5PQTMxTWxsZXdZYiIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.aKYANSsHkRiJcEUihqU1a4enp7GiwI0fm9JOoOtWj2c\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 12:03:07\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjA2OTg4LCJleHAiOjE3NTM2MTA1ODgsIm5iZiI6MTc1MzYwNjk4OCwianRpIjoiVzA3VG5PQTMxTWxsZXdZYiIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.aKYANSsHkRiJcEUihqU1a4enp7GiwI0fm9JOoOtWj2c\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"userCompany\" => App\\Models\\UserCompany {#3827\n          #connection: \"mysql\"\n          #table: \"user_company\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #original: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #changes: []\n          #casts: array:2 [\n            \"user_id\" => \"int\"\n            \"company_id\" => \"int\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: array:2 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n          ]\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"company_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3847\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 12:03:09\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #original: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 12:03:09\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:54 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"contractsList\" => []\n    \"checkCreateButton\" => true\n    \"decryptedServiceProviderId\" => 1\n    \"explodedServiceProviders\" => array:1 [\n      0 => \"1\"\n    ]\n  ]\n  \"name\" => \"work-orders.dashboard\"\n  \"view\" => \"livewire.work-orders.dashboard\"\n  \"component\" => \"App\\Http\\Livewire\\WorkOrders\\Dashboard\"\n  \"id\" => \"HANO8yBShzsXO2v5UNjk\"\n]", "notifications.messages-notifications-list #gcU9HewDjBApO7N7Ll3i": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"khalil-project\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"gcU9HewDjBApO7N7Ll3i\"\n]", "notifications.new-notifications-list-top-nav #qj8RbGp1ED6a6kC94Goc": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"qj8RbGp1ED6a6kC94Goc\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3820\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 12:03:07\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjA2OTg4LCJleHAiOjE3NTM2MTA1ODgsIm5iZiI6MTc1MzYwNjk4OCwianRpIjoiVzA3VG5PQTMxTWxsZXdZYiIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.aKYANSsHkRiJcEUihqU1a4enp7GiwI0fm9JOoOtWj2c\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #original: array:78 [\n        \"id\" => 7070\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$050zfVjlzJ86xiJKeV3Eo.9KJcEjFcpsv39wfJCyE2kHWy/Jy6s3e\"\n        \"name\" => \"KH BMA from POA\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => null\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => \"5936,5947\"\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => \"2\"\n        \"role_cities\" => \"3,65\"\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => \"947,952\"\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"building_manager\"\n        \"user_privileges\" => \"{\"assets\": [\"create\", \"edit\", \"view\"], \"tenant\": [\"create\", \"edit\", \"view\"], \"contracts\": [\"create\", \"edit\", \"view\"], \"inventory\": [\"view\"], \"warehouse\": null, \"workorder\": [\"create\", \"edit\", \"view\"]}\"\n        \"approved_max_amount\" => null\n        \"created_by\" => 6721\n        \"project_id\" => 173\n        \"project_user_id\" => 6721\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-07-24 18:44:44\"\n        \"modified_at\" => \"2025-07-27 12:03:07\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"assigned_workers\" => null\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khalil-project\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNjA2OTg4LCJleHAiOjE3NTM2MTA1ODgsIm5iZiI6MTc1MzYwNjk4OCwianRpIjoiVzA3VG5PQTMxTWxsZXdZYiIsInN1YiI6IjY5NiIsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.aKYANSsHkRiJcEUihqU1a4enp7GiwI0fm9JOoOtWj2c\"\n        \"offline_mode\" => 0\n        \"sleep_mode\" => 0\n        \"salary\" => null\n        \"attendance_target\" => null\n        \"role\" => null\n        \"admin_level\" => null\n        \"attendance_mandatory\" => null\n        \"show_extra_info\" => 0\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:3 [\n        \"userCompany\" => App\\Models\\UserCompany {#3827\n          #connection: \"mysql\"\n          #table: \"user_company\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #original: array:5 [\n            \"id\" => 295\n            \"user_id\" => 7070\n            \"company_id\" => 124\n            \"created_at\" => \"2025-07-27 12:02:44\"\n            \"updated_at\" => \"2025-07-27 12:02:44\"\n          ]\n          #changes: []\n          #casts: array:2 [\n            \"user_id\" => \"int\"\n            \"company_id\" => \"int\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: array:2 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n          ]\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"company_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3847\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 12:03:09\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #original: array:23 [\n            \"id\" => 173\n            \"user_id\" => 7034\n            \"project_name\" => \"Khalil project\"\n            \"project_name_ar\" => \"مشروع خليل\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2024-05-13 17:18:02\"\n            \"updated_at\" => \"2025-07-27 12:03:09\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => \"1970-01-01 03:00:00\"\n            \"contract_end_date\" => \"1970-01-01 03:00:00\"\n            \"share_post\" => 1\n            \"deleted_at\" => null\n            \"crm_workspace_slug\" => \"khalil-project\"\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => App\\Models\\CrmUser {#4040\n          #connection: \"mysql\"\n          #table: \"crm_user\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 19\n            \"user_id\" => 7070\n            \"crm_user_id\" => 680\n            \"created_at\" => \"2025-07-24 18:44:46\"\n            \"updated_at\" => \"2025-07-24 18:44:46\"\n            \"instagram_connect\" => 0\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n            \"whatsapp_account_id\" => 0\n            \"whatsapp_number_status\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 19\n            \"user_id\" => 7070\n            \"crm_user_id\" => 680\n            \"created_at\" => \"2025-07-24 18:44:46\"\n            \"updated_at\" => \"2025-07-24 18:44:46\"\n            \"instagram_connect\" => 0\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n            \"whatsapp_account_id\" => 0\n            \"whatsapp_number_status\" => null\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"crm_user_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:54 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 1\n    \"projectId\" => null\n    \"project\" => App\\Models\\ProjectsDetails {#4693\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 173\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khalil project\"\n        \"project_name_ar\" => \"مشروع خليل\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 173\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khalil project\"\n        \"project_name_ar\" => \"مشروع خليل\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => null\n    \"flagWorkorderSidebarMenu\" => true\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => null\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu", "captcha_answer": "14", "_flash": "array:2 [\n  \"old\" => array:4 [\n    0 => \"flash_message\"\n    1 => \"flash_type\"\n    2 => \"flash_message\"\n    3 => \"flash_type\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/dashboards/admin\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7070", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]", "flash_message": "You are not authorized to access that page.", "flash_type": "warning"}, "request": {"path_info": "/dashboards/admin", "status_code": "<pre class=sf-dump id=sf-dump-552312242 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-552312242\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-676919929 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753607005436</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676919929\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1036197714 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753607005436</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036197714\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1105229488 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InBjdlBFVlFzVHRsNWs5QUIrMjJyb1E9PSIsInZhbHVlIjoiWTcwWXNBZFJrSW5ldkZ2OXR2MkhsVXQrakxvQndRcGdjdTRaRmdmSTBIYXBWME1RTlI5OTNGRnh1M3l4RW1hdGJTbDVIUG84U3lPdFdqMUVYTllydDcyVzU0Z3YzeVhmMTZmaW1FeWdkL1Q5SVhiOW1pOUVDTkVlU001RHdmREMiLCJtYWMiOiIyYTgzNGI3MzJmZTQ4NjZkN2ZhMDJhOTNhYmM1NGJlZGM2YTc2MzM0N2FjZDlmMzgxNTNmM2ZjNGZlN2EyYzAwIiwidGFnIjoiIn0%3D; osool_session=DkE3ngZTltbwIEmVxRaXIdo4MpXqQYFhqvvKbnB1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105229488\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-89214033 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InBjdlBFVlFzVHRsNWs5QUIrMjJyb1E9PSIsInZhbHVlIjoiWTcwWXNBZFJrSW5ldkZ2OXR2MkhsVXQrakxvQndRcGdjdTRaRmdmSTBIYXBWME1RTlI5OTNGRnh1M3l4RW1hdGJTbDVIUG84U3lPdFdqMUVYTllydDcyVzU0Z3YzeVhmMTZmaW1FeWdkL1Q5SVhiOW1pOUVDTkVlU001RHdmREMiLCJtYWMiOiIyYTgzNGI3MzJmZTQ4NjZkN2ZhMDJhOTNhYmM1NGJlZGM2YTc2MzM0N2FjZDlmMzgxNTNmM2ZjNGZlN2EyYzAwIiwidGFnIjoiIn0%3D; osool_session=DkE3ngZTltbwIEmVxRaXIdo4MpXqQYFhqvvKbnB1</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52205</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"15 characters\">_=1753607005436</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"15 characters\">_=1753607005436</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/dashboards/admin?_=1753607005436</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753607005.6729</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753607005</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89214033\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1135172105 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135172105\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1139400880 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 27 Jul 2025 09:03:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlI0WEhMNHFpOVlDRkp3QzIzajRRRVE9PSIsInZhbHVlIjoiZG14K3VjaURCTmwvQVFIRXhtUXFidDdZU0lVMWdYb2pIYk1FMGNNR1lQVzlPckVybnN2cE1xSllpSG9rQ2s2TmJ0TlN6VElSSlFlYkxxZklwaXp1d0gyb1dWODluSGxOcUNSU2dtRzgzM2o3Tnl6L0VvcUFXWExEZCszc3FVTWEiLCJtYWMiOiJkZGVkZGFiOGQ1MDM3NjBiY2E0Njk1M2Y5ODM3ZmNjYTFhYmMzMzg2NWRjNjNhZDQ0NDJlMWJjOGE0YjgxZGQ4IiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:03:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6InVGd2RxdXM0TFBieExwaWtKMmFhNFE9PSIsInZhbHVlIjoiWlBLdmpmcnNYVmdNRG8vN0dtWkpmaG8xWFBFT05OOUtjSzdab0hKK0d5ZlE0QnNpTHVDeGxHbGpJWkZGTGNzVDd4RlBJQjdySFFXYmVTbUpucld6aFh3NGlkQ1Z5OWdrNFN4U2pISXJOc2VaMUU0VGVSSFdNWWJmUWNJN2Fld0QiLCJtYWMiOiJiMGU5NzdiNzkzNWE0MmI0ZmY0ODM3MWZjMDIwZjU2OGQxZjkyZTc3YTU1ZWJlNjdhZDlkNzRhZjExYjlhZDRjIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:03:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlI0WEhMNHFpOVlDRkp3QzIzajRRRVE9PSIsInZhbHVlIjoiZG14K3VjaURCTmwvQVFIRXhtUXFidDdZU0lVMWdYb2pIYk1FMGNNR1lQVzlPckVybnN2cE1xSllpSG9rQ2s2TmJ0TlN6VElSSlFlYkxxZklwaXp1d0gyb1dWODluSGxOcUNSU2dtRzgzM2o3Tnl6L0VvcUFXWExEZCszc3FVTWEiLCJtYWMiOiJkZGVkZGFiOGQ1MDM3NjBiY2E0Njk1M2Y5ODM3ZmNjYTFhYmMzMzg2NWRjNjNhZDQ0NDJlMWJjOGE0YjgxZGQ4IiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:03:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6InVGd2RxdXM0TFBieExwaWtKMmFhNFE9PSIsInZhbHVlIjoiWlBLdmpmcnNYVmdNRG8vN0dtWkpmaG8xWFBFT05OOUtjSzdab0hKK0d5ZlE0QnNpTHVDeGxHbGpJWkZGTGNzVDd4RlBJQjdySFFXYmVTbUpucld6aFh3NGlkQ1Z5OWdrNFN4U2pISXJOc2VaMUU0VGVSSFdNWWJmUWNJN2Fld0QiLCJtYWMiOiJiMGU5NzdiNzkzNWE0MmI0ZmY0ODM3MWZjMDIwZjU2OGQxZjkyZTc3YTU1ZWJlNjdhZDlkNzRhZjExYjlhZDRjIiwidGFnIjoiIn0%3D; expires=Sun, 27-Jul-2025 11:03:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1139400880\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2044112205 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psw36qBzypL6JyWjxc2PmrxzxiiI3Z2PRPEtaTsu</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">flash_message</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">flash_type</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">flash_message</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">flash_type</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7070</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>flash_message</span>\" => \"<span class=sf-dump-str title=\"43 characters\">You are not authorized to access that page.</span>\"\n  \"<span class=sf-dump-key>flash_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044112205\", {\"maxDepth\":0})</script>\n"}}